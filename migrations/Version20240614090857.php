<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240614090857 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE customer ADD return_transactions_count INT NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE customer ADD return_transactions_amount NUMERIC(14, 2) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE customer ADD average_return_transaction_amount NUMERIC(14, 2) NOT NULL DEFAULT 0');
    }
}
