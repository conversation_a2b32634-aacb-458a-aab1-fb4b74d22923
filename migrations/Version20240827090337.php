<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240827090337 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE billable_members_per_tenant (billable_members_per_tenant_id UUID NOT NULL, period DATE NOT NULL, tenant_id VARCHAR(255) NOT NULL, billable_members_count INT NOT NULL, created_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, created_by VARCHAR(255) DEFAULT NULL, updated_by VARCHAR(255) DEFAULT NULL, billable_report_item_id UUID NOT NULL, PRIMARY KEY(billable_members_per_tenant_id))');
        $this->addSql('COMMENT ON COLUMN billable_members_per_tenant.billable_members_per_tenant_id IS \'(DC2Type:billable_members_per_tenant_id)\'');
        $this->addSql('COMMENT ON COLUMN billable_members_per_tenant.period IS \'(DC2Type:date_immutable)\'');
        $this->addSql('COMMENT ON COLUMN billable_members_per_tenant.tenant_id IS \'(DC2Type:store_id)\'');
        $this->addSql('COMMENT ON COLUMN billable_members_per_tenant.created_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN billable_members_per_tenant.updated_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('CREATE TABLE billable_transactions_per_tenant (billable_transactions_per_tenant_id UUID NOT NULL, period DATE NOT NULL, tenant_id VARCHAR(255) NOT NULL, billable_transactions_count INT NOT NULL, created_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, created_by VARCHAR(255) DEFAULT NULL, updated_by VARCHAR(255) DEFAULT NULL, billable_report_item_id UUID NOT NULL, PRIMARY KEY(billable_transactions_per_tenant_id))');
        $this->addSql('COMMENT ON COLUMN billable_transactions_per_tenant.billable_transactions_per_tenant_id IS \'(DC2Type:billable_transactions_per_tenant_id)\'');
        $this->addSql('COMMENT ON COLUMN billable_transactions_per_tenant.period IS \'(DC2Type:date_immutable)\'');
        $this->addSql('COMMENT ON COLUMN billable_transactions_per_tenant.tenant_id IS \'(DC2Type:store_id)\'');
        $this->addSql('COMMENT ON COLUMN billable_transactions_per_tenant.created_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN billable_transactions_per_tenant.updated_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('CREATE UNIQUE INDEX billable_members_per_tenant_period_tenant_id_unique ON billable_members_per_tenant (period, tenant_id)');
        $this->addSql('CREATE UNIQUE INDEX billable_transactions_per_tenant_period_tenant_id_unique ON billable_transactions_per_tenant (period, tenant_id)');
    }
}
