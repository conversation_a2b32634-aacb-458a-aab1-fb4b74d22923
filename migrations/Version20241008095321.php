<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241008095321 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE bulk_action (bulk_action_id UUID NOT NULL, store_id VARCHAR(255) NOT NULL, requested_by_admin_at TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL, criteria JSON NOT NULL, message TEXT DEFAULT NULL, error_details TEXT DEFAULT NULL, created_at TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL, created_by <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT NULL, updated_by VARCHAR(255) DEFAULT NULL, finished_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, processed_row_count INT DEFAULT NULL, type_value VARCHAR(255) NOT NULL, status_value VARCHAR(255) NOT NULL, PRIMARY KEY(bulk_action_id))');
        $this->addSql('CREATE INDEX IDX_671AAAFBB092A811 ON bulk_action (store_id)');
        $this->addSql('CREATE INDEX bulkActionCreatedAtIdx ON bulk_action (created_at)');
        $this->addSql('COMMENT ON COLUMN bulk_action.bulk_action_id IS \'(DC2Type:bulk_action_id)\'');
        $this->addSql('COMMENT ON COLUMN bulk_action.store_id IS \'(DC2Type:store_id)\'');
        $this->addSql('COMMENT ON COLUMN bulk_action.requested_by_admin_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN bulk_action.criteria IS \'(DC2Type:json_array)\'');
        $this->addSql('COMMENT ON COLUMN bulk_action.created_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN bulk_action.updated_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN bulk_action.finished_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE bulk_action_item (bulk_action_item_id UUID NOT NULL, store_id VARCHAR(255) NOT NULL, bulk_action_id UUID NOT NULL, object_to_process_id VARCHAR(255) NOT NULL, error_message TEXT DEFAULT NULL, created_at TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL, created_by VARCHAR(255) DEFAULT NULL, updated_by VARCHAR(255) DEFAULT NULL, package_number INT DEFAULT NULL, error_details TEXT DEFAULT NULL, status_value VARCHAR(255) NOT NULL, dtype VARCHAR(255) NOT NULL, PRIMARY KEY(bulk_action_item_id))');
        $this->addSql('CREATE INDEX IDX_640116EEB092A811 ON bulk_action_item (store_id)');
        $this->addSql('CREATE INDEX IDX_640116EE765AF47F ON bulk_action_item (bulk_action_id)');
        $this->addSql('CREATE INDEX bulkActionItemCreatedAtIdx ON bulk_action_item (created_at)');
        $this->addSql('COMMENT ON COLUMN bulk_action_item.bulk_action_item_id IS \'(DC2Type:bulk_action_item_id)\'');
        $this->addSql('COMMENT ON COLUMN bulk_action_item.store_id IS \'(DC2Type:store_id)\'');
        $this->addSql('COMMENT ON COLUMN bulk_action_item.bulk_action_id IS \'(DC2Type:bulk_action_id)\'');
        $this->addSql('COMMENT ON COLUMN bulk_action_item.created_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN bulk_action_item.updated_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('ALTER TABLE bulk_action ADD CONSTRAINT FK_671AAAFBB092A811 FOREIGN KEY (store_id) REFERENCES store (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE bulk_action_item ADD CONSTRAINT FK_640116EEB092A811 FOREIGN KEY (store_id) REFERENCES store (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE bulk_action_item ADD CONSTRAINT FK_640116EE765AF47F FOREIGN KEY (bulk_action_id) REFERENCES bulk_action (bulk_action_id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
    }
}
