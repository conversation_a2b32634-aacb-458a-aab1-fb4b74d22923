<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230531091923 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql("UPDATE settings set string_value = 'active_points' WHERE setting_key = 'levelDowngradeBase' AND string_value = 'none'");
    }
}
