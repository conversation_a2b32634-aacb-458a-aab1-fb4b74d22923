<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240429115932 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX achievementProgressCanceledAtIdx ON achievement_member_progress (canceled_at)');
        $this->addSql('CREATE INDEX achievementProgressMemberCompletedIdx ON achievement_member_progress (achievement_id, completed_at, canceled_at)');
    }
}
