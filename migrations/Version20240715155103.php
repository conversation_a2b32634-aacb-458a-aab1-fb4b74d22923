<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240715155103 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            DELETE FROM tier_condition
            WHERE id NOT IN (
                SELECT MIN(id)
                FROM tier_condition
                GROUP BY level_id, condition_id
            )
        ');

        $this->addSql('CREATE UNIQUE INDEX IF NOT EXISTS unique_level_condition ON tier_condition (level_id, condition_id)');
    }
}
