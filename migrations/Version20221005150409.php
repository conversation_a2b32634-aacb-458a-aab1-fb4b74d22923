<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\ORM\EntityManagerInterface;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Repository;
use OpenLoyalty\User\Infrastructure\Entity\Admin;
use OpenLoyalty\User\Infrastructure\Entity\Customer;
use OpenLoyalty\User\Infrastructure\Entity\Repository\DoctrineAdminRepository;
use OpenLoyalty\User\Infrastructure\Persistence\Doctrine\Repository\DoctrineUserRepository;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

final class Version20221005150409 extends AbstractMigration implements ContainerAwareInterface
{
    private ?ContainerInterface $container;

    public function setContainer(ContainerInterface $container = null): void
    {
        $this->container = $container;
    }

    public function getDescription(): string
    {
        return 'Decoupling symfony roles from acl roles.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE "user" ADD roles JSON NOT NULL DEFAULT \'{}\';');
    }

    public function postUp(Schema $schema): void
    {
        if (null === $this->container) {
            $this->write('The migration has been performed, but the data in the roles column in the user table needs to be completed.');

            return;
        }

        /**
         * @var Repository $customerRepository
         */
        $customerRepository = $this->container->get(DoctrineUserRepository::class);
        /**
         * @var Repository $adminRepository
         */
        $adminRepository = $this->container->get(DoctrineAdminRepository::class);
        /**
         * @var EntityManagerInterface $entityManager
         */
        $entityManager = $this->container->get('doctrine.orm.entity_manager');

        $usersWithRoles = $this->connection->fetchAllAssociative('SELECT ur.user_id, r.role FROM users_roles ur LEFT JOIN roles r ON ur.role_id = r.id;');
        foreach ($usersWithRoles as $userWithRole) {
            $id = new CustomerId($userWithRole['user_id']);
            /** @var Customer $user */
            $user = $customerRepository->find($id);
            if (null === $user) {
                /** @var Admin $user */
                $user = $adminRepository->find($id);
            }

            $user->addRole($userWithRole['role']);
        }

        $entityManager->flush();
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE "user" DROP roles');
    }
}
