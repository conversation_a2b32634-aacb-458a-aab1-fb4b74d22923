<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220510142012 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE reward ALTER tax TYPE NUMERIC(14, 3)');
        $this->addSql('ALTER TABLE segment_criterion ALTER percent TYPE NUMERIC(14, 3)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE reward ALTER tax TYPE NUMERIC(14, 2)');
        $this->addSql('ALTER TABLE segment_criterion ALTER percent TYPE NUMERIC(14, 2)');
    }
}
