<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241105140057 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE webhook_payload (webhook_payload_id UUID NOT NULL, store_id VARCHAR(255) NOT NULL, trigger_id VARCHAR(255) NOT NULL, trigger_type VARCHAR(255) NOT NULL, payload_type TEXT NOT NULL, payload JSON NOT NULL, created_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, created_by <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT NULL, updated_by VARCHAR(255) DEFAULT NULL, PRIMARY KEY(webhook_payload_id))');
        $this->addSql('CREATE INDEX webhookPayloadCreatedAtIdx ON webhook_payload (created_at)');
        $this->addSql('CREATE INDEX webhookPayloadFindByIdx ON webhook_payload (store_id, trigger_id, trigger_type, payload_type)');
        $this->addSql('COMMENT ON COLUMN webhook_payload.webhook_payload_id IS \'(DC2Type:webhook_payload_id)\'');
        $this->addSql('COMMENT ON COLUMN webhook_payload.store_id IS \'(DC2Type:store_id)\'');
        $this->addSql('COMMENT ON COLUMN webhook_payload.payload_type IS \'(DC2Type:webhook_payload_type)\'');
        $this->addSql('COMMENT ON COLUMN webhook_payload.created_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN webhook_payload.updated_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
    }
}
