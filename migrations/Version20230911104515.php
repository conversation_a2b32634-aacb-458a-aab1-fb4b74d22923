<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230911104515 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX IF NOT EXISTS campaignEffectResultWalletTypeIdx ON campaign_calculated_effect_result (wallet_type_id)');
        $this->addSql('CREATE INDEX IF NOT EXISTS transferOwnerIdx ON transfer (owner)');
    }
}
