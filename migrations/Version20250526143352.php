<?php

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250526143352 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP SEQUENCE refresh_tokens_id_seq CASCADE');
        $this->addSql('CREATE TABLE leaderboard (leaderboard_id UUID NOT NULL, store_id VARCHAR(255) NOT NULL, campaign_id UUID NOT NULL, iter_period VARCHAR(255) NOT NULL, period_from VARCHAR(255) NOT NULL, period_to VARCHAR(255) NOT NULL, recalculated_at TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL, completed_at TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL, metric_type VARCHAR(255) NOT NULL, metric_wallet_type_code VARCHAR(255) NOT NULL, PRIMARY KEY(leaderboard_id))');
        $this->addSql('CREATE INDEX IDX_182E5253B092A811 ON leaderboard (store_id)');
        $this->addSql('CREATE INDEX IDX_182E5253F639F774 ON leaderboard (campaign_id)');
        $this->addSql('CREATE UNIQUE INDEX leaderboardReadModelIdx ON leaderboard (store_id, leaderboard_id, iter_period)');
        $this->addSql('COMMENT ON COLUMN leaderboard.leaderboard_id IS \'(DC2Type:leaderboard_id)\'');
        $this->addSql('COMMENT ON COLUMN leaderboard.store_id IS \'(DC2Type:store_id)\'');
        $this->addSql('COMMENT ON COLUMN leaderboard.campaign_id IS \'(DC2Type:campaign_id)\'');
        $this->addSql('COMMENT ON COLUMN leaderboard.recalculated_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN leaderboard.completed_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('ALTER TABLE leaderboard ADD CONSTRAINT FK_182E5253B092A811 FOREIGN KEY (store_id) REFERENCES store (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE leaderboard ADD CONSTRAINT FK_182E5253F639F774 FOREIGN KEY (campaign_id) REFERENCES campaign (campaign_id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('DROP TABLE refresh_tokens');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('CREATE SEQUENCE refresh_tokens_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE refresh_tokens (id INT NOT NULL, refresh_token VARCHAR(128) NOT NULL, username VARCHAR(255) NOT NULL, valid TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX uniq_9bace7e1c74f2195 ON refresh_tokens (refresh_token)');
        $this->addSql('ALTER TABLE leaderboard DROP CONSTRAINT FK_182E5253B092A811');
        $this->addSql('ALTER TABLE leaderboard DROP CONSTRAINT FK_182E5253F639F774');
        $this->addSql('DROP TABLE leaderboard');
    }
}
