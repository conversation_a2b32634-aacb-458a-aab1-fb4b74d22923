<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220801222900 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE wallet_type ADD unit_days_expiry_after VARCHAR(255)');
        $this->addSql('ALTER TABLE wallet_type ADD unit_days_active_count INT');
        $this->addSql('ALTER TABLE wallet_type ADD unit_years_active_count INT');
        $this->addSql('ALTER TABLE wallet_type ADD unit_days_locked INT');
        $this->addSql('ALTER TABLE wallet_type ADD all_time_not_locked BOOLEAN');

        $storeIds = $this->connection->createQueryBuilder()
            ->select('s.id')
            ->from('store', 's')
            ->execute()->fetchAllAssociative();

        foreach ($storeIds as $storeId) {
            $unitDaysExpiryAfter = $this->connection->createQueryBuilder()
                ->select('s.id, s.string_value')
                ->from('settings', 's')
                ->where('store_id = :storeId')
                ->andWhere('setting_key = :key')
                ->setParameter('key', 'pointsDaysExpiryAfter')
                ->setParameter('storeId', $storeId['id'])
                ->execute()->fetchAssociative();

            $this->addSql("DELETE FROM settings WHERE id='".$unitDaysExpiryAfter['id']."'");

            $unitDaysActiveCount = $this->connection->createQueryBuilder()
                ->select('s.id, s.integer_value')
                ->from('settings', 's')
                ->where('store_id = :storeId')
                ->andWhere('setting_key = :key')
                ->setParameter('storeId', $storeId['id'])
                ->setParameter('key', 'pointsDaysActiveCount')
                ->execute()->fetchAssociative();

            $this->addSql("DELETE FROM settings WHERE id='".$unitDaysActiveCount['id']."'");

            $unitYearsActiveCount = $this->connection->createQueryBuilder()
                ->select('s.id, s.integer_value')
                ->from('settings', 's')
                ->where('store_id = :storeId')
                ->andWhere('setting_key = :key')
                ->setParameter('storeId', $storeId['id'])
                ->setParameter('key', 'pointsYearsActiveCount')
                ->execute()->fetchAssociative();

            $this->addSql("DELETE FROM settings WHERE id='".$unitYearsActiveCount['id']."'");

            $unitDaysLocked = $this->connection->createQueryBuilder()
                ->select('s.id, s.integer_value')
                ->from('settings', 's')
                ->where('store_id = :storeId')
                ->andWhere('setting_key = :key')
                ->setParameter('storeId', $storeId['id'])
                ->setParameter('key', 'pointsDaysLocked')
                ->execute()->fetchAssociative();

            $this->addSql("DELETE FROM settings WHERE id='".$unitDaysLocked['id']."'");

            $allTimeNotLocked = $this->connection->createQueryBuilder()
                ->select('s.id, s.boolean_value')
                ->from('settings', 's')
                ->where('store_id = :storeId')
                ->andWhere('setting_key = :key')
                ->setParameter('storeId', $storeId['id'])
                ->setParameter('key', 'allTimeNotLocked')
                ->execute()->fetchAssociative();

            $this->addSql("DELETE FROM settings WHERE id='".$allTimeNotLocked['id']."'");

            $qry = 'UPDATE wallet_type SET ';

            $qry .= "unit_days_expiry_after ='".($unitDaysExpiryAfter['string_value'] ?? 'after_x_days')."',";
            $qry .= 'unit_days_active_count ='.($unitDaysActiveCount['integer_value'] ?? 30).',';
            $qry .= 'unit_years_active_count ='.($unitYearsActiveCount['integer_value'] ?? 0).',';
            $qry .= 'unit_days_locked ='.($unitDaysLocked['integer_value'] ?? 10).',';
            if (true === ($allTimeNotLocked['boolean_value'] ?? true)) {
                $qry .= 'all_time_not_locked = true';
            } else {
                $qry .= 'all_time_not_locked = false';
            }

            $this->addSql($qry." WHERE store_id = '".$storeId['id']."' AND is_default = true");
            $this->addSql("UPDATE wallet_type SET unit_days_expiry_after= 'after_x_days', all_time_not_locked = false WHERE store_id = '".$storeId['id']."' AND is_default = false");
        }
    }

    public function down(Schema $schema): void
    {
    }
}
