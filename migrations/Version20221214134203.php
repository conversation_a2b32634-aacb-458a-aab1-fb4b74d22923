<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221214134203 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Added new table for segment customers';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE segment_customers (segment_customer_id UUID NOT NULL, segment_id UUID DEFAULT NULL, customer_id UUID NOT NULL, store_id VARCHAR(255) NOT NULL, PRIMARY KEY(segment_customer_id))');
        $this->addSql('CREATE INDEX IDX_FC0F130DB296AAD ON segment_customers (segment_id)');
        $this->addSql('CREATE UNIQUE INDEX unique_segment_customer_idx ON segment_customers (segment_id, customer_id)');
        $this->addSql('COMMENT ON COLUMN segment_customers.segment_customer_id IS \'(DC2Type:segment_customer_id)\'');
        $this->addSql('COMMENT ON COLUMN segment_customers.segment_id IS \'(DC2Type:segment_id)\'');
        $this->addSql('COMMENT ON COLUMN segment_customers.customer_id IS \'(DC2Type:customer_id)\'');
        $this->addSql('COMMENT ON COLUMN segment_customers.store_id IS \'(DC2Type:store_id)\'');
        $this->addSql('ALTER TABLE segment_customers ADD CONSTRAINT FK_FC0F130DB296AAD FOREIGN KEY (segment_id) REFERENCES segment (segment_id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE segment_customers');
    }
}
