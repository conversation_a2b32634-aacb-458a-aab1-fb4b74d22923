<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230223105831 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add import tables';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE import (import_id UUID NOT NULL, store_id VARCHAR(255) NOT NULL, original_filename VARCHAR(255) NOT NULL, filename VARCHAR(255) NOT NULL, uploaded_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, items_to_import INT NOT NULL, error_message TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by VARCHAR(255) DEFAULT NULL, updated_by VARCHAR(255) DEFAULT NULL, type_value VARCHAR(255) NOT NULL, status_value VARCHAR(255) NOT NULL, dtype VARCHAR(255) NOT NULL, PRIMARY KEY(import_id))');
        $this->addSql('CREATE INDEX IDX_9D4ECE1DB092A811 ON import (store_id)');
        $this->addSql('COMMENT ON COLUMN import.import_id IS \'(DC2Type:import_id)\'');
        $this->addSql('COMMENT ON COLUMN import.store_id IS \'(DC2Type:store_id)\'');
        $this->addSql('COMMENT ON COLUMN import.uploaded_date IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN import.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN import.updated_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE import_item (import_item_id UUID NOT NULL, import_id UUID NOT NULL, store_id VARCHAR(255) NOT NULL, item_body TEXT NOT NULL, error_message TEXT DEFAULT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, updated_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by VARCHAR(255) DEFAULT NULL, updated_by VARCHAR(255) DEFAULT NULL, status_value VARCHAR(255) NOT NULL, entity_id VARCHAR(255) DEFAULT NULL, entity_data JSON DEFAULT NULL, dtype VARCHAR(255) NOT NULL, PRIMARY KEY(import_item_id))');
        $this->addSql('CREATE INDEX IDX_F237CB9EB6A263D9 ON import_item (import_id)');
        $this->addSql('CREATE INDEX IDX_F237CB9EB092A811 ON import_item (store_id)');
        $this->addSql('COMMENT ON COLUMN import_item.import_item_id IS \'(DC2Type:import_item_id)\'');
        $this->addSql('COMMENT ON COLUMN import_item.import_id IS \'(DC2Type:import_id)\'');
        $this->addSql('COMMENT ON COLUMN import_item.store_id IS \'(DC2Type:store_id)\'');
        $this->addSql('COMMENT ON COLUMN import_item.created_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN import_item.updated_at IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('COMMENT ON COLUMN import_item.entity_data IS \'(DC2Type:json_array)\'');
        $this->addSql('ALTER TABLE import ADD CONSTRAINT FK_9D4ECE1DB092A811 FOREIGN KEY (store_id) REFERENCES store (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE import_item ADD CONSTRAINT FK_F237CB9EB6A263D9 FOREIGN KEY (import_id) REFERENCES import (import_id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE import_item ADD CONSTRAINT FK_F237CB9EB092A811 FOREIGN KEY (store_id) REFERENCES store (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE import_item DROP CONSTRAINT FK_F237CB9EB6A263D9');
        $this->addSql('DROP TABLE import');
        $this->addSql('DROP TABLE import_item');
    }
}
