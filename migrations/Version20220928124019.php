<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220928124019 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add new column in transaction table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE transaction ADD attempt_match INT DEFAULT 0 NOT NULL');
        $this->addSql('update transaction set attempt_match = 1 where matched = true');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE transaction DROP attempt_match');
    }
}
