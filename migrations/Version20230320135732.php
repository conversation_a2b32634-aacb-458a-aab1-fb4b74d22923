<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230320135732 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('COMMENT ON COLUMN data_analytics_event.creation_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_member.registration_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_member.update_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_member.deletion_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_member_tier.assignment_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_member_transaction.registration_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_segment.creation_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_segment.update_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_tenant.creation_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_tier.creation_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_tier.update_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
    }
}
