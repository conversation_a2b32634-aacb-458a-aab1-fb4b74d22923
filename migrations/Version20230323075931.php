<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230323075931 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE data_analytics_member_activity (member_activity_id UUID NOT NULL, member_id UUID NOT NULL, activity_change_date TIMESTAMP(6) NOT NULL, tenant_id VARCHAR(255) NOT NULL, activity_type_type VARCHAR(255) NOT NULL, active_member_settings_transaction_in_xdays INT DEFAULT NULL, PRIMARY KEY(member_activity_id))');
        $this->addSql('CREATE INDEX memberActivityTenantIdIdx ON data_analytics_member_activity (tenant_id)');
        $this->addSql('CREATE INDEX memberActivityChangeDateIdx ON data_analytics_member_activity (activity_change_date)');
        $this->addSql('COMMENT ON COLUMN data_analytics_member_activity.member_activity_id IS \'(DC2Type:member_activity_id)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_member_activity.member_id IS \'(DC2Type:customer_id)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_member_activity.activity_change_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_member_activity.tenant_id IS \'(DC2Type:store_id)\'');
    }
}
