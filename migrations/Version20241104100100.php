<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241104100100 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX IF NOT EXISTS importItemImportIdStatusValuePackageNumberIdx ON import_item (import_id, status_value, package_number)');
        $this->addSql('CREATE INDEX IF NOT EXISTS importItemImportIdStatusValueIdx ON import_item (import_id, status_value)');
    }
}
