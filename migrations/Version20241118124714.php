<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241118124714 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE achievement_member_progress_rule_item ADD trigger_id UUID DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN achievement_member_progress_rule_item.trigger_id IS \'(DC2Type:uuid)\'');
    }
}
