<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231207144258 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE data_analytics_unit_blocked (transfer_id UUID NOT NULL, tenant_id VARCHAR(255) NOT NULL, member_id UUID NOT NULL, wallet_type_code VARCHAR(255) NOT NULL, value NUMERIC(24, 12) NOT NULL, blocked_at TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL, is_cancelled BOOLEAN DEFAULT \'false\' NOT NULL, cancellation_date TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(transfer_id))');
        $this->addSql('COMMENT ON COLUMN data_analytics_unit_blocked.transfer_id IS \'(DC2Type:transfer_id)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_unit_blocked.tenant_id IS \'(DC2Type:store_id)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_unit_blocked.member_id IS \'(DC2Type:customer_id)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_unit_blocked.blocked_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN data_analytics_unit_blocked.cancellation_date IS \'(DC2Type:datetime_immutable_microseconds)\'');
    }
}
