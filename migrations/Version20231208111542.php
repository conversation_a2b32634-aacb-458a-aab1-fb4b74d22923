<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231208111542 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE customer ADD registered_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL');
        $this->addSql('COMMENT ON COLUMN customer.registered_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('update customer set registered_at = created_at');
        $this->addSql('CREATE INDEX IF NOT EXISTS member_registered_at_idx ON customer (registered_at)');
    }
}
