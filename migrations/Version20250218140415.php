<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250218140415 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE campaign_rule_effect SET units_expiration_rule_expression = NULL WHERE units_expiration_rule_expiration_rule <> \'expression\'');
        $this->addSql('UPDATE campaign_rule_effect SET units_lock_rule_expression = NULL WHERE units_lock_rule_lock_rule <> \'expression\'');
        $this->addSql('UPDATE campaign_read_model SET recreate_required_on = NOW() WHERE recreate_required_on IS NULL');
    }
}
