<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\ORM\EntityManagerInterface;
use OpenLoyalty\Core\Domain\Enum\TierMode;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TierSetId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Level\Domain\Entity\Condition;
use OpenLoyalty\Level\Domain\Entity\ConditionValue;
use OpenLoyalty\Level\Domain\Enum\ConditionAttribute;
use OpenLoyalty\Level\Domain\Enum\DowngradeMode;
use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Domain\TierSet;
use OpenLoyalty\Level\Domain\TierSetTranslation;
use OpenLoyalty\Level\Domain\ValueObject\ConditionId;
use OpenLoyalty\Level\Domain\ValueObject\Downgrade;
use OpenLoyalty\Settings\Infrastructure\Entity\SettingsEntry;
use OpenLoyalty\Settings\Infrastructure\Entity\StringSettingEntry;
use OpenLoyalty\Settings\Infrastructure\Model\SettingsNames;
use OpenLoyalty\User\Domain\Provider\TierAssignTypeProvider;
use OpenLoyalty\User\Infrastructure\LevelDowngradeModeProvider;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

final class Version20240226124309 extends AbstractMigration implements ContainerAwareInterface
{
    private ?ContainerInterface $container;

    public function setContainer(ContainerInterface $container = null): void
    {
        $this->container = $container;
    }

    public function up(Schema $schema): void
    {
        /**
         * @var EntityManagerInterface $entityManager
         */
        $entityManager = $this->container->get('doctrine.orm.entity_manager');

        $storeIds = $this->connection->createQueryBuilder()
            ->select('DISTINCT l.store_id')
            ->from('level', 'l')
            ->execute()->fetchAllAssociative();

        $this->updateDefaultSettingDowngradeMode($storeIds);
        foreach ($storeIds as $storeId) {
            $downgradeMode = $this->getSettingsByKey(
                SettingsNames::LEVEL_DOWNGRADE_MODE->value,
                new StoreId($storeId['store_id'])
            );

            if ('after_x_days' === $downgradeMode->getValue()) {
                continue;
            }

            $tierModeSettingsEntry = $this->getSettingsByKey(
                SettingsNames::TIERS_MODE->value,
                new StoreId($storeId['store_id']),
                TierMode::TRADITIONAL_MODE->value
            );

            if (null !== $tierModeSettingsEntry) {
                $tierModeSettingsEntry->setValue(TierMode::CUSTOM_MODE->value);
                $entityManager->persist($tierModeSettingsEntry);
            }

            $tierAssignTypeSettingsEntry = $this->getSettingsByKey('tierAssignType', new StoreId($storeId['store_id']));
            $tierWalletCodeSettingsEntry = $this->getSettingsByKey(SettingsNames::TIER_WALLET_CODE->value, new StoreId($storeId['store_id']));

            $defaultTierSet = $entityManager->createQueryBuilder()
                ->select('t')
                ->from(TierSet::class, 't')
                ->where('t.store = :id')
                ->andWhere('t.isDefault = :isDefault')
                ->setParameter('id', new StoreId($storeId['store_id']))
                ->setParameter('isDefault', true)
                ->getQuery()->getOneOrNullResult();

            /**
             * @var Store $store
             */
            $store = $entityManager->createQueryBuilder()
                ->select('s')
                ->from(Store::class, 's')
                ->where('s.storeId = :id')
                ->setParameter('id', new StoreId($storeId['store_id']))
                ->getQuery()->getOneOrNullResult();
            /**
             * @var UuidGeneratorInterface $uuidGenerator
             */
            $uuidGenerator = $this->container->get(UuidGeneratorInterface::class);
            if (null === $defaultTierSet) {
                $tierType = $tierAssignTypeSettingsEntry->getValue();
                $attribute = TierAssignTypeProvider::TYPE_TRANSACTIONS === $tierType ? ConditionAttribute::TOTAL_SPENDING->value : ConditionAttribute::ACTIVE_UNITS->value;
                $walletType = null !== $tierWalletCodeSettingsEntry ? $tierWalletCodeSettingsEntry->getValue() : 'default';

                $condition = new Condition(
                    new ConditionId($uuidGenerator->generate()),
                    $attribute,
                    $attribute === ConditionAttribute::ACTIVE_UNITS->value ? $walletType : null
                );

                $downgrade = $downgradeMode->getValue() === DowngradeMode::AutomaticDowngrade->value ? DowngradeMode::AutomaticDowngrade : DowngradeMode::NoDowngrade;

                $translation = new TierSetTranslation();
                $translation->setName('Default Tier set');

                //default TierSet
                $tierSet = new TierSet(
                    new TierSetId($uuidGenerator->generate()),
                    $store,
                    true,
                    [$condition],
                    new Downgrade($downgrade),
                    [],
                    true,
                    true
                );
                $tierSet->assignTranslations(['en' => $translation]);

                $entityManager->persist($tierSet);
                $tierSet->mergeNewTranslations();

                /**
                 * @var Level[] $tiers
                 */
                $tiers = $entityManager->createQueryBuilder()
                    ->select('l')
                    ->from(Level::class, 'l')
                    ->where('l.store = :id')
                    ->setParameter('id', new StoreId($storeId['store_id']))
                    ->orderBy('l.conditionValue', 'ASC')
                    ->getQuery()->getResult();

                $sortOrder = 0;
                /**
                 * @var Level $tier
                 */
                foreach ($tiers as $tier) {
                    $conditionValue = new ConditionValue($condition, $tier->getConditionValue());
                    if (0.0 === $tier->getConditionValue()) {
                        $tier->markAsDefault();
                    }
                    $tier->setTierSet($tierSet);
                    $tier->setConditions([$conditionValue]);
                    $tier->changeSortOrder($sortOrder);

                    $entityManager->persist($tier);
                    $entityManager->flush();
                    ++$sortOrder;
                }
            }
        }
    }

    private function getSettingsByKey(string $key, StoreId $storeId, ?string $stringValue = null): ?SettingsEntry
    {
        /**
         * @var EntityManagerInterface $entityManager
         */
        $entityManager = $this->container->get('doctrine.orm.entity_manager');

        $query = $entityManager->createQueryBuilder()
            ->select('s')
            ->from(StringSettingEntry::class, 's')
            ->where('s.store = :id')
            ->andWhere('s.key = :key')
            ->setParameter('id', $storeId)
            ->setParameter('key', $key);
        if (null !== $stringValue) {
            $query
                ->andWhere('s.stringValue = :value')
                ->setParameter('value', $stringValue);
        }

        /**
         * @var ?SettingsEntry $result
         */
        $result = $query->getQuery()->getOneOrNullResult();

        return $result;
    }

    private function updateDefaultSettingDowngradeMode(array $storeIds): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = $this->container->get('doctrine.orm.entity_manager');
        foreach ($storeIds as $store) {
            $storeId = new StoreId($store['store_id']);
            $storeEntity = $entityManager->createQueryBuilder()
                ->select('s')
                ->from(Store::class, 's')
                ->where('s.storeId = :id')
                ->setParameter('id', $storeId)
                ->getQuery()->getOneOrNullResult();

            $downgradeMode = $this->getSettingsByKey(SettingsNames::LEVEL_DOWNGRADE_MODE->value, $storeId);
            if (null === $downgradeMode) {
                $entityManager->persist(new StringSettingEntry(
                    SettingsNames::LEVEL_DOWNGRADE_MODE->value,
                    LevelDowngradeModeProvider::MODE_NONE,
                    $storeEntity
                ));
            }
            $tierAssignType = $this->getSettingsByKey(SettingsNames::TIER_ASSIGN_TYPE_SETTINGS_KEY->value, $storeId);
            if (null === $tierAssignType) {
                $entityManager->persist(new StringSettingEntry(
                    SettingsNames::TIER_ASSIGN_TYPE_SETTINGS_KEY->value,
                    TierAssignTypeProvider::TYPE_POINTS,
                    $storeEntity
                ));
            }
            $tierWalletCode = $this->getSettingsByKey(SettingsNames::TIER_WALLET_CODE->value, $storeId);
            if (null === $tierWalletCode) {
                $entityManager->persist(new StringSettingEntry(
                    SettingsNames::TIER_WALLET_CODE->value,
                    'default',
                    $storeEntity
                ));
            }
        }
        $entityManager->flush();
    }
}
