<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241125105318 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('COMMENT ON COLUMN achievement_member_progress.unique_referees IS NULL');
        $this->addSql('COMMENT ON COLUMN activation_code.data IS NULL');
        $this->addSql('COMMENT ON COLUMN bulk_action.criteria IS NULL');
        $this->addSql('COMMENT ON COLUMN campaign_rule_condition.values IS NULL');
        $this->addSql('COMMENT ON COLUMN campaign_rule_condition.days IS NULL');
        $this->addSql('COMMENT ON COLUMN campaign_rule_condition.months IS NULL');
        $this->addSql('COMMENT ON COLUMN data_analytics_member_activity.active_member_settings_active_member_settings IS NULL');
        $this->addSql('COMMENT ON COLUMN export.criteria IS NULL');
        $this->addSql('COMMENT ON COLUMN import.additional_data IS NULL');
        $this->addSql('COMMENT ON COLUMN segment_criterion.labels IS NULL');
        $this->addSql('COMMENT ON COLUMN segment_criterion.gender_options IS NULL');
        $this->addSql('COMMENT ON COLUMN segment_criterion.channel_ids IS NULL');
        $this->addSql('COMMENT ON COLUMN segment_criterion.sku_ids IS NULL');
        $this->addSql('COMMENT ON COLUMN segment_criterion.customers IS NULL');
        $this->addSql('COMMENT ON COLUMN segment_criterion.countries IS NULL');
        $this->addSql('COMMENT ON COLUMN segment_criterion.cities IS NULL');
        $this->addSql('COMMENT ON COLUMN segment_criterion.postal IS NULL');
        $this->addSql('COMMENT ON COLUMN segment_criterion.makers IS NULL');
        $this->addSql('COMMENT ON COLUMN segment_criterion.provinces IS NULL');
        $this->addSql('COMMENT ON COLUMN segment_criterion.tiers IS NULL');
        $this->addSql('COMMENT ON COLUMN segment_criterion.levels IS NULL');
        $this->addSql('COMMENT ON COLUMN transaction.excluded_delivery_skus IS NULL');
        $this->addSql('COMMENT ON COLUMN transaction.excluded_skus IS NULL');
        $this->addSql('COMMENT ON COLUMN transaction.excluded_categories IS NULL');

        $this->addSql('COMMENT ON COLUMN import_item.entity_data IS \'(DC2Type:extended_json)\'');
        $this->addSql('COMMENT ON COLUMN campaign_rule_condition.value IS \'(DC2Type:extended_json)\'');
    }
}
