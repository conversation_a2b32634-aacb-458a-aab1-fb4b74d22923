<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\ORM\EntityManagerInterface;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Settings\Infrastructure\Entity\StringSettingEntry;
use OpenLoyalty\Settings\Infrastructure\Model\SettingsNames;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

final class Version20240209162151 extends AbstractMigration implements ContainerAwareInterface
{
    private ?ContainerInterface $container;

    public function setContainer(ContainerInterface $container = null): void
    {
        $this->container = $container;
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SEQUENCE tier_condition_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE tier_set_member_progress_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE tier_set_member_progress_condition_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE tier_set_translation_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE tier_condition (id INT NOT NULL, level_id UUID DEFAULT NULL, condition_id UUID NOT NULL, value NUMERIC(24, 12) NOT NULL, created_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, created_by VARCHAR(255) DEFAULT NULL, updated_by VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_AE61242D5FB14BA7 ON tier_condition (level_id)');
        $this->addSql('CREATE INDEX IDX_AE61242D887793B6 ON tier_condition (condition_id)');
        $this->addSql('COMMENT ON COLUMN tier_condition.level_id IS \'(DC2Type:level_id)\'');
        $this->addSql('COMMENT ON COLUMN tier_condition.condition_id IS \'(DC2Type:condition_id)\'');
        $this->addSql('COMMENT ON COLUMN tier_condition.created_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN tier_condition.updated_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('CREATE TABLE tier_set (tier_set_id UUID NOT NULL, store_id VARCHAR(255) NOT NULL, active BOOLEAN DEFAULT \'false\' NOT NULL, labels JSON DEFAULT NULL, created_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, created_by VARCHAR(255) DEFAULT NULL, updated_by VARCHAR(255) DEFAULT NULL, is_default BOOLEAN DEFAULT \'false\' NOT NULL, PRIMARY KEY(tier_set_id))');
        $this->addSql('CREATE INDEX IDX_2068749B092A811 ON tier_set (store_id)');
        $this->addSql('COMMENT ON COLUMN tier_set.tier_set_id IS \'(DC2Type:tier_set_id)\'');
        $this->addSql('COMMENT ON COLUMN tier_set.store_id IS \'(DC2Type:store_id)\'');
        $this->addSql('COMMENT ON COLUMN tier_set.labels IS \'(DC2Type:labels_json_array)\'');
        $this->addSql('COMMENT ON COLUMN tier_set.created_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN tier_set.updated_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('CREATE TABLE tier_set_condition (id UUID NOT NULL, tier_set_id UUID NOT NULL, attribute VARCHAR(255) NOT NULL, wallet_type VARCHAR(255) DEFAULT NULL, event_name VARCHAR(255) DEFAULT NULL, created_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, created_by VARCHAR(255) DEFAULT NULL, updated_by VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX tierSetIdx ON tier_set_condition (tier_set_id)');
        $this->addSql('CREATE UNIQUE INDEX uniqueAttributePerTierSetIdx ON tier_set_condition (tier_set_id, attribute, wallet_type)');
        $this->addSql('COMMENT ON COLUMN tier_set_condition.id IS \'(DC2Type:condition_id)\'');
        $this->addSql('COMMENT ON COLUMN tier_set_condition.tier_set_id IS \'(DC2Type:tier_set_id)\'');
        $this->addSql('COMMENT ON COLUMN tier_set_condition.created_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN tier_set_condition.updated_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('CREATE TABLE tier_set_member_progress (id INT NOT NULL, tier_set_id UUID NOT NULL, member_id UUID NOT NULL, current_level_id UUID DEFAULT NULL, created_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, created_by VARCHAR(255) DEFAULT NULL, updated_by VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_161A4F3884ABD8C4 ON tier_set_member_progress (tier_set_id)');
        $this->addSql('CREATE INDEX memberIdTierSetIdIdx ON tier_set_member_progress (member_id, tier_set_id)');
        $this->addSql('COMMENT ON COLUMN tier_set_member_progress.id IS \'(DC2Type:tier_set_member_progress_id)\'');
        $this->addSql('COMMENT ON COLUMN tier_set_member_progress.tier_set_id IS \'(DC2Type:tier_set_id)\'');
        $this->addSql('COMMENT ON COLUMN tier_set_member_progress.member_id IS \'(DC2Type:customer_id)\'');
        $this->addSql('COMMENT ON COLUMN tier_set_member_progress.current_level_id IS \'(DC2Type:level_id)\'');
        $this->addSql('COMMENT ON COLUMN tier_set_member_progress.created_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN tier_set_member_progress.updated_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('CREATE TABLE tier_set_member_progress_condition (id INT NOT NULL, tier_set_member_progress_id INT NOT NULL, condition_id UUID NOT NULL, value NUMERIC(24, 12) NOT NULL, created_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, updated_at TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, created_by VARCHAR(255) DEFAULT NULL, updated_by VARCHAR(255) DEFAULT NULL, recreate_required_on TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_6330A353C6BADBBE ON tier_set_member_progress_condition (tier_set_member_progress_id)');
        $this->addSql('CREATE INDEX recreateRequiredOnIdx ON tier_set_member_progress_condition (recreate_required_on)');
        $this->addSql('CREATE INDEX conditionIdIdx ON tier_set_member_progress_condition (condition_id)');
        $this->addSql('COMMENT ON COLUMN tier_set_member_progress_condition.tier_set_member_progress_id IS \'(DC2Type:tier_set_member_progress_id)\'');
        $this->addSql('COMMENT ON COLUMN tier_set_member_progress_condition.condition_id IS \'(DC2Type:condition_id)\'');
        $this->addSql('COMMENT ON COLUMN tier_set_member_progress_condition.created_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN tier_set_member_progress_condition.updated_at IS \'(DC2Type:datetime_immutable_microseconds)\'');
        $this->addSql('COMMENT ON COLUMN tier_set_member_progress_condition.recreate_required_on IS \'(DC2Type:datetime_immutable)\'');
        $this->addSql('CREATE TABLE tier_set_translation (id INT NOT NULL, translatable_id UUID NOT NULL, name VARCHAR(255) DEFAULT NULL, description TEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_3A2C022D2C2AC5D3 ON tier_set_translation (translatable_id)');
        $this->addSql('CREATE UNIQUE INDEX tier_set_translation_unique_translation ON tier_set_translation (translatable_id, locale)');
        $this->addSql('COMMENT ON COLUMN tier_set_translation.translatable_id IS \'(DC2Type:tier_set_id)\'');
        $this->addSql('ALTER TABLE tier_condition ADD CONSTRAINT FK_AE61242D5FB14BA7 FOREIGN KEY (level_id) REFERENCES level (id) ON DELETE SET NULL NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tier_condition ADD CONSTRAINT FK_AE61242D887793B6 FOREIGN KEY (condition_id) REFERENCES tier_set_condition (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tier_set ADD CONSTRAINT FK_2068749B092A811 FOREIGN KEY (store_id) REFERENCES store (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tier_set_condition ADD CONSTRAINT FK_C121701C84ABD8C4 FOREIGN KEY (tier_set_id) REFERENCES tier_set (tier_set_id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tier_set_member_progress ADD CONSTRAINT FK_161A4F3884ABD8C4 FOREIGN KEY (tier_set_id) REFERENCES tier_set (tier_set_id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tier_set_member_progress_condition ADD CONSTRAINT FK_6330A353C6BADBBE FOREIGN KEY (tier_set_member_progress_id) REFERENCES tier_set_member_progress (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE tier_set_translation ADD CONSTRAINT FK_3A2C022D2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES tier_set (tier_set_id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE level ADD tier_set_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE level ADD is_default BOOLEAN DEFAULT \'false\' NOT NULL');
        $this->addSql('ALTER TABLE level ADD sort_order INT DEFAULT NULL');
        $this->addSql('ALTER TABLE level ALTER condition_value DROP NOT NULL');
        $this->addSql('COMMENT ON COLUMN level.tier_set_id IS \'(DC2Type:tier_set_id)\'');
        $this->addSql('ALTER TABLE level ADD CONSTRAINT FK_9AEACC1384ABD8C4 FOREIGN KEY (tier_set_id) REFERENCES tier_set (tier_set_id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_9AEACC1384ABD8C4 ON level (tier_set_id)');
        $this->addSql('CREATE INDEX levelDisplayOrderIdx ON level (sort_order)');
        $this->addSql('ALTER TABLE level_rewards ADD labels JSON DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN level_rewards.labels IS \'(DC2Type:labels_json_array)\'');

        $storeIds = $this->connection->createQueryBuilder()
            ->select('DISTINCT l.store_id')
            ->from('level', 'l')
            ->execute()->fetchAllAssociative();

        /**
         * @var EntityManagerInterface $entityManager
         */
        $entityManager = $this->container->get('doctrine.orm.entity_manager');

        foreach ($storeIds as $storeId) {
            /**
             * @var Store $store
             */
            $store = $entityManager->createQueryBuilder()
                ->select('s')
                ->from(Store::class, 's')
                ->where('s.storeId = :id')
                ->setParameter('id', new StoreId($storeId['store_id']))
                ->getQuery()->getOneOrNullResult();

            $entry = new StringSettingEntry(
                SettingsNames::TIERS_MODE->value,
                'traditional',
                $store
            );
            $entityManager->persist($entry);
        }
        $entityManager->flush();
    }
}
