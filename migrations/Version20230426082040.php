<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230426082040 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add wallet type to reward';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE reward ADD wallet_type_id UUID DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN reward.wallet_type_id IS \'(DC2Type:wallet_type_id)\'');
    }
}
