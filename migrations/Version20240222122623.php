<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240222122623 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE campaign_rule_effect ADD units_expiration_rule_expiration_rule VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE campaign_rule_effect ADD units_expiration_rule_expression VARCHAR(255) DEFAULT NULL');
        $this->addSql(
            'UPDATE campaign_rule_effect SET units_expiration_rule_expiration_rule = \'from_wallet\', units_expiration_rule_expression = NULL'
        );
    }
}
