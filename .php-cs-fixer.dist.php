<?php

use OpenLoyalty\Utility\Infrastructure\Tools\PhpCsFixer\OpenLoyaltyCopyrightFixer;
use <PERSON>p<PERSON><PERSON><PERSON>er\Config;
use Php<PERSON>F<PERSON>er\Finder;

$finder = Finder::create()
    ->in(__DIR__ . '/src')
    ->in(__DIR__ . '/migrations')
    ->in(__DIR__ . '/tests')
    ->exclude('var');

return (new Config())
    ->setRules([
        '@Symfony' => true,
        'php_unit_method_casing' => false,
        'single_line_comment_spacing' => false,
        'phpdoc_separation' => false,
        'no_superfluous_phpdoc_tags' => false,
        'nullable_type_declaration_for_default_null_value' => false,
        'global_namespace_import' => false,
        'statement_indentation' => false,
        'blank_line_between_import_groups' => false,
        'operator_linebreak' => false,
        'fully_qualified_strict_types' => false,
        'blank_line_after_opening_tag' => false,
        'Openloyalty/copyright_notice' => true,
        'void_return' => true,
    ])
    ->setFinder($finder)
    ->registerCustomFixers([
        new OpenLoyaltyCopyrightFixer(),
    ]);
