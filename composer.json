{"name": "openloyalty/framework", "type": "library", "license": "proprietary", "description": "Open Loyalty is loyalty software for developing dedicated loyalty applications, prepared for large-scale projects, proven by partners.", "minimum-stability": "stable", "require": {"php": "^8.2", "ext-amqp": "*", "ext-ctype": "*", "ext-curl": "*", "ext-iconv": "*", "ext-intl": "*", "ext-json": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "ext-pcntl": "*", "a2lix/auto-form-bundle": "0.4.*", "a2lix/translation-form-bundle": "^3.0", "auth0/auth0-php": "8.2.1", "aws/aws-sdk-php": "^3.176", "beberlei/doctrineextensions": "^1.3", "broadway/broadway": "^2.4", "broadway/broadway-bundle": "^0.6.1", "broadway/snapshotting": "^0.3.0", "broadway/uuid-generator": "^1.0", "doctrine/annotations": "^1.12", "doctrine/cache": "^2.1", "doctrine/common": "^3.2", "doctrine/doctrine-bundle": "^2.5", "doctrine/doctrine-fixtures-bundle": "^3.4", "doctrine/doctrine-migrations-bundle": "^3.0.2", "doctrine/orm": "^2.10", "dragonmantank/cron-expression": "^3.3", "erusev/parsedown": "^1.7", "fakerphp/faker": "^1.9", "flow-php/doctrine-dbal-bulk": "^0.2", "friendsofsymfony/rest-bundle": "^3.0", "gedmo/doctrine-extensions": "^3.0", "gesdinet/jwt-refresh-token-bundle": "^1.1", "graylog2/gelf-php": "^1.7", "gumlet/php-image-resize": "^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.4.5", "halaxa/json-machine": "^1.1", "jms/serializer": "^3.18", "jms/serializer-bundle": "^4.2", "knplabs/doctrine-behaviors": "^2.3", "knplabs/knp-gaufrette-bundle": "0.8", "kodus/file-cache": "^1.1", "league/flysystem-aws-s3-v3": "^3.13", "league/flysystem-bundle": "^3.1", "lexik/jwt-authentication-bundle": "^2.15", "nelmio/api-doc-bundle": "4.18.3", "nelmio/cors-bundle": "^2.0", "nesbot/carbon": "^2.53|^3.8", "php-http/guzzle7-adapter": "^1.0", "prewk/xml-string-streamer": "^1.1", "ramsey/uuid-doctrine": "^1.6", "scienta/doctrine-json-functions": "^5.5", "sensio/framework-extra-bundle": "^6.1", "sentry/sentry-symfony": "^5.0", "snc/redis-bundle": "^3.3", "symfony/amazon-mailer": "6.4.*", "symfony/amazon-sqs-messenger": "6.4.*", "symfony/amqp-messenger": "6.4.*", "symfony/asset": "6.4.*", "symfony/cache": "6.4.*", "symfony/console": "6.4.*", "symfony/dependency-injection": "6.4.*", "symfony/doctrine-messenger": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/expression-language": "6.4.*", "symfony/flex": "^1.12", "symfony/form": "6.4.*", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4.*", "symfony/intl": "6.4.*", "symfony/lock": "6.4.*", "symfony/mailer": "6.4.*", "symfony/messenger": "6.4.*", "symfony/mime": "6.4.*", "symfony/monolog-bundle": "^3.6", "symfony/process": "6.4.*", "symfony/property-access": "6.4.*", "symfony/property-info": "6.4.*", "symfony/proxy-manager-bridge": "6.4.*", "symfony/rate-limiter": "6.4.*", "symfony/runtime": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/serializer": "6.4.*", "symfony/stopwatch": "6.4.*", "symfony/templating": "6.4.*", "symfony/translation": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/validator": "6.4.*", "symfony/web-link": "6.4.*", "symfony/yaml": "6.4.*", "twig/twig": "3.14.1"}, "require-dev": {"brianium/paratest": "6.10.0", "coduo/php-matcher": "^6.0", "dama/doctrine-test-bundle": "^6.7", "ekino/phpstan-banned-code": "^2.0", "friendsofphp/php-cs-fixer": "^3.4", "league/openapi-psr7-validator": "^0.22.0", "phpat/phpat": "^0.10.10", "phpstan/phpstan": "^1.5.6", "phpstan/phpstan-doctrine": "^1.3.3", "phpstan/phpstan-phpunit": "^1.1.1", "phpstan/phpstan-symfony": "^1.1.8", "phpstan/phpstan-webmozart-assert": "^1.1.2", "phpunit/phpunit": "^9.6.8", "qossmic/deptrac-shim": "^1.0", "rector/rector": "^0.15.17", "sempro/phpunit-pretty-print": "^1.4", "symfony/browser-kit": "6.4.*", "symfony/css-selector": "6.4.*", "symfony/phpunit-bridge": "6.4.*", "symfony/psr-http-message-bridge": "^2", "symfony/web-profiler-bundle": "6.4.*"}, "config": {"platform": {"php": "8.2"}, "preferred-install": {"*": "dist"}, "sort-packages": true, "cache-files-dir": "/cache/openloyalty/composer", "allow-plugins": {"symfony/flex": true, "php-http/discovery": false, "symfony/runtime": true}}, "autoload": {"files": ["src/Kernel.php", "src/Application.php"], "psr-4": {"OpenLoyalty\\": "src"}}, "autoload-dev": {"psr-4": {"OpenLoyalty\\Integration\\": "tests/Integration", "OpenLoyalty\\Unit\\": "tests/Unit", "OpenLoyalty\\Test\\Common\\": "tests/Common", "OpenLoyalty\\Architecture\\": "tests/Architecture", "OpenLoyalty\\Test\\": "tests"}}, "scripts": {"pre-install-cmd": ["bin/api-doc-gen"], "pre-update-cmd": ["bin/api-doc-gen"], "auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"branch-alias": {"dev-develop": "4.2.x-dev", "dev-4.1": "4.1.x-dev"}, "symfony": {"allow-contrib": false, "require": "6.4.*"}}}