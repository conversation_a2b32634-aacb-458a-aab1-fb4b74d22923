<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Tools\BulkActions\Application\UseCase;

use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponse;
use OpenLoyalty\Tools\BulkActions\Application\DataMapper\BulkActionDataMapper;
use OpenLoyalty\Tools\BulkActions\Domain\BulkActionRepositoryInterface;

final readonly class GetBulkActionsListUseCase
{
    public function __construct(
        private BulkActionRepositoryInterface $bulkActionRepository,
        private SearchableResponderInterface $searchableResponder,
        private BulkActionDataMapper $dataMapper
    ) {
    }

    public function execute(CriteriaCollectionInterface $criteriaCollection): SearchableResponse
    {
        $searchableResponse = $this->searchableResponder->fromCriteria($this->bulkActionRepository, $criteriaCollection);
        $bulkActions = $this->dataMapper->mapList($searchableResponse->getItems());
        $total = $searchableResponse->getTotal();

        return new SearchableResponse($bulkActions, $total->getAll(), $total->getFiltered());
    }
}
