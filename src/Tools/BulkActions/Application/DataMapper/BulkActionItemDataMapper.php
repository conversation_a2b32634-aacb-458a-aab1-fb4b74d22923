<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Tools\BulkActions\Application\DataMapper;

use OpenLoyalty\Core\Application\DataMapper\DataMapperInterface;
use OpenLoyalty\Tools\BulkActions\Application\Response\BulkActionItem as BulkActionItemResponse;
use OpenLoyalty\Tools\BulkActions\Domain\Entity\BulkActionItem;

final readonly class BulkActionItemDataMapper implements DataMapperInterface
{
    public function map(BulkActionItem $entity): BulkActionItemResponse
    {
        return new BulkActionItemResponse(
            $entity->getBulkActionItemId(),
            $entity->getStatus()->getValue(),
            $entity->getCreatedAt(),
            $entity->getUpdatedAt(),
            $entity->getBulkActionType()->getValue(),
            $entity->getObjectToProcessId(),
            $entity->getErrorMessage(),
        );
    }

    /**
     * @param array<BulkActionItem> $entities
     *
     * @return array<BulkActionItemResponse>
     */
    public function mapList(array $entities): array
    {
        $bulkActionItems = [];
        foreach ($entities as $entity) {
            $bulkActionItems[] = $this->map($entity);
        }

        return $bulkActionItems;
    }
}
