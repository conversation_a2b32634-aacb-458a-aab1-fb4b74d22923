<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Tools\BulkActions\Domain\ValueObject;

use OpenLoyalty\Tools\BulkActions\Domain\Enum\BulkActionStatuses;
use OpenLoyalty\Tools\BulkActions\Domain\Exception\NotSupportedBulkActionStatusException;

final class BulkActionStatus
{
    /**
     * @throws NotSupportedBulkActionStatusException
     */
    public function __construct(
        private readonly string $value
    ) {
        if (!in_array(
            $this->value,
            BulkActionStatuses::toArray()
        )) {
            throw new NotSupportedBulkActionStatusException('Bulk action status: '.$this->value.' is not supported.');
        }
    }

    public function getValue(): string
    {
        return $this->value;
    }
}
