<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Tools\ExpressionLanguage\Provider\Time;

use DateTimeImmutable;
use DateTimeInterface;
use InvalidArgumentException;
use OpenLoyalty\Tools\ExpressionLanguage\CompileNotSupportedException;
use Symfony\Component\ExpressionLanguage\ExpressionFunction;
use Symfony\Component\ExpressionLanguage\ExpressionFunctionProviderInterface;

final readonly class GetDateInFutureByDaysExpressionLanguageProvider implements ExpressionFunctionProviderInterface
{
    public function __construct(private int $limit)
    {
    }

    public function getFunctions(): array
    {
        return [
            new ExpressionFunction(
                'add_days_to_date',
                function ($valueDate, $daysCount): void {
                    throw new CompileNotSupportedException();
                },
                function (array $arguments, $valueDate, $daysCount) {
                    if (!$valueDate instanceof DateTimeInterface) {
                        throw new InvalidArgumentException('Parameter valueDate in add_days_to_date is not a datetime');
                    }

                    if (!is_int($daysCount)) {
                        throw new InvalidArgumentException('Parameter daysCount in add_days_to_date is not a integer');
                    }

                    if (0 >= $daysCount) {
                        throw new InvalidArgumentException('Parameter daysCount in add_days_to_date is not a positive integer');
                    }

                    if ($this->limit < $daysCount) {
                        throw new InvalidArgumentException(sprintf('Parameter daysCount in add_days_to_date should be %s or less.', $this->limit));
                    }

                    return DateTimeImmutable::createFromInterface($valueDate)->modify(sprintf('+ %s days', $daysCount));
                }
            ),
        ];
    }
}
