<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Infrastructure\DataFixtures\ORM;

use DateTime;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Faker\Provider\Uuid;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\RewardCategoryId;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Model\Label;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Level\Infrastructure\DataFixtures\ORM\LoadLevelData;
use OpenLoyalty\Reward\Application\Command\CreateReward;
use OpenLoyalty\Reward\Application\Command\CreateRewardCategory;
use OpenLoyalty\Reward\Domain\DynamicCouponReward;
use OpenLoyalty\Reward\Domain\RewardTranslation;
use OpenLoyalty\Reward\Domain\StaticCouponReward;
use OpenLoyalty\Reward\Domain\ValueObject\CouponGenerator;
use OpenLoyalty\Reward\Domain\ValueObject\RewardActivity;
use OpenLoyalty\Reward\Domain\ValueObject\RewardTarget;
use OpenLoyalty\Reward\Domain\ValueObject\RewardUsageLimit;
use OpenLoyalty\Reward\Domain\ValueObject\RewardVisibility;
use OpenLoyalty\Segment\Infrastructure\DataFixtures\ORM\LoadSegmentData;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;

class LoadRewardData extends Fixture implements OrderedFixtureInterface, FixtureGroupInterface
{
    public const REWARD_ID = '6a861f8a-6906-4002-8b9c-4db407e810ea';
    public const REWARD2_ID = 'bcefe312-cb97-43d5-ad54-7b24217c9e58';
    public const REWARD3_ID = '566d8fcf-0a48-421b-99fd-5dcbafac4afc';
    public const REWARD4_ID = '566d8fcf-0a48-421b-99fd-7b24217c9e5d';
    public const REWARD5_ID = '566d8fcf-0a48-421b-99fd-7b24217c9e5e';
    public const NOSEGMENTS_REWARD_ID = '000096cf-6361-4d70-e169-676e55555555';
    public const NOLEVEL_REWARD_ID = '000096cf-6361-4d70-e169-676e66666666';
    public const REWARD_CATEGORY1_ID = '00ca7e90-6361-4465-e76f-727900000001';
    public const REWARD_CATEGORY2_ID = '00ca7e90-6361-4465-e76f-727900000002';

    public const DATA = [
        LoadSettingsData::DEFAULT_STORE_CODE => [
            'rewardId' => self::REWARD_ID,
            'reward2Id' => self::REWARD2_ID,
            'reward3Id' => self::REWARD3_ID,
            'reward4Id' => self::REWARD4_ID,
            'reward5Id' => self::REWARD5_ID,
            'nosegmentsRewardId' => self::NOSEGMENTS_REWARD_ID,
            'nolevelRewardId' => self::NOLEVEL_REWARD_ID,
            'rewardCategory1Id' => self::REWARD_CATEGORY1_ID,
            'rewardCategory2Id' => self::REWARD_CATEGORY2_ID,
        ],
        LoadSettingsData::STORE_A_CODE => [
            'rewardId' => '220096cf-6361-4d70-e169-676e00000001',
            'reward2Id' => '220096cf-6361-4d70-e169-676e00000002',
            'reward3Id' => '220096cf-6361-4d70-e169-676e00000003',
            'reward4Id' => '220096cf-6361-4d70-e169-676e00000004',
            'reward5Id' => '220096cf-6361-4d70-e169-676e00000005',
            'nosegmentsRewardId' => '220096cf-6361-4d70-e169-676e55555555',
            'nolevelRewardId' => '220096cf-6361-4d70-e169-676e66666666',
            'rewardCategory1Id' => '22ca7e90-6361-4465-e76f-727900000001',
            'rewardCategory2Id' => '22ca7e90-6361-4465-e76f-727900000002',
        ],
        LoadSettingsData::STORE_B_CODE => [
            'rewardId' => 'ac0119bc-913d-4e81-95ba-cb6548bc98ba',
            'reward2Id' => 'd1964dfd-d79f-4afe-84f1-dbf851a1dfc6',
            'reward3Id' => 'ac92800f-6264-48af-806f-d86f2c3b947a',
            'reward4Id' => 'ac92800f-6264-48af-806f-d86f2c3b947b',
            'reward5Id' => 'ac92800f-6264-48af-806f-d86f2c3b947c',
            'nosegmentsRewardId' => 'ea78427f-1a74-4cd7-894e-3d8d22731724',
            'nolevelRewardId' => 'd05be9fb-6a92-42c4-b5ac-c44529d3d6b4',
            'rewardCategory1Id' => '9b3e4803-11e1-4b35-bdb5-75615e078e22',
            'rewardCategory2Id' => 'e8ca7b6f-9796-43cd-b0d8-809a85085213',
        ],
    ];

    public function __construct(
        private readonly CommandBusInterface $commandBus
    ) {
    }

    public function load(ObjectManager $manager): void
    {
        $this->loadData(LoadSettingsData::DEFAULT_STORE_CODE);
        $this->loadData(LoadSettingsData::STORE_A_CODE);
        $this->loadData(LoadSettingsData::STORE_B_CODE);
    }

    protected function loadData(string $storeCode): void
    {
        /** @var Store $store */
        $store = $this->getReference($storeCode);

        $this->commandBus->dispatch(
            new CreateRewardCategory(
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory1Id']),
                $store->getStoreId(),
                [
                    'name' => 'Category A',
                    'sortOrder' => 0,
                    'active' => true,
                    'translations' => [
                        'en' => [
                            'name' => 'Reward category A',
                        ],
                    ],
                ]
            ));

        $this->commandBus->dispatch(
            new CreateRewardCategory(
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory2Id']),
                $store->getStoreId(),
                [
                    'name' => 'Category B',
                    'sortOrder' => 0,
                    'active' => true,
                    'translations' => [
                        'en' => [
                            'name' => 'Reward category B',
                        ],
                    ],
                ]
            ));

        $createReward = new CreateReward(
            new RewardId(self::DATA[$store->getCode()]['rewardId']),
            $store->getStoreId(),
            [
                'en' => new RewardTranslation(
                    'Test configured reward',
                    'Some _Reward_ short description',
                    'Some _Reward_ condition description',
                    'How to use coupon in this _reward_',
                    'Some _Brand_ description',
                    'Brand name'
                ),
                'pl' => new RewardTranslation(
                    'Skonfigurowana testowa kampania',
                    'Opis skonfigurowanej kampanii testowej',
                ),
            ],
            StaticCouponReward::TYPE,
            true,
            10,
            [
            ],
            [
                new SegmentId(LoadSegmentData::DATA[$store->getCode()]['segment2Id']),
            ],
            new RewardActivity(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')),
            new RewardVisibility(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')),
            0.2,
            20,
            30,
            [
                new Label('type', 'promotion'),
            ],
            false,
            true,
            [
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory1Id']),
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory2Id']),
            ],
            new DateTime('2015-01-02'),
            new RewardUsageLimit(10, 3),
            RewardTarget::create(RewardTarget::SEGMENT)
        );
        $createReward->setCouponOptions(null, null, null, new CouponGenerator(12));
        $createReward->setStaticCouponOptions(20);
        $this->commandBus->dispatch($createReward);

        $createReward = new CreateReward(
            new RewardId(self::DATA[$store->getCode()]['reward2Id']),
            $store->getStoreId(),
            [
                'en' => new RewardTranslation(
                    'Test reward reward',
                ),
                'pl' => new RewardTranslation(
                    'Testowa kampania z nagrodą'
                ),
            ],
            StaticCouponReward::TYPE,
            true,
            5,
            [
                new LevelId(LoadLevelData::DATA[$store->getCode()]['level0Id']),
                new LevelId(LoadLevelData::DATA[$store->getCode()]['level1Id']),
                new LevelId(LoadLevelData::DATA[$store->getCode()]['level2Id']),
                new LevelId(LoadLevelData::DATA[$store->getCode()]['level3Id']),
            ],
            [],
            new RewardActivity(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')),
            new RewardVisibility(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')),
            0.2,
            20,
            30,
            [
                new Label('type', 'test'),
            ],
            false,
            true,
            [
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory1Id']),
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory2Id']),
            ],
            new DateTime('2015-01-01'),
            null,
            RewardTarget::create(RewardTarget::LEVEL)
        );
        $createReward->setCouponOptions(null, null, null, new CouponGenerator(12));
        $createReward->setStaticCouponOptions(20);
        $this->commandBus->dispatch($createReward);

        $createReward = new CreateReward(
            new RewardId(self::DATA[$store->getCode()]['reward3Id']),
            $store->getStoreId(),
            [
                'en' => new RewardTranslation(
                    'Test reward reward 2',
                ),
                'pl' => new RewardTranslation(
                    'Testowa kampania z nagrodą'
                ),
            ],
            StaticCouponReward::TYPE,
            true,
            5,
            [
            ],
            [
                new SegmentId(LoadSegmentData::DATA[$store->getCode()]['segment2Id']),
            ],
            new RewardActivity(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')),
            new RewardVisibility(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')),
            0.2,
            20,
            30,
            [
                new Label('type', 'test'),
            ],
            false,
            true,
            [
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory1Id']),
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory2Id']),
            ],
            new DateTime('2015-01-01'),
            new RewardUsageLimit(10, 2),
            RewardTarget::create(RewardTarget::SEGMENT)
        );
        $createReward->setCouponOptions(10, 20, null, new CouponGenerator(12));
        $createReward->setStaticCouponOptions(20);
        $this->commandBus->dispatch($createReward);

        $createReward = new CreateReward(
            new RewardId(self::DATA[$store->getCode()]['reward4Id']),
            $store->getStoreId(),
            [
                'en' => new RewardTranslation(
                    'Test reward reward 4',
                ),
            ],
            StaticCouponReward::TYPE,
            true,
            5,
            [
            ],
            [
                new SegmentId(LoadSegmentData::DATA[$store->getCode()]['segment2Id']),
            ],
            new RewardActivity(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')),
            new RewardVisibility(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')),
            0.2,
            20,
            30,
            [
                new Label('type', 'test'),
            ],
            false,
            true,
            [
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory1Id']),
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory2Id']),
            ],
            new DateTime('2015-01-01'),
            new RewardUsageLimit(10, 2),
            RewardTarget::create(RewardTarget::SEGMENT)
        );
        $createReward->setCouponOptions(10, 20, null, new CouponGenerator(12));
        $createReward->setStaticCouponOptions(20);
        $this->commandBus->dispatch($createReward);

        $createReward = new CreateReward(
            new RewardId(self::DATA[$store->getCode()]['reward5Id']),
            $store->getStoreId(),
            [
                'en' => new RewardTranslation(
                    'Test reward reward 5',
                ),
            ],
            DynamicCouponReward::TYPE,
            true,
            5,
            [
            ],
            [
                new SegmentId(LoadSegmentData::DATA[$store->getCode()]['segment2Id']),
            ],
            new RewardActivity(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')),
            new RewardVisibility(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')),
            0.2,
            20,
            30,
            [
                new Label('type', 'test'),
            ],
            false,
            true,
            [
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory1Id']),
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory2Id']),
            ],
            new DateTime('2015-01-01'),
            new RewardUsageLimit(10, 2),
            RewardTarget::create(RewardTarget::SEGMENT)
        );
        $createReward->setCouponOptions(10, 20, null, new CouponGenerator(12));
        $createReward->setStaticCouponOptions(20);
        $this->commandBus->dispatch($createReward);

        $createReward = new CreateReward(
            new RewardId(self::DATA[$store->getCode()]['nosegmentsRewardId']),
            $store->getStoreId(),
            [
                'en' => new RewardTranslation(
                    'No segments reward',
                ),
                'pl' => new RewardTranslation(
                    'Brak segmentów nagroda'
                ),
            ],
            StaticCouponReward::TYPE,
            true,
            5,
            [
                new LevelId(LoadLevelData::DATA[$store->getCode()]['level0Id']),
                new LevelId(LoadLevelData::DATA[$store->getCode()]['level1Id']),
                new LevelId(LoadLevelData::DATA[$store->getCode()]['level2Id']),
            ],
            [],
            new RewardActivity(true),
            new RewardVisibility(true),
            0.2,
            20,
            30,
            [
                new Label('type', 'cashback'),
            ],
            false,
            true,
            [
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory2Id']),
            ],
            new DateTime('2015-01-01'),
            new RewardUsageLimit(0, 0),
            RewardTarget::create(RewardTarget::LEVEL)
        );
        $createReward->setCouponOptions(null, null, null, new CouponGenerator(12));
        $createReward->setStaticCouponOptions(20);
        $this->commandBus->dispatch($createReward);

        $createReward = new CreateReward(
            new RewardId(self::DATA[$store->getCode()]['nolevelRewardId']),
            $store->getStoreId(),
            [
                'en' => new RewardTranslation(
                    'No levels reward',
                ),
                'pl' => new RewardTranslation(
                    'Brak poziomów nagroda'
                ),
            ],
            StaticCouponReward::TYPE,
            true,
            5,
            [],
            [
                new SegmentId(LoadSegmentData::DATA[$store->getCode()]['segment2Id']),
            ],
            new RewardActivity(true),
            new RewardVisibility(true),
            0.2,
            20,
            30,
            [
                new Label('type', 'cashback'),
            ],
            false,
            true,
            [
                new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory2Id']),
            ],
            new DateTime('2015-01-01'),
            new RewardUsageLimit(0, 0),
            RewardTarget::create(RewardTarget::SEGMENT)
        );
        $createReward->setCouponOptions(null, null, null, new CouponGenerator(12));
        $createReward->setStaticCouponOptions(20);
        $this->commandBus->dispatch($createReward);

        $this->loadRandomActiveRewards($store);
    }

    /**
     * add some extra random data.
     */
    protected function loadRandomActiveRewards(Store $store): void
    {
        for ($i = 0; $i < 12; ++$i) {
            $createReward = new CreateReward(
                new RewardId(Uuid::uuid()),
                $store->getStoreId(),
                [
                    'en' => new RewardTranslation(
                        'Random reward '.$i,
                    ),
                    'pl' => new RewardTranslation(
                        'Losowa nagroda '.$i
                    ),
                ],
                StaticCouponReward::TYPE,
                false,
                1,
                [],
                [],
                new RewardActivity(false, new DateTime('now'), new DateTime(sprintf('+%u days', rand(10, 30)))),
                new RewardVisibility(true),
                0.2,
                20,
                30,
                [
                    new Label('type', 'cashback'),
                ],
                true,
                true,
                [
                    new RewardCategoryId(self::DATA[$store->getCode()]['rewardCategory2Id']),
                ],
                new DateTime('2015-01-01'),
                new RewardUsageLimit(rand(10, 50), 10)
            );
            $createReward->setCouponOptions(null, null, null, new CouponGenerator(12));
            $createReward->setStaticCouponOptions(20);
            $this->commandBus->dispatch($createReward);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getOrder()
    {
        return 2;
    }

    /**
     * {@inheritdoc}
     */
    public static function getGroups(): array
    {
        return ['setup'];
    }
}
