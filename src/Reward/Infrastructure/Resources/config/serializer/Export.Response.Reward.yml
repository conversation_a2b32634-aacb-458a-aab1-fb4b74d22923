OpenLoyalty\Reward\Ui\Console\Command\Export\Response\Reward:
    exclusion_policy: ALL
    discriminator:
        disabled: true
    properties:
        rewardId:
            expose: true
            inline: true
        tenantId:
            expose: true
            inline: true
        type:
            expose: true
        name:
            expose: true
        brandName:
            expose: true
        levels:
            expose: true
        segments:
            expose: true
        categories:
            expose: true
        tax:
            expose: true
            type: float<2>
        active:
            expose: true
        costInPoints:
            expose: true
            type: float<6>
        activity:
            expose: true
        visibility:
            expose: true
        taxPriceValue:
            expose: true
            type: float<6>
        price:
            expose: true
            type: float<6>
        labels:
            expose: true
        featured:
            expose: true
        public:
            expose: true
        usageLimit:
            expose: true
        fulfillmentTracking:
            expose: true
        createdAt:
            expose: true
        pointValue:
            expose: true
            type: float<6>
        daysInactive:
            expose: true
            type: int
        daysValid:
            expose: true
            type: int
        couponGenerator:
            expose: true
        couponValue:
            expose: true
            type: float<6>
        segmentNames:
            expose: true
        levelNames:
            expose: true
        categoryNames:
            expose: true
        usageLeft:
            expose: true
            type: int