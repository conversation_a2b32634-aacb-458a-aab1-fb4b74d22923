<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="en" target-language="pl" datatype="plaintext" original="file.ext">
        <body>
            <trans-unit id="reward.bought.deliver_status.changed">
                <source>reward.bought.deliver_status.changed</source>
                <target>Status został zmieniony.</target>
            </trans-unit>
            <trans-unit id="reward.bought.deliver_status.empty">
                <source>reward.bought.deliver_status.empty</source>
                <target>Pole status dostawy jest wymagane i nie może być puste!</target>
            </trans-unit>
            <trans-unit id="reward.invalid_data">
                <source>reward.invalid_data</source>
                <target>Nieprawidłowe dane</target>
            </trans-unit>
            <trans-unit id="reward.missing_data_in_row">
                <source>reward.missing_data_in_row</source>
                <target>Brakuje pól w wierszu %row%</target>
            </trans-unit>
            <trans-unit id="reward.invalid_value_field_in_row">
                <source>reward.invalid_value_field_in_row</source>
                <target>Nieprawidłowa wartość dla pola %name% w wierszu %row%</target>
            </trans-unit>
            <trans-unit id="reward.purchase_not_available">
                <source>reward.purchase_not_available</source>
                <target>Brak aktywnego zakupu odpowiadającego wartości pola %name% w wierszu %row%. Może użytkownik nie wykupił jeszcze nagrody z tej kampanii, zakup jest nieaktywny albo dany couponId nie jest przypisany do tego użytkownika?</target>
            </trans-unit>
            <trans-unit id="reward.purchase_not_found">
                <source>reward.purchase_not_found</source>
                <target>Brak zakupu odpowiadającego wartości pola %name% w wierszu %row%. Może użytkownik nie wykupił jeszcze nagrody z tej kampanii albo dany couponId nie jest przypisany do tego użytkownika?</target>
            </trans-unit>
            <trans-unit id="reward.invalid_value_in_row">
                <source>reward.invalid_value_in_row</source>
                <target>%content% w wierszu %row%</target>
            </trans-unit>
            <trans-unit id="reward.no_coupons_left">
                <source>reward.no_coupons_left</source>
                <target>Brak wolnych kuponów</target>
            </trans-unit>
            <trans-unit id="reward.too_low_coupon_value">
                <source>reward.too_low_coupon_value</source>
                <target>Wartość kuponu jest równa 0</target>
            </trans-unit>
            <trans-unit id="reward.transaction_required">
                <source>reward.transaction_required</source>
                <target>Transakcja wymagana</target>
            </trans-unit>
            <trans-unit id="reward.invalid_transaction">
                <source>reward.invalid_transaction</source>
                <target>Nieprawidłowa transakcja</target>
            </trans-unit>
            <trans-unit id="reward.not_allowed">
                <source>reward.not_allowed</source>
                <target>Akcja niedozwolona</target>
            </trans-unit>
            <trans-unit id="reward.not_enough_points">
                <source>reward.not_enough_points</source>
                <target>Brak wystarczającej ilości punktów</target>
            </trans-unit>
            <trans-unit id="reward.limit_per_customer_exceeded">
                <source>reward.limit_per_customer_exceeded</source>
                <target>Wyczerpano limit dla tego użytkownika</target>
            </trans-unit>
            <trans-unit id="reward.limit_exceeded">
                <source>reward.limit_exceeded</source>
                <target>Wyczerpano limit</target>
            </trans-unit>
        </body>
    </file>
</xliff>
