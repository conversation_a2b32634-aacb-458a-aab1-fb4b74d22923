<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Infrastructure\DependencyInjection;

use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader;
use Symfony\Component\HttpKernel\DependencyInjection\Extension;

class OpenLoyaltyRewardExtension extends Extension
{
    /**
     * {@inheritdoc}
     */
    public function load(array $configs, ContainerBuilder $container): void
    {
        $configuration = new Configuration();
        $config = $this->processConfiguration($configuration, $configs);

        $container->setParameter('oloy.reward.bought.export.filename_prefix', $config['reward_bought']['export']['filename_prefix']);
        $container->setParameter('oloy.reward.bought.export.headers', $config['reward_bought']['export']['default_headers']);
        $container->setParameter('oloy.reward.bought.export.fields', $config['reward_bought']['export']['default_fields']);
        $loader = new Loader\YamlFileLoader($container, new FileLocator(__DIR__.'/../Resources/config'));
        $loader->load('services.yml');
        $loader->load('domain.yml');
        $loader->load('voters.yml');
        $loader->load('controllers.yml');
        $loader->load('commands.yml');
    }
}
