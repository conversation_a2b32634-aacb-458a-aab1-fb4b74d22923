<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Infrastructure;

use OpenLoyalty\Account\Domain\AccountGateInterface;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\Model\Wallet;
use OpenLoyalty\Core\Domain\Model\WalletType as WalletTypeModel;
use OpenLoyalty\Reward\Domain\AccountFacadeInterface;

class AccountFacade implements AccountFacadeInterface
{
    public function __construct(
        private AccountGateInterface $accountGate,
    ) {
    }

    public function getWallet(StoreId $storeId, CustomerId $customerId, ?string $walletCode = null): Wallet
    {
        return $this->accountGate->getWallet($storeId, $customerId, $walletCode, true);
    }

    public function getWalletTypeByCode(string $code, StoreId $storeId): ?WalletTypeModel
    {
        return $this->accountGate->getWalletTypeByCode($code, $storeId);
    }

    public function getWalletTypeById(WalletTypeId $walletTypeId): ?WalletTypeModel
    {
        return $this->accountGate->getWalletTypeById($walletTypeId);
    }
}
