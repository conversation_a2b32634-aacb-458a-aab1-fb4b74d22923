<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Infrastructure\Search\CriteriaBuilder\Doctrine;

use DateTime;
use OpenLoyalty\Core\Domain\Search\Context\ContextInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\BooleanCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Infrastructure\Search\Doctrine\Context;
use OpenLoyalty\Core\Infrastructure\Search\Doctrine\CriteriaBuilder\BaseCriteriaBuilder;

class VisibleRewardCriteriaBuilder extends BaseCriteriaBuilder
{
    public function build(ContextInterface $context, CriteriaInterface $criteria): void
    {
        if (!$criteria instanceof BooleanCriteria) {
            return;
        }

        $queryBuilder = $context->getQueryBuilder();
        $parameterName = $this->getParameterName($criteria);
        $queryBuilder->setParameter($parameterName, new DateTime());

        if (true === $criteria->getValue()) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    sprintf('%s.visibility.allTime = true', Context::DEFAULT_ALIAS),
                    $queryBuilder->expr()->andX(
                        sprintf('%s.visibility.from <= :%s', Context::DEFAULT_ALIAS, $parameterName),
                        sprintf('%s.visibility.to >= :%s', Context::DEFAULT_ALIAS, $parameterName)
                    )
                )
            );

            return;
        }

        $queryBuilder->andWhere(
            $queryBuilder->expr()->andX(
                sprintf('%s.visibility.allTime = false', Context::DEFAULT_ALIAS),
                $queryBuilder->expr()->orX(
                    sprintf('%s.visibility.from > :%s', Context::DEFAULT_ALIAS, $parameterName),
                    sprintf('%s.visibility.to < :%s', Context::DEFAULT_ALIAS, $parameterName)
                )
            )
        );
    }

    public function allows(CriteriaInterface $criteria): bool
    {
        return $criteria instanceof BooleanCriteria && 'visible' === $criteria->getName()
            && in_array($criteria->getOperator(), [CriteriaInterface::DEFAULT]);
    }
}
