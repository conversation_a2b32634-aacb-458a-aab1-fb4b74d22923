<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Infrastructure\Persistence\Doctrine\Type;

use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\Type;
use OpenLoyalty\Core\Domain\Id\SegmentId;

final class RewardSegmentsJsonArrayDoctrineType extends Type
{
    private const NAME = 'reward_segments_json_array';

    public function getSQLDeclaration(array $fieldDeclaration, AbstractPlatform $platform): string
    {
        return $platform->getJsonTypeDeclarationSQL($fieldDeclaration);
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): string|false
    {
        if (!is_array($value)) {
            return json_encode([]);
        }

        $serialized = [];
        /** @var SegmentId $segmentId */
        foreach ($value as $segmentId) {
            $serialized[] = (string) $segmentId;
        }

        return json_encode($serialized);
    }

    public function convertToPHPValue($value, AbstractPlatform $platform): array
    {
        if (null === $value || '' === $value) {
            return [];
        }

        $value = (is_resource($value)) ? stream_get_contents($value) : $value;

        $decoded = json_decode($value, true);

        if (!$decoded) {
            return [];
        }

        $labels = [];

        foreach ($decoded as $item) {
            $labels[] = new SegmentId($item);
        }

        return $labels;
    }

    public function requiresSQLCommentHint(AbstractPlatform $platform): bool
    {
        return true;
    }

    public function getName(): string
    {
        return self::NAME;
    }
}
