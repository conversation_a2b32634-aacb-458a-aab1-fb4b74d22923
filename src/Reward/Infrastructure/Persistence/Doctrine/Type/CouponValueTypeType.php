<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Infrastructure\Persistence\Doctrine\Type;

use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\AbstractEnumType;
use OpenLoyalty\Reward\Domain\CouponValueType;

class CouponValueTypeType extends AbstractEnumType
{
    public const NAME = 'coupon_value_type';

    public function getName(): string
    {
        return self::NAME;
    }

    public static function getEnumsClass(): string
    {
        return CouponValueType::class;
    }
}
