OpenLoyalty\Reward\Domain\CouponReward:
    type: entity
    fields:
        daysInactive:
            type: integer
            nullable: true
            column: days_inactive
        daysValid:
            type: integer
            nullable: true
            column: days_valid
        dateValid:
            type: datetime_immutable
            options:
                default: null
    embedded:
        couponGenerator:
            class: OpenLoyalty\Reward\Domain\ValueObject\CouponGenerator
    oneToMany:
        coupons:
            targetEntity: OpenLoyalty\Reward\Domain\Model\Coupon
            mappedBy: reward
            cascade: ["persist", "remove"]
            orphanRemoval: true
            onDelete: CASCADE
