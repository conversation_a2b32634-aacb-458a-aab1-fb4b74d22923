OpenLoyalty\Reward\Domain\Model\RewardPhoto:
    type: entity
    table: reward_photo
    id:
        photoId:
            type: photo_id
            column: photo_id
    fields:
        createdAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        updatedAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        createdBy:
            type: string
            nullable: true
        updatedBy:
            type: string
            nullable: true
    embedded:
        path:
            class: OpenLoyalty\Reward\Domain\PhotoPath
            columnPrefix: false
        mimeType:
            class: OpenLoyalty\Reward\Domain\PhotoMimeType
            columnPrefix: false
        originalName:
            class: OpenLoyalty\Reward\Domain\PhotoOriginalName
            columnPrefix: false
    manyToOne:
        reward:
            targetEntity: OpenLoyalty\Reward\Domain\Reward
            inversedBy: photos
            fetch: EAGER
            joinColumn:
                name: reward_id
                referencedColumnName: id

