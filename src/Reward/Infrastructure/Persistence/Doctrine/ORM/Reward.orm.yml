OpenLoyalty\Reward\Domain\Reward:
    type: entity
    inheritanceType: SINGLE_TABLE
    table: reward
    indexes:
        rewardUpdatedAtIdx:
            columns: [ updated_at ]
        rewardCreatedAtIdx:
            columns: [ created_at ]
    discriminatorMap:
        material: OpenLoyalty\Reward\Domain\MaterialReward
        static_coupon: OpenLoyalty\Reward\Domain\StaticCouponReward
        dynamic_coupon: OpenLoyalty\Reward\Domain\DynamicCouponReward
        coupon: OpenLoyalty\Reward\Domain\CouponReward
        conversion_coupon: OpenLoyalty\Reward\Domain\ConversionCouponReward
    id:
        rewardId:
            type: reward_id
            column: id
    fields:
        active:
            type: boolean
            options:
                default: 0
        costInPoints:
            type: points
            scale: 12
            precision: 24
            nullable: true
            column: cost_in_points
        levels:
            type: reward_levels_json_array
        segments:
            type: reward_segments_json_array
        tax:
            type: decimal
            scale: 3
            precision: 14
            nullable: true
            column: tax
        taxPriceValue:
            type: decimal
            scale: 2
            precision: 14
            nullable: true
            column: tax_price_value
        price:
            type: decimal
            scale: 2
            precision: 14
            nullable: true
            column: price
        labels:
            type: labels_json_array
            column: labels
            nullable: true
        categories:
            type: reward_categories_json_array
            nullable: true
        featured:
            type: boolean
            options:
                default: 1
        public:
            type: boolean
            options:
        createdAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        updatedAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        createdBy:
            type: string
            nullable: true
        updatedBy:
            type: string
            nullable: true
        storeId:
            type: store_id
        fulfillmentTracking:
            type: boolean
            options:
                default: 0
        walletTypeId:
            type: wallet_type_id
            nullable: true
    embedded:
        activity:
            class: OpenLoyalty\Reward\Domain\ValueObject\RewardActivity
            columnPrefix: activity_
        visibility:
            class: OpenLoyalty\Reward\Domain\ValueObject\RewardVisibility
            columnPrefix: visibility_
        brandIcon:
            class: OpenLoyalty\Reward\Domain\Model\RewardBrandIcon
        usageLimit:
            class: OpenLoyalty\Reward\Domain\ValueObject\RewardUsageLimit
            columnPrefix: usage_limit_
        target:
            class: OpenLoyalty\Reward\Domain\ValueObject\RewardTarget
            columnPrefix: reward_
    oneToMany:
        photos:
            targetEntity: OpenLoyalty\Reward\Domain\Model\RewardPhoto
            mappedBy: reward
            fetch: EAGER
            cascade: ["persist", "remove"]
            orphanRemoval: true
            onDelete: CASCADE
