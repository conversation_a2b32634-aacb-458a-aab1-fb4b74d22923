<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Reward\Infrastructure\Persistence\Doctrine\Repository;

use Doctrine\ORM\EntityManagerInterface;
use OpenLoyalty\Core\Domain\Id\PhotoId;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Reward\Domain\Model\RewardPhoto;
use OpenLoyalty\Reward\Domain\Repository\RewardPhotoRepositoryInterface;

class RewardPhotoRepository implements RewardPhotoRepositoryInterface
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function save(RewardPhoto $photo): void
    {
        $this->entityManager->persist($photo);
        $this->entityManager->flush();
    }

    public function remove(RewardPhoto $photo): void
    {
        $this->entityManager->remove($photo);
        $this->entityManager->flush();
    }

    public function findOneByIdRewardId(PhotoId $photoId, RewardId $rewardId): ?RewardPhoto
    {
        return $this
            ->entityManager
            ->getRepository(RewardPhoto::class)
            ->findOneBy(['reward' => $rewardId, 'photoId' => $photoId]);
    }
}
