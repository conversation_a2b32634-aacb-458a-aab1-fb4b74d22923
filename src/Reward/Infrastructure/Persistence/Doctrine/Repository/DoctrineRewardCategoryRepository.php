<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Infrastructure\Persistence\Doctrine\Repository;

use OpenLoyalty\Core\Domain\Id\RewardCategoryId;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\Reward\Domain\RewardCategory;
use OpenLoyalty\Reward\Domain\RewardCategoryRepository;

class DoctrineRewardCategoryRepository extends DoctrineRepository implements RewardCategoryRepository
{
    protected function getClass(): string
    {
        return RewardCategory::class;
    }

    public function byId(RewardCategoryId $rewardCategoryId): ?RewardCategory
    {
        return parent::find($rewardCategoryId);
    }

    public function save(RewardCategory $rewardCategory): void
    {
        $this->entityManager->persist($rewardCategory);
        $rewardCategory->mergeNewTranslations();
        $this->entityManager->flush();
    }

    public function remove(RewardCategory $rewardCategory): void
    {
        $this->entityManager->remove($rewardCategory);
        $this->entityManager->flush();
    }
}
