<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Infrastructure\Persistence\Doctrine\Repository;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\IssuedRewardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Search\Context\ContextInterface;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\Core\Infrastructure\Search\Doctrine\Context;
use OpenLoyalty\Reward\Domain\IssuedReward;
use OpenLoyalty\Reward\Domain\Repository\IssuedRewardRepositoryInterface;
use OpenLoyalty\Reward\Domain\Reward;

class IssuedRewardRepository extends DoctrineRepository implements IssuedRewardRepositoryInterface
{
    protected function getClass(): string
    {
        return IssuedReward::class;
    }

    public function byId(IssuedRewardId $issuedRewardId): ?IssuedReward
    {
        return $this->find($issuedRewardId);
    }

    public function getByCouponCode(StoreId $storeId, string $couponCode): ?IssuedReward
    {
        return $this->findOneBy([
            'storeId' => $storeId,
            'issuedCoupon.code' => $couponCode,
        ]);
    }

    public function getIssuedCount(Reward $reward, CustomerId $customerId = null): int
    {
        $builder = $this->entityManager->createQueryBuilder();

        $builder->select('count(ir.issuedRewardId)')
            ->from(IssuedReward::class, 'ir')
            ->andWhere('ir.reward = :reward');

        if (null !== $customerId) {
            $builder->andWhere('ir.customerId = :customerId');
            $builder->setParameter('customerId', $customerId);
        }

        $builder->setParameter('reward', $reward);

        return $builder->getQuery()->getSingleScalarResult() ?? 0;
    }

    public function getCouponsExpiringAt(StoreId $storeId, DateTimeImmutable $from, DateTimeImmutable $to): iterable
    {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.issuedCoupon.usedAt IS NULL')
            ->andWhere('t.issuedCoupon.activeTo IS NOT NULL')
            ->andWhere('t.issuedCoupon.activeTo >= :from')
            ->andWhere('t.issuedCoupon.activeTo < :to')
            ->setParameter('storeId', $storeId)
            ->setParameter('from', $from)
            ->setParameter('to', $to);

        return $this->toIterableWithClearEntityManagerCache($queryBuilder->getQuery());
    }

    protected function getBaseContext(array $params): ContextInterface
    {
        $context = parent::getBaseContext($params);
        $context->getQueryBuilder()
            ->leftJoin(Reward::class, 'r', 'WITH', 'r.rewardId = '.Context::DEFAULT_ALIAS.'.reward');

        return $context;
    }

    public function save(IssuedReward $issuedReward): void
    {
        $this->entityManager->persist($issuedReward);
        $this->entityManager->flush();
    }
}
