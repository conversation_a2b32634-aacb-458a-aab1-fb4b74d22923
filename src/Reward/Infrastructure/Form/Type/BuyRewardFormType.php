<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Infrastructure\Form\Type;

use OpenLoyalty\Core\Infrastructure\Form\Type\PointsType;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\DateRangeValid;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\PointsRangeValid;
use OpenLoyalty\Reward\Domain\ConversionCouponReward;
use OpenLoyalty\Reward\Domain\DynamicCouponReward;
use OpenLoyalty\Reward\Domain\Reward;
use OpenLoyalty\Reward\Domain\StaticCouponReward;
use OpenLoyalty\Reward\Infrastructure\Validator\Constraints\WalletTypeValid;
use OpenLoyalty\User\Infrastructure\Validator\Constraint\Customer;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\GreaterThan;
use Symfony\Component\Validator\Constraints\LessThan;
use Symfony\Component\Validator\Constraints\LessThanOrEqual;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Uuid;

class BuyRewardFormType extends AbstractType
{
    private const MAX_INT = 1000000000000;

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /** @var bool $isAdmin */
        $isAdmin = $options['is_admin'];
        /** @var Reward $reward */
        $reward = $options['reward'];

        if (null !== $reward && ConversionCouponReward::TYPE !== $reward->getType()) {
            $builder->add('quantity', NumberType::class, [
                'required' => true,
                'constraints' => [
                    new NotBlank(),
                    new GreaterThan(['value' => 0]),
                    new LessThanOrEqual(['value' => 32]),
                ],
            ]);
        }

        if ($isAdmin) {
            if (null !== $reward && ConversionCouponReward::TYPE !== $reward->getType()) {
                $builder->add('withoutPoints', CheckboxType::class, [
                    'required' => true,
                    'constraints' => [],
                ]);
            }
            $builder->add('customerId', TextType::class, [
                'required' => true,
                'constraints' => [new NotBlank(), new Uuid(['groups' => ['create']]), new Customer()],
                'documentation' => [
                    'example' => '7239e6f7-e52e-466b-8c24-699709b64076',
                ],
            ]);
        }

        if (null !== $reward && DynamicCouponReward::TYPE === $reward->getType()) {
            $builder->add('couponValue', PointsType::class, [
                'attr' => ['min' => PointsType::MIN_POINTS],
                'constraints' => [
                    new NotBlank(),
                    new PointsRangeValid(['min' => PointsType::MIN_POINTS, 'max' => PointsType::MAX_POINTS]),
                ],
            ]);
        }

        if (null !== $reward && ConversionCouponReward::TYPE === $reward->getType()) {
            $builder->add('units', PointsType::class, [
                'required' => true,
                'constraints' => [
                    new NotBlank(),
                    new GreaterThan(['value' => 0]),
                    new LessThan(['value' => self::MAX_INT]),
                ],
            ]);
        }

        $builder->add('rewardWalletCode', TextType::class, [
            'required' => false,
            'constraints' => [
                new WalletTypeValid(),
            ],
        ]);

        if (in_array(
            $reward->getType(),
            [StaticCouponReward::TYPE, DynamicCouponReward::TYPE, ConversionCouponReward::TYPE],
            true
        )) {
            $builder->add('dateValid', DateTimeType::class, [
                'required' => false,
                'format' => DateTimeType::HTML5_FORMAT,
                'input' => 'datetime_immutable',
                'widget' => 'single_text',
                'constraints' => [new DateRangeValid()],
            ]);
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setRequired([
            'reward',
            'is_admin',
        ]);

        $resolver->setDefaults([
            'reward' => null,
            'is_admin' => true,
            'withoutPoints' => false,
            'quantity' => 1,
        ]);
    }
}
