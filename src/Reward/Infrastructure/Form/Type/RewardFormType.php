<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Infrastructure\Form\Type;

use OpenLoyalty\Core\Infrastructure\Form\DataTransformer\LabelsDataTransformer;
use OpenLoyalty\Core\Infrastructure\Form\Type\PointsType;
use OpenLoyalty\Core\Infrastructure\Service\LocaleProviderInterface;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\DateRangeValid;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\LevelsValid;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\SegmentsValid;
use OpenLoyalty\Reward\Domain\ConversionCouponReward;
use OpenLoyalty\Reward\Domain\CouponValueType;
use OpenLoyalty\Reward\Domain\DynamicCouponReward;
use OpenLoyalty\Reward\Domain\MaterialReward;
use OpenLoyalty\Reward\Domain\RewardTranslation;
use OpenLoyalty\Reward\Domain\StaticCouponReward;
use OpenLoyalty\Reward\Domain\ValueObject\RewardTarget;
use OpenLoyalty\Reward\Infrastructure\Form\DataTransformer\CategoriesDataTransformer;
use OpenLoyalty\Reward\Infrastructure\Form\DataTransformer\LevelsDataTransformer;
use OpenLoyalty\Reward\Infrastructure\Form\DataTransformer\PercentTypeDataTransformer;
use OpenLoyalty\Reward\Infrastructure\Form\DataTransformer\RewardTargetDataTransformer;
use OpenLoyalty\Reward\Infrastructure\Form\DataTransformer\SegmentsDataTransformer;
use OpenLoyalty\Reward\Infrastructure\Validator\Constraints\RewardCategoriesValid;
use OpenLoyalty\Reward\Infrastructure\Validator\Constraints\WalletTypeValid;
use OpenLoyalty\Transaction\Infrastructure\Form\Type\LabelFormType;
use OpenLoyalty\Translation\Infrastructure\Form\Type\TranslationsType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Event\PreSubmitEvent;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Validator\Constraints\GreaterThan;
use Symfony\Component\Validator\Constraints\GreaterThanOrEqual;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\LessThan;
use Symfony\Component\Validator\Constraints\LessThanOrEqual;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Optional;
use Symfony\Component\Validator\Constraints\Range;

class RewardFormType extends AbstractType
{
    private const MAX_INT = **********;
    private const TAX_PERCENT_MIN = 0;
    private const TAX_PERCENT_MAX = 100;
    private const NAME_LENGTH_MIN = 0;
    private const NAME_LENGTH_MAX = 255;

    public function __construct(
        protected LocaleProviderInterface $localeProvider
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('translations', TranslationsType::class, [
            'required' => true,
            'translation_class' => RewardTranslation::class,
            'fields' => [
                'name' => [
                    'field_type' => TextType::class,
                    'locale_options' => [
                        $this->localeProvider->getConfigurationDefaultLocale() => [
                            'constraints' => [
                                new NotBlank(),
                                new Length(['min' => self::NAME_LENGTH_MIN, 'max' => self::NAME_LENGTH_MAX]),
                            ],
                        ],
                    ],
                ],
                'shortDescription' => [
                    'field_type' => TextareaType::class,
                ],
                'usageInstruction' => [
                    'field_type' => TextareaType::class,
                ],
                'conditionsDescription' => [
                    'field_type' => TextareaType::class,
                ],
                'brandDescription' => [
                    'field_type' => TextareaType::class,
                ],
                'brandName' => [
                    'field_type' => TextType::class,
                ],
            ],
        ]);

        if (!isset($options['reward_type'])) {
            $builder->add('reward', ChoiceType::class, [
                'choices' => [
                    StaticCouponReward::TYPE,
                    MaterialReward::TYPE,
                    DynamicCouponReward::TYPE,
                    ConversionCouponReward::TYPE,
                ],
                'required' => true,
                'constraints' => [new NotBlank()],
            ]);
        }

        $builder->add(
            $builder->create('categories', CollectionType::class, [
                'entry_type' => TextType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'error_bubbling' => false,
                'constraints' => [
                    new RewardCategoriesValid(),
                ],
            ])->addModelTransformer(new CategoriesDataTransformer())
        );

        $builder->add('tax', NumberType::class, [
            'required' => false,
            'scale' => 2,
            'constraints' => [
                new Optional(),
                new Range([
                    'min' => self::TAX_PERCENT_MIN,
                    'max' => self::TAX_PERCENT_MAX,
                ]),
            ],
        ])->addModelTransformer(new PercentTypeDataTransformer('tax'));

        $builder->add('price', NumberType::class, [
            'scale' => 2,
            'required' => false,
            'constraints' => [
                new Optional(),
                new GreaterThan(['value' => 0]), new LessThan(['value' => self::MAX_INT]),
            ],
        ]);

        $builder->add('taxPriceValue', NumberType::class, [
            'scale' => 2,
            'required' => false,
            'constraints' => [
                new Optional(),
                new GreaterThan(['value' => 0]), new LessThan(['value' => self::MAX_INT]),
            ],
        ]);

        $builder->add('active', CheckboxType::class, [
            'required' => false,
        ]);

        $builder->add(
            $builder->create('target', ChoiceType::class, [
                'required' => false,
                'choices' => RewardTarget::ALLOWED_TARGETS,
            ])->addModelTransformer(new RewardTargetDataTransformer())
        );
        $builder->add(
            $builder->create('levels', CollectionType::class, [
                'entry_type' => TextType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'error_bubbling' => false,
                'constraints' => [
                    new LevelsValid(),
                ],
            ])->addModelTransformer(new LevelsDataTransformer())
        );
        $builder->add(
            $builder->create('segments', CollectionType::class, [
                'entry_type' => TextType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'error_bubbling' => false,
                'constraints' => [
                    new SegmentsValid(),
                ],
            ])->addModelTransformer(new SegmentsDataTransformer())
        );

        $builder->add('activity', RewardActivityFormType::class);
        $builder->add('visibility', RewardVisibilityFormType::class);

        $builder->add($builder->create('labels', CollectionType::class, [
            'allow_add' => true,
            'allow_delete' => true,
            'entry_type' => LabelFormType::class,
        ])->addModelTransformer(new LabelsDataTransformer()));

        $builder->add('featured', CheckboxType::class, [
            'required' => false,
        ]);

        $builder->add('public', CheckboxType::class, [
            'required' => false,
        ]);

        $builder->add('costInPoints', PointsType::class, [
            'required' => false,
            'empty_data' => '0',
            'constraints' => [new GreaterThanOrEqual(['value' => 0]), new LessThan(['value' => PointsType::MAX_POINTS])],
        ]);

        $builder->add('sourceWalletTypeCode', TextType::class, [
            'required' => false,
            'constraints' => [
                new WalletTypeValid(),
            ],
        ]);

        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (PreSubmitEvent $event) use ($options): void {
            $this->adjustRewardForm($event, $options);

            $data = $event->getData();
            unset($data['photos']);

            $target = $data['target'] ?? null;

            if (null === $target) {
                $data['segments'] = [];
                $data['levels'] = [];
            } elseif ('level' === $target) {
                $data['segments'] = [];
            } elseif ('segment' === $target) {
                $data['levels'] = [];
            }

            $event->setData($data);
        });
    }

    public function adjustRewardForm(PreSubmitEvent $event, array $options): void
    {
        $data = $event->getData();
        $form = $event->getForm();
        $reward = $options['reward_type'] ?? $data['reward'] ?? null;
        $couponValueType = $data['couponValueType'] ?? null;

        if (null === $reward) {
            return;
        }

        $form->add('usageLimit', UsageLimitFormType::class, [
            'disable_general' => in_array(
                $reward,
                [StaticCouponReward::TYPE, DynamicCouponReward::TYPE, ConversionCouponReward::TYPE],
                true
                )
                && !isset($data['couponGenerator']),
        ]);

        if (MaterialReward::TYPE === $reward) {
            $form->add('fulfillmentTracking', CheckboxType::class);
        }

        if (in_array(
            $reward,
            [StaticCouponReward::TYPE, DynamicCouponReward::TYPE, ConversionCouponReward::TYPE],
            true
        )) {
            $form->add('daysInactive', IntegerType::class, [
                'required' => false,
                'constraints' => [new Range(['min' => 1, 'max' => self::MAX_INT])],
            ]);
            if (!isset($data['dateValid'])) {
                $form->add('daysValid', IntegerType::class, [
                    'required' => false,
                    'constraints' => [new Range(['min' => 1, 'max' => self::MAX_INT])],
                ]);
            }
            $form->add('dateValid', DateTimeType::class, [
                'required' => false,
                'format' => DateTimeType::HTML5_FORMAT,
                'input' => 'datetime_immutable',
                'widget' => 'single_text',
                'constraints' => [new DateRangeValid()],
            ]);
            if (isset($data['couponGenerator'])) {
                $form->add('couponGenerator', CouponGeneratorFormType::class);
            }
        }

        if (StaticCouponReward::TYPE === $reward) {
            $form->add('couponValue', NumberType::class, [
                'scale' => 2,
                'required' => false,
                'constraints' => [
                    new NotBlank(),
                    new GreaterThan(['value' => 0]),
                    $couponValueType == CouponValueType::Percentage->value
                        ? new LessThanOrEqual(['value' => 100])
                        : new LessThan(['value' => self::MAX_INT]),
                ],
            ]);
            $form->add('couponValueType', EnumType::class, [
                'class' => CouponValueType::class,
                'required' => true,
                'empty_data' => CouponValueType::Money->value,
            ]);
        }

        if (ConversionCouponReward::TYPE === $reward) {
            $form->add('unitsConversion', UnitsConversionFormType::class);
        }
    }
}
