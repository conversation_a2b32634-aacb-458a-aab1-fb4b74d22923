<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Infrastructure\Form\DataTransformer;

use OpenLoyalty\Reward\Domain\ValueObject\CouponGenerator;
use Symfony\Component\Form\DataTransformerInterface;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
class CouponGeneratorDataTransformer implements DataTransformerInterface
{
    public function transform(mixed $value): ?array
    {
        return $value;
    }

    public function reverseTransform(mixed $value): ?CouponGenerator
    {
        if (!isset($value['length']) || !isset($value['characterSet'])) {
            return null;
        }

        return new CouponGenerator(
            $value['length'],
            $value['characterSet'],
            $value['prefix'] ?? null,
        );
    }
}
