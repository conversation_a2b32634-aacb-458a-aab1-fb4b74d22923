deptrac:
    paths:
        - ./src
    layers:
        -   name: RewardDomain
            collectors:
                -   type: directory
                    regex: src/Reward/Domain/*
        -   name: RewardApplication
            collectors:
                -   type: directory
                    regex: src/Reward/Application/*
        -   name: RewardInfrastructure
            collectors:
                -   type: directory
                    regex: src/Reward/Infrastructure/*
        -   name: RewardUi
            collectors:
                -   type: directory
                    regex: src/Reward/Ui/*
    ruleset:
        RewardDomain:
            - CoreDomain
            - CoreInfrastructure # dependency to remove
            - UserDomain
            - SegmentDomain
            - RewardApplication # dependency to remove
        RewardApplication:
            - RewardDomain
            - CoreDomain
            - CoreApplication
            - MessagingDomain
            - TranslationApplication
            - CoreInfrastructure # dependency to remove
            - UserDomain
            - UserApplication
            - AuditApplication
            - SegmentDomain
            - RewardInfrastructure # dependency to remove
        RewardInfrastructure:
            - RewardDomain
            - RewardApplication
            - CoreDomain
            - CoreApplication
            - CoreInfrastructure
            - AccountDomain
            - PointsApplication
            - LevelDomain
            - LevelInfrastructure
            - UserDomain
            - UserInfrastructure
            - SegmentDomain
            - SegmentInfrastructure
            - SettingsInfrastructure
            - TransactionInfrastructure
            - TranslationInfrastructure
        RewardUi:
            - RewardDomain
            - RewardApplication
            - RewardInfrastructure
            - CoreDomain
            - CoreApplication
            - CoreInfrastructure
            - CoreUi
            - UserDomain
            - UserApplication
            - UserInfrastructure
            - LevelDomain
            - SegmentDomain
            - SettingsInfrastructure
    skip_violations:
        OpenLoyalty\Reward\Ui\Rest\Controller\PostBuy:
            - OpenLoyalty\Account\Domain\Exception\NotEnoughPointsException