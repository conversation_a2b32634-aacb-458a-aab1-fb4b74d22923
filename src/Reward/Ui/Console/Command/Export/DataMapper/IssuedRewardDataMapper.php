<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Ui\Console\Command\Export\DataMapper;

use OpenLoyalty\Core\Application\Export\ExportDataMapperInterface;
use OpenLoyalty\Reward\Application\Response\IssuedReward as IssuedRewardResponse;
use OpenLoyalty\Reward\Domain\Repository\RewardRepository;
use OpenLoyalty\Reward\Ui\Console\Command\Export\Response\IssuedReward;

final class IssuedRewardDataMapper implements ExportDataMapperInterface
{
    public function __construct(
        private readonly RewardRepository $rewardRepository,
        private readonly RewardDataMapper $rewardDataMapper
    ) {
    }

    /**
     * @param IssuedRewardResponse[] $issuedRewards
     *
     * @return IssuedReward[]
     */
    public function mapList(array $issuedRewards): array
    {
        $rewards = [];
        foreach ($issuedRewards as $reward) {
            $rewards[] = $this->map($reward);
        }

        return $rewards;
    }

    /**
     * @param IssuedRewardResponse $issuedReward
     */
    public function map(object $issuedReward, array $context = []): IssuedReward
    {
        $tax = null !== $issuedReward->tax ? round($issuedReward->tax, 2) : null;
        $reward = $this->rewardRepository->byId($issuedReward->rewardId);
        if (null !== $reward) {
            $reward = $this->rewardDataMapper->map($reward);
        }

        return new IssuedReward(
            $issuedReward->issuedRewardId,
            $issuedReward->storeId,
            $reward,
            $issuedReward->name,
            $issuedReward->customerId,
            $issuedReward->customerData,
            $issuedReward->costInPoints,
            $issuedReward->token,
            $issuedReward->redemptionDate,
            $issuedReward->rewardType,
            $tax,
            $issuedReward->taxPriceValue,
            $issuedReward->price,
            $issuedReward->status,
            $issuedReward->statusLastUpdatedAt,
            $issuedReward->issuedCoupon,
            $issuedReward->actionCause,
        );
    }
}
