<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Ui\Console\Command\Export;

use OpenLoyalty\Core\Ui\Console\Command\AbstractExportCommand;
use OpenLoyalty\Reward\Ui\Console\Command\Export\Response\IssuedReward;

class IssuedRewardExportCommand extends AbstractExportCommand
{
    protected static $defaultName = 'oloy:issued-rewards:analytics:export';

    protected function getResponseClass(): string
    {
        return IssuedReward::class;
    }

    protected function getExportBasename(): string
    {
        return 'issued_rewards';
    }

    protected function getExportDirectory(): string
    {
        return 'Rewards';
    }
}
