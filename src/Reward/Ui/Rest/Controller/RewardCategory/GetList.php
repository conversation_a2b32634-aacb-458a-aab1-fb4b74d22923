<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Ui\Rest\Controller\RewardCategory;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\Controller\Annotations\View;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View as FosView;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableTotalResponse;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchFormFactoryInterface;
use OpenLoyalty\Reward\Application\UseCase\Category\GetRewardCategoriesListUseCase;
use OpenLoyalty\Reward\Domain\RewardCategory;
use OpenLoyalty\Reward\Infrastructure\Form\Type\RewardCategorySearchType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetList extends AbstractFOSRestController
{
    private SearchFormFactoryInterface $searchFormFactory;
    private GetRewardCategoriesListUseCase $useCase;

    public function __construct(SearchFormFactoryInterface $searchFormFactory, GetRewardCategoriesListUseCase $useCase)
    {
        $this->searchFormFactory = $searchFormFactory;
        $this->useCase = $useCase;
    }

    /**
     * @Route(methods={"GET"}, name="oloy.reward.category.list", path="/{storeCode}/rewardCategory")
     *
     * @Security("is_granted('LIST_ALL_REWARD_CATEGORIES')")
     *
     * @View(serializerGroups={"admin", "Default"}, template="")
     *
     * @Operation(
     *     tags={"Reward category"},
     *     summary="Get reward categories list",
     *     operationId="rewardCategoryGetList",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(
     *         name="active",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="boolean")
     *     ),
     *     @OA\Parameter(
     *         name="name",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(ref="#/components/parameters/page"),
     *     @OA\Parameter(ref="#/components/parameters/itemsOnPage"),
     *     @OA\Parameter(ref="#/components/parameters/orderBy"),
     *     @OA\Response(
     *         response="200",
     *         description="List of messages",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="items",
     *                     type="array",
     *                     @OA\Items(
     *                         allOf={
     *                             @OA\Property(properties={
     *                                  @OA\Property(property="name", type="string", example="Category name")
     *                             }),
     *                             @OA\Property(ref=@Model(type=RewardCategory::class))
     *                         }
     *                      )
     *                 ),
     *                 @OA\Property(property="total", ref=@Model(type=SearchableTotalResponse::class))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="400",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="401",
     *         ref="#/components/responses/Unauthorized"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     )
     * )
     */
    public function __invoke(Request $request, ParamFetcherInterface $paramFetcher): FosView
    {
        $form = $this->searchFormFactory->createAndHandle(
            RewardCategorySearchType::class,
            $request->query->all(),
            $request->getLocale()
        );

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $result = $this->useCase->execute($form->getData());

        return $this->view($result, Response::HTTP_OK);
    }
}
