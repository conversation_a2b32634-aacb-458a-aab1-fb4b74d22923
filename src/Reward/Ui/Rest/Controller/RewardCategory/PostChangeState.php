<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Ui\Rest\Controller\RewardCategory;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\Controller\Annotations\View;
use FOS\RestBundle\View\View as FosView;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Reward\Application\Exception\EmptyActivateFieldException;
use OpenLoyalty\Reward\Application\UseCase\Category\ChangeRewardCategoryActivityUseCase;
use OpenLoyalty\Reward\Domain\RewardCategory;
use OpenLoyalty\Ui\Rest\Responder\ErrorResponderInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

class PostChangeState extends AbstractFOSRestController
{
    private ChangeRewardCategoryActivityUseCase $useCase;
    private TranslatorInterface $translator;
    private ErrorResponderInterface $formErrorResponder;

    public function __construct(
        ChangeRewardCategoryActivityUseCase $useCase,
        TranslatorInterface $translator,
        ErrorResponderInterface $formErrorResponder
    ) {
        $this->useCase = $useCase;
        $this->translator = $translator;
        $this->formErrorResponder = $formErrorResponder;
    }

    /**
     * @Route(methods={"POST"}, name="oloy.reward.category.change_state", path="/{storeCode}/rewardCategory/{rewardCategory}/active", requirements={"rewardCategory"="%routing.uuid%"})
     *
     * @Security("is_granted('EDIT', rewardCategory)")
     *
     * @View(serializerGroups={"admin", "Default"}, template="")
     *
     * @Operation(
     *     tags={"Reward category"},
     *     summary="Activate a reward category",
     *     operationId="rewardCategoryPostChangeState",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(ref="#/components/parameters/rewardCategory"),
     *     @OA\RequestBody(
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 required={"active"},
     *                 @OA\Property(
     *                     property="active",
     *                     description="If the category should be active",
     *                     type="boolean"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="204",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         description="Returned when reward category does not exist"
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Returned when active parameter is not present",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="401",
     *         ref="#/components/responses/Unauthorized"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     )
     * )
     */
    public function __invoke(Request $request, RewardCategory $rewardCategory): FosView
    {
        try {
            $activate = $request->request->get('active', null);

            if (null !== $activate) {
                $activate = \filter_var($activate, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            }

            $this->useCase->execute(
                $rewardCategory->getRewardCategoryId(),
                $activate
            );
        } catch (EmptyActivateFieldException $e) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans('Active field is required'),
            );
        }

        return $this->view(null, Response::HTTP_NO_CONTENT);
    }
}
