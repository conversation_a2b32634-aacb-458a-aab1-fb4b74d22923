<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Ui\Rest\Controller\RewardCategory;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\Controller\Annotations\View;
use FOS\RestBundle\View\View as FosView;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Reward\Application\UseCase\Category\UpdateRewardCategoryUseCase;
use OpenLoyalty\Reward\Domain\RewardCategory;
use OpenLoyalty\Reward\Infrastructure\Form\Type\EditRewardCategoryFormType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class Put extends AbstractFOSRestController
{
    private FormFactoryInterface $formFactory;
    private UpdateRewardCategoryUseCase $useCase;

    public function __construct(
        FormFactoryInterface $formFactory,
        UpdateRewardCategoryUseCase $useCase
    ) {
        $this->formFactory = $formFactory;
        $this->useCase = $useCase;
    }

    /**
     * @Route(methods={"PUT"}, name="oloy.reward.category.edit", path="/{storeCode}/rewardCategory/{rewardCategory}", requirements={"rewardCategory"="%routing.uuid%"})
     *
     * @Security("is_granted('EDIT', rewardCategory)")
     *
     * @View(serializerGroups={"admin", "Default"}, template="")
     *
     * @Operation(
     *     tags={"Reward category"},
     *     summary="Update a reward category",
     *     operationId="rewardCategoryPut",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(ref="#/components/parameters/rewardCategory"),
     *     @OA\RequestBody(
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(property="reward_category", ref=@Model(type=EditRewardCategoryFormType::class))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Return updated reward category ID.",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="rewardCategoryId",
     *                     type="string",
     *                     example="00000000-0000-0000-0000-000000000000"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Returned when form contains errors",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="401",
     *         ref="#/components/responses/Unauthorized"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         description="Returned when reward not found",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Request $request, RewardCategory $rewardCategory): FosView
    {
        $form = $this->formFactory->createNamed(
            'reward_category',
            EditRewardCategoryFormType::class,
            null,
            ['method' => Request::METHOD_PUT]
        );

        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        return $this->view(
            $this->useCase->execute(
                $rewardCategory->getRewardCategoryId(),
                $form->getData()->toArray()
            ),
            Response::HTTP_OK
        );
    }
}
