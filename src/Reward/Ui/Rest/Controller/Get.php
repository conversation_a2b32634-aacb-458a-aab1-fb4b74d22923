<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View as FosView;
use OpenLoyalty\Core\Infrastructure\Service\FOSContextProvider;
use OpenLoyalty\Reward\Domain\Reward;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;

class Get extends AbstractFOSRestController
{
    /**
     * @Route(methods={"GET"}, name="oloy.reward.get", path="/{storeCode}/reward/{reward}", requirements={"reward"="%routing.uuid%"})
     * @Security("is_granted('VIEW', reward)")
     */
    public function __invoke(Request $request, Reward $reward): FosView
    {
        $view = $this->view($reward);
        $view->getContext()->setGroups(['admin', 'Default']);
        $view->getContext()->setAttribute(
            FOSContextProvider::OUTPUT_FORMAT_ATTRIBUTE_NAME,
            $request->get('format')
        );

        return $view;
    }
}
