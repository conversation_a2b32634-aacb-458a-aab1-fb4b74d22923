<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Ui\Rest\Controller\Photo;

use Assert\AssertionFailedException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\Controller\Annotations\View;
use FOS\RestBundle\View\View as FosView;
use InvalidArgumentException;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Id\PhotoId;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Reward\Application\UseCase\Photo\DeleteRewardPhotoUseCase;
use OpenLoyalty\Reward\Domain\Reward;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class Delete extends AbstractFOSRestController
{
    public function __construct(
        private readonly DeleteRewardPhotoUseCase $useCase,
        private readonly StoreContextProviderInterface $storeContextProvider
    ) {
    }

    /**
     * @Route(methods={"DELETE"}, name="oloy.reward.remove_photo", path="/{storeCode}/reward/{reward}/photo/{photoId}")
     * @Security("is_granted('EDIT', reward)")
     *
     * @Operation(
     *     tags={"Reward"},
     *     summary="Delete reward’s image",
     *     operationId="photoDelete",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(ref="#/components/parameters/reward"),
     *     @OA\Parameter(
     *         name="photoId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response="204",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="400",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     *
     * @View(serializerGroups={"admin", "Default"}, template="")
     */
    public function __invoke(Request $request, Reward $reward): FosView
    {
        try {
            $this->useCase->execute(
                $reward->getRewardId(),
                new PhotoId($request->attributes->get('photoId')), /* @phpstan-ignore-line */
                $this->storeContextProvider->getStore()->getStoreId()
            );
        } catch (InvalidArgumentException|AssertionFailedException $exception) {
            throw $this->createNotFoundException($exception->getMessage());
        }

        return $this->view(null, Response::HTTP_NO_CONTENT);
    }
}
