<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Ui\Rest\Controller\Member;

use FOS\RestBundle\Context\Context;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View as FosView;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchFormFactoryInterface;
use OpenLoyalty\Reward\Application\UseCase\GetAvailableRewardsListUseCase;
use OpenLoyalty\Reward\Infrastructure\Form\Type\RewardSearchType;
use OpenLoyalty\User\Infrastructure\Entity\Customer;
use OpenLoyalty\User\Infrastructure\Entity\User;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Security("is_granted('ROLE_PARTICIPANT')")
 */
final class GetList extends AbstractFOSRestController
{
    public function __construct(
        private readonly GetAvailableRewardsListUseCase $useCase,
        private readonly SearchFormFactoryInterface $searchFormFactory,
        private readonly StoreContextProviderInterface $storeContextProvider
    ) {
    }

    /**
     * @Route(methods={"GET"}, name="oloy.reward.member.available", path="/{storeCode}/member/reward")
     * @Security("is_granted('LIST_REWARDS_AVAILABLE_FOR_ME')")
     */
    public function __invoke(Request $request): FosView
    {
        $customerId = $this->getLoggedCustomerId();

        $form = $this->searchFormFactory->createAndHandle(
            RewardSearchType::class,
            $request->query->all(),
            $request->getLocale()
        );

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $result = $this->useCase->execute(
            $this->storeContextProvider->getStore()->getStoreId(),
            $customerId,
            $form->getData() //@phpstan-ignore argument.type
        );
        $view = $this->view($result, Response::HTTP_OK);

        $context = new Context();
        $context->setGroups(['customer', 'Default']);
        $context->setAttribute('customerId', $customerId);

        $view->setContext($context);

        return $view;
    }

    protected function getLoggedCustomerId(): CustomerId
    {
        /** @var User $user */
        $user = $this->getUser();

        if (!$user instanceof Customer) {
            throw $this->createNotFoundException();
        }

        return new CustomerId($user->getId());
    }
}
