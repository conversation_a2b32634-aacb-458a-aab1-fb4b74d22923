<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Ui\Rest\Controller\BrandIcon;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\Controller\Annotations\View;
use OpenLoyalty\Reward\Application\Exception\BrandIconNotFoundException;
use OpenLoyalty\Reward\Application\Response\BrandIconResponse;
use OpenLoyalty\Reward\Application\UseCase\BrandIcon\GetBrandIconUseCase;
use OpenLoyalty\Reward\Domain\Reward;
use OpenLoyalty\Reward\Ui\Rest\Controller\Responder\RewardFileResponder;
use Symfony\Component\HttpFoundation\Response;

class Get extends AbstractFOSRestController
{
    private GetBrandIconUseCase $useCase;

    private RewardFileResponder $rewardFileResponder;

    public function __construct(
        GetBrandIconUseCase $useCase,
        RewardFileResponder $rewardFileResponder
    ) {
        $this->useCase = $useCase;
        $this->rewardFileResponder = $rewardFileResponder;
    }

    /**
     * @Route(methods={"GET"}, name="oloy.reward.get_brand_icon", path="/{storeCode}/reward/{reward}/brand_icon")
     *
     * @View(serializerGroups={"admin", "Default"}, template="")
     */
    public function __invoke(Reward $reward): Response
    {
        try {
            /** @var BrandIconResponse $response */
            $response = $this->useCase->execute($reward);

            return ($this->rewardFileResponder)($response->getContent(), $response->getMimeType());
        } catch (BrandIconNotFoundException $e) {
            throw $this->createNotFoundException($e->getMessage());
        }
    }
}
