<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View as FosView;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Account\Domain\Exception\NotEnoughPointsException as AccountNotEnoughPointsException;
use OpenLoyalty\Core\Infrastructure\Exception\AlreadyInUseException;
use OpenLoyalty\Reward\Application\UseCase\BuyRewardUseCase;
use OpenLoyalty\Reward\Domain\Exception\NoCouponsLeftException;
use OpenLoyalty\Reward\Domain\Exception\NonUniqueCodeException;
use OpenLoyalty\Reward\Domain\Exception\NotAllowedException;
use OpenLoyalty\Reward\Domain\Exception\NotEnoughPointsException;
use OpenLoyalty\Reward\Domain\Exception\RewardLimitException;
use OpenLoyalty\Reward\Domain\Exception\RewardNotActiveException;
use OpenLoyalty\Reward\Domain\Reward;
use OpenLoyalty\Reward\Infrastructure\Form\Type\BuyRewardFormType;
use OpenLoyalty\Reward\Infrastructure\Security\Voter\RewardVoter;
use OpenLoyalty\User\Infrastructure\Entity\Admin;
use OpenLoyalty\User\Infrastructure\Entity\User;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints\GroupSequence;
use Symfony\Contracts\Translation\TranslatorInterface;

class PostBuy extends AbstractFOSRestController
{
    public function __construct(
        private readonly BuyRewardUseCase $useCase,
        private readonly TranslatorInterface $translator,
        private readonly FormFactoryInterface $formFactory
    ) {
    }

    /**
     * Buy reward for customer as admin.
     *
     * @Route(methods={"POST"}, name="oloy.reward.buy", path="/{storeCode}/reward/{reward}/buy")
     *
     * @Operation(
     *     tags={"Reward"},
     *     summary="Buy a reward",
     *     operationId="rewardPostBuy",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(ref="#/components/parameters/reward"),
     *     @OA\RequestBody(
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 oneOf={
     *                     @OA\Schema(ref="#/components/schemas/StaticCoupon"),
     *                     @OA\Schema(ref="#/components/schemas/DynamicCoupon"),
     *                     @OA\Schema(ref="#/components/schemas/ConversionCoupon"),
     *                     @OA\Schema(ref="#/components/schemas/MaterialReward")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Return created reward ID.",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(
     *                         property="issuedRewardId",
     *                         type="string",
     *                         description="Created reward identity",
     *                         example="00000000-0000-0000-0000-000000000000"
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="400",
     *         ref="#/components/responses/BadRequest"
     *     )
     * )
     */
    public function __invoke(Reward $reward, Request $request): FosView
    {
        /** @var User $currentUser */
        $currentUser = $this->getUser();
        $isAdmin = $currentUser instanceof Admin;
        if ($isAdmin) {
            $this->denyAccessUnlessGranted(RewardVoter::BUY_FOR_CUSTOMER_ADMIN);
        } else {
            $this->denyAccessUnlessGranted(RewardVoter::BUY, $reward);
        }

        if (!$isAdmin && !$reward->isVisibleAt()) {
            $this->createNotFoundException();
        }

        $form = $this->formFactory->createNamed(
            '',
            BuyRewardFormType::class,
            null,
            [
                'reward' => $reward,
                'is_admin' => $isAdmin,
                'validation_groups' => new GroupSequence(['create', 'Default']),
            ]
        );
        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $data = $form->getData();
        if (!$isAdmin) {
            $data['customerId'] = $currentUser->getId();
        }

        try {
            $issuedRewardIds = $this->useCase->execute($reward, $data);

            return $this->view($issuedRewardIds, Response::HTTP_OK);
        } catch (RewardNotActiveException|RewardLimitException|NotAllowedException|AccountNotEnoughPointsException|NotEnoughPointsException|NoCouponsLeftException|NonUniqueCodeException|AlreadyInUseException $e) {
            $form->addError(new FormError($this->translator->trans($e->getMessage())));
        }

        return $this->view($form, Response::HTTP_BAD_REQUEST);
    }
}
