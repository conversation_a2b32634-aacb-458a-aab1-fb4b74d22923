<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Ui\Rest\Controller\Coupon;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View as FosView;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\UuidCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableTotalResponse;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchFormFactoryInterface;
use OpenLoyalty\Reward\Application\UseCase\Coupon\GetRewardCouponsListUseCase;
use OpenLoyalty\Reward\Domain\Model\Coupon;
use OpenLoyalty\Reward\Domain\Reward;
use OpenLoyalty\Reward\Infrastructure\Form\Type\CouponSearchType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetList extends AbstractFOSRestController
{
    private SearchFormFactoryInterface $searchFormFactory;
    private GetRewardCouponsListUseCase $useCase;

    public function __construct(
        SearchFormFactoryInterface $searchFormFactory,
        GetRewardCouponsListUseCase $useCase
    ) {
        $this->searchFormFactory = $searchFormFactory;
        $this->useCase = $useCase;
    }

    /**
     * @Route(
     *     methods={"GET"},
     *     name="oloy.reward.coupon.list",
     *     path="/{storeCode}/reward/{reward}/coupon",
     *     requirements={"reward"="%routing.uuid%"}
     * )
     * @Security("is_granted('LIST_ALL_COUPONS')")
     *
     * @Operation(
     *     tags={"Reward"},
     *     summary="Get reward’s coupons list",
     *     operationId="couponGetList",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(ref="#/components/parameters/reward"),
     *     @OA\Parameter(ref="#/components/parameters/page"),
     *     @OA\Parameter(ref="#/components/parameters/itemsOnPage"),
     *     @OA\Parameter(ref="#/components/parameters/orderBy"),
     *     @OA\Parameter(
     *         name="code",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="issued",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="boolean")
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Returned when successful",
     *             @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="items",
     *                     type="array",
     *                     @OA\Items(ref=@Model(type=Coupon::class))
     *                 ),
     *                 @OA\Property(property="total", ref=@Model(type=SearchableTotalResponse::class))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="400",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Request $request, Reward $reward): FosView
    {
        $form = $this->searchFormFactory->createAndHandle(
            CouponSearchType::class,
            $request->query->all(),
            $request->getLocale()
        );

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        /** @var CriteriaCollectionInterface $criteria */
        $criteria = $form->getData();
        $criteria->add(UuidCriteria::fromIdentifier(
            'reward',
            CriteriaInterface::EQUAL,
            $reward->getRewardId()
        )->setAsInternal());

        $result = $this->useCase->execute($form->getData());

        return $this->view($result, Response::HTTP_OK);
    }
}
