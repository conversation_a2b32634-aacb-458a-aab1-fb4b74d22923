<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\Webhook\Response;

final readonly class RewardTranslation
{
    public function __construct(
        public ?string $name,
        public ?string $shortDescription,
        public ?string $conditionsDescription,
        public ?string $usageInstruction,
        public ?string $brandDescription,
        public ?string $brandName,
        public ?string $locale
    ) {
    }
}
