<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\Webhook\Response;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\Model\Label;

final readonly class Reward
{
    public function __construct(
        public RewardId $rewardId,
        public bool $active,
        public bool $featured,
        public bool $public,
        public ?bool $fulfillmentTracking,
        public bool $canBeBoughtManually,
        public string $type,
        public ?string $name,
        public ?string $brandName,
        public ?string $shortDescription,
        public ?string $conditionsDescription,
        public ?string $usageInstruction,
        public ?string $brandDescription,
        public ?string $target,
        public ?float $costInPoints,
        public ?float $tax,
        public ?float $taxPriceValue,
        public ?float $price,
        /**
         * @var array<string>
         */
        public array $levels,
        /**
         * @var array<string>
         */
        public array $segments,
        /**
         * @var array<string>
         */
        public array $categories,
        /**
         * @var array<Label>
         */
        public array $labels,
        /**
         * @var array<RewardPhoto>
         */
        public array $photos,
        /**
         * @var array<RewardTranslation>
         */
        public array $translations,
        public DateTimeImmutable $createdAt,
        public ?RewardBrandIcon $brandIcon,
        public ?RewardActivity $activity,
        public ?RewardVisibility $visibility,
        public ?RewardUsageLimit $usageLimit,
        public ?WalletTypeId $walletTypeId,
        /**
         * @var array<RewardCoupon>
         */
        public ?array $coupons, // for CouponReward
        public ?int $daysInactive, // for CouponReward
        public ?int $daysValid, // for CouponReward
        public ?DateTimeImmutable $dateValid, // for CouponReward
        public ?RewardCouponGenerator $couponGenerator, // for CouponReward
        public ?RewardUnitsConversion $unitsConversion, // for ConversionCouponReward
        public ?float $couponValue, // for StaticCouponReward
        public ?string $couponValueType, // for StaticCouponReward
        public ?float $pointValue = null // for CashbackReward
    ) {
    }
}
