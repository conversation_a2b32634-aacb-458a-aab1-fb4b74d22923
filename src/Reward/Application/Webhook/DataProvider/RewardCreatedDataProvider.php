<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\Webhook\DataProvider;

use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\Webhook\DataProvider\WebhookDataProviderInterface;
use OpenLoyalty\Core\Domain\Webhook\Event\WebhookEventInterface;
use OpenLoyalty\Core\Domain\Webhook\Response\WebhookData;
use OpenLoyalty\Messaging\Domain\Webhook\Event\CommandWebhookEvent;
use OpenLoyalty\Reward\Application\Command\CreateReward;
use OpenLoyalty\Reward\Application\Webhook\Factory\RewardResponseFactory;
use OpenLoyalty\Reward\Domain\Repository\RewardRepository;
use Psr\Log\LoggerInterface;

final readonly class RewardCreatedDataProvider implements WebhookDataProviderInterface
{
    public function __construct(
        private StoreRepository $storeRepository,
        private RewardRepository $rewardRepository,
        private RewardResponseFactory $rewardResponseFactory,
        private LoggerInterface $webhookLogger
    ) {
    }

    public function getEventName(): string
    {
        return 'RewardCreated';
    }

    public function getData(
        WebhookEventInterface|CommandWebhookEvent $event
    ): ?WebhookData {
        $store = $this->storeRepository->byId($event->getStoreId(), true);
        if (null === $store) {
            $this->webhookLogger->warning(
                sprintf(
                    'Store with ID: %s not found for %s webhook.',
                    $event->getStoreId(),
                    $this->getEventName()
                )
            );

            return null;
        }

        /** @var CreateReward $command */
        $command = $event->getCommand();

        $reward = $this->rewardRepository->byId($command->getRewardId());
        if (null === $reward) {
            $this->webhookLogger->warning(
                sprintf(
                    'Reward with ID: %s not found for %s webhook.',
                    $command->getRewardId(),
                    $this->getEventName()
                )
            );

            return null;
        }

        return new WebhookData(
            $this->getEventName(),
            $store->getCode(),
            [
                'reward' => $this->rewardResponseFactory->create($reward),
            ]
        );
    }

    public function supports(string $eventClassName): bool
    {
        return CreateReward::class === $eventClassName;
    }
}
