<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\Webhook\Factory;

use OpenLoyalty\Reward\Application\Webhook\Response\RewardCoupon;
use OpenLoyalty\Reward\Domain\Model\Coupon;

final readonly class RewardCouponFactory
{
    /**
     * @param  Coupon[]       $coupons
     * @return RewardCoupon[]
     */
    public function create(array $coupons): array
    {
        $couponsResponses = [];
        foreach ($coupons as $coupon) {
            $couponsResponses[] = new RewardCoupon(
                $coupon->getCode(),
                $coupon->isIssued()
            );
        }

        return $couponsResponses;
    }
}
