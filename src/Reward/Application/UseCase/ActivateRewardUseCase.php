<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\UseCase;

use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Reward\Application\Command\ActivateReward;

final readonly class ActivateRewardUseCase
{
    public function __construct(
        private CommandBusInterface $commandBus
    ) {
    }

    public function execute(RewardId $rewardId, StoreId $storeId): void
    {
        $this->commandBus->dispatch(new ActivateReward($rewardId, $storeId));
    }
}
