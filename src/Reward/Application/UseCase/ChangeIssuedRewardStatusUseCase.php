<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\UseCase;

use OpenLoyalty\Core\Domain\Id\IssuedRewardId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Reward\Application\Command\ChangeIssuedRewardStatus;

class ChangeIssuedRewardStatusUseCase
{
    /**
     * @var \OpenLoyalty\Core\Domain\Message\CommandBusInterface
     */
    private $commandBus;

    public function __construct(CommandBusInterface $commandBus)
    {
        $this->commandBus = $commandBus;
    }

    public function execute(IssuedRewardId $issuedRewardId, string $status, ?string $comment = null): void
    {
        $this->commandBus->dispatch(
            new ChangeIssuedRewardStatus($issuedRewardId, $status, $comment)
        );
    }
}
