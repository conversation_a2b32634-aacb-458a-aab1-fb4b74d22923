<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\UseCase\Coupon;

use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Reward\Application\Command\CreateRewardCoupons;
use OpenLoyalty\Reward\Domain\Reward;

class CreateRewardCouponsUseCase
{
    public function __construct(
        private CommandBusInterface $commandBus
    ) {
    }

    public function execute(Reward $reward, array $data, bool $forceSync = false): void
    {
        $this->commandBus->dispatch(new CreateRewardCoupons($reward, $data['coupons']), $forceSync);
    }
}
