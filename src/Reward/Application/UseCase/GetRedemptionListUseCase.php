<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\UseCase;

use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponse;
use OpenLoyalty\Reward\Application\DataMapper\IssuedRewardDataMapperInterface;
use OpenLoyalty\Reward\Domain\Repository\IssuedRewardRepositoryInterface;

final class GetRedemptionListUseCase
{
    public function __construct(
        private readonly IssuedRewardRepositoryInterface $issuedRewardRepository,
        private readonly SearchableResponderInterface $searchableResponder,
        private readonly IssuedRewardDataMapperInterface $issuedRewardDataMapper
    ) {
    }

    public function execute(CriteriaCollectionInterface $criteriaCollection): SearchableResponse
    {
        $issuedRewardResponse = $this->searchableResponder->fromCriteria($this->issuedRewardRepository, $criteriaCollection, true);

        return new SearchableResponse(
            $this->issuedRewardDataMapper->mapList($issuedRewardResponse->getItems()),
            $issuedRewardResponse->getTotal()->getAll(),
            $issuedRewardResponse->getTotal()->getFiltered(),
            $issuedRewardResponse->getTotal()->isEstimated()
        );
    }
}
