<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\UseCase;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\IssuedRewardId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Provider\LockProviderInterface;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Core\Infrastructure\Exception\AlreadyInUseException;
use OpenLoyalty\Reward\Application\Command\IssueRewards;
use OpenLoyalty\Reward\Domain\Exception\NoCouponsLeftException;
use OpenLoyalty\Reward\Domain\Exception\NotAllowedException;
use OpenLoyalty\Reward\Domain\Exception\NotEnoughPointsException;
use OpenLoyalty\Reward\Domain\Exception\RewardLimitExceededException;
use OpenLoyalty\Reward\Domain\Exception\RewardLimitException;
use OpenLoyalty\Reward\Domain\Exception\RewardLimitPerCustomerExceededException;
use OpenLoyalty\Reward\Domain\Exception\RewardNotActiveException;
use OpenLoyalty\Reward\Domain\Reward;

class BuyRewardUseCase
{
    public function __construct(
        private readonly CommandBusInterface $commandBus,
        private readonly LockProviderInterface $lockProvider,
        private readonly UuidGeneratorInterface $uuidGenerator
    ) {
    }

    /**
     * @throws RewardNotActiveException
     * @throws NotAllowedException
     * @throws RewardLimitExceededException
     * @throws RewardLimitException
     * @throws RewardLimitPerCustomerExceededException
     * @throws NoCouponsLeftException
     * @throws NotEnoughPointsException
     * @throws AlreadyInUseException
     */
    public function execute(
        Reward $reward,
        array $data
    ): array {
        $lockResource = [
            get_class($this),
            (string) $reward->getRewardId(),
            $data['customerId'],
        ];

        // This use case cannot be used with cashback and custom rewards.
        if (!$reward->canBeBoughtManually()) {
            throw new NotAllowedException();
        }

        $this->lockProvider->acquire($lockResource, true);

        try {
            $withoutPoints = $data['withoutPoints'] ?? false;
            $customerId = is_string($data['customerId']) ? new CustomerId($data['customerId']) : $data['customerId'];
            $quantity = $data['quantity'] ?? 1;
            $rewardWalletCode = $data['rewardWalletCode'] ?? null;

            $issuedRewardIds = [];

            for ($i = 0; $i < $quantity; ++$i) {
                $issuedRewardIds[] = new IssuedRewardId($this->uuidGenerator->generate());
            }

            $this->commandBus->dispatch(
                new IssueRewards(
                    $issuedRewardIds,
                    $customerId,
                    $reward->getRewardId(),
                    $withoutPoints,
                    [
                        'couponValue' => $data['couponValue'] ?? null,
                        'units' => $data['units'] ?? null,
                        'dateValid' => $data['dateValid'] ?? null,
                    ],
                    $rewardWalletCode
                )
            );
        } finally {
            $this->lockProvider->release($lockResource);
        }

        return $issuedRewardIds;
    }
}
