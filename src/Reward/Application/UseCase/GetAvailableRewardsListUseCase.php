<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\UseCase;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Search\Criteria\BooleanCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponse;
use OpenLoyalty\Reward\Domain\Repository\RewardRepository;
use OpenLoyalty\Reward\Domain\Search\Criteria\MatchSegmentsAndLevelsCriteria;
use OpenLoyalty\Segment\Domain\Model\SegmentCustomer;
use OpenLoyalty\Segment\Domain\SegmentCustomerRepositoryInterface;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class GetAvailableRewardsListUseCase
{
    public function __construct(
        private SegmentCustomerRepositoryInterface $segmentCustomerRepository,
        private RewardRepository $rewardRepository,
        private SearchableResponderInterface $searchableResponder,
        private CustomerRepositoryInterface $customerRepository
    ) {
    }

    public function execute(
        StoreId $storeId,
        CustomerId $customerId,
        CriteriaCollectionInterface $criteriaCollection
    ): SearchableResponse {
        $customerSegments = $this->segmentCustomerRepository
            ->findBy(['customerId' => $customerId]);

        $customer = $this->customerRepository->getById($customerId);

        if (null !== $customer && (string) $customer->getStoreId() !== (string) $storeId) {
            throw new NotFoundHttpException();
        }

        $segmentIds = array_map(
            static fn (SegmentCustomer $segment): string => (string) $segment->getSegment()->getSegmentId(),
            $customerSegments
        );

        $criteriaCollection->add((new BooleanCriteria('visible', CriteriaInterface::DEFAULT, true))
            ->setAsInternal());
        $criteriaCollection->add((new MatchSegmentsAndLevelsCriteria($segmentIds, [$customer->getLevelId()]))
            ->setAsInternal());

        return $this->searchableResponder->fromCriteria($this->rewardRepository, $criteriaCollection);
    }
}
