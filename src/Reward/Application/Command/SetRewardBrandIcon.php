<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\Command;

use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Messaging\Domain\NotifiableCommandInterface;
use OpenLoyalty\Reward\Domain\Model\RewardBrandIcon;
use OpenLoyalty\Reward\Domain\Model\RewardFile;

final class SetRewardBrandIcon extends RewardCommand implements NotifiableCommandInterface
{
    public function __construct(
        RewardId $rewardId,
        private readonly RewardBrandIcon|RewardFile $rewardFile,
        private readonly StoreId $storeId
    ) {
        parent::__construct($rewardId);
    }

    public function getRewardBrandIcon(): RewardBrandIcon|RewardFile
    {
        return $this->rewardFile;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }
}
