<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\Command;

use OpenLoyalty\Core\Domain\Id\RewardCategoryId;
use OpenLoyalty\Core\Domain\Message\CommandInterface;

abstract class RewardCategoryCommand implements CommandInterface
{
    /**
     * @var \OpenLoyalty\Core\Domain\Id\RewardCategoryId
     */
    protected $rewardCategoryId;

    public function __construct(RewardCategoryId $rewardCategoryId)
    {
        $this->rewardCategoryId = $rewardCategoryId;
    }

    public function getRewardCategoryId(): RewardCategoryId
    {
        return $this->rewardCategoryId;
    }
}
