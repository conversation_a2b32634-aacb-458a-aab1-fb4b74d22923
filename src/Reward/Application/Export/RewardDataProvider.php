<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\Export;

use DateTimeImmutable;
use OpenLoyalty\Core\Application\Export\AbstractPaginatableDataProvider;
use OpenLoyalty\Core\Application\Export\DateRangeCriteriaBuilder;
use OpenLoyalty\Core\Application\Export\ExportDataMapperInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\OrderByCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Reward\Application\UseCase\GetRewardsListUseCase;
use OpenLoyalty\Reward\Domain\Repository\RewardRepository;

final class RewardDataProvider extends AbstractPaginatableDataProvider
{
    public function __construct(
        private readonly GetRewardsListUseCase $useCase,
        private readonly DateRangeCriteriaBuilder $criteriaBuilder,
        private readonly SearchableResponderInterface $searchableResponder,
        private readonly ExportDataMapperInterface $mapper,
        RewardRepository $rewardRepository
    ) {
        parent::__construct($rewardRepository);
    }

    public function getItemsCountByDate(?DateTimeImmutable $date): int
    {
        return $this->searchableResponder->countByCriteria(
            $this->repository,
            $this->criteriaBuilder->getDateRangeCriteria($date)
        );
    }

    public function getItemsByCriteria(CriteriaCollection $criteria): array
    {
        $criteria->add(new OrderByCriteria('rewardId', OrderByCriteria::ASC));

        $rewardEntities = $this->useCase->execute($criteria);

        return $this->mapper->mapList($rewardEntities->getItems());
    }
}
