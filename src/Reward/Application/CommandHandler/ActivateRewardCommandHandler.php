<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\CommandHandler;

use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Reward\Application\Command\ActivateReward;
use OpenLoyalty\Reward\Application\Exception\AlreadyActivated;
use OpenLoyalty\Reward\Domain\Repository\RewardRepository;

class ActivateRewardCommandHandler implements CommandHandlerInterface
{
    /**
     * @var \OpenLoyalty\Reward\Domain\Repository\RewardRepository
     */
    protected $rewardRepository;

    public function __construct(RewardRepository $rewardRepository)
    {
        $this->rewardRepository = $rewardRepository;
    }

    /**
     * @throws AlreadyActivated
     */
    public function __invoke(ActivateReward $command): void
    {
        /** @var \OpenLoyalty\Reward\Domain\Reward $reward */
        $reward = $this->rewardRepository->byId($command->getRewardId());

        if ($reward->isActive()) {
            throw new AlreadyActivated();
        }

        $reward->activate();

        $this->rewardRepository->save($reward);
    }
}
