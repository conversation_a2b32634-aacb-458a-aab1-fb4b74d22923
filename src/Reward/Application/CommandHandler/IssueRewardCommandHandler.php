<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\CommandHandler;

use OpenLoyalty\Core\Domain\Exception\NotFoundException;
use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Core\Domain\ValueObject\ActionCause;
use OpenLoyalty\Reward\Application\Command\IssueRewards;
use OpenLoyalty\Reward\Domain\Issuer\RewardIssuerInterface;
use OpenLoyalty\Reward\Domain\Repository\RewardRepository;

class IssueRewardCommandHandler implements CommandHandlerInterface
{
    private RewardIssuerInterface $rewardIssuer;
    private RewardRepository $rewardRepository;

    public function __construct(RewardIssuerInterface $rewardIssuer, RewardRepository $rewardRepository)
    {
        $this->rewardIssuer = $rewardIssuer;
        $this->rewardRepository = $rewardRepository;
    }

    public function __invoke(IssueRewards $command): void
    {
        $reward = $this->rewardRepository->byId($command->getRewardId());

        if (null === $reward) {
            throw new NotFoundException();
        }

        $this->rewardIssuer->issue(
            $command->getIssuedRewardIds(),
            $reward,
            $command->getCustomerId(),
            $command->isWithoutPoints(),
            $command->getParams(),
            new ActionCause(null, $command->getCustomerId()),
            $command->getRewardWalletCode()
        );
    }
}
