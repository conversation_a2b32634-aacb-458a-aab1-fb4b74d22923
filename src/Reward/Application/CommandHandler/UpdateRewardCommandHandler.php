<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\CommandHandler;

use OpenLoyalty\Core\Domain\Exception\NotFoundException;
use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Reward\Application\Command\UpdateReward;
use OpenLoyalty\Reward\Domain\ConversionCouponReward;
use OpenLoyalty\Reward\Domain\CouponValueType;
use OpenLoyalty\Reward\Domain\DynamicCouponReward;
use OpenLoyalty\Reward\Domain\MaterialReward;
use OpenLoyalty\Reward\Domain\Repository\RewardRepository;
use OpenLoyalty\Reward\Domain\Reward;
use OpenLoyalty\Reward\Domain\StaticCouponReward;

class UpdateRewardCommandHandler implements CommandHandlerInterface
{
    protected RewardRepository $rewardRepository;

    public function __construct(RewardRepository $rewardRepository)
    {
        $this->rewardRepository = $rewardRepository;
    }

    public function __invoke(UpdateReward $command): void
    {
        /** @var Reward $reward */
        $reward = $this->rewardRepository->byId($command->getRewardId());

        if (null === $reward) {
            throw new NotFoundException();
        }

        switch ($reward->getType()) {
            case MaterialReward::TYPE:
                if (!$reward instanceof MaterialReward) {
                    throw new \InvalidArgumentException(get_class($reward));
                }
                $reward->setFulfillmentTracking($command->isFulfillmentTracking());
                break;
            case DynamicCouponReward::TYPE:
                if (!$reward instanceof DynamicCouponReward) {
                    throw new \InvalidArgumentException(get_class($reward));
                }
                $reward->setCouponGenerator($command->getCouponGenerator());
                $reward->setValidity($command->getDaysInactive(), $command->getDaysValid(), $command->getDateValid());
                break;
            case StaticCouponReward::TYPE:
                if (!$reward instanceof StaticCouponReward) {
                    throw new \InvalidArgumentException(get_class($reward));
                }
                $reward->setCouponValue($command->getCouponValue());
                $reward->setCouponGenerator($command->getCouponGenerator());
                $reward->changeCouponValueType($command->getCouponValueType() ?? CouponValueType::Money);
                $reward->setValidity($command->getDaysInactive(), $command->getDaysValid(), $command->getDateValid());
                break;
            case ConversionCouponReward::TYPE:
                if (!$reward instanceof ConversionCouponReward) {
                    throw new \InvalidArgumentException(get_class($reward));
                }
                $reward->changeUnitsConversion($command->getUnitsConversion());
                $reward->setCouponGenerator($command->getCouponGenerator());
                $reward->setValidity($command->getDaysInactive(), $command->getDaysValid(), $command->getDateValid());
                break;
            default:
                throw new \InvalidArgumentException('Not supported reward');
        }

        $reward->setTranslations($command->getTranslations());
        $reward->setCostInPoints($command->getCostInPoints());
        $reward->setLevels($command->getLevels());
        $reward->setSegments($command->getSegments());
        $reward->setFeatured($command->isFeatured());
        $reward->setPublic($command->isPublic());
        $reward->setPriceValues($command->getPrice(), $command->getTax(), $command->getTaxPriceValue());
        $reward->updateActivity($command->getActivity());
        $reward->updateVisibility($command->getVisibility());
        $reward->updateCategories($command->getCategories());
        $reward->updateLabels($command->getLabels());
        $reward->updateUsageLimit($command->getUsageLimit());
        $reward->setTarget($command->getTarget());
        $reward->changeWalletType($command->getWalletTypeId());

        if ($command->isActive()) {
            $reward->activate();
        } else {
            $reward->deactivate();
        }

        $this->rewardRepository->save($reward);
    }
}
