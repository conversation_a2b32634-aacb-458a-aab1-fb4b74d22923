<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Application\CommandHandler;

use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Reward\Application\Command\CreateRewardCategory;
use OpenLoyalty\Reward\Domain\RewardCategory;
use OpenLoyalty\Reward\Domain\RewardCategoryRepository;

class CreateRewardCategoryCommandHandler implements CommandHandlerInterface
{
    /**
     * @var \OpenLoyalty\Reward\Domain\RewardCategoryRepository
     */
    protected $rewardCategoryRepository;

    /**
     * @var \OpenLoyalty\Core\Domain\StoreRepository
     */
    protected $storeRepository;

    public function __construct(RewardCategoryRepository $rewardCategoryRepository, StoreRepository $storeRepository)
    {
        $this->rewardCategoryRepository = $rewardCategoryRepository;
        $this->storeRepository = $storeRepository;
    }

    public function __invoke(CreateRewardCategory $command): void
    {
        $data = $command->getRewardCategoryData();
        RewardCategory::validateRequiredData($data);

        $store = $this->storeRepository->byId($command->getStoreId());

        $rewardCategory = new RewardCategory($command->getRewardCategoryId(), $store, $data);
        $this->rewardCategoryRepository->save($rewardCategory);
    }
}
