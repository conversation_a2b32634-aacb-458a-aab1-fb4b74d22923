<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Domain\Validator;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Reward\Domain\CouponReward;
use OpenLoyalty\Reward\Domain\Exception\NoCouponsLeftException;
use OpenLoyalty\Reward\Domain\Exception\RewardLimitExceededException;
use OpenLoyalty\Reward\Domain\Exception\RewardLimitPerCustomerExceededException;
use OpenLoyalty\Reward\Domain\Repository\CouponRepositoryInterface;
use OpenLoyalty\Reward\Domain\Repository\IssuedRewardRepositoryInterface;
use OpenLoyalty\Reward\Domain\Reward;

class RewardValidator implements RewardValidatorInterface
{
    /**
     * @var IssuedRewardRepositoryInterface
     */
    protected $issueRewardRepository;

    /**
     * @var \OpenLoyalty\Reward\Domain\Repository\CouponRepositoryInterface
     */
    protected $couponRepository;

    public function __construct(
        IssuedRewardRepositoryInterface $issueRewardRepository,
        CouponRepositoryInterface $couponRepository
    ) {
        $this->issueRewardRepository = $issueRewardRepository;
        $this->couponRepository = $couponRepository;
    }

    public function validateRewardLimits(Reward $reward, CustomerId $customerId, int $quantity = 1): void
    {
        if ($reward instanceof CouponReward) {
            if (!$reward->isCouponGeneratorActive() && $this->couponRepository->getNotIssuedCouponsCount($reward) < $quantity) {
                throw new NoCouponsLeftException();
            }
        }

        $usageLimit = $reward->getUsageLimit();

        if (null === $usageLimit) {
            return;
        }

        if (null !== $usageLimit->getGeneral()) {
            $allIssued = $this->issueRewardRepository->getIssuedCount($reward);
            if ($allIssued + $quantity > $usageLimit->getGeneral()) {
                throw new RewardLimitExceededException();
            }
        }

        if (null !== $usageLimit->getPerUser()) {
            $allCustomerIssued = $this->issueRewardRepository->getIssuedCount($reward, $customerId);
            if ($allCustomerIssued + $quantity > $usageLimit->getPerUser()) {
                throw new RewardLimitPerCustomerExceededException();
            }
        }
    }
}
