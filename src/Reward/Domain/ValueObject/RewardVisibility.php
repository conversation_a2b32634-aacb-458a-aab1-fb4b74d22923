<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Domain\ValueObject;

use DateTime;

class RewardVisibility
{
    public function __construct(
        private bool $allTime = true,
        private ?DateTime $from = null,
        private ?DateTime $to = null
    ) {
    }

    public function isAllTime(): bool
    {
        return $this->allTime;
    }

    public function getFrom(): ?DateTime
    {
        if ($this->allTime) {
            return null;
        }

        return $this->from;
    }

    public function getTo(): ?DateTime
    {
        if ($this->allTime) {
            return null;
        }

        return $this->to;
    }
}
