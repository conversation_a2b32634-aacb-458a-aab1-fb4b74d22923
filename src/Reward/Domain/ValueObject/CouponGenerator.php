<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Domain\ValueObject;

class CouponGenerator
{
    public const NUMBERS_AND_LETTERS_CHARACTER_SET = 'alphanum';
    public const LETTERS_CHARACTER_SET = 'alpha';
    public const NUMBERS_CHARACTER_SET = 'num';

    /**
     * @var int|null
     */
    protected $length;

    /**
     * @var string|null
     */
    protected $characterSet;

    /**
     * @var string|null
     */
    protected $prefix;

    public function __construct(int $length, string $characterSet = self::NUMBERS_AND_LETTERS_CHARACTER_SET, ?string $prefix = null)
    {
        $this->length = $length;
        $this->characterSet = $characterSet;
        $this->prefix = $prefix;
    }

    public function getLength(): ?int
    {
        return $this->length;
    }

    public function getCharacterSet(): ?string
    {
        return $this->characterSet;
    }

    public function getPrefix(): ?string
    {
        return $this->prefix;
    }
}
