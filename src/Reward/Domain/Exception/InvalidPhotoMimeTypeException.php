<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Domain\Exception;

use DomainException;

class InvalidPhotoMimeTypeException extends DomainException
{
    public static function create(string $types): self
    {
        return new self(
            sprintf(
                'Given file has invalid mime type. Expected types: %s',
                $types
            )
        );
    }
}
