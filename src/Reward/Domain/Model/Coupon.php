<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Domain\Model;

use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Reward\Domain\Reward;

class Coupon implements TimestampableInterface, BlameableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    protected string $code;
    protected ?Store $store = null;
    private ?Reward $reward = null;
    protected bool $issued = false;

    public function __construct(string $code)
    {
        $this->code = $code;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getReward(): ?Reward
    {
        return $this->reward;
    }

    public function setReward(Reward $reward): void
    {
        $this->reward = $reward;
    }

    public function getStore(): ?Store
    {
        return $this->store;
    }

    public function setStore(?Store $store): void
    {
        $this->store = $store;
    }

    public function isIssued(): bool
    {
        return $this->issued;
    }
}
