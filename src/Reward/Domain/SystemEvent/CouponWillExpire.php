<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Domain\SystemEvent;

use OpenLoyalty\Core\Domain\Id\IssuedRewardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventInterface;
use OpenLoyalty\Core\Domain\Webhook\Event\WebhookEventInterface;

class CouponWillExpire implements EventInterface, WebhookEventInterface
{
    public function __construct(
        private IssuedRewardId $issuedRewardId,
        private StoreId $storeId
    ) {
    }

    public function getIssuedRewardId(): IssuedRewardId
    {
        return $this->issuedRewardId;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }
}
