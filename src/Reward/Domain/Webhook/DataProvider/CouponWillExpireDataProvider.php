<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Domain\Webhook\DataProvider;

use OpenLoyalty\Core\Domain\Exception\StoreNotFoundException;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\Webhook\DataProvider\WebhookDataProviderInterface;
use OpenLoyalty\Core\Domain\Webhook\Event\WebhookEventInterface;
use OpenLoyalty\Core\Domain\Webhook\Factory\MemberResponseFactoryInterface;
use OpenLoyalty\Core\Domain\Webhook\Response\WebhookData;
use OpenLoyalty\Reward\Domain\Repository\IssuedRewardRepositoryInterface;
use OpenLoyalty\Reward\Domain\SystemEvent\CouponWillExpire;

final readonly class CouponWillExpireDataProvider implements WebhookDataProviderInterface
{
    public function __construct(
        private IssuedRewardRepositoryInterface $issuedRewardRepository,
        private StoreRepository $storeRepository,
        private MemberResponseFactoryInterface $memberResponseFactory
    ) {
    }

    public function getEventName(): string
    {
        return 'CouponWillExpire';
    }

    /**
     * @throws StoreNotFoundException
     */
    public function getData(
        WebhookEventInterface|CouponWillExpire $event
    ): ?WebhookData {
        $issuedReward = $this->issuedRewardRepository->byId($event->getIssuedRewardId());

        if (null === $issuedReward || null === $issuedReward->getIssuedCoupon()) {
            return null;
        }

        $store = $this->storeRepository->byId($event->getStoreId(), true);

        if (null === $store) {
            throw new StoreNotFoundException($event->getStoreId());
        }

        $member = $this->memberResponseFactory->create($issuedReward->getCustomerId());

            $data = [
                'customer' => $member,
                'issuedCoupon' => $issuedReward->getIssuedCoupon(),
            ];

        return new WebhookData(
            $this->getEventName(),
            $store->getCode(),
            $data
        );
    }

    public function supports(string $eventClassName): bool
    {
        return CouponWillExpire::class === $eventClassName;
    }
}
