<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Domain\Provider;

use OpenLoyalty\Reward\Domain\Reward;

interface RewardVisibilityProviderInterface
{
    /**
     * @return string[] array of `CustomerId`s
     */
    public function getCustomersRewardIsVisibleFor(Reward $reward): array;

    public function isVisibleForCustomer(Reward $reward, string $customerId): bool;

    public function customerMatchesLevelOrSegment(Reward $reward, string $customerId): bool;
}
