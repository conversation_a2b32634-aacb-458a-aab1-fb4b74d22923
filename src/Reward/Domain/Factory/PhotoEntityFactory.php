<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Domain\Factory;

use OpenLoyalty\Core\Domain\Id\PhotoId;
use OpenLoyalty\Reward\Domain\Model\RewardPhoto;
use OpenLoyalty\Reward\Domain\PhotoMimeType;
use OpenLoyalty\Reward\Domain\PhotoOriginalName;
use OpenLoyalty\Reward\Domain\PhotoPath;
use OpenLoyalty\Reward\Domain\Reward;

class PhotoEntityFactory implements PhotoEntityFactoryInterface
{
    public function crete(
        Reward $reward,
        PhotoId $photoId,
        PhotoPath $photoPath,
        PhotoOriginalName $originalName,
        PhotoMimeType $mimeType
    ): RewardPhoto {
        return new RewardPhoto($reward, $photoId, $photoPath, $originalName, $mimeType);
    }
}
