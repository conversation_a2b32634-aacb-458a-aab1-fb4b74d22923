<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Domain;

use Assert\Assertion as Assert;
use Assert\AssertionFailedException;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use OpenLoyalty\Core\Domain\Id\RewardCategoryId;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Infrastructure\Model\FallbackTranslatable;

class RewardCategory implements TranslatableInterface, TimestampableInterface
{
    use FallbackTranslatable;
    use TimestampableTrait;
    use BlameableTrait;

    protected RewardCategoryId $rewardCategoryId;
    protected bool $active = true;
    protected ?int $sortOrder = 0;
    protected Store $store;

    public function __construct(RewardCategoryId $rewardCategoryId, Store $store, array $data = [])
    {
        $this->rewardCategoryId = $rewardCategoryId;
        $this->setFromArray($data);
        $this->store = $store;
    }

    public function setFromArray(array $data): void
    {
        if (isset($data['sortOrder'])) {
            $this->sortOrder = $data['sortOrder'];
        }

        if (isset($data['active'])) {
            $this->active = $data['active'];
        }

        if (array_key_exists('translations', $data)) {
            foreach ($data['translations'] as $locale => $transData) {
                if (array_key_exists('name', $transData)) {
                    $this->translate($locale, false)->setName($transData['name']);
                }
            }
            /** @var RewardTranslation $translation */
            foreach ($this->getTranslations() as $translation) {
                if (!isset($data['translations'][$translation->getLocale()])) {
                    $this->removeTranslation($translation);
                }
            }
        }
    }

    /**
     * @throws AssertionFailedException
     */
    public static function validateRequiredData(array $data): void
    {
        Assert::keyIsset($data, 'sortOrder');
        Assert::integer($data['sortOrder']);
    }

    public function getRewardCategoryId(): RewardCategoryId
    {
        return $this->rewardCategoryId;
    }

    public function setRewardCategoryId(RewardCategoryId $rewardCategoryId): void
    {
        $this->rewardCategoryId = $rewardCategoryId;
    }

    public function getName(): ?string
    {
        return $this->translateFieldFallback(null, 'name')->getName();
    }

    public function setName(?string $name): void
    {
        $this->translate(null, false)->setName($name);
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): void
    {
        $this->active = $active;
    }

    public function getSortOrder(): ?int
    {
        return $this->sortOrder;
    }

    public function setSortOrder(?int $sortOrder): void
    {
        $this->sortOrder = $sortOrder;
    }

    public function getStore(): Store
    {
        return $this->store;
    }
}
