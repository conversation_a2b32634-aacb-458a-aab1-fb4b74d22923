<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Domain;

use DateTimeInterface;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;

class StaticCouponReward extends CouponReward
{
    public const TYPE = 'static_coupon';

    protected string|int|float $couponValue;

    protected CouponValueType $couponValueType = CouponValueType::Money;

    public function __construct(
        RewardId $rewardId,
        StoreId $storeId,
        bool $active,
        float $costInPoints,
        DateTimeInterface $createdAt,
        float $couponValue,
        ?WalletTypeId $walletTypeId = null,
        CouponValueType $couponValueType = CouponValueType::Money
    ) {
        parent::__construct(
            $rewardId,
            $storeId,
            $active,
            $costInPoints,
            $createdAt,
            $walletTypeId
        );
        $this->couponValue = $couponValue;
        $this->couponValueType = $couponValueType;
    }

    public function getCouponValue(): float
    {
        return (float) $this->couponValue;
    }

    public function getType(): string
    {
        return self::TYPE;
    }

    public function setCouponValue(float $couponValue): void
    {
        $this->couponValue = $couponValue;
    }

    public function getCouponValueType(): CouponValueType
    {
        return $this->couponValueType;
    }

    public function changeCouponValueType(CouponValueType $couponValueType): void
    {
        $this->couponValueType = $couponValueType;
    }
}
