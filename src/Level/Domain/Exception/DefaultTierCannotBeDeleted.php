<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain\Exception;

use OpenLoyalty\Core\Domain\Exception\DomainException;

class DefaultTierCannotBeDeleted extends DomainException
{
    public function __construct()
    {
        parent::__construct('level.cannot_delete_default_tier');
    }
}
