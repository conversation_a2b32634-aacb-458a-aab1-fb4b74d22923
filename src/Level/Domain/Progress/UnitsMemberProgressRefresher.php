<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain\Progress;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Level\Domain\AccountFacadeInterface;
use OpenLoyalty\Level\Domain\Context\ContextInterface;
use OpenLoyalty\Level\Domain\Entity\Condition;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgress;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgressCondition;
use OpenLoyalty\Level\Domain\TierSetMemberProgressRepositoryInterface;
use OpenLoyalty\Level\Domain\ValueObject\ConditionId;
use OpenLoyalty\User\Domain\Customer;

abstract class UnitsMemberProgressRefresher implements TierMemberProgressRefresherInterface
{
    public function __construct(
        private readonly TierSetMemberProgressRepositoryInterface $memberProgressRepository,
        protected readonly AccountFacadeInterface $accountFacade,
    ) {
    }

    public function refreshProgress(
        TierSetMemberProgress $memberProgress,
        Customer $customer,
        DateTimeImmutable $currentTime,
        ?ContextInterface $context
    ): void {
        $tierSet = $memberProgress->getTierSet();
        $storeId = $tierSet->getStoreId();
        $tierSetConditions = $tierSet->getConditionsByAttribute($this->getAttribute());

        if (empty($tierSetConditions)) {
            return;
        }

        /**
         * @var Condition $condition
         */
        foreach ($tierSetConditions as $condition) {
            $units = $this->getUnits($condition, $storeId, $customer->getCustomerId());

            $progress = $this->updateCondition($memberProgress, $units, $condition->getId());
            $memberProgress->addCondition($progress);
        }
        $memberProgress->setProgressUpdatedAt($currentTime);
        $this->memberProgressRepository->persist($memberProgress);
    }

    public function updateCondition(
        TierSetMemberProgress $memberProgress,
        float $units,
        ConditionId $conditionId
    ): TierSetMemberProgressCondition {
        $progressCondition = $memberProgress->getMemberProgressConditionByAttribute($conditionId);
        if (null === $progressCondition) {
            $progressCondition = new TierSetMemberProgressCondition(
                $memberProgress,
                $conditionId,
                $units,
                null
            );
        }
        $progressCondition->setValue($units);

        return $progressCondition;
    }

    abstract public function getUnits(Condition $condition, StoreId $storeId, CustomerId $customerId): float;

    abstract public function getAttribute(): string;
}
