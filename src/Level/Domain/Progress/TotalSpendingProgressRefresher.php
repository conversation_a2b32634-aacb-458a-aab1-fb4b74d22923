<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain\Progress;

use DateTimeImmutable;
use OpenLoyalty\Level\Domain\Context\ContextInterface;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgress;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgressCondition;
use OpenLoyalty\Level\Domain\Enum\ConditionAttribute;
use OpenLoyalty\Level\Domain\TierSetMemberProgressRepositoryInterface;
use OpenLoyalty\User\Domain\Customer;

readonly class TotalSpendingProgressRefresher implements TierMemberProgressRefresherInterface
{
    public function __construct(
        private TierSetMemberProgressRepositoryInterface $memberProgressRepository,
    ) {
    }

    public function refreshProgress(
        TierSetMemberProgress $memberProgress,
        Customer $customer,
        DateTimeImmutable $currentTime,
        ?ContextInterface $context
    ): void {
        $tierSet = $memberProgress->getTierSet();
        $totalSpendingConditions = $tierSet->getConditionsByAttribute(ConditionAttribute::TOTAL_SPENDING->value);

        if (empty($totalSpendingConditions)) {
            return;
        }

        foreach ($totalSpendingConditions as $condition) {
            $totalSpendingProgress = $memberProgress->getMemberProgressConditionByAttribute($condition->getId());

            if (null === $totalSpendingProgress) {
                $totalSpendingProgress = new TierSetMemberProgressCondition(
                    $memberProgress,
                    $condition->getId(),
                    $customer->getTransactionsAmount(),
                    null
                );
            }

            $totalSpendingProgress->setValue($customer->getTransactionsAmount());
            $memberProgress->addCondition($totalSpendingProgress);
        }

        $memberProgress->setProgressUpdatedAt($currentTime);
        $this->memberProgressRepository->persist($memberProgress);
    }

    public function supports(TierRefreshTriggerType $triggerType): bool
    {
        return TierRefreshTriggerType::Transaction === $triggerType;
    }
}
