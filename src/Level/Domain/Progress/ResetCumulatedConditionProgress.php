<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain\Progress;

use OpenLoyalty\Level\Domain\Entity\Condition;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgress;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgressCondition;
use OpenLoyalty\Level\Domain\Enum\ConditionAttribute;
use OpenLoyalty\Level\Domain\TierSetMemberProgressRepositoryInterface;
use OpenLoyalty\User\Domain\Customer;
use Psr\Log\LoggerInterface;

final readonly class ResetCumulatedConditionProgress implements ResetCumulatedConditionProgressInterface
{
    public function __construct(
        private TierSetMemberProgressRepositoryInterface $memberProgressRepository,
        private LoggerInterface $tierLogger
    ) {
    }

    public function reset(TierSetMemberProgress $memberProgress, Customer $member, \DateTimeImmutable $currentTime): void
    {
        $conditionValues = $this->getCumulatedConditions($memberProgress);

        if (empty($conditionValues)) {
            return;
        }

        $conditionValuesForLogger = $this->prepareConditionValuesForLog($conditionValues);

        foreach ($conditionValues as $conditionValue) {
            if (0.0 !== $conditionValue->getPendingNextPeriodDowngradeValue()) {
                $conditionValue->setValue($conditionValue->getPendingNextPeriodDowngradeValue());
                $conditionValue->setPendingNextPeriodDowngradeValue(0.0);
            } else {
                $conditionValue->setValue(0.0);
            }
        }

        if (null !== $memberProgress->getPeriodNextRecalculationAt()) {
            $memberProgress->setOldRecalculationAt($memberProgress->getPeriodNextRecalculationAt());
        }

        $memberProgress->setProgressUpdatedAt($currentTime->modify('+1 hour'));
        $this->memberProgressRepository->persist($memberProgress);

        $this->tierLogger->info(
            'Reset cumulated units balance conditions',
            [
                'storeId' => (string) $memberProgress->getTierSet()->getStoreId(),
                'customerId' => (string) $memberProgress->getMemberId(),
                'currentTier' => (string) $memberProgress->getCurrentLevelId(),
                'tierSetId' => (string) $memberProgress->getTierSet()->getTierSetId(),
                'currentTime' => $currentTime,
                'conditionValues' => $conditionValuesForLogger,
            ]
        );
    }

    /**
     * @return array<TierSetMemberProgressCondition>
     */
    private function getCumulatedConditions(TierSetMemberProgress $memberProgress): array
    {
        $conditions = $memberProgress
            ->getTierSet()
            ->getConditionsByAttribute(ConditionAttribute::CUMULATED_EARNED_UNITS->value);

        if (empty($conditions)) {
            return [];
        }

        $conditionValues = [];
        /**
         * @var Condition $condition
         */
        foreach ($conditions as $condition) {
            $conditionValue = $memberProgress->getMemberProgressConditionByAttribute($condition->getId());

            if (null === $conditionValue) {
                continue;
            }

            $conditionValues[] = $conditionValue;
        }

        return $conditionValues;
    }

    /**
     * @param  array<TierSetMemberProgressCondition>          $progressCondition
     * @return array<int<0, max>, array<string,float|string>>
     */
    private function prepareConditionValuesForLog(array $progressCondition): array
    {
        $conditionValues = [];
        foreach ($progressCondition as $condition) {
            $conditionValues[] = [
                'conditionId' => (string) $condition->getConditionId(),
                'value' => $condition->getValue(),
            ];
        }

        return $conditionValues;
    }
}
