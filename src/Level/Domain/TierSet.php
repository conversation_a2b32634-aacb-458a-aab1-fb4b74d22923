<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TierSetId;
use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Infrastructure\Model\FallbackTranslatable;
use OpenLoyalty\Level\Domain\Entity\Condition;
use OpenLoyalty\Level\Domain\Exception\CannotAddMoreThanOneConditionException;
use OpenLoyalty\Level\Domain\ValueObject\ConditionId;
use OpenLoyalty\Level\Domain\ValueObject\Downgrade;

class TierSet implements TranslatableInterface, TimestampableInterface, BlameableInterface
{
    use FallbackTranslatable;
    use TimestampableTrait;
    use BlameableTrait;

    /**
     * @var Collection<Condition>
     */
    private Collection $conditions;

    /**
     * @var Collection<int, Level>
     */
    private Collection $tiers;

    public function __construct(
        private TierSetId $tierSetId,
        private Store $store,
        private bool $active,
        array $conditions,
        private Downgrade $downgrade,
        private array $labels = [],
        private readonly bool $isDefault = false,
        private bool $isMigrated = false
    ) {
        $this->replaceConditions($conditions);
    }

    public function getTierSetId(): TierSetId
    {
        return $this->tierSetId;
    }

    public function getName(): ?string
    {
        return $this->translateFieldFallback(null, 'name')->getName();
    }

    public function getDescription(): ?string
    {
        return $this->translateFieldFallback(null, 'description')->getDescription();
    }

    /**
     * @return Condition[]
     */
    public function getConditions(): array
    {
        return $this->conditions->toArray();
    }

    /**
     * @return Level[]
     */
    public function getTiers(): array
    {
        return $this->tiers->toArray();
    }

    /**
     * @param Condition[] $conditions
     */
    public function replaceConditions(array $conditions): void
    {
        foreach ($conditions as $condition) {
            $condition->setTierSet($this);
        }
        $this->conditions = new ArrayCollection($conditions);
    }

    /**
     * @param Level[] $tiers
     */
    public function replaceTiers(array $tiers): void
    {
        foreach ($tiers as $tier) {
            $tier->setTierSet($this);
        }
        $this->tiers = new ArrayCollection($tiers);
    }

    public function removeCondition($condition): void
    {
        $this->conditions->removeElement($condition);
    }

    public function addCondition(Condition $condition): void
    {
        $this->conditions->add($condition);
        $condition->setTierSet($this);
    }

    public function findConditionByAttribute(string $attribute): ?Condition
    {
        foreach ($this->getConditions() as $condition) {
            if ($condition->getAttribute() === $attribute) {
                return $condition;
            }
        }

        return null;
    }

    private function checkIfConditionExist(ConditionId $id): ?Condition
    {
        foreach ($this->conditions as $condition) {
            if ((string) $condition->getId() === (string) $id) {
                return $condition;
            }
        }

        return null;
    }

    /**
     * @throws CannotAddMoreThanOneConditionException
     */
    public function updateConditions(array $conditions): void
    {
        if ($this->isMigrated && 1 < count($conditions)) {
            throw new CannotAddMoreThanOneConditionException();
        }

        /**
         * @var Condition $condition
         */
        foreach ($conditions as $condition) {
            $existInTierSet = $this->checkIfConditionExist($condition->getId());
            if (null !== $existInTierSet) {
                $existInTierSet->setWalletType($condition->getWalletType());
            } else {
                $this->addCondition($condition);
            }
        }

        foreach ($this->conditions as $oldCondition) {
            $exist = false;
            foreach ($conditions as $condition) {
                if ((string) $oldCondition->getId() === (string) $condition->getId()) {
                    $exist = true;
                }
            }
            if (false === $exist) {
                $this->removeCondition($oldCondition);
            }
        }
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function activate(): void
    {
        $this->active = true;
    }

    public function deactivate(): void
    {
        $this->active = false;
    }

    public function getLabels(): array
    {
        return $this->labels;
    }

    public function updateLabels(array $labels): void
    {
        $this->labels = $labels;
    }

    public function isDefault(): bool
    {
        return $this->isDefault;
    }

    public function isMigrated(): bool
    {
        return $this->isMigrated;
    }

    public function getStore(): Store
    {
        return $this->store;
    }

    public function getStoreId(): StoreId
    {
        return $this->store->getStoreId();
    }

    public function getConditionsByAttribute(string $attribute): array
    {
        $result = [];
        /**
         * @var Condition $condition
         */
        foreach ($this->conditions->toArray() as $condition) {
            if ($attribute === $condition->getAttribute()) {
                $result[] = $condition;
            }
        }

        return $result;
    }

    /**
     * @param array<string, ?TierSetTranslation> $translations
     */
    public function assignTranslations(array $translations): void
    {
        foreach ($translations as $locale => $transData) {
            if (null === $transData) {
                continue;
            }

            $this->translate($locale, false)->setName($transData->getName());
            $this->translate($locale, false)->setDescription($transData->getDescription());
        }

        /** @var TierSetTranslation $translation */
        foreach ($this->getTranslations() as $translation) {
            if (!isset($translations[$translation->getLocale()])) {
                $this->removeTranslation($translation);
            }
        }
    }

    public function setDowngrade(Downgrade $downgrade): void
    {
        $this->downgrade = $downgrade;
    }

    public function getDowngrade(): Downgrade
    {
        return $this->downgrade;
    }
}
