<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain\Entity;

use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\TierSetMemberProgressId;
use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;
use OpenLoyalty\Level\Domain\TierSet;
use OpenLoyalty\Level\Domain\ValueObject\ConditionId;
use OpenLoyalty\Level\Domain\ValueObject\PeriodicDowngradeRecalculation;

class TierSetMemberProgress implements TimestampableInterface, BlameableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    /** @@set */
    private TierSetMemberProgressId $id;
    /**
     * @var Collection<TierSetMemberProgressCondition>
     */
    private Collection $memberProgressConditions;
    private ?PeriodicDowngradeRecalculation $periodicDowngradeRecalculation = null;
    private ?DateTimeImmutable $lastDowngradeAt;
    private ?DateTimeImmutable $lastPromotionAt;
    private ?DateTimeImmutable $progressUpdatedAt;

    public function __construct(
        private readonly TierSet $tierSet,
        private readonly CustomerId $memberId,
        private ?LevelId $currentLevelId,
        private ?DateTimeImmutable $assignedManuallyTierAt,
        array $memberProgressConditions = []
    ) {
        $this->replaceConditions($memberProgressConditions);
    }

    public function getId(): TierSetMemberProgressId
    {
        return $this->id;
    }

    public function getTierSet(): TierSet
    {
        return $this->tierSet;
    }

    public function getMemberId(): CustomerId
    {
        return $this->memberId;
    }

    public function isAssignedManually(): bool
    {
        return null !== $this->assignedManuallyTierAt;
    }

    public function setAssignedManually(DateTimeImmutable $assignedManuallyTierAt): void
    {
        $this->assignedManuallyTierAt = $assignedManuallyTierAt;
    }

    public function removeAssignedManually(): void
    {
        $this->assignedManuallyTierAt = null;
    }

    public function getAssignedManuallyTierAt(): ?DateTimeImmutable
    {
        return $this->assignedManuallyTierAt;
    }

    public function getCurrentLevelId(): ?LevelId
    {
        return $this->currentLevelId;
    }

    public function setCurrentLevelId(LevelId $currentLevelId): void
    {
        $this->currentLevelId = $currentLevelId;
    }

    public function getLastPromotionAt(): ?DateTimeImmutable
    {
        return $this->lastPromotionAt ?? null;
    }

    public function setLastPromotionAt(DateTimeImmutable $lastPromotionAt): void
    {
        $this->lastPromotionAt = $lastPromotionAt;
    }

    public function getProgressUpdatedAt(): ?DateTimeImmutable
    {
        return $this->progressUpdatedAt;
    }

    public function setProgressUpdatedAt(?DateTimeImmutable $progressUpdatedAt): void
    {
        $this->progressUpdatedAt = $progressUpdatedAt;
    }

    public function getLastDowngradeAt(): ?DateTimeImmutable
    {
        return $this->lastDowngradeAt;
    }

    public function setLastDowngradeAt(DateTimeImmutable $lastDowngradeAt): void
    {
        $this->lastDowngradeAt = $lastDowngradeAt;
    }

    public function getMemberProgressConditions(): array
    {
        return $this->memberProgressConditions->toArray();
    }

    public function getMemberProgressConditionByAttribute(ConditionId $conditionId): ?TierSetMemberProgressCondition
    {
        /**
         * @var TierSetMemberProgressCondition $progressCondition
         */
        foreach ($this->memberProgressConditions->toArray() as $progressCondition) {
            if ((string) $conditionId === (string) $progressCondition->getConditionId()) {
                return $progressCondition;
            }
        }

        return null;
    }

    public function replaceConditions(array $conditions): void
    {
        $this->memberProgressConditions = new ArrayCollection($conditions);
    }

    public function addCondition(TierSetMemberProgressCondition $condition): void
    {
        $this->memberProgressConditions->add($condition);
    }

    public function setPeriodRecalculationDates(
        PeriodicDowngradeRecalculation $periodicDowngradeRecalculation
    ): void {
        $this->periodicDowngradeRecalculation = $periodicDowngradeRecalculation;
    }

    public function getPeriodNextRecalculationAt(): ?DateTimeImmutable
    {
        return $this->periodicDowngradeRecalculation?->getNextRecalculationAt();
    }

    public function getPeriodicDowngradeRecalculation(): ?PeriodicDowngradeRecalculation
    {
        return $this->periodicDowngradeRecalculation;
    }

    public function getOldRecalculationAt(): ?DateTimeImmutable
    {
        return $this->periodicDowngradeRecalculation?->getOldRecalculationAt();
    }

    public function setOldRecalculationAt(DateTimeImmutable $oldRecalculationAt): void
    {
        $this->periodicDowngradeRecalculation?->setOldRecalculationAt($oldRecalculationAt);
    }
}
