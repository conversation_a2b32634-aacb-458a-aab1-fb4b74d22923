<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain\Factory;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Level\Domain\Entity\TierMemberHistory;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgress;
use OpenLoyalty\Level\Domain\Enum\MemberTierAction;

interface TierMemberHistoryFactoryInterface
{
    public function create(
        TierSetMemberProgress $memberProgress,
        ?LevelId $newTierId,
        bool $downgrade,
        MemberTierAction $action,
        DateTimeImmutable $changedAt
    ): TierMemberHistory;
}
