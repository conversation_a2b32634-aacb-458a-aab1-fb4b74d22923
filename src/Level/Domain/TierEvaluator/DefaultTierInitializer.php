<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain\TierEvaluator;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\TierSetId;
use OpenLoyalty\Level\Domain\CustomerFacadeInterface;
use OpenLoyalty\Level\Domain\Exception\TierSetNotFoundException;
use OpenLoyalty\Level\Domain\Factory\TierSetMemberProgressFactoryInterface;
use OpenLoyalty\Level\Domain\LevelRepository;
use OpenLoyalty\Level\Domain\TierChanger\TierChangerInterface;
use OpenLoyalty\Level\Domain\TierSetMemberProgressRepositoryInterface;
use OpenLoyalty\Level\Domain\TierSetRepositoryInterface;

final readonly class DefaultTierInitializer
{
    public function __construct(
        private TierSetMemberProgressRepositoryInterface $memberProgressRepository,
        private TierSetRepositoryInterface $tierSetRepository,
        private CustomerFacadeInterface $customerFacade,
        private LevelRepository $levelRepository,
        private TierSetMemberProgressFactoryInterface $memberProgressFactory,
        private TierChangerInterface $tierChanger
    ) {
    }

    /**
     * @throws TierSetNotFoundException
     */
    public function initialize(CustomerId $customerId, TierSetId $tierSetId, ?LevelId $manuallyAssignedLevelId, DateTimeImmutable $currentTime): void
    {
        $customer = $this->customerFacade->getCustomer($customerId);
        if (null === $customer) {
            return;
        }

        $manuallyAssignedLevel = null !== $manuallyAssignedLevelId ? $this->levelRepository->byId($manuallyAssignedLevelId) : null;

        if (null !== $manuallyAssignedLevel) {
            $this->tierChanger->setManually($manuallyAssignedLevel, $tierSetId, $customerId, $currentTime);

            return;
        }

        $memberProgress = $this->memberProgressRepository->getMemberProgressByTierSet(
            $customer->getCustomerId(),
            $tierSetId,
        );

        if (null === $memberProgress) {
            $tierSet = $this->tierSetRepository->byId($tierSetId);

            if (null === $tierSet) {
                throw new TierSetNotFoundException();
            }

            $memberProgress = $this->memberProgressFactory->create(
                $tierSet,
                $customer->getCustomerId(),
                null
            );
        }

        $defaultLevel = $this->levelRepository->findDefault($tierSetId);
        $this->tierChanger->changeAutomatically($defaultLevel, $memberProgress, $currentTime);
        $this->memberProgressRepository->persist($memberProgress);
    }
}
