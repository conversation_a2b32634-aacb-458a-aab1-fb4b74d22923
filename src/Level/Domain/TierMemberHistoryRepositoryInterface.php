<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Repository;
use OpenLoyalty\Level\Domain\Entity\TierMemberHistory;

interface TierMemberHistoryRepositoryInterface extends Repository
{
    public function save(TierMemberHistory $entity): void;

    /**
     * @return TierMemberHistory[]
     */
    public function findAllForStore(
        StoreId $storeId
    ): array;
}
