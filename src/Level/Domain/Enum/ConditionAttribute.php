<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain\Enum;

enum ConditionAttribute: string
{
    case ACTIVE_UNITS = 'activeUnits';
    case TOTAL_EARNED_UNITS = 'totalEarnedUnits';
    case CUMULATED_EARNED_UNITS = 'cumulatedEarnedUnits';
    case MONTH_SINCE_JOINING_PROGRAM = 'monthsSinceJoiningProgram';
    case TOTAL_SPENDING = 'totalSpending';
}
