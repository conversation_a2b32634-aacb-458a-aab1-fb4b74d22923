<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain\TierChanger;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\TierSetId;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgress;
use OpenLoyalty\Level\Domain\Level;

interface TierChangerInterface
{
    public function changeAutomatically(
        ?Level $tier,
        TierSetMemberProgress $memberProgress,
        \DateTimeImmutable $currentTime
    ): void;

    public function setManually(
        Level $newTier,
        TierSetId $tierSetId,
        CustomerId $customerId,
        \DateTimeImmutable $currentTime
    ): void;

    public function removeManually(
        TierSetId $tierSetId,
        CustomerId $customerId,
        \DateTimeImmutable $currentTime
    ): void;
}
