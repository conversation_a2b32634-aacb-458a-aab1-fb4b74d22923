<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain\Checker;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Level\Domain\Provider\EnvironmentVariableProviderInterface;
use OpenLoyalty\Level\Domain\TierSetRepositoryInterface;

final readonly class TierSetLimitReachedChecker implements TierSetLimitReachedCheckerInterface
{
    public function __construct(
        private TierSetRepositoryInterface $tierSetRepository,
        private EnvironmentVariableProviderInterface $environmentVariableProvider
    ) {
    }

    public function isLimitForActiveReached(StoreId $storeId): bool
    {
        return $this->tierSetRepository->countAllActiveInAllStores($storeId) >= $this->environmentVariableProvider->getActiveTierSetLimit();
    }

    public function isTotalLimitReached(StoreId $storeId): bool
    {
        return $this->tierSetRepository->countAllInAllStores($storeId) >= $this->environmentVariableProvider->getTotalTierSetLimit();
    }
}
