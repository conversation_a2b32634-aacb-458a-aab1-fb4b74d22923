<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Domain;

use Knp\DoctrineBehaviors\Contract\Entity\TranslationInterface;
use OpenLoyalty\Core\Infrastructure\Model\FallbackTranslation;

class LevelTranslation implements TranslationInterface
{
    use FallbackTranslation;

    /**
     * @@set
     */
    private int $id;
    private ?string $name = null;
    private ?string $description = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }
}
