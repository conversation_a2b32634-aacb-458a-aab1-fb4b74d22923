<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Infrastructure\Validator\Constraints;

use OpenLoyalty\Level\Domain\TierSetRepositoryInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

class AllowOneConditionForMigratedTierSetValidator extends ConstraintValidator
{
    public function __construct(
        private readonly TierSetRepositoryInterface $tierSetRepository,
        private readonly TranslatorInterface $translator,
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (null === $value) {
            return;
        }

        if (null === $constraint->tierSetId) {
            $this->context->buildViolation($this->translator->trans('tierSet.tier_set_not_found'))->addViolation();
        }

        $tierSet = $this->tierSetRepository->byId($constraint->tierSetId);

        if ($tierSet->isMigrated() && 1 < count($value)) {
            $this->context->buildViolation(
                $constraint->message
            )->addViolation();
        }
    }
}
