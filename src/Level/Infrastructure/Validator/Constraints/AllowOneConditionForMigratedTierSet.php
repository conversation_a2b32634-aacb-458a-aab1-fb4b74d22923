<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Infrastructure\Validator\Constraints;

use OpenLoyalty\Core\Domain\Id\TierSetId;
use Symfony\Component\Validator\Constraint;

class AllowOneConditionForMigratedTierSet extends Constraint
{
    public ?TierSetId $tierSetId = null;
    public string $message = 'Cannot add more than one condition to a migrated tier set.';
}
