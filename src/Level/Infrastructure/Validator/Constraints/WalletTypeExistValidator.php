<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Infrastructure\Validator\Constraints;

use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Level\Domain\AccountFacadeInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

class WalletTypeExistValidator extends ConstraintValidator
{
    public function __construct(
        private readonly AccountFacadeInterface $accountFacade,
        private readonly StoreContextProviderInterface $storeContextProvider,
        private readonly TranslatorInterface $translator
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (empty($value)) {
            return;
        }

        $storeId = $this->storeContextProvider->getStore()->getStoreId();
        $walletType = $this->accountFacade->getWalletTypeByCode($value, $storeId);

        if (null === $walletType) {
            $this->context->buildViolation($this->translator->trans($constraint->message))->addViolation();
        }
    }
}
