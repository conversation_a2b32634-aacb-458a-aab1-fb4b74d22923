<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Infrastructure\Validator\Constraints;

use DateTimeImmutable;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

final class UniqueRecalculationDatesValidator extends ConstraintValidator
{
    public function __construct(
        private readonly TranslatorInterface $translator,
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (empty($value) || !is_array($value)) {
            return;
        }

        $dates = array_filter($value, static fn ($date) => $date instanceof DateTimeImmutable);

        if (empty($dates)) {
            return;
        }

        $formattedDates = array_map(static fn (DateTimeImmutable $date) => $date->format('m-d'), $dates);
        $dateCounts = array_count_values($formattedDates);

        foreach ($formattedDates as $index => $formattedDate) {
            if ($dateCounts[$formattedDate] > 1) {
                $this->context->buildViolation(
                    $this->translator->trans($constraint->message),
                )->atPath((string) $index)->addViolation();

                return;
            }
        }
    }
}
