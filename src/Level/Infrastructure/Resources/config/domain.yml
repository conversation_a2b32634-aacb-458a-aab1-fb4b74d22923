services:

    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    OpenLoyalty\Level\Domain\LevelRepository: '@OpenLoyalty\Level\Infrastructure\Persistence\Doctrine\Repository\DoctrineLevelRepository'

    OpenLoyalty\Level\Infrastructure\Persistence\Doctrine\Repository\DoctrineLevelRepository:
        public: true

    OpenLoyalty\Level\Infrastructure\Persistence\Doctrine\Repository\DoctrineRewardRepository: ~
    OpenLoyalty\Level\Domain\RewardRepositoryInterface: '@OpenLoyalty\Level\Infrastructure\Persistence\Doctrine\Repository\DoctrineRewardRepository'

    OpenLoyalty\Level\Infrastructure\Persistence\Doctrine\Repository\DoctrineTierSetRepository: ~
    OpenLoyalty\Level\Domain\TierSetRepositoryInterface: '@OpenLoyalty\Level\Infrastructure\Persistence\Doctrine\Repository\DoctrineTierSetRepository'

    OpenLoyalty\Level\Infrastructure\Persistence\Doctrine\Repository\DoctrineConditionRepository: ~
    OpenLoyalty\Level\Domain\ConditionRepositoryInterface: '@OpenLoyalty\Level\Infrastructure\Persistence\Doctrine\Repository\DoctrineConditionRepository'

    OpenLoyalty\Level\Infrastructure\Provider\NextLevelProviderInterface:
        class: OpenLoyalty\Level\Infrastructure\Provider\NextLevelProvider

    OpenLoyalty\Level\Infrastructure\LevelExpireCheckerInterface:
        class: OpenLoyalty\Level\Infrastructure\LevelExpireChecker

    OpenLoyalty\Level\Application\LevelExpireNotifier: ~
    OpenLoyalty\Level\Application\LevelExpireNotifierInterface: '@OpenLoyalty\Level\Application\LevelExpireNotifier'

    OpenLoyalty\Level\Domain\LevelValidator: ~
    OpenLoyalty\Level\Domain\LevelValidatorInterface: '@OpenLoyalty\Level\Domain\LevelValidator'

    OpenLoyalty\Level\Domain\Provider\EnvironmentVariableProviderInterface:
        class: OpenLoyalty\Level\Domain\Provider\EnvironmentVariableProvider
        arguments:
            $activeTierSetLimit: '%active_tier_set_limit%'
            $totalTierSetLimit: '%total_tier_set_limit%'

    OpenLoyalty\Level\Domain\Checker\TierSetLimitReachedChecker: ~
    OpenLoyalty\Level\Domain\Checker\TierSetLimitReachedCheckerInterface: '@OpenLoyalty\Level\Domain\Checker\TierSetLimitReachedChecker'