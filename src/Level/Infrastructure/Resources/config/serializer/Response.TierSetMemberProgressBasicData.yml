OpenLoyalty\Level\Application\Response\TierSetMemberProgressBasicData:
    exclusion_policy: all
    discriminator:
        disabled: true
    properties:
        currentTierName:
            expose: true
        tierSetId:
            expose: true
            inline: true
        tierSetName:
            expose: true
        manually:
            expose: true
    virtual_properties:
        currentTierId:
            exp: "null !== object.currentTierId ? object.currentTierId.__toString() : null"
