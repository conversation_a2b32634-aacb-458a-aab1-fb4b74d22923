OpenLoyalty\Level\Application\Job\EvaluateMemberTierJob:
    exclusion_policy: ALL
    properties:
        memberTierSet:
            expose: true
            type: OpenLoyalty\Level\Application\DTO\MemberTierSet
        currentTime:
            expose: true
            type: DateTimeImmutable<'Y-m-d H:i:s.u e P'>
        triggerType:
            expose: true
            type: enum<OpenLoyalty\Level\Domain\Progress\TierRefreshTriggerType>
        storeId:
            expose: true
            type: OpenLoyalty\Core\Domain\Id\StoreId
        dispositionTierChangeType:
            expose: true
            type: enum<OpenLoyalty\Level\Domain\Enum\DispositionTierChangeType>
        context:
            expose: true
            type: OpenLoyalty\Level\Domain\Context\Context