OpenLoyalty\Level\Domain\TierSetTranslation:
    type: entity
    cache:
        usage: NONSTRICT_READ_WRITE
        region: entity_translations
    id:
        id:
            type: integer
            generator:
                strategy: AUTO
    fields:
        name:
            type: string
            nullable: true
        description:
            type: text
            nullable: true
            column: description
    manyToOne:
        translatable:
            targetEntity: OpenLoyalty\Level\Domain\TierSet
            inversedBy: translations
            cascade: [ 'persist', 'merge' ]
            joinColumn:
                name: translatable_id
                nullable: false
                referencedColumnName: tier_set_id
                onDelete: CASCADE
