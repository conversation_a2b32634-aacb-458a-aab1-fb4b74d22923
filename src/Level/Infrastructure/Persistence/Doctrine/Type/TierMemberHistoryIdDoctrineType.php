<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Infrastructure\Persistence\Doctrine\Type;

use Doctrine\DBAL\Platforms\AbstractPlatform;
use OpenLoyalty\Level\Domain\ValueObject\TierMemberHistoryId;
use Ramsey\Uuid\Doctrine\UuidType;

class TierMemberHistoryIdDoctrineType extends UuidType
{
    public const NAME = 'tier_member_history_id';

    public function convertToPHPValue($value, AbstractPlatform $platform): ?TierMemberHistoryId
    {
        if (empty($value)) {
            return null;
        }

        if ($value instanceof TierMemberHistoryId) {
            return $value;
        }

        if (!is_string($value)) {
            return null;
        }

        return new TierMemberHistoryId($value);
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value instanceof TierMemberHistoryId) {
            return (string) $value;
        }

        if (!empty($value) && is_string($value)) {
            return $value;
        }

        return null;
    }

    public function getName(): string
    {
        return self::NAME;
    }

    public function requiresSQLCommentHint(AbstractPlatform $platform): bool
    {
        return true;
    }
}
