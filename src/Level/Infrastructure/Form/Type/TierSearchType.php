<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Infrastructure\Form\Type;

use OpenLoyalty\Core\Domain\Search\Criteria\OrderByCriteria;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\BooleanCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\NumericCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\TextCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\TranslatableCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\UuidCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class TierSearchType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('levelId', UuidCriteriaType::class);
        $builder->add('conditionValue', NumericCriteriaType::class);
        $builder->add('name', TranslatableCriteriaType::class, [
            'locale' => $options['locale'],
        ]);
        $builder->add('description', TranslatableCriteriaType::class, [
            'locale' => $options['locale'],
        ]);
        $builder->add('active', BooleanCriteriaType::class);
        $builder->add('reward:name', TextCriteriaType::class);
        $builder->add('reward:code', TextCriteriaType::class);
        $builder->add('reward:value', NumericCriteriaType::class);
    }

    public function getParent(): string
    {
        return SearchType::class;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'default_order_by' => 'sortOrder',
            'default_sort_direction' => OrderByCriteria::ASC,
        ]);
    }
}
