<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Application\Command\TierSet;

use OpenLoyalty\Core\Domain\Id\TierSetId;
use OpenLoyalty\Core\Domain\Message\CommandInterface;
use OpenLoyalty\Level\Domain\TierSetTranslation;
use OpenLoyalty\Level\Domain\ValueObject\Downgrade;

final class UpdateTierSet implements CommandInterface
{
    public function __construct(
        private TierSetId $tierSetId,
        private bool $active,
        private array $conditions,
        private array $labels,
        /**
         * @var array<string, ?TierSetTranslation>
         */
        private array $translations,
        private Downgrade $downgrade
    ) {
    }

    public function getTierSetId(): TierSetId
    {
        return $this->tierSetId;
    }

    public function getConditions(): array
    {
        return $this->conditions;
    }

    public function getLabels(): array
    {
        return $this->labels;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function getTranslations(): array
    {
        return $this->translations;
    }

    public function getDowngrade(): Downgrade
    {
        return $this->downgrade;
    }
}
