<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Application\CommandHandler;

use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Id\Level\RewardId;
use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\Exception\VersionValueIsInvalid;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventBusInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;
use OpenLoyalty\DataAnalytics\Domain\Tier\Event\TierWasCreated;
use OpenLoyalty\Level\Application\Command\CreateLevel;
use OpenLoyalty\Level\Domain\Exception\NonUniqueLevelConditionValueException;
use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Domain\LevelRepository;
use OpenLoyalty\Level\Domain\LevelTranslation;
use OpenLoyalty\Level\Domain\Reward;

class CreateLevelCommandHandler implements CommandHandlerInterface
{
    public function __construct(
        private readonly LevelRepository $levelRepository,
        private readonly StoreRepository $storeRepository,
        private readonly UuidGeneratorInterface $uuidGenerator,
        private readonly AnalyticsEventBusInterface $analyticsEventBus
    ) {
    }

    /**
     * @throws AssertionFailedException
     * @throws NonUniqueLevelConditionValueException
     * @throws VersionValueIsInvalid
     */
    public function __invoke(CreateLevel $command): void
    {
        $data = $command->getLevelData();
        if ($this->levelRepository->findByConditionValue((float) $data['conditionValue'], $command->getStoreId())) {
            throw new NonUniqueLevelConditionValueException();
        }

        $store = $this->storeRepository->byId($command->getStoreId());
        $level = new Level($command->getLevelId(), $store, (float) $data['conditionValue']);
        $this->assignLevelTranslations($level, $data);

        if (isset($data['rewards']) && is_array($data['rewards'])) {
            foreach ($data['rewards'] as $reward) {
                $newReward = new Reward(
                    new RewardId($reward['id']),
                    $level,
                    $reward['name'],
                    $reward['value'],
                    $reward['code']
                );
                $newReward->setActive($reward['active']);
                $newReward->setStartAt($reward['startAt'] ?? null);
                $newReward->setEndAt($reward['endAt'] ?? null);

                $level->addReward($newReward);
            }
        }

        $this->levelRepository->save($level);

        $this->analyticsEventBus->dispatch(
            new TierWasCreated(
                new AnalyticsEventId($this->uuidGenerator->generate()),
                new Version(1),
                $level->getLevelId(),
                $level->getName() ?? '',
                $level->getCreatedAt(),
                $level->getStore()->getStoreId()
            )
        );
    }

    protected function assignLevelTranslations(Level $level, array $data): void
    {
        if (!array_key_exists('translations', $data)) {
            return;
        }

        foreach ($data['translations'] as $locale => $transData) {
            if (array_key_exists('name', $transData)) {
                $level->translate($locale, false)->setName($transData['name']);
            }
            if (array_key_exists('description', $transData)) {
                $level->translate($locale, false)->setDescription($transData['description']);
            }
        }
        /** @var LevelTranslation $translation */
        foreach ($level->getTranslations() as $translation) {
            if (!isset($data['translations'][$translation->getLocale()])) {
                $level->removeTranslation($translation);
            }
        }
    }
}
