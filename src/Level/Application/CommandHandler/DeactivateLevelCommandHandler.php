<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Application\CommandHandler;

use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Level\Application\Command\DeactivateLevel;
use OpenLoyalty\Level\Domain\CampaignFacadeInterface;
use OpenLoyalty\Level\Domain\Exception\UsedTierCannotBeDeactivated;
use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Domain\LevelRepository;

final class DeactivateLevelCommandHandler implements CommandHandlerInterface
{
    public function __construct(
        private readonly LevelRepository $levelRepository,
        private readonly CampaignFacadeInterface $campaignFacade
    ) {
    }

    /**
     * @throws UsedTierCannotBeDeactivated
     */
    public function __invoke(DeactivateLevel $command): void
    {
        /** @var Level $level */
        $level = $this->levelRepository->byId($command->getLevelId());
        if (null === $level) {
            return;
        }

        if (true === $this->campaignFacade->isTierUsedInCampaigns($command->getLevelId())) {
            throw new UsedTierCannotBeDeactivated();
        }

        $level->deactivate();

        $this->levelRepository->save($level);
    }
}
