<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Application\Factory;

use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Level\Application\DTO\Reward as RewardDTO;
use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Domain\TierSet;

final readonly class TierFactory implements TierFactoryInterface
{
    public function __construct(
        private RewardFactoryInterface $rewardFactory
    ) {
    }

    public function create(
        LevelId $levelId,
        Store $store,
        array $translations,
        bool $isActive,
        TierSet $tierSet,
        array $conditions,
        array $rewards
    ): Level {
        $tier = new Level(
            $levelId,
            $store,
            null
        );

        $tier->assignTranslations($translations);
        $tier->setActive($isActive);
        $tier->setTierSet($tierSet);
        $tier->setConditions($conditions);

        if (!empty($rewards)) {
            /**
             * @var RewardDTO $reward
             */
            foreach ($rewards as $reward) {
                $newReward = $this->rewardFactory->createFromDto($reward, $tier);

                $tier->addReward($newReward);
            }
        }

        return $tier;
    }
}
