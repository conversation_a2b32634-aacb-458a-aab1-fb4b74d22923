<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Application\Factory;

use Assert\AssertionFailedException;
use OpenLoyalty\Level\Application\DTO\Reward as RewardDTO;
use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Domain\Reward;

final class RewardFactory implements RewardFactoryInterface
{
    /**
     * @throws AssertionFailedException
     */
    public function createFromDto(RewardDTO $reward, Level $tier): Reward
    {
        return new Reward(
            $reward->getRewardId(),
            $tier,
            $reward->getName(),
            $reward->getValue(),
            $reward->getCode(),
            $reward->getLabels(),
            $reward->isActive(),
            $reward->getStartAt(),
            $reward->getEndAt()
        );
    }
}
