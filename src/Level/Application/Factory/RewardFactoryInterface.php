<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Application\Factory;

use OpenLoyalty\Level\Application\DTO\Reward as RewardDTO;
use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Domain\Reward;

interface RewardFactoryInterface
{
    public function createFromDto(RewardDTO $reward, Level $tier): Reward;
}
