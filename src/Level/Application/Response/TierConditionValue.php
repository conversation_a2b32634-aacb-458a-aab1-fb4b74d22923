<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Application\Response;

use OpenLoyalty\Level\Domain\ValueObject\ConditionId;

final readonly class TierConditionValue
{
    public function __construct(
        public ConditionId $conditionId,
        public string $attribute,
        public int|float $value
    ) {
    }
}
