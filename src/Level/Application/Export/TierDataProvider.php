<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Application\Export;

use DateTimeImmutable;
use OpenLoyalty\Core\Application\Export\AbstractPaginatableDataProvider;
use OpenLoyalty\Core\Application\Export\DateRangeCriteriaBuilder;
use OpenLoyalty\Core\Application\Export\ExportDataMapperInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\OrderByCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Level\Application\UseCase\GetListUseCase;
use OpenLoyalty\Level\Domain\LevelRepository;

final class TierDataProvider extends AbstractPaginatableDataProvider
{
    public function __construct(
        private readonly GetListUseCase $useCase,
        private readonly DateRangeCriteriaBuilder $criteriaBuilder,
        private readonly SearchableResponderInterface $searchableResponder,
        private readonly ExportDataMapperInterface $mapper,
        LevelRepository $tierRepository
    ) {
        parent::__construct($tierRepository);
    }

    public function getItemsCountByDate(?DateTimeImmutable $date): int
    {
        return $this->searchableResponder->countByCriteria(
            $this->repository,
            $this->criteriaBuilder->getDateRangeCriteria($date)
        );
    }

    public function getItemsByCriteria(CriteriaCollection $criteria): array
    {
        $criteria->add(new OrderByCriteria('levelId', OrderByCriteria::ASC));

        $tierEntities = $this->useCase->execute($criteria);

        return $this->mapper->mapList($tierEntities->getItems());
    }
}
