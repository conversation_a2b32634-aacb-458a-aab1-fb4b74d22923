<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Application\UseCase\TierSet;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TierSetId;
use OpenLoyalty\Level\Application\DTO\MemberTierSet;
use OpenLoyalty\Level\Application\Job\EvaluateMemberTierDispatcher;
use OpenLoyalty\Level\Domain\CustomerFacadeInterface;
use OpenLoyalty\Level\Domain\Enum\DispositionTierChangeType;
use OpenLoyalty\Level\Domain\Progress\TierRefreshTriggerType;
use OpenLoyalty\Level\Domain\TierSetMemberProgressConditionRepositoryInterface;
use OpenLoyalty\Level\Domain\TierSetRepositoryInterface;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;

class RefreshTiersWithTimeConditionUseCase
{
    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly CustomerFacadeInterface $customerFacade,
        private readonly EvaluateMemberTierDispatcher $memberTierDispatcher,
        private readonly TierSetMemberProgressConditionRepositoryInterface $memberProgressConditionRepository,
        private readonly TierSetRepositoryInterface $tierSetRepository
    ) {
    }

    public function execute(
        StoreId $storeId,
        DateTimeImmutable $currentTime,
        bool $isForce,
        ?CustomerId $customerId,
        ?TierSetId $tierSetId,
        DispositionTierChangeType $dispositionTierChangeType
    ): void {
        $customer = null;
        if (null !== $customerId) {
            $customer = $this->customerFacade->getActiveCustomer($customerId);
            if (null === $customer) {
                return;
            }
        }

        $combineMemberTierSet = $this->combineMembersForTierSets($isForce, $tierSetId, $storeId, $customer, $currentTime);

        $this->memberTierDispatcher->dispatch(
            $storeId,
            $dispositionTierChangeType,
            $combineMemberTierSet,
            $currentTime,
            true === $isForce ? TierRefreshTriggerType::All : TierRefreshTriggerType::Time
        );
    }

    private function combineMembersForTierSets(bool $isForce, ?TierSetId $tierSetId, StoreId $storeId, ?Customer $customer, DateTimeImmutable $currentTime): \Generator
    {
        if (null !== $customer) {
            return $this->getForMember($customer, $storeId, $tierSetId);
        }

        if (true === $isForce) {
            return $this->getForForce($storeId, $tierSetId);
        }

        return $this->getForRequireRecreation($storeId, $currentTime, $tierSetId);
    }

    private function getForForce(StoreId $storeId, ?TierSetId $tierSetId): \Generator
    {
        $tierSets = null === $tierSetId ? $this->tierSetRepository->findActiveByStore($storeId) :
            [$this->tierSetRepository->byId($tierSetId)];
        $memberIds = $this->customerRepository->getAllActiveIdsIterable($storeId);

        foreach ($memberIds as $memberId) {
            foreach ($tierSets as $tierSet) {
                yield new MemberTierSet($memberId, $tierSet->getTierSetId());
            }
        }
    }

    private function getForMember(Customer $customer, StoreId $storeId, ?TierSetId $tierSetId): \Generator
    {
        $tierSets = null === $tierSetId ? $this->tierSetRepository->findActiveByStore($storeId) :
            [$this->tierSetRepository->byId($tierSetId)];

        foreach ($tierSets as $tierSet) {
            yield new MemberTierSet($customer->getCustomerId(), $tierSet->getTierSetId());
        }
    }

    private function getForRequireRecreation(StoreId $storeId, DateTimeImmutable $currentTime, ?TierSetId $tierSetId): \Generator
    {
        $result = $this->memberProgressConditionRepository->getAllProgressRequireRecreation(
            $storeId,
            $currentTime,
            $tierSetId
        );

        foreach ($result as $item) {
            yield new MemberTierSet($item['memberId'], $item['tierSetId']);
        }
    }
}
