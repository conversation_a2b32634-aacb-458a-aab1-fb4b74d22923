<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Level\Ui\Rest\Controller;

use Assert\AssertionFailedException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Domain\Provider\TierModeProviderInterface;
use OpenLoyalty\Level\Application\UseCase\CreateLevelUseCase;
use OpenLoyalty\Level\Domain\Exception\NonUniqueLevelConditionValueException;
use OpenLoyalty\Level\Infrastructure\Form\Type\LevelFormType;
use OpenLoyalty\Level\Infrastructure\Model\Level as BundleLevel;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class Post extends AbstractFOSRestController
{
    public function __construct(
        private readonly FormFactoryInterface $formFactory,
        private readonly CreateLevelUseCase $useCase,
        private readonly StoreContextProviderInterface $storeContextProvider,
        private readonly TierModeProviderInterface $tierModeProvider,
    ) {
    }

    /**
     * @Route(methods={"POST"}, name="oloy.tier.create", path="/{storeCode}/tier/create")
     *
     * @Security("is_granted('CREATE_LEVEL')")
     *
     * @Operation(
     *     tags={"Tier"},
     *     summary="Add a tier",
     *     operationId="tierPost",
     *     deprecated=true,
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\RequestBody(
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(property="level",
     *                      allOf={
     *                          @OA\Property(ref=@Model(type=LevelFormType::class)),
     *                          @OA\Property(properties={
     *                              @OA\Property(property="translations", type="object", ref="#/components/schemas/Translations"),
     *                          })
     *                      }
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Return created tier ID.",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="levelId",
     *                     type="string",
     *                     description="Created tier identity",
     *                     example="00000000-0000-0000-0000-000000000000"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Returned when form contains errors",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     )
     * )
     *
     * @throws AssertionFailedException
     * @throws NonUniqueLevelConditionValueException
     */
    public function __invoke(Request $request): View
    {
        if (!$this->tierModeProvider->isTraditionalMode($this->storeContextProvider->getStore()->getStoreId())) {
            return $this->view(['message' => 'This endpoint is deprecated for custom tier mode'], Response::HTTP_BAD_REQUEST);
        }

        $form = $this->formFactory->createNamed('level', LevelFormType::class);
        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        /** @var BundleLevel $level */
        $level = $form->getData();
        $store = $this->storeContextProvider->getStore();
        $level->setStoreCode($store->getCode());

        $levelId = $this->useCase->execute($level, $store);

        return $this->view($levelId);
    }
}
