<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Infrastructure\Sender;

use OpenLoyalty\Messaging\Domain\Model\EmailMessageInterface;
use OpenLoyalty\Messaging\Domain\Model\MessageInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;

class MailerMessageSender implements MessageSenderInterface
{
    public function __construct(
        private MailerInterface $mailer
    ) {
    }

    public function supports(MessageInterface $message): bool
    {
        return $message instanceof EmailMessageInterface;
    }

    public function send(MessageInterface $message): void
    {
        if (!$message instanceof EmailMessageInterface) {
            throw new \InvalidArgumentException();
        }

        $msg = new Email();
        $msg->subject($message->getSubject())
             ->from(new Address($message->getSenderEmail(), $message->getSenderName()))
             ->to(new Address($message->getRecipientEmail(), $message->getRecipientName()))
             ->html($message->getContent())
             ->text($message->getPlainContent() ?: strip_tags($message->getContent()));

        $this->mailer->send($msg);
    }
}
