imports:
    - { resource: services/*.yml }

services:

    _defaults:
        autowire: true
        autoconfigure: true

    _instanceof:
        OpenLoyalty\Messaging\Infrastructure\Sender\MessageSenderInterface:
            tags: ['oloy.messaging.sender']
        OpenLoyalty\Core\Domain\Message\EventHandlerInterface:
            tags: [ { name: messenger.message_handler, bus: event.bus } ]

    OpenLoyalty\Messaging\Infrastructure\DataFixtures\ORM\:
        resource: '../../DataFixtures/ORM/*'

    OpenLoyalty\Messaging\Infrastructure\Form\:
        resource: '../../Form'

    OpenLoyalty\Messaging\Infrastructure\Validator\:
        resource: '../../Validator'

    OpenLoyalty\Messaging\Infrastructure\Sender\MessageSenderDispatcher: ~
    OpenLoyalty\Messaging\Domain\Sender\MessageSenderDispatcherInterface: '@OpenLoyalty\Messaging\Infrastructure\Sender\MessageSenderDispatcher'

    OpenLoyalty\Messaging\Infrastructure\Sender\MailerMessageSender: ~

    OpenLoyalty\Messaging\Application\JobHandler\MessageHandler: ~

    OpenLoyalty\Messaging\Infrastructure\Webhook\WebhookEventsProvider:
        arguments: [ !tagged_iterator { tag: 'oloy.webhook.data.provider' } ]
        public: true
    OpenLoyalty\Messaging\Domain\Webhook\WebhookEventsProviderInterface: '@OpenLoyalty\Messaging\Infrastructure\Webhook\WebhookEventsProvider'

    OpenLoyalty\Messaging\Infrastructure\Webhook\WebhookDataProviderRegistry:
        arguments: [ !tagged_iterator { tag: 'oloy.webhook.data.provider' } ]

    OpenLoyalty\Messaging\Domain\Webhook\WebhookDataProviderRegistryInterface: '@OpenLoyalty\Messaging\Infrastructure\Webhook\WebhookDataProviderRegistry'

    OpenLoyalty\Messaging\Application\UseCase\:
        resource: '../../../Application/UseCase'

    OpenLoyalty\Messaging\Application\DataMapper\:
        resource: '../../../Application/DataMapper'

    OpenLoyalty\Messaging\Domain\Factory\WebhookSubscriptionFactory: ~
    OpenLoyalty\Messaging\Domain\Factory\WebhookSubscriptionFactoryInterface: '@OpenLoyalty\Messaging\Domain\Factory\WebhookSubscriptionFactory'

    OpenLoyalty\Messaging\Infrastructure\Persistence\Doctrine\Repository\WebhookSubscriptionRepository:
        public: true
    OpenLoyalty\Messaging\Infrastructure\Persistence\Doctrine\Repository\CachedWebhookSubscriptionRepository:
    OpenLoyalty\Messaging\Domain\WebhookSubscriptionRepositoryInterface: '@OpenLoyalty\Messaging\Infrastructure\Persistence\Doctrine\Repository\WebhookSubscriptionRepository'
    OpenLoyalty\Messaging\Domain\CachedWebhookSubscriptionRepositoryInterface: '@OpenLoyalty\Messaging\Infrastructure\Persistence\Doctrine\Repository\CachedWebhookSubscriptionRepository'

    OpenLoyalty\Messaging\Infrastructure\Persistence\Doctrine\Repository\WebhookPayloadRepository:
    OpenLoyalty\Messaging\Domain\WebhookPayloadRepositoryInterface: '@OpenLoyalty\Messaging\Infrastructure\Persistence\Doctrine\Repository\WebhookPayloadRepository'

    OpenLoyalty\Messaging\Infrastructure\Webhook\WebhookDispatcher: ~
    OpenLoyalty\Messaging\Domain\Webhook\WebhookDispatcherInterface: '@OpenLoyalty\Messaging\Infrastructure\Webhook\WebhookDispatcher'

    OpenLoyalty\Messaging\Application\JobHandler\DispatchWebhookEventJobHandler: ~
    OpenLoyalty\Messaging\Application\JobHandler\DispatchWebhookJobHandler: ~
    OpenLoyalty\Messaging\Application\JobHandler\DispatchWebhookPayloadJobHandler: ~

    OpenLoyalty\Messaging\Infrastructure\Webhook\Client\Request\Header\RequestHeadersResolver: ~
    OpenLoyalty\Messaging\Infrastructure\Webhook\Client\Request\Header\RequestHeadersResolverInterface: '@OpenLoyalty\Messaging\Infrastructure\Webhook\Client\Request\Header\RequestHeadersResolver'

    OpenLoyalty\Messaging\Infrastructure\Webhook\Client\Request\Body\RequestBodyResolver: ~
    OpenLoyalty\Messaging\Infrastructure\Webhook\Client\Request\Body\RequestBodyResolverInterface: '@OpenLoyalty\Messaging\Infrastructure\Webhook\Client\Request\Body\RequestBodyResolver'

    OpenLoyalty\Messaging\Infrastructure\Webhook\Client\DefaultWebhookClient:
        public: true
        arguments:
            $timeout: '%webhook_timeout%'

    OpenLoyalty\Messaging\Infrastructure\Webhook\Client\WebhookClientInterface: '@OpenLoyalty\Messaging\Infrastructure\Webhook\Client\DefaultWebhookClient'

    OpenLoyalty\Messaging\Domain\Webhook\Event\Listener\WebhookEventListener: ~

    OpenLoyalty\Messaging\Infrastructure\Webhook\WebhookEventNameResolver: ~
    OpenLoyalty\Messaging\Domain\Webhook\WebhookEventNameResolverInterface: '@OpenLoyalty\Messaging\Infrastructure\Webhook\WebhookEventNameResolver'

    OpenLoyalty\Messaging\Infrastructure\WebhookFacade: ~
    OpenLoyalty\Messaging\Domain\WebhookFacadeInterface: '@OpenLoyalty\Messaging\Infrastructure\WebhookFacade'

    messenger.middleware.command_webhook:
        class: OpenLoyalty\Messaging\Infrastructure\Messenger\CommandWebhookMiddleware

    OpenLoyalty\Messaging\Domain\Provider\EnvironmentVariableProviderInterface:
        class: OpenLoyalty\Messaging\Domain\Provider\EnvironmentVariableProvider
        arguments:
            $globalActiveWebhookSubscriptionLimit: '%global_active_webhook_subscription_limit%'