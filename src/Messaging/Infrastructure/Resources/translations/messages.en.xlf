<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="en" target-language="en" datatype="plaintext" original="file.ext">
        <body>
            <trans-unit id="oloy.account.available_points_amount_changed">
                <source>oloy.account.available_points_amount_changed</source>
                <target>Earned points</target>
            </trans-unit>
            <trans-unit id="oloy.customer.level_changed_automatically">
                <source>oloy.customer.level_changed_automatically</source>
                <target>Gained new level</target>
            </trans-unit>
            <trans-unit id="oloy.reward.customer_bought_reward">
                <source>oloy.reward.customer_bought_reward</source>
                <target>Issued Reward Reward</target>
            </trans-unit>
            <trans-unit id="oloy.customer.registered_without_activation">
                <source>oloy.customer.registered_without_activation</source>
                <target>Customer registered and awaits activation</target>
            </trans-unit>
            <trans-unit id="oloy.reward.coupon_was_redeemed">
                <source>oloy.reward.coupon_was_redeemed</source>
                <target>Customer redeemed reward reward</target>
            </trans-unit>
            <trans-unit id="oloy.user.requested_password_reset">
                <source>oloy.user.requested_password_reset</source>
                <target>User requested password reset</target>
            </trans-unit>
            <trans-unit id="oloy.customer.phone_number_was_changed">
                <source>oloy.customer.phone_number_was_changed</source>
                <target>Phone number changed</target>
            </trans-unit>
            <trans-unit id="oloy.messaging.cannot_deactivate_current_activation_message">
                <source>oloy.messaging.cannot_deactivate_current_activation_message</source>
                <target>Cannot deactivate activation message in user identification/activation channel</target>
            </trans-unit>
            <trans-unit id="oloy.customer.email_was_changed">
                <source>oloy.customer.email_was_changed</source>
                <target>Email changed</target>
            </trans-unit>
            <trans-unit id="oloy.messaging.insufficient_configuration">
                <source>oloy.messaging.insufficient_configuration</source>
                <target>The configuration of the channel is insufficient to activate this message template. Did you fill in the credentials?</target>
            </trans-unit>
            <trans-unit id="oloy.messaging.already_exists">
                <source>oloy.messaging.already_exists</source>
                <target>Message for this combination of channel, target and event already exists.</target>
            </trans-unit>
            <trans-unit id="oloy.reward.has_become_available">
                <source>oloy.reward.has_become_available</source>
                <target>Reward has become available</target>
            </trans-unit>
            <trans-unit id="oloy.webhook.subscription.event_name_and_url_combination_must_be_unique">
                <source>oloy.webhook.subscription.event_name_and_url_combination_must_be_unique</source>
                <target>Event name and url combination must be unique</target>
            </trans-unit>
            <trans-unit id="oloy.webhook.subscription.event_name_is_invalid">
                <source>oloy.webhook.subscription.event_name_is_invalid</source>
                <target>Event name is invalid</target>
            </trans-unit>
            <trans-unit id="oloy.webhook.subscription.header_name_is_invalid">
                <source>oloy.webhook.subscription.header_name_is_invalid</source>
                <target>The header name is invalid. Allowed special characters are: ~ ` ! # $ % ^ &amp; * - _ + ' . |</target>
            </trans-unit>
            <trans-unit id="oloy.webhook.subscription.header_value_is_invalid">
                <source>oloy.webhook.subscription.header_value_is_invalid</source>
                <target>Header value is invalid.</target>
            </trans-unit>
            <trans-unit id="oloy.webhook.subscription.global_limit_reached">
                <source>oloy.webhook.subscription.global_limit_reached</source>
                <target>You have reached the global limit of {{ limit }} webhook subscriptions. To add a new webhook, please delete an existing one or contact your Customer Success Manager or the support <NAME_EMAIL>.</target>
            </trans-unit>
        </body>
    </file>
</xliff>
