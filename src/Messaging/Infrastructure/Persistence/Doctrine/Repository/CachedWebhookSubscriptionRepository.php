<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Infrastructure\Persistence\Doctrine\Repository;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\WebhookSubscriptionId;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Messaging\Domain\CachedWebhookSubscriptionRepositoryInterface;
use OpenLoyalty\Messaging\Domain\Entity\WebhookSubscription;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\Cache\TagAwareCacheInterface;

final class CachedWebhookSubscriptionRepository implements CachedWebhookSubscriptionRepositoryInterface
{
    private const CACHE_TAG = 'webhook_subscriptions';

    public function __construct(
        private readonly WebhookSubscriptionRepository $repository,
        private readonly TagAwareCacheInterface $redisAdapter,
    ) {
    }

    public function clearCache(): void
    {
        $this->redisAdapter->invalidateTags([self::CACHE_TAG]);
    }

    public function save(WebhookSubscription $webhookPayload): void
    {
        $this->repository->save($webhookPayload);
        $this->clearCache();
    }

    public function remove(WebhookSubscription $webhookSubscription): void
    {
        $this->repository->remove($webhookSubscription);
        $this->clearCache();
    }

    public function byId(WebhookSubscriptionId $webhookSubscriptionId): ?WebhookSubscription
    {
        return $this->repository->byId($webhookSubscriptionId);
    }

    /**
     * @return array<WebhookSubscription>
     */
    public function byEventName(string $evenName, StoreId $storeId): array
    {
        $cacheKey = sprintf(__FUNCTION__.'_%s_%s', (string) $storeId, $evenName);

        return $this->redisAdapter->get(
            $cacheKey,
            function (ItemInterface $item) use ($storeId, $evenName) {
                $item->tag([self::CACHE_TAG]);

                return $this->repository->byEventName($evenName, $storeId);
            });
    }

    /**
     * {@inheritdoc}
     */
    public function countByEventNames(array $eventNames, StoreId $storeId): int
    {
        $cacheKey = sprintf(__FUNCTION__.'_%s_%s', (string) $storeId, md5(implode(',', $eventNames)));

        return $this->redisAdapter->get(
            $cacheKey,
            function (ItemInterface $item) use ($storeId, $eventNames) {
            $item->tag([self::CACHE_TAG]);

            return $this->repository->countByEventNames($eventNames, $storeId);
        });
    }

    public function byEventNameAndUrl(string $evenName, string $url, StoreId $storeId): ?WebhookSubscription
    {
        $cacheKey = sprintf(__FUNCTION__.'_%s_%s_%s', (string) $storeId, $evenName, md5($url));

        return $this->redisAdapter->get(
            $cacheKey,
            function (ItemInterface $item) use ($storeId, $evenName, $url) {
                $item->tag([self::CACHE_TAG]);

                return $this->repository->byEventNameAndUrl($evenName, $url, $storeId);
            });
    }

    public function countAllWebhooksInAllStores(?WebhookSubscriptionId $excludedId = null): int
    {
        $cacheKey = sprintf(__FUNCTION__.'_%s', (string) $excludedId);

        return $this->redisAdapter->get(
            $cacheKey,
            function (ItemInterface $item) use ($excludedId) {
                $item->tag([self::CACHE_TAG]);

                return $this->repository->countAllWebhooksInAllStores($excludedId);
            });
    }

    /**
     * @return array<WebhookSubscription>
     */
    public function findByCriteria(CriteriaCollectionInterface $criteria, bool $useQuickResultCache = false): array
    {
        return $this->repository->findByCriteria($criteria, $useQuickResultCache);
    }

    public function findOneByCriteria(CriteriaCollectionInterface $criteria, bool $useQuickResultCache = false): ?object
    {
        return $this->repository->findOneByCriteria($criteria, $useQuickResultCache);
    }

    public function countByCriteria(CriteriaCollectionInterface $criteria, bool $estimated = false): int
    {
        return $this->repository->countByCriteria($criteria, $estimated);
    }
}
