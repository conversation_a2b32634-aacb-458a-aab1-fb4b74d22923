<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Messaging\Infrastructure\Webhook\Client\Request\Header;

final class RequestHeadersResolver implements RequestHeadersResolverInterface
{
    private const USER_AGENT = 'OpenLoyalty';
    private const CONTENT_TYPE = 'application/json';

    /**
     * {@inheritdoc}
     */
    public function prepareHeaders(array $headers): array
    {
        $requestHeaders = [
            'Content-Type' => self::CONTENT_TYPE,
            'User-Agent' => self::USER_AGENT,
        ];

        foreach ($headers as $header) {
            $requestHeaders[$header->getHeaderName()] = $header->getHeaderValue();
        }

        return $requestHeaders;
    }
}
