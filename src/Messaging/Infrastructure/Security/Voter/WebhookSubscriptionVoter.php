<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Infrastructure\Security\Voter;

use OpenLoyalty\Messaging\Domain\Entity\WebhookSubscription;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\PermissionAccess;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * @extends Voter<string, mixed>
 */
class WebhookSubscriptionVoter extends Voter
{
    public const PERMISSION_RESOURCE = 'WEBHOOK_SUBSCRIPTION';

    public const CREATE = 'CREATE_WEBHOOK_SUBSCRIPTION';
    public const UPDATE = 'UPDATE_WEBHOOK_SUBSCRIPTION';
    public const LIST = 'LIST_WEBHOOK_SUBSCRIPTIONS';
    public const LIST_EVENTS = 'LIST_WEBHOOK_EVENTS';
    public const GET = 'GET_WEBHOOK_SUBSCRIPTION';
    public const DELETE = 'DELETE_WEBHOOK_SUBSCRIPTION';

    public function __construct(private UserPermissionCheckerInterface $permissionChecker)
    {
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return ($subject instanceof WebhookSubscription && in_array($attribute, [self::GET, self::UPDATE, self::DELETE])) ||
               (null === $subject && in_array($attribute, [self::CREATE, self::LIST, self::LIST_EVENTS]));
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        /** @var User $user */
        $user = $token->getUser();

        if (!$user instanceof User) {
            return false;
        }

        $viewAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionInCurrentStore(
                $user,
                self::PERMISSION_RESOURCE,
                [PermissionAccess::VIEW]
            );

        $fullAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionInCurrentStore(
            $user,
            self::PERMISSION_RESOURCE,
            [PermissionAccess::VIEW, PermissionAccess::MODIFY]
        );

        return match ($attribute) {
            self::CREATE, self::UPDATE, self::DELETE => $fullAdmin,
            self::LIST, self::LIST_EVENTS, self::GET => $viewAdmin,
            default => false,
        };
    }
}
