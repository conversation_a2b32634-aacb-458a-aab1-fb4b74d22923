<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Domain\Webhook\Event;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventInterface;
use OpenLoyalty\Core\Domain\Webhook\Event\WebhookEventInterface;
use OpenLoyalty\Messaging\Domain\NotifiableCommandInterface;

final readonly class CommandWebhookEvent implements EventInterface, WebhookEventInterface
{
    public function __construct(
        private StoreId $storeId,
        private NotifiableCommandInterface $command
    ) {
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getCommand(): NotifiableCommandInterface
    {
        return $this->command;
    }
}
