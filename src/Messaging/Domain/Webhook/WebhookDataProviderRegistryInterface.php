<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Domain\Webhook;

use OpenLoyalty\Core\Domain\Webhook\DataProvider\WebhookDataProviderInterface;

interface WebhookDataProviderRegistryInterface
{
    /**
     * @return array<WebhookDataProviderInterface>
     */
    public function getProviders(string $eventClassName): array;
}
