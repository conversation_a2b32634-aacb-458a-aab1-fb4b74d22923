<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Domain;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Search\SearchableInterface;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use OpenLoyalty\Messaging\Domain\DTO\WebhookPayloadData;
use OpenLoyalty\Messaging\Domain\Entity\WebhookPayload;
use OpenLoyalty\Messaging\Domain\Enum\WebhookPayloadType;

interface WebhookPayloadRepositoryInterface extends SearchableInterface
{
    public function save(WebhookPayload $webhookPayload): void;

    public function remove(WebhookPayload $webhookPayload): void;

    /**
     * @return iterable<WebhookPayloadData>
     */
    public function byTrigger(
        string $triggerId,
        Trigger $triggerType,
        WebhookPayloadType $payloadType,
        StoreId $storeId
    ): iterable;
}
