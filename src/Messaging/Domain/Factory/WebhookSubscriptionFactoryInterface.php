<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Domain\Factory;

use OpenLoyalty\Core\Domain\Id\WebhookSubscriptionId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Messaging\Domain\Entity\WebhookSubscription;

interface WebhookSubscriptionFactoryInterface
{
    public function create(
        WebhookSubscriptionId $webhookSubscriptionId,
        Store $store,
        string $eventName,
        string $url,
        ?string $headerName,
        ?string $headerValue,
        array $headers
    ): WebhookSubscription;
}
