<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Domain\DTO;

class Recipient
{
    private ?string $email;
    private ?string $phone;

    public function __construct(
        ?string $email,
        ?string $phone
    ) {
        $this->email = $email;
        $this->phone = $phone;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }
}
