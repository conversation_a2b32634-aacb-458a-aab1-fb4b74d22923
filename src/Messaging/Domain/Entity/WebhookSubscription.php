<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Domain\Entity;

use Assert\Assertion;
use Assert\AssertionFailedException;
use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\WebhookSubscriptionId;
use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Messaging\Domain\ValueObject\WebhookHeader;

class WebhookSubscription implements TimestampableInterface, BlameableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    private function __construct(
        private readonly WebhookSubscriptionId $webhookSubscriptionId,
        private readonly Store $store,
        private readonly string $eventName,
        private string $url,
        private ?string $headerName,
        private ?string $headerValue,
        DateTimeImmutable $createdAt,
        /**
         * @var WebhookHeader[]
         */
        private array $headers = []
    ) {
        $this->createdAt = $createdAt;
    }

    /**
     * @throws AssertionFailedException
     */
    public static function create(
        WebhookSubscriptionId $webhookSubscriptionId,
        Store $store,
        string $eventName,
        string $url,
        ?string $headerName,
        ?string $headerValue,
        array $headers,
    ): self {
        Assertion::url($url);

        return new self(
            $webhookSubscriptionId,
            $store,
            $eventName,
            $url,
            $headerName,
            $headerValue,
            new DateTimeImmutable(),
            $headers
        );
    }

    public function getWebhookSubscriptionId(): WebhookSubscriptionId
    {
        return $this->webhookSubscriptionId;
    }

    public function getStore(): Store
    {
        return $this->store;
    }

    public function getEventName(): string
    {
        return $this->eventName;
    }

    /**
     * @throws AssertionFailedException
     */
    public function changeUrl(string $url): void
    {
        $this->url = $url;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function changeHeaderName(?string $headerName): void
    {
        $this->headerName = $headerName;
    }

    public function getHeaderName(): ?string
    {
        return $this->headerName;
    }

    public function changeHeaderValue(?string $headerValue): void
    {
        $this->headerValue = $headerValue;
    }

    public function getHeaderValue(): ?string
    {
        return $this->headerValue;
    }

    /**
     * @param WebhookHeader[] $headers
     */
    public function replaceHeaders(array $headers): void
    {
        $this->headers = $headers;
    }

    /**
     * @return WebhookHeader[]
     */
    public function getHeaders(): array
    {
        return $this->headers;
    }
}
