<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Domain\Entity;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\WebhookPayloadId;
use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;
use OpenLoyalty\Messaging\Domain\Enum\WebhookPayloadType;

class WebhookPayload implements TimestampableInterface, BlameableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    public function __construct(
        private readonly WebhookPayloadId $webhookPayloadId,
        private readonly StoreId $storeId,
        private readonly string $triggerId,
        private readonly string $triggerType,
        private readonly WebhookPayloadType $payloadType,
        private readonly string $payload
    ) {
    }
}
