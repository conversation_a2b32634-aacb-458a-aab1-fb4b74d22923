<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Domain;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\WebhookSubscriptionId;
use OpenLoyalty\Core\Domain\Search\SearchableInterface;
use OpenLoyalty\Messaging\Domain\Entity\WebhookSubscription;

interface WebhookSubscriptionRepositoryInterface extends SearchableInterface
{
    public function save(WebhookSubscription $webhookPayload): void;

    public function remove(WebhookSubscription $webhookSubscription): void;

    public function byId(WebhookSubscriptionId $webhookSubscriptionId): ?WebhookSubscription;

    /**
     * @return array<WebhookSubscription>
     */
    public function byEventName(string $evenName, StoreId $storeId): array;

    /**
     * @param string[] $eventNames
     */
    public function countByEventNames(array $eventNames, StoreId $storeId): int;

    public function byEventNameAndUrl(string $evenName, string $url, StoreId $storeId): ?WebhookSubscription;

    public function countAllWebhooksInAllStores(?WebhookSubscriptionId $excludedId = null): int;
}
