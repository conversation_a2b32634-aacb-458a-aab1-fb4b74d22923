deptrac:
    paths:
        - ./src
    layers:
        -   name: MessagingDomain
            collectors:
                -   type: directory
                    regex: src/Messaging/Domain/*
        -   name: MessagingApplication
            collectors:
                -   type: directory
                    regex: src/Messaging/Application/*
        -   name: MessagingInfrastructure
            collectors:
                -   type: directory
                    regex: src/Messaging/Infrastructure/*
        -   name: MessagingUi
            collectors:
                -   type: directory
                    regex: src/Messaging/Ui/*
    ruleset:
        MessagingDomain:
            - MessagingApplication # dependency to remove
            - CoreDomain
            - UserDomain
        MessagingApplication:
            - MessagingDomain
            - MessagingInfrastructure # dependency to remove
            - CoreDomain
            - CoreApplication
        MessagingInfrastructure:
            - MessagingDomain
            - MessagingApplication
            - CoreDomain
            - CoreInfrastructure
            - UserInfrastructure
        MessagingUi:
            - MessagingDomain
            - MessagingApplication
            - MessagingInfrastructure
            - CoreDomain
            - CoreInfrastructure
    skip_violations:
        OpenLoyalty\Messaging\Application\Webhook\Job\DispatchWebhookRequest:
            - OpenLoyalty\Transaction\Domain\ValueObject\CustomerBasicData
            - OpenLoyalty\User\Domain\ValueObject\CustomerData
            - OpenLoyalty\Reward\Domain\IssuedReward