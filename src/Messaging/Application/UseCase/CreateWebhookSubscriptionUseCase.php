<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Application\UseCase;

use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Messaging\Application\Command\CreateWebhookSubscription;

final class CreateWebhookSubscriptionUseCase
{
    public function __construct(private CommandBusInterface $commandBus)
    {
    }

    public function execute(CreateWebhookSubscription $command): void
    {
        $this->commandBus->dispatch($command);
    }
}
