<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Application\Command;

use OpenLoyalty\Core\Domain\Id\WebhookSubscriptionId;
use OpenLoyalty\Core\Domain\Message\CommandInterface;
use OpenLoyalty\Messaging\Domain\ValueObject\WebhookHeader;

final class UpdateWebhookSubscription implements CommandInterface
{
    public function __construct(
        private readonly WebhookSubscriptionId $webhookSubscriptionId,
        private readonly string $url,
        private readonly ?string $headerName,
        private readonly ?string $headerValue,
        /**
         * @var WebhookHeader[]
         */
        private readonly array $headers
    ) {
    }

    public function getWebhookSubscriptionId(): WebhookSubscriptionId
    {
        return $this->webhookSubscriptionId;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function getHeaderName(): ?string
    {
        return $this->headerName;
    }

    public function getHeaderValue(): ?string
    {
        return $this->headerValue;
    }

    /**
     * @return WebhookHeader[]
     */
    public function getHeaders(): array
    {
        return $this->headers;
    }
}
