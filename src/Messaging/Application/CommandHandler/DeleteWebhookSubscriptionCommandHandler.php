<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Application\CommandHandler;

use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Messaging\Application\Command\DeleteWebhookSubscription;
use OpenLoyalty\Messaging\Domain\CachedWebhookSubscriptionRepositoryInterface;
use OpenLoyalty\Messaging\Domain\Exception\Webhook\WebhookSubscriptionNotFoundException;

class DeleteWebhookSubscriptionCommandHandler implements CommandHandlerInterface
{
    public function __construct(private CachedWebhookSubscriptionRepositoryInterface $repository)
    {
    }

    /**
     * @throws \OpenLoyalty\Messaging\Domain\Exception\Webhook\WebhookSubscriptionNotFoundException
     */
    public function __invoke(DeleteWebhookSubscription $command): void
    {
        $webhookSubscription = $this->repository->byId($command->getWebhookSubscriptionId());
        if (null === $webhookSubscription) {
            throw new WebhookSubscriptionNotFoundException();
        }

        $this->repository->remove($webhookSubscription);
    }
}
