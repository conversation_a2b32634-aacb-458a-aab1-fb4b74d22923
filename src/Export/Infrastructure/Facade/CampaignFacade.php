<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Export\Infrastructure\Facade;

use Generator;
use OpenLoyalty\Campaign\Domain\CampaignGateInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\StoreCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Export\Application\DataProvider\CampaignCode\DTO\CampaignCode;
use OpenLoyalty\Export\Domain\Facade\CampaignFacadeInterface;

final class CampaignFacade implements CampaignFacadeInterface
{
    public function __construct(
        private readonly CampaignGateInterface $campaignGate
    ) {
    }

    public function getCampaignCodes(
        CriteriaCollectionInterface $criteriaCollection,
        Store $store
    ): Generator {
        $criteriaCollection->add(new StoreCriteria('storeId', $store));
        $campaignCodes = $this->campaignGate->getCampaignCodes($criteriaCollection);

        /**
         * @var \OpenLoyalty\Campaign\Domain\Entity\Code\CampaignCode $campaignCode
         */
        foreach ($campaignCodes as $campaignCode) {
            yield new CampaignCode(
                (string) $campaignCode->getCodeId(),
                $campaignCode->getCode(),
                $campaignCode->getStatus()->getValue(),
                $campaignCode->getCreatedAt(),
                (string) $campaignCode->getUsedByMemberId(),
                $campaignCode->getUsedAt()
            );
        }
    }
}
