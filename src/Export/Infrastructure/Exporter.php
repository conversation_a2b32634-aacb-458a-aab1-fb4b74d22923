<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Export\Infrastructure;

use Doctrine\ORM\Exception\ORMException;
use OpenLoyalty\Core\Domain\Exception\NotFoundException;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Export\Domain\DataProviderFactory;
use OpenLoyalty\Export\Domain\Exception\NotSupportedExportTypeException;
use OpenLoyalty\Export\Domain\ExportRepositoryInterface;
use OpenLoyalty\Export\Domain\FileExporterInterface;
use OpenLoyalty\Export\Domain\ValueObject\ExportId;
use OpenLoyalty\Export\Infrastructure\Form\ExportFormFactory;
use Psr\Log\LoggerInterface;

final readonly class Exporter
{
    public function __construct(
        private ExportRepositoryInterface $exportRepository,
        private ExportFormFactory $exportFormFactory,
        private DataProviderFactory $dataProviderFactory,
        private FileExporterInterface $fileExporter,
        private LoggerInterface $exportLogger
    ) {
    }

    /**
     * @throws NotFoundException
     * @throws ORMException
     */
    public function export(StoreId $storeId, ExportId $exportId): void
    {
        $export = $this->exportRepository->byId($exportId);

        if (!$export) {
            throw new NotFoundException(sprintf('Export with id %d (storeId = %s) doesn\'t exist.', $exportId, $storeId));
        }

        try {
            $form = $this->exportFormFactory->create($export->getType(), [
                'method' => 'POST',
                'with_pagination' => false,
                'csrf_protection' => false,
            ]);
            $form->submit($export->getCriteria());

            if (!$form->isSubmitted() || !$form->isValid()) {
                $errors = [];
                foreach ($form->getErrors() as $error) {
                    $errors[] = $error->getMessage();
                }

                $export->fail(json_encode($errors, JSON_THROW_ON_ERROR));

                return;
            }

            $dataProvider = $this->dataProviderFactory->create($export->getType());
            $file = $export->getType()->getValue().DIRECTORY_SEPARATOR.$export->getExportId().'.csv';

            $exportedRows = $this->fileExporter->export(
                $file,
                $dataProvider->getDataToExport($form->getData(), $export->getStore()),
                $dataProvider->getFileHeader()
            );

            $export->done($file, $exportedRows);
        } catch (ORMException $exception) {
            //for entity managed closed issue
            throw $exception;
        } catch (NotSupportedExportTypeException) {
            $export->error('Export with type: '.$export->getType()->getValue().' is not supported.');
        } catch (\Throwable $exception) {
            $this->exportLogger->error(
                $exception->getMessage(),
                [
                    'exportId' => (string) $export->getExportId(),
                    'exception' => (string) $exception,
                ]
            );

            $export->error('Export failed.');
        }

        $this->exportRepository->save($export);
    }
}
