<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Export\Infrastructure\Form\Member;

use OpenLoyalty\Export\Domain\ValueObject\ExportType;
use OpenLoyalty\Export\Infrastructure\Form\ExportFormInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormInterface;

final class MemberTierForm implements ExportFormInterface
{
    public function __construct(
        private readonly FormFactoryInterface $formFactory,
    ) {
    }

    public function supports(ExportType $type): bool
    {
        return ExportType::MEMBER_TIER === $type->getValue();
    }

    public function createForm(array $options = []): FormInterface
    {
        return $this->formFactory->createNamed(
            ExportType::MEMBER_TIER,
            MemberTierType::class,
            null,
            $options
        );
    }
}
