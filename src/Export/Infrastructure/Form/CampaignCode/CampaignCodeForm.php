<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Export\Infrastructure\Form\CampaignCode;

use OpenLoyalty\Export\Domain\ValueObject\ExportType;
use OpenLoyalty\Export\Infrastructure\Form\ExportFormInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormInterface;

class CampaignCodeForm implements ExportFormInterface
{
    public function __construct(
        private readonly FormFactoryInterface $formFactory,
    ) {
    }

    public function supports(ExportType $type): bool
    {
        return ExportType::CAMPAIGN_CODE === $type->getValue();
    }

    public function createForm(array $options = []): FormInterface
    {
        return $this->formFactory->createNamed(
            ExportType::CAMPAIGN_CODE,
            CampaignCodeType::class,
            null,
            $options
        );
    }
}
