<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Export\Infrastructure\Form;

use OpenLoyalty\Export\Domain\ValueObject\ExportType;
use Symfony\Component\Form\FormInterface;

interface ExportFormInterface
{
    public function supports(ExportType $type): bool;

    public function createForm(array $options = []): FormInterface;
}
