<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Export\Infrastructure\Form\Reward;

use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\DateTimeCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\NumericCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\TextCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\TextListCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\UuidCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchType;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\DateTimeCriteriaRangeValid;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class RewardFulfillmentType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('rewardId', UuidCriteriaType::class, [
            'field_name' => 'reward',
            'required' => false,
            'documentation' => [
                'type' => 'string',
                'format' => 'uuid',
            ],
        ]);
        $builder->add('name', TextCriteriaType::class, [
            'required' => false,
            'documentation' => [
                'type' => 'string',
            ],
        ]);
        $builder->add('rewardType', TextCriteriaType::class, [
            'default_operator' => CriteriaInterface::EQUAL,
            'allowed_operators' => [CriteriaInterface::EQUAL],
            'required' => false,
            'documentation' => [
                'type' => 'string',
            ],
        ]);
        $builder->add('token', TextCriteriaType::class, [
            'required' => false,
            'documentation' => [
                'type' => 'string',
            ],
        ]);
        $builder->add('status', TextListCriteriaType::class, [
            'default_operator' => CriteriaInterface::EQUAL,
            'allowed_operators' => [CriteriaInterface::IN, CriteriaInterface::EQUAL],
            'required' => false,
            'documentation' => [
                'type' => 'array',
                'items' => [
                    'type' => 'string',
                ],
            ],
        ]);
        $builder->add('costInPoints', NumericCriteriaType::class, [
            'required' => false,
            'documentation' => [
                'type' => 'number',
                'format' => 'float',
            ],
        ]);
        $builder->add('redemptionDate', DateTimeCriteriaType::class, [
            'required' => false,
            'documentation' => [
                'type' => 'string',
                'format' => 'date-time',
            ],
            'constraints' => [new DateTimeCriteriaRangeValid()],
        ]);
        $builder->add('customerId', UuidCriteriaType::class, [
            'required' => false,
            'documentation' => [
                'type' => 'string',
                'format' => 'uuid',
            ],
        ]);
        $builder->add('customerData:email', TextCriteriaType::class, [
            'required' => false,
            'documentation' => [
                'type' => 'string',
            ],
        ]);
        $builder->add('customerData:firstName', TextCriteriaType::class, [
            'required' => false,
            'documentation' => [
                'type' => 'string',
            ],
        ]);
        $builder->add('customerData:lastName', TextCriteriaType::class, [
            'required' => false,
            'documentation' => [
                'type' => 'string',
            ],
        ]);
        $builder->add('customerData:phone', TextCriteriaType::class, [
            'required' => false,
            'documentation' => [
                'type' => 'string',
            ],
        ]);
        $builder->add('customerData:loyaltyCardNumber', TextCriteriaType::class, [
            'required' => false,
            'documentation' => [
                'type' => 'string',
            ],
        ]);
    }

    public function getParent(): string
    {
        return SearchType::class;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'store_field' => 'storeId',
            'field_mappings' => [
                'rewardId' => 'reward',
            ],
        ]);
    }
}
