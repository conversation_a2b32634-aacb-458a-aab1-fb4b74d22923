<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Export\Application\DataProvider\Member\DTO;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\ChannelId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\User\Domain\ValueObject\Gender;

final class Member
{
    public function __construct(
        private readonly CustomerId $memberId,
        private readonly StoreId $tenantId,
        private readonly string $currency,
        private readonly string $storeCode,
        private readonly bool $active = false,
        private readonly ?ChannelId $channelId = null,
        private readonly ?string $firstName = null,
        private readonly ?string $lastName = null,
        private readonly ?Gender $gender = null,
        private readonly ?string $email = null,
        private readonly ?string $phone = null,
        private readonly ?DateTimeImmutable $birthDate = null,
        private readonly ?DateTimeImmutable $lastLevelRecalculation = null,
        private readonly ?string $address = null,
        private readonly ?string $loyaltyCardNumber = null,
        private readonly ?DateTimeImmutable $createdAt = null,
        private readonly ?DateTimeImmutable $updatedAt = null,
        private readonly ?LevelId $levelId = null,
        private readonly ?LevelId $manuallyAssignedLevelId = null,
        private readonly bool $agreement1 = false,
        private readonly bool $agreement2 = false,
        private readonly bool $agreement3 = false,
        private readonly ?string $company = null,
        private readonly int $transactionsCount = 0,
        private readonly float $transactionsAmount = 0.0,
        private readonly float $transactionsAmountWithoutDeliveryCosts = 0.0,
        private readonly float $transactionsAmountExcludedForLevel = 0.0,
        private readonly float $averageTransactionAmount = 0.0,
        private readonly ?DateTimeImmutable $lastTransactionDate = null,
        private readonly ?DateTimeImmutable $firstTransactionDate = null,
        private readonly ?DateTimeImmutable $levelAchievementDate = null,
        private readonly ?CustomerId $referrerMemberId = null,
        private readonly ?string $labels = null,
        private readonly bool $anonymized = false,
        private readonly ?string $referralToken = null,
        private readonly ?DateTimeImmutable $registeredAt = null
    ) {
    }

    public function getTenantId(): string
    {
        return (string) $this->tenantId;
    }

    public function getMemberId(): string
    {
        return (string) $this->memberId;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function getChannelId(): ?string
    {
        return (string) $this->channelId;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function getGender(): ?string
    {
        return (string) $this->gender;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function getBirthDate(): ?DateTimeImmutable
    {
        return $this->birthDate;
    }

    public function getLastLevelRecalculation(): ?DateTimeImmutable
    {
        return $this->lastLevelRecalculation;
    }

    public function getAddress(): ?string
    {
        return (string) $this->address;
    }

    public function getLoyaltyCardNumber(): ?string
    {
        return $this->loyaltyCardNumber;
    }

    public function getCreatedAt(): ?DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function getLevelId(): ?string
    {
        return (string) $this->levelId;
    }

    public function getManuallyAssignedLevelId(): ?string
    {
        return (string) $this->manuallyAssignedLevelId;
    }

    public function isAgreement1(): bool
    {
        return $this->agreement1;
    }

    public function isAgreement2(): bool
    {
        return $this->agreement2;
    }

    public function isAgreement3(): bool
    {
        return $this->agreement3;
    }

    public function getCompany(): ?string
    {
        return (string) $this->company;
    }

    public function getTransactionsCount(): int
    {
        return $this->transactionsCount;
    }

    public function getTransactionsAmount(): float
    {
        return $this->transactionsAmount;
    }

    public function getTransactionsAmountWithoutDeliveryCosts(): float
    {
        return $this->transactionsAmountWithoutDeliveryCosts;
    }

    public function getTransactionsAmountExcludedForLevel(): float
    {
        return $this->transactionsAmountExcludedForLevel;
    }

    public function getAverageTransactionAmount(): float
    {
        return $this->averageTransactionAmount;
    }

    public function getLastTransactionDate(): ?DateTimeImmutable
    {
        return $this->lastTransactionDate;
    }

    public function getFirstTransactionDate(): ?DateTimeImmutable
    {
        return $this->firstTransactionDate;
    }

    public function getLevelAchievementDate(): ?DateTimeImmutable
    {
        return $this->levelAchievementDate;
    }

    public function getReferrerMemberId(): ?string
    {
        return (string) $this->referrerMemberId;
    }

    public function getLabels(): ?string
    {
        return (string) $this->labels;
    }

    public function isAnonymized(): bool
    {
        return $this->anonymized;
    }

    public function getReferralToken(): ?string
    {
        return $this->referralToken;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function getStoreCode(): string
    {
        return $this->storeCode;
    }

    public function getRegisteredAt(): ?DateTimeImmutable
    {
        return $this->registeredAt;
    }
}
