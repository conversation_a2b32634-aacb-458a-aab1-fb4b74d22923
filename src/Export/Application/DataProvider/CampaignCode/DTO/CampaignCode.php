<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Export\Application\DataProvider\CampaignCode\DTO;

use DateTimeImmutable;

final class CampaignCode
{
    public function __construct(
        private string $codeId,
        private string $code,
        private string $status,
        private DateTimeImmutable $createdAt,
        private ?string $usedByMemberId,
        private ?DateTimeImmutable $usedAt,
    ) {
    }

    public function getCodeId(): string
    {
        return $this->codeId;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUsedByMemberId(): ?string
    {
        return $this->usedByMemberId;
    }

    public function getUsedAt(): ?DateTimeImmutable
    {
        return $this->usedAt;
    }
}
