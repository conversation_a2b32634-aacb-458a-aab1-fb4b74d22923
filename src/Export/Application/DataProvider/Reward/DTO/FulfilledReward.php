<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Export\Application\DataProvider\Reward\DTO;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\IssuedRewardId;
use OpenLoyalty\Core\Domain\Id\RewardId;

final class FulfilledReward
{
    public function __construct(
        public readonly IssuedRewardId $fulfilledRewardId,
        public readonly RewardId $rewardId,
        public readonly string $token,
        public readonly ?string $name,
        public readonly ?string $status,
        public readonly float $costInPoints,
        public readonly ?float $tax,
        public readonly ?float $taxPriceValue,
        public readonly ?float $price,
        public readonly DateTimeImmutable $redemptionDate,
        public readonly string $rewardType,
        public readonly ?string $issuedCoupon, //serialized object
        public readonly ?string $unitsConversion, //serialized object
        public readonly CustomerId $memberId,
        public readonly string $memberData //serialized object
    ) {
    }
}
