<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Export\Application\Response;

use DateTimeImmutable;
use OpenApi\Annotations as OA;
use OpenLoyalty\Export\Domain\ValueObject\ExportId;
use OpenLoyalty\Export\Domain\ValueObject\ExportStatus;
use OpenLoyalty\Export\Domain\ValueObject\ExportType;

final class ExportBasicData
{
    public function __construct(
        public readonly ExportId $exportId,
        /**
         * @OA\Property(
         *     type="string",
         *     enum={ExportType::CAMPAIGN_CODE, ExportType::MEMBER, ExportType::MEMBER_TIER, ExportType::MEMBER_SEGMENT, ExportType::REWARD_FULFILLMENT},
         * )
         */
        public readonly string $type,
        /**
         * @OA\Property(
         *     type="string",
         *     enum={ExportStatus::PENDING, ExportStatus::DONE, ExportStatus::FAILED, ExportStatus::ERROR},
         * )
         */
        public readonly string $status,
        public readonly DateTimeImmutable $createdAt,
        public readonly ?DateTimeImmutable $finishedAt,
        public readonly string $criteria,
        public readonly ?int $exportedRows,
        public readonly ?string $message = null
    ) {
    }
}
