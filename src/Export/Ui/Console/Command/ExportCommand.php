<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Export\Ui\Console\Command;

use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Export\Domain\ValueObject\ExportId;
use OpenLoyalty\Export\Infrastructure\Exporter;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class ExportCommand extends Command
{
    public function __construct(
        private readonly Exporter $exporter,
        private readonly StoreRepository $storeRepository
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('oloy:export:run-item')
            ->setDescription('Run existing export item')
            ->addArgument('exportId', InputArgument::REQUIRED);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($input->getOption('store-code')) {
            $storeId = $this->storeRepository->byCode($input->getOption('store-code'))?->getStoreId();

            if (null === $storeId) {
                $output->writeln('<error>Store code not found</error>');

                return self::FAILURE;
            }
        } else {
            $output->writeln('<error>store-code is required</error>');

            return self::FAILURE;
        }

        $exportId = $input->getArgument('exportId');

        $this->exporter->export($storeId, new ExportId($exportId));

        return self::SUCCESS;
    }
}
