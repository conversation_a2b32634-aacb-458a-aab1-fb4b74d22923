<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Export\Domain\ValueObject;

use OpenLoyalty\Export\Domain\Exception\NotSupportedExportStatusException;

final class ExportStatus
{
    public const PENDING = 'pending';
    public const DONE = 'done';
    public const FAILED = 'failed';
    public const ERROR = 'error';

    private const ALLOWED_STATUSES = [
        self::PENDING,
        self::DONE,
        self::FAILED,
        self::ERROR,
    ];

    /**
     * @throws NotSupportedExportStatusException
     */
    public function __construct(
        private readonly string $value
    ) {
        if (!in_array(
            $this->value,
            self::ALLOWED_STATUSES
        )) {
            throw new NotSupportedExportStatusException('Export status: '.$this->value.' is not supported.');
        }
    }

    public function getValue(): string
    {
        return $this->value;
    }
}
