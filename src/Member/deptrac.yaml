deptrac:
    paths:
        - ./src
    layers:
        -   name: MemberDomain
            collectors:
                -   type: directory
                    regex: src/Member/Domain/*
        -   name: MemberApplication
            collectors:
                -   type: directory
                    regex: src/Member/Application/*
        -   name: MemberInfrastructure
            collectors:
                -   type: directory
                    regex: src/Member/Infrastructure/*
        -   name: MemberUi
            collectors:
                -   type: directory
                    regex: src/Member/Ui/*
    ruleset:
        MemberDomain:
        MemberApplication:
            - MemberDomain
            - CoreDomain
            - CoreApplication
            - CoreInfrastructure # dependency to remove
            - UserDomain
            - UserApplication
        MemberInfrastructure:
            - MemberDomain
            - MemberApplication
        MemberUi:
            - MemberDomain
            - MemberApplication
            - MemberInfrastructure
            - CoreDomain
            - CoreApplication
            - CoreInfrastructure
            - CoreUi
            - UserDomain
            - UserApplication
            - LevelDomain
            - ChannelDomain
