<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Member\Application\Export;

use DateTimeImmutable;
use Generator;
use OpenLoyalty\Core\Application\Export\ExportDataMapperInterface;
use OpenLoyalty\Core\Application\Export\IterableDataProviderInterface;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;

final readonly class CampaignVisibilityDataProvider implements IterableDataProviderInterface
{
    public function __construct(
        private ExportDataMapperInterface $mapper,
        private CustomerRepositoryInterface $customerRepository,
    ) {
    }

    public function getItemsCountByDate(?DateTimeImmutable $date): int
    {
        return $this->customerRepository->getAllActiveMembersCount();
    }

    public function getItemsByDate(?DateTimeImmutable $date): Generator
    {
        $items = $this->customerRepository->getCampaignVisibility();

        foreach ($items as $item) {
            yield $this->mapper->map($item);
        }
    }
}
