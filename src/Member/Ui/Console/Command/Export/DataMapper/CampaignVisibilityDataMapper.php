<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Member\Ui\Console\Command\Export\DataMapper;

use Assert\AssertionFailedException;
use OpenLoyalty\Core\Application\Export\ExportDataMapperInterface;
use OpenLoyalty\Member\Ui\Console\Command\Export\Response\CampaignVisibility;
use OpenLoyalty\User\Domain\ValueObject\MemberCampaignVisibility;

class CampaignVisibilityDataMapper implements ExportDataMapperInterface
{
    /**
     * @param  MemberCampaignVisibility[] $items
     * @return CampaignVisibility[]
     * @throws AssertionFailedException
     */
    public function mapList(array $items): array
    {
        $executions = [];
        foreach ($items as $item) {
            $executions[] = $this->map($item);
        }

        return $executions;
    }

    /**
     * @param  MemberCampaignVisibility $item
     * @param  mixed[]                  $context
     * @return CampaignVisibility
     *
     * @throws AssertionFailedException
     */
    public function map(object $item, array $context = []): object
    {
        return new CampaignVisibility(
            $item->getStoreId(),
            $item->getCustomerId(),
            $item->getEmail() ?? '',
            $item->getPhone() ?? '',
            $item->getLoyaltyCardNumber() ?? '',
            $item->getVisibileCampaignIds(),
        );
    }
}
