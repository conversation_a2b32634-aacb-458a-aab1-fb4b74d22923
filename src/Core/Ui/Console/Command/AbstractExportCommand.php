<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Ui\Console\Command;

use Carbon\CarbonImmutable;
use DateTimeImmutable;
use Exception;
use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use OpenLoyalty\Core\Application\Export\DataProviderInterface;
use OpenLoyalty\Core\Infrastructure\Exception\NotSupportedExportTypeException;
use OpenLoyalty\Core\Infrastructure\Export\ExporterInterface;
use OpenLoyalty\Core\Infrastructure\Export\ExporterResolver;
use ReflectionClass;
use ReflectionException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use function array_combine;
use function sprintf;

abstract class AbstractExportCommand extends Command
{
    private const ALL_UNTIL_FILENAME_DATE_PREFIX = 'all_until_';
    private ExporterInterface $exporter;

    /**
     * @throws NotSupportedExportTypeException
     */
    public function __construct(
        private readonly FilesystemOperator $analyticsStorage,
        private readonly DataProviderInterface $dataProvider,
        ExporterResolver $exporterResolver
    ) {
        parent::__construct();
        $this->exporter = $exporterResolver->get($dataProvider);
    }

    abstract protected function getResponseClass(): string;

    abstract protected function getExportBasename(): string;

    abstract protected function getExportDirectory(): string;

    protected function configure(): void
    {
        $this->addArgument(
            'date',
            InputArgument::OPTIONAL,
            'The day you want to export analytics data'
            )
            ->setDescription('Export data for analytics to CSV file');
    }

    /**
     * @throws ReflectionException
     * @throws FilesystemException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $date = $this->getDate($input->getArgument('date'));
        } catch (Exception) {
            $output->writeln('<error>The given date is incorrect.</error> ');

            return self::FAILURE;
        }

        if (null === $date && $input->isInteractive()) {
            $helper = $this->getHelper('question');
            $question = new ConfirmationQuestion(
                'Are you sure you want to run data export without specifying a date? This will export all data since the system was running. [y/N]: ',
                false
            );
            /* @phpstan-ignore-next-line */
            if (!$helper->ask($input, $output, $question)) {
                return Command::SUCCESS;
            }
        }

        $itemsCount = $this->dataProvider->getItemsCountByDate($date);
        if (0 === $itemsCount) {
            $output->writeln('<info>There is no data to export.</info> ');

            return self::SUCCESS;
        }

        /** @var string $temporaryFile */
        $temporaryFile = tempnam(sys_get_temp_dir(), 'export_');

        $progressBar = new ProgressBar($output, $itemsCount);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s%');
        $progressBar->start();

        $properties = $this->getProperties();
        $fields = array_combine($properties, $properties);

        $maxRows = $this->getMaxRowsPerFile();

        if (null === $maxRows) {
            foreach ($this->exporter->export($temporaryFile, $fields, $properties, $date) as $progress) {
                $progressBar->advance($progress);
            }

            $progressBar->finish();

            $this->writeToFile($output, $date, $temporaryFile, null);

            return self::SUCCESS;
        }

        $itemIterator = 0;
        $fileNameIterator = 0;
        $fileBreakPoint = $maxRows;
        foreach ($this->exporter->export($temporaryFile, $fields, $properties, $date) as $progress) {
            $progressBar->advance($progress);
            if ($itemIterator >= $fileBreakPoint) {
                $this->writeToFile($output, $date, $temporaryFile, $fileNameIterator);

                $fileBreakPoint += $maxRows;
                ++$fileNameIterator;

                $this->clearFileExceptHeaders($temporaryFile);
            }
            $itemIterator += $progress;
        }

        $progressBar->finish();
        $output->writeln('');

        /** @var array<string> $file */
        $file = file($temporaryFile);
        if (count($file) > 1) {
            $this->writeToFile($output, $date, $temporaryFile, $fileNameIterator);
        }

        return self::SUCCESS;
    }

    // Should be overridden in child if file portioning is needed
    protected function getMaxRowsPerFile(): ?int
    {
        return null;
    }

    /**
     * @throws Exception
     */
    private function getDate(?string $argument): ?DateTimeImmutable
    {
        if (empty($argument)) {
            return null;
        }

        return new CarbonImmutable($argument);
    }

    /**
     * @throws ReflectionException
     */
    private function getProperties(): array
    {
        $reflectionClass = new ReflectionClass($this->getResponseClass());
        $properties = $reflectionClass->getProperties();
        $fields = [];
        foreach ($properties as $property) {
            $fields[] = $property->getName();
        }

        return $fields;
    }

    private function getExportFilename(?DateTimeImmutable $date, string $suffix): string
    {
        $allTimePrefix = '';
        if (null === $date) {
            $date = new CarbonImmutable();
            $allTimePrefix = self::ALL_UNTIL_FILENAME_DATE_PREFIX;
        }

        return sprintf(
            '%s_%s%s%s.csv',
            $this->getExportBasename(),
            $allTimePrefix,
            $date->format('Y_m_d'),
            $suffix
        );
    }

    private function getPath(?DateTimeImmutable $date, ?int $count): string
    {
        if (null === $count) {
            $suffix = '';
        } else {
            $suffix = '_'.$count;
        }

        return sprintf(
            '%s/%s',
            $this->getExportDirectory(),
            $this->getExportFilename($date, $suffix)
        );
    }

    private function clearFileExceptHeaders(string $temporaryFile): void
    {
        /** @var resource $fileHandle */
        $fileHandle = fopen($temporaryFile, 'r+');
        /** @var string $firstLine */
        $firstLine = fgets($fileHandle);
        ftruncate($fileHandle, 0);
        rewind($fileHandle);
        fwrite($fileHandle, $firstLine);
        fclose($fileHandle);
    }

    private function writeToFile(OutputInterface $output, ?DateTimeImmutable $date, string $temporaryFile, ?int $count): void
    {
        $output->writeln('');

        $output->writeln('Writing content to a file...');

        $destination = $this->getPath($date, $count);
        $this->analyticsStorage->writeStream($destination, fopen($temporaryFile, 'r'));

        $output->writeln('The content has been written to the file: '.$destination);
        $output->writeln('');
    }
}
