<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Ui\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Exception\NamespaceNotFoundException;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

final class RedisFlushallCommand extends Command
{
    private const BASE_COMMAND = 'redis:flushall';

    private ParameterBagInterface $parameterBag;

    public function __construct(ParameterBagInterface $parameterBag)
    {
        parent::__construct();
        $this->parameterBag = $parameterBag;
    }

    protected function configure(): void
    {
        $this->setName('oloy:redis:flushall')->addOption('force');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (!$input->getOption('force')) {
            $output->writeln('<comment>This operation will remove ALL DATA all databases. Add --force if you want to continue.</comment>');

            return Command::FAILURE;
        }

        try {
            $command = $this->getApplication()->find(self::BASE_COMMAND);
        } catch (NamespaceNotFoundException $exception) {
            $environment = $this->parameterBag->get('kernel.environment');
            $output->writeln(
                \sprintf(
                    'You are in a %s environment where the command %s is not available.',
                    $environment,
                    self::BASE_COMMAND
                )
            );

            return 0;
        }

        $command->run(
            new ArrayInput(['--no-interaction' => true]),
            $output
        );

        return Command::SUCCESS;
    }
}
