<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Ui\Console\Command;

use Carbon\CarbonImmutable;
use DomainException;
use OpenLoyalty\Core\Domain\BillableReportItemRepositoryInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class GenerateBillableReportCommand extends Command
{
    public function __construct(
        private readonly BillableReportItemRepositoryInterface $billableReportItemRepository
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('oloy:report:billable:generate')
            ->setDescription('Generate billable report')
            ->addArgument('period', InputArgument::REQUIRED, 'Period Y-m-d');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $periodArg = $input->getArgument('period');
        if (!is_string($periodArg)) {
            throw new DomainException('Invalid argument period');
        }

        $period = new CarbonImmutable($periodArg);
        $this->billableReportItemRepository->generate($period);

        return self::SUCCESS;
    }
}
