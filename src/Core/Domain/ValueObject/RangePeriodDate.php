<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\ValueObject;

use DateTimeImmutable;

final readonly class RangePeriodDate
{
    private function __construct(
        private ?DateTimeImmutable $date = null,
        private ?int $days = null,
        private bool $isEmpty = false,
    ) {
    }

    public static function empty(): self
    {
        return new self(isEmpty: true);
    }

    public static function fromDate(?DateTimeImmutable $date): self
    {
        return new self(date: $date);
    }

    public static function fromDays(?int $days): self
    {
        return new self(days: $days);
    }

    public function getDate(): ?DateTimeImmutable
    {
        if ($this->isEmpty) {
            return null;
        }

        return $this->date;
    }

    public function getDays(): ?int
    {
        if ($this->isEmpty) {
            return null;
        }

        return $this->days;
    }

    public function isEmpty(): bool
    {
        return $this->isEmpty;
    }
}
