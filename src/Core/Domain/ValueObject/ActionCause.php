<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\ValueObject;

use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\CustomEventId;
use OpenLoyalty\Core\Domain\Id\TransactionId;

class ActionCause
{
    private ?CampaignId $campaignId;
    private ?CustomerId $customerId;
    private ?TransactionId $transactionId;
    private ?CustomEventId $customEventId;
    private ?string $internalEventName;

    public function __construct(
        ?CampaignId $campaignId,
        ?CustomerId $customerId,
        ?TransactionId $transactionId = null,
        ?CustomEventId $customEventId = null,
        ?string $internalEventName = null
    ) {
        $this->campaignId = $campaignId;
        $this->customerId = $customerId;
        $this->transactionId = $transactionId;
        $this->customEventId = $customEventId;
        $this->internalEventName = $internalEventName;
    }

    public function getCampaignId(): ?CampaignId
    {
        return $this->campaignId;
    }

    public function getCustomerId(): ?CustomerId
    {
        return $this->customerId;
    }

    public function getTransactionId(): ?TransactionId
    {
        return $this->transactionId;
    }

    public function getCustomEventId(): ?CustomEventId
    {
        return $this->customEventId;
    }

    public function getInternalEventName(): ?string
    {
        return $this->internalEventName;
    }
}
