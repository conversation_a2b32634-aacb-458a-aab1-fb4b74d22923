<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\ValueObject\Achievement;

final readonly class RuleValuesDetails
{
    public function __construct(
        public string $achievementRuleId,
        public float $periodGoal,
        public float $currentPeriodValue,
        public ?int $consecutivePeriods = null,
        public ?int $completedConsecutivePeriods = null,
        public ?float $periodValue = null,
    ) {
    }

    public function isSame(self $ruleValuesDetails): bool
    {
        return $this->achievementRuleId === $ruleValuesDetails->achievementRuleId &&
            $this->periodGoal === $ruleValuesDetails->periodGoal &&
            $this->currentPeriodValue === $ruleValuesDetails->currentPeriodValue &&
            $this->consecutivePeriods === $ruleValuesDetails->consecutivePeriods &&
            $this->completedConsecutivePeriods === $ruleValuesDetails->completedConsecutivePeriods &&
            $this->periodValue === $ruleValuesDetails->periodValue
        ;
    }
}
