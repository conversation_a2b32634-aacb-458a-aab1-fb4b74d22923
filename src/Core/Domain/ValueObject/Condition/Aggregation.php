<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\ValueObject\Condition;

use OpenLoyalty\Core\Domain\Condition\Schema\AggregationType;

final class Aggregation
{
    public function __construct(
        private AggregationType $type,
        private ?string $field = null
    ) {
    }

    public function getType(): AggregationType
    {
        return $this->type;
    }

    public function getField(): ?string
    {
        return $this->field;
    }
}
