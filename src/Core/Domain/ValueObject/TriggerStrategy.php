<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\ValueObject;

use OpenLoyalty\Core\Domain\Exception\InvalidTriggerStrategyException;

final class TriggerStrategy
{
    public const DAILY = 'daily';
    public const BIRTHDAY = 'birthday';
    public const WEEKLY = 'weekly';
    public const MONTHLY = 'monthly';
    public const REGISTRATION_ANNIVERSARY = 'registration_anniversary';

    public const ALLOWED_TYPES = [
        self::DAILY => self::DAILY,
        self::BIRTHDAY => self::BIRTHDAY,
        self::WEEKLY => self::WEEKLY,
        self::MONTHLY => self::MONTHLY,
        self::REGISTRATION_ANNIVERSARY => self::REGISTRATION_ANNIVERSARY,
    ];

    private ?string $type;
    private ?string $executionSchedule;

    /**
     * @throws InvalidTriggerStrategyException
     */
    private function __construct(?string $type, ?string $executionSchedule)
    {
        if (null !== $type && !in_array($type, self::ALLOWED_TYPES)) {
            throw new InvalidTriggerStrategyException();
        }

        $this->type = $type;
        $this->executionSchedule = $executionSchedule;
    }

    /**
     * @throws InvalidTriggerStrategyException
     */
    public static function create(?string $type, ?string $executionSchedule = null): self
    {
        return new self($type, $executionSchedule);
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function getExecutionSchedule(): ?string
    {
        return $this->executionSchedule;
    }

    public function equals(self $triggerStrategy): bool
    {
        return $triggerStrategy->getType() === $this->type;
    }

    public function __toString(): string
    {
        return $this->type ?? '';
    }
}
