<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Context\Strategy;

use DateTimeInterface;
use OpenLoyalty\Core\Domain\Context\Transaction as TransactionContext;
use OpenLoyalty\Core\Domain\Exception\InvalidTriggerException;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use OpenLoyalty\Transaction\Domain\Transaction;

class TransactionStrategy implements ContextStrategyInterface
{
    protected Transaction $transaction;

    public function __construct(Transaction $transaction)
    {
        $this->transaction = $transaction;
    }

    /**
     * @throws InvalidTriggerException
     */
    public function getLastTrigger(): Trigger
    {
        $documentType = $this->transaction->getDocumentType();
        $triggerType = (Transaction::TYPE_SELL === $documentType) ? Trigger::TRANSACTION : Trigger::RETURN_TRANSACTION;

        return Trigger::create($triggerType);
    }

    public function getCurrentTime(): DateTimeInterface
    {
        return $this->transaction->getPurchasedAt();
    }

    public function getContextItems(): array
    {
        return [
            'transaction' => TransactionContext::create($this->transaction),
        ];
    }
}
