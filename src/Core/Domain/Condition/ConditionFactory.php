<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Condition;

use OpenLoyalty\Core\Domain\Condition\Custom\ExpressionLanguage;
use OpenLoyalty\Core\Domain\Condition\Generic\Contains;
use OpenLoyalty\Core\Domain\Condition\Generic\ContainsOneOf;
use OpenLoyalty\Core\Domain\Condition\Generic\HasAtLeastOneLabel;
use OpenLoyalty\Core\Domain\Condition\Generic\IsEqual;
use OpenLoyalty\Core\Domain\Condition\Generic\IsNotEqual;
use OpenLoyalty\Core\Domain\Condition\Generic\IsNotOneOf;
use OpenLoyalty\Core\Domain\Condition\Generic\IsOneOf;
use OpenLoyalty\Core\Domain\Condition\Generic\MatchesRegex;
use OpenLoyalty\Core\Domain\Condition\Generic\NotContains;
use OpenLoyalty\Core\Domain\Condition\Generic\NotContainsOneOf;
use OpenLoyalty\Core\Domain\Condition\Numeric\IsGreater;
use OpenLoyalty\Core\Domain\Condition\Numeric\IsGreaterOrEqual;
use OpenLoyalty\Core\Domain\Condition\Numeric\IsLess;
use OpenLoyalty\Core\Domain\Condition\Numeric\IsLessOrEqual;
use OpenLoyalty\Core\Domain\Condition\Numeric\IsNumberBetween;
use OpenLoyalty\Core\Domain\Condition\String\EndsWith;
use OpenLoyalty\Core\Domain\Condition\String\StartsWith;
use OpenLoyalty\Core\Domain\Condition\Time\IsAfter;
use OpenLoyalty\Core\Domain\Condition\Time\IsBefore;
use OpenLoyalty\Core\Domain\Condition\Time\IsBetween;
use OpenLoyalty\Core\Domain\Condition\Time\IsDayOfMonth;
use OpenLoyalty\Core\Domain\Condition\Time\IsDayOfWeek;
use OpenLoyalty\Core\Domain\Condition\Time\IsMonthOfYear;
use OpenLoyalty\Core\Domain\Condition\Time\IsNotBetween;
use OpenLoyalty\Core\Domain\Condition\Time\IsTimeBetween;

class ConditionFactory implements ConditionFactoryInterface
{
    private const CONDITIONS_CLASS_NAME = [
        ExpressionLanguage::OPERATOR => ExpressionLanguage::class,
        IsEqual::OPERATOR => IsEqual::class,
        HasAtLeastOneLabel::OPERATOR => HasAtLeastOneLabel::class,
        Contains::OPERATOR => Contains::class,
        NotContains::OPERATOR => NotContains::class,
        ContainsOneOf::OPERATOR => ContainsOneOf::class,
        NotContainsOneOf::OPERATOR => NotContainsOneOf::class,
        IsNotEqual::OPERATOR => IsNotEqual::class,
        IsNotOneOf::OPERATOR => IsNotOneOf::class,
        IsOneOf::OPERATOR => IsOneOf::class,
        MatchesRegex::OPERATOR => MatchesRegex::class,
        IsGreater::OPERATOR => IsGreater::class,
        IsGreaterOrEqual::OPERATOR => IsGreaterOrEqual::class,
        IsLess::OPERATOR => IsLess::class,
        IsLessOrEqual::OPERATOR => IsLessOrEqual::class,
        EndsWith::OPERATOR => EndsWith::class,
        StartsWith::OPERATOR => StartsWith::class,
        IsAfter::OPERATOR => IsAfter::class,
        IsBefore::OPERATOR => IsBefore::class,
        IsBetween::OPERATOR => IsBetween::class,
        IsDayOfWeek::OPERATOR => IsDayOfWeek::class,
        IsMonthOfYear::OPERATOR => IsMonthOfYear::class,
        IsNotBetween::OPERATOR => IsNotBetween::class,
        IsDayOfMonth::OPERATOR => IsDayOfMonth::class,
        IsTimeBetween::OPERATOR => IsTimeBetween::class,
        IsNumberBetween::OPERATOR => IsNumberBetween::class,
    ];

    /**
     * @@param mixed $data
     * @throws InvalidConditionException
     */
    public function create(string $operator, ?string $attribute, $data): ConditionInterface
    {
        if (isset(self::CONDITIONS_CLASS_NAME[$operator])) {
            /** @var ConditionInterface $conditionClass */
            $conditionClass = self::CONDITIONS_CLASS_NAME[$operator];

            return $conditionClass::fromArray(
                $attribute,
                $data
            );
        }

        throw new InvalidConditionException();
    }

    public static function getOperators(): array
    {
        return array_keys(self::CONDITIONS_CLASS_NAME);
    }
}
