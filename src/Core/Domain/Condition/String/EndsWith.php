<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Condition\String;

use OpenLoyalty\Core\Domain\Condition\AbstractCondition;
use OpenLoyalty\Core\Domain\Condition\ConditionInterface;
use OpenLoyalty\Core\Domain\Condition\InvalidConditionException;

class EndsWith extends AbstractCondition
{
    public const OPERATOR = 'ends_with';

    public function __construct(string $attributeName, string $value)
    {
        parent::__construct($attributeName, $value);
    }

    public function getExpression(): string
    {
        return sprintf(
            "ends_with(lower(%s), '%s')",
            $this->getAttributeName(),
            mb_strtolower($this->getData())
        );
    }

    public function getOperator(): string
    {
        return self::OPERATOR;
    }

    /**
     * @throws InvalidConditionException
     */
    public static function fromArray(?string $attribute, $data): ConditionInterface
    {
        if (!is_string($data)) {
            throw new InvalidConditionException('Value is not a string');
        }

        return new self($attribute, $data);
    }
}
