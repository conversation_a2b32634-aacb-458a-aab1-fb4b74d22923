<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Condition;

/**
 * @deprecated Deprecated, use new condition attribute feature (\OpenLoyalty\Core\Domain\Condition\Schema\ConditionExpressionBuilder)
 */
abstract class AbstractCondition implements ConditionInterface
{
    private string $attributeName;

    private mixed $data;

    public function __construct(string $attributeName, mixed $data)
    {
        $this->attributeName = $attributeName;
        $this->data = $data;
    }

    protected function getAttributeName(): string
    {
        return $this->attributeName;
    }

    protected function getData(): mixed
    {
        return $this->data;
    }
}
