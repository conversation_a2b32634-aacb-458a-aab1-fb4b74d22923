<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Condition\Schema;

enum OperatorType: string
{
    // general
    case IsEqual = 'eq';
    case IsNotEqual = 'neq';
    case Between = 'between';
    case NotBetween = 'not_between';

    case StartsWith = 'starts_with';
    case EndsWith = 'ends_with';
    case MatchesRegex = 'match';
    case NotMatchesRegex = 'not';

    case IsGreater = 'gt';
    case IsGreaterOrEqual = 'gte';
    case IsLess = 'lt';
    case IsLessOrEqual = 'lte';

    case IsAfter = 'is_after';
    case IsBefore = 'is_before';

    case In = 'in';
    case NotIn = 'not_in';
    case OneOf = 'one_of';
    case NotOneOf = 'not_one_of';

    case InValueGroup = 'in_value_group';
    case NotInValueGroup = 'not_in_value_group';
    case OneOfValueGroup = 'one_of_value_group';
    case NotOneOfValueGroup = 'not_one_of_value_group';
}
