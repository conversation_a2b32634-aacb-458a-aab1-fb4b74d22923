<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Condition\Generic;

use OpenLoyalty\Core\Domain\Condition\AbstractCondition;
use OpenLoyalty\Core\Domain\Condition\InvalidConditionException;

final class NotContainsOneOf extends AbstractCondition
{
    public const OPERATOR = 'not_contains_one_of';

    /**
     * @param string[] $values
     */
    public function __construct(
        string $attributeName,
        array $values,
    ) {
        parent::__construct($attributeName, $values);
    }

    /**
     * @return string[]
     */
    public function getData(): array
    {
        $data = parent::getData();

        if (!is_array($data)) {
            return [];
        }

        return array_map('strval', $data);
    }

    public function getExpression(): string
    {
        $criteria = [];
        foreach ($this->getData() as $value) {
            $criteria[] = sprintf(
                '\'%s\' not in %s',
                $value,
                $this->getAttributeName(),
            );
        }

        return sprintf('(%s)', implode(' and ', $criteria));
    }

    public function getOperator(): string
    {
        return self::OPERATOR;
    }

    /**
     * @throws InvalidConditionException
     */
    public static function fromArray(?string $attribute, mixed $data): self
    {
        if (!is_array($data)) {
            throw new InvalidConditionException('Value is not an array');
        }
        if (null === $attribute) {
            throw new InvalidConditionException('Attribute is not a string');
        }

        return new self($attribute, $data);
    }
}
