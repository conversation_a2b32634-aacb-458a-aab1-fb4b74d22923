<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Condition\Generic;

use OpenLoyalty\Core\Domain\Condition\AbstractCondition;
use OpenLoyalty\Core\Domain\Condition\ConditionInterface;
use OpenLoyalty\Core\Domain\Condition\InvalidConditionException;

class IsEqual extends AbstractCondition
{
    public const OPERATOR = 'is_equal';

    public function getExpression(): string
    {
        return sprintf(
            $this->getRuleString($this->getData()),
            $this->getAttributeName(),
            $this->getData()
        );
    }

    private function getRuleString($value): string
    {
        if (is_numeric($value)) {
            return '%s == %s';
        }

        if (is_bool($value)) {
            return '%s == '.($value ? 'true' : 'false');
        }

        return 'lower(%s) == lower(\'%s\')';
    }

    public function getOperator(): string
    {
        return self::OPERATOR;
    }

    /**
     * @throws \OpenLoyalty\Core\Domain\Condition\InvalidConditionException
     */
    public static function fromArray(?string $attribute, $data): ConditionInterface
    {
        if (!is_scalar($data)) {
            throw new InvalidConditionException('Value is not a scalar value');
        }

        return new self($attribute, $data);
    }
}
