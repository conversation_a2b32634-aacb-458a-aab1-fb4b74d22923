<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Condition\Time;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Condition\AbstractCondition;
use OpenLoyalty\Core\Domain\Condition\ConditionInterface;
use OpenLoyalty\Core\Domain\Condition\InvalidConditionException;

class IsNotBetween extends AbstractCondition
{
    public const OPERATOR = 'is_not_between';
    private DateTimeImmutable $from;
    private DateTimeImmutable $to;

    public function __construct(string $attributeName, DateTimeImmutable $from, DateTimeImmutable $to)
    {
        parent::__construct($attributeName, ['from' => $from, 'to' => $to]);
        $this->from = $from;
        $this->to = $to;
    }

    public function getExpression(): string
    {
        return sprintf(
            'is_not_between(%1$s, to_date(\'%2$s\'), to_date(\'%3$s\'))',
            $this->getAttributeName(),
            $this->from->format(DateTimeImmutable::ATOM),
            $this->to->format(DateTimeImmutable::ATOM)
        );
    }

    public function getOperator(): string
    {
        return self::OPERATOR;
    }

    /**
     * @throws InvalidConditionException
     */
    public static function fromArray(?string $attribute, $data): ConditionInterface
    {
        $dateFrom = $data['from'] ?? null;
        $dateTo = $data['to'] ?? null;

        if (!$dateFrom instanceof DateTimeImmutable) {
            throw new InvalidConditionException('Value from is not a datetime');
        }

        if (!$dateTo instanceof DateTimeImmutable) {
            throw new InvalidConditionException('Value to is not a datetime');
        }

        return new self($attribute, $dateFrom, $dateTo);
    }
}
