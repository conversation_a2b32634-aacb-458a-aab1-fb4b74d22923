<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Limit;

use DateTimeImmutable;
use Exception;
use OpenLoyalty\Core\Domain\ValueObject\LimitInterval;
use OpenLoyalty\Core\Domain\ValueObject\LimitPeriod;

class LimitIntervalCalculator implements LimitIntervalCalculatorInterface
{
    private const DATE_FORMAT = 'Y-m-d';
    private const TIME_DAY_BEGIN = 'T00:00:00';
    private const TIME_DAY_END = 'T23:59:59';
    private const TIME_HOUR_BEGIN = 'T%02d:00:00';
    private const TIME_HOUR_END = 'T%02d:59:59';

    /**
     * @throws Exception
     */
    public function calculate(LimitInterval $interval, DateTimeImmutable $currentTime): LimitPeriod
    {
        $stringFromTime = '';
        $stringToTime = '';
        $fromTime = '';
        $toTime = '';

        switch ($interval->getType()) {
            case LimitInterval::CALENDAR_HOURS:
                $stringFromTime = '-%d hours';
                $stringToTime = 'this hour';
                $fromTime = sprintf(
                    self::TIME_HOUR_BEGIN,
                    date('H', strtotime(
                        sprintf($stringFromTime, $interval->getValue() - 1),
                        $currentTime->getTimestamp()
                    ))
                );
                $toTime = sprintf(
                    self::TIME_HOUR_END,
                    date('H', strtotime(
                        $stringToTime,
                        $currentTime->getTimestamp()
                    ))
                );
                break;

            case LimitInterval::CALENDAR_DAYS:
                $stringFromTime = '-%d days';
                $stringToTime = 'this day';
                $fromTime = self::TIME_DAY_BEGIN;
                $toTime = self::TIME_DAY_END;
                break;

            case LimitInterval::CALENDAR_WEEKS:
                $stringFromTime = 'monday this week, -%d week';
                $stringToTime = 'sunday this week';
                $fromTime = self::TIME_DAY_BEGIN;
                $toTime = self::TIME_DAY_END;
                break;

            case LimitInterval::CALENDAR_MONTHS:
                $stringFromTime = 'first day of this month, -%d months';
                $stringToTime = 'last day of this month';
                $fromTime = self::TIME_DAY_BEGIN;
                $toTime = self::TIME_DAY_END;
                break;

            case LimitInterval::CALENDAR_YEARS:
                $stringFromTime = 'first day of January this year, -%d years';
                $stringToTime = 'last day of December this year';
                $fromTime = self::TIME_DAY_BEGIN;
                $toTime = self::TIME_DAY_END;
                break;
        }

        $fromDateMarker = $this->getDateMarker(
            strtotime(
                sprintf($stringFromTime, $interval->getValue() - 1), $currentTime->getTimestamp()
            ),
            $fromTime
        );
        $toDateMarker = $this->getDateMarker(
            strtotime($stringToTime, $currentTime->getTimestamp()),
            $toTime
        );

        return new LimitPeriod(
            new DateTimeImmutable($fromDateMarker),
            new DateTimeImmutable($toDateMarker)
        );
    }

    private function getDateMarker(
        int|false $timestamp,
        string $hourSuffix
    ): string {
        return date(
                self::DATE_FORMAT,
                $timestamp,
            ).$hourSuffix;
    }
}
