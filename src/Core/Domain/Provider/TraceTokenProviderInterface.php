<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Provider;

use Symfony\Component\Messenger\Envelope;

interface TraceTokenProviderInterface
{
    public function getCurrentTraceToken(): ?string;

    public function initializeForHttpRequest(): void;

    public function initializeForCli(): void;

    public function initializeForMessage(Envelope $envelope): void;

    public function popCurrentTraceToken(): void;

    public function restoreInitialToken(): void;

    public function markMessageAsFailed(): void;

    public function runningToken(): void;
}
