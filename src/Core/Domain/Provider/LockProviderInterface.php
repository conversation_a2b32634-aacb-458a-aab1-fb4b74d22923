<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Provider;

use Symfony\Component\Lock\Key;

interface LockProviderInterface
{
    public function acquire(array $resourceParts, bool $blocking = false, ?int $ttl = null, bool $autorelease = false): bool;

    public function acquireFromKey(Key $key, bool $blocking = false, ?int $ttl = null, bool $autorelease = false): bool;

    public function release(array $resourceParts): void;
}
