<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\ReadModel\Entity;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;

abstract class ReadModelEntity implements TimestampableInterface
{
    use TimestampableTrait;

    protected int $id;
    protected ?DateTimeImmutable $recreateRequiredOn = null;

    public function __construct(
        protected StoreId $storeId
    ) {
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function markAsRecreateRequired(DateTimeImmutable $requestedAt): void
    {
        $this->recreateRequiredOn = $requestedAt;
    }

    public function markAsRecreateNotRequired(): void
    {
        $this->recreateRequiredOn = null;
    }

    public function getRecreateRequiredOn(): ?DateTimeImmutable
    {
        return $this->recreateRequiredOn;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }
}
