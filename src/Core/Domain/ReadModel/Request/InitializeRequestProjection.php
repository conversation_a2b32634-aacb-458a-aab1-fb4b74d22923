<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\ReadModel\Request;

use OpenLoyalty\Core\Domain\Id\StoreId;

final class InitializeRequestProjection implements QueryRequestProjectionInterface
{
    public function __construct(
        public readonly string $projectorClass,
        public readonly StoreId $storeId
    ) {
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }
}
