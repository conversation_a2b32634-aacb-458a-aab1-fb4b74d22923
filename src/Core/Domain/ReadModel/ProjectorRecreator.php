<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\ReadModel;

use OpenLoyalty\Core\Domain\Message\JobBusInterface;
use OpenLoyalty\Core\Domain\ReadModel\Entity\ReadModelEntity;
use OpenLoyalty\Core\Domain\ReadModel\Request\ActionRequestProjectionInterface;
use OpenLoyalty\Core\Domain\ReadModel\Request\MultiRequestProjection;
use OpenLoyalty\Core\Domain\ReadModel\Request\QueryRequestProjectionInterface;
use OpenLoyalty\Core\Domain\ReadModel\Request\RequestProjectionInterface;
use Psr\Log\LoggerInterface;

class ProjectorRecreator implements ProjectorRecreatorInterface
{
    private const WARNING_LEVEL = 10000;

    public function __construct(
        private readonly ProjectorResolver $projectorResolver,
        private readonly int $multiRequestPackageSize,
        private readonly JobBusInterface $jobBus,
        private readonly LoggerInterface $projectorLogger,
        private readonly RecreateRequestManagerInterface $recreateRequestManager,
        private readonly bool $processRequestsExecuteImmediately,
        private readonly ProjectionRefresherInterface $projectionRefresher,
    ) {
    }

    public function recreate(RequestProjectionInterface $requestProjection): void
    {
        $requests[] = $requestProjection;
        if ($requestProjection instanceof MultiRequestProjection) {
            $requests = $requestProjection->getRequests();
        }

        foreach ($requests as $requestItem) {
            // if request projection is a query (may contain a lot of projections to rebuild)
            if ($requestItem instanceof QueryRequestProjectionInterface) {
                $this->postPoneRecreateQuery($requestItem);
                continue;
            }

            $projector = $this->getProjector($requestItem);

            if ($requestItem instanceof ActionRequestProjectionInterface) {
                $projector->recreateAction($requestItem);
                continue;
            }

            // getProjection should return at most one element
            /** @var ReadModelEntity[] $projections */
            $projections = iterator_to_array($projector->getWriteRepository()->getProjections($requestItem));

            if (0 === count($projections)) {
                $this->projectionRefresher->recreate($projector, $requestItem, $projector->getDefault($requestItem));
                continue;
            }

            foreach ($projections as $projection) {
                $this->projectionRefresher->recreate($projector, $projector->getSingleRequestProjectionBy($projection), $projection);
            }
        }
    }

    protected function postPoneRecreateQuery(QueryRequestProjectionInterface $requestProjection): void
    {
        $projector = $this->getProjector($requestProjection);
        /** @var ReadModelEntity[] $projections */
        $projections = $projector->getWriteRepository()->getProjections($requestProjection);

        $requestsPackage = [];
        $requestCount = 0;
        foreach ($projections as $projection) {
            $requestsPackage[] = $projector->getSingleRequestProjectionBy($projection);

            if ($this->multiRequestPackageSize === count($requestsPackage)) {
                $this->recreateQueue(new MultiRequestProjection($requestProjection->getStoreId(), $requestsPackage));
                $requestsPackage = [];
            }
            ++$requestCount;
        }

        if (count($requestsPackage) > 0) {
            $this->recreateQueue(new MultiRequestProjection($requestProjection->getStoreId(), $requestsPackage));
        }

        $this->projectionRefresher->skip($requestProjection);

        if ($requestCount > self::WARNING_LEVEL) {
            $this->projectorLogger->warning(sprintf(
                'Projector %s postponed %s items',
                get_class($projector),
                $requestCount,
            ));
        }
    }

    public function recreateQueue(RequestProjectionInterface $requestProjection): void
    {
        $this->jobBus->dispatch($requestProjection, false);
    }

    public function recreateLate(RequestProjectionInterface $requestProjection): void
    {
        $requests = [];

        if ($requestProjection instanceof MultiRequestProjection) {
            foreach ($requestProjection->getRequests() as $request) {
                $requests[] = $request;
            }
        } else {
            $requests[] = $requestProjection;
        }

        foreach ($requests as $request) {
            $this->recreateRequestManager->add($request);

            // used when cron is disabled
            if ($this->processRequestsExecuteImmediately) {
                $this->recreateQueue($requestProjection);
            }
        }
    }

    private function getProjector(RequestProjectionInterface $requestProjection): ProjectorInterface
    {
        return $this->projectorResolver->resolve($requestProjection);
    }
}
