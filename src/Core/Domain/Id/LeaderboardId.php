<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Id;

use Assert\Assertion as Assert;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Model\Identifier;

final class LeaderboardId implements Identifier
{
    /**
     * @OA\Property(type="string", format="uuid")
     */
    protected string $leaderboardId;

    public function __construct(string $leaderboardId)
    {
        Assert::uuid($leaderboardId);

        $this->leaderboardId = $leaderboardId;
    }

    public function __toString(): string
    {
        return $this->leaderboardId;
    }

    public function getLeaderboardId(): string
    {
        return $this->leaderboardId;
    }
}
