<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Core\Domain\Id;

use Assert\Assertion as Assert;
use OpenLoyalty\Core\Domain\Model\Identifier;

/**
 * Class EarningRuleId.
 */
class EarningRuleId implements Identifier
{
    private string $earningRuleId;

    /**
     * EarningRuleId constructor.
     *
     * @param $earningRuleId
     */
    public function __construct(string $earningRuleId)
    {
        Assert::string($earningRuleId);
        Assert::uuid($earningRuleId);

        $this->earningRuleId = $earningRuleId;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return $this->earningRuleId;
    }
}
