<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Id;

use Assert\Assertion as Assert;
use OpenLoyalty\Core\Domain\Model\Identifier;

class GroupOfValuesId implements Identifier
{
    public function __construct(
        private readonly string $groupOfValuesId
    ) {
        Assert::uuid($groupOfValuesId);
    }

    public function __toString()
    {
        return $this->groupOfValuesId;
    }
}
