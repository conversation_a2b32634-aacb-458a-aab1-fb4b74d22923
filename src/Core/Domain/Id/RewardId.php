<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Id;

use Assert\Assertion as Assert;
use OpenLoyalty\Core\Domain\Model\Identifier;

class RewardId implements Identifier
{
    protected string $rewardId;

    public function __construct(string $rewardId)
    {
        Assert::uuid($rewardId);

        $this->rewardId = $rewardId;
    }

    public function __toString(): string
    {
        return $this->rewardId;
    }
}
