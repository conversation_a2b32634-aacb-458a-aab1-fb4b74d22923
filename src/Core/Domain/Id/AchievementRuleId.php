<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Id;

use Assert\Assertion as Assert;
use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Model\Identifier;

class AchievementRuleId implements Identifier
{
    private string $achievementRuleId;

    /**
     * @throws AssertionFailedException
     */
    public function __construct(string $achievementRuleId)
    {
        Assert::uuid($achievementRuleId);

        $this->achievementRuleId = $achievementRuleId;
    }

    public function __toString(): string
    {
        return $this->achievementRuleId;
    }

    public function getAchievementRuleId(): string
    {
        return $this->achievementRuleId;
    }
}
