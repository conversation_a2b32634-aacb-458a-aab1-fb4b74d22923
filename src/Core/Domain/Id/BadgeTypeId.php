<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Id;

use Assert\Assertion as Assert;
use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Model\Identifier;

class BadgeTypeId implements Identifier
{
    private string $badgeTypeId;

    /**
     * @throws AssertionFailedException
     */
    public function __construct(string $badgeTypeId)
    {
        Assert::uuid($badgeTypeId);
        $this->badgeTypeId = $badgeTypeId;
    }

    public function __toString(): string
    {
        return $this->badgeTypeId;
    }

    public function getBadgeTypeId(): string
    {
        return $this->badgeTypeId;
    }
}
