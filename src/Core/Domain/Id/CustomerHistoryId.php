<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Id;

use Assert\Assertion as Assert;
use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Model\Identifier;

final class CustomerHistoryId implements Identifier
{
    protected string $customerHistoryId;

    /**
     * @throws AssertionFailedException
     */
    public function __construct(string $customerHistoryId)
    {
        Assert::uuid($customerHistoryId);

        $this->customerHistoryId = $customerHistoryId;
    }

    public function __toString(): string
    {
        return $this->customerHistoryId;
    }
}
