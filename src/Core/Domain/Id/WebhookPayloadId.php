<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Id;

use Assert\Assertion as Assert;
use Assert\AssertionFailedException;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Model\Identifier;

readonly class WebhookPayloadId implements Identifier
{
    /**
     * @throws AssertionFailedException
     */
    public function __construct(
        /**
         * @OA\Property(type="string",  format="uuid")
         */
        private string $WebhookPayloadId
    ) {
        Assert::uuid($WebhookPayloadId);
    }

    public function __toString(): string
    {
        return $this->WebhookPayloadId;
    }

    public function getWebhookPayloadId(): string
    {
        return $this->WebhookPayloadId;
    }
}
