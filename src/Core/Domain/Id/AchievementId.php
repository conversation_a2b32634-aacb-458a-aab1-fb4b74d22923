<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Id;

use Assert\Assertion as Assert;
use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Model\Identifier;

class AchievementId implements Identifier
{
    private string $achievementId;

    /**
     * @throws AssertionFailedException
     */
    public function __construct(string $achievementId)
    {
        Assert::uuid($achievementId);

        $this->achievementId = $achievementId;
    }

    public function __toString(): string
    {
        return $this->achievementId;
    }

    public function getAchievementId(): string
    {
        return $this->achievementId;
    }
}
