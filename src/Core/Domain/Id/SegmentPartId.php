<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Id;

use Assert\Assertion as Assert;
use OpenLoyalty\Core\Domain\Model\Identifier;

/**
 * Class SegmentPartId.
 */
class SegmentPartId implements Identifier
{
    protected string $segmentPartId;

    /**
     * SegmentPartId constructor.
     *
     * @throws \Assert\AssertionFailedException
     */
    public function __construct(string $segmentPartId)
    {
        Assert::uuid($segmentPartId);

        $this->segmentPartId = $segmentPartId;
    }

    public function __toString(): string
    {
        return $this->segmentPartId;
    }
}
