<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Search\Criteria;

class InvalidUuidCriteria extends AbstractCriteria
{
    /**
     * @var string|null
     */
    protected $value;

    public function __construct(string $name, string $operator, ?string $value)
    {
        parent::__construct($name, $operator);

        $this->value = $value;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }
}
