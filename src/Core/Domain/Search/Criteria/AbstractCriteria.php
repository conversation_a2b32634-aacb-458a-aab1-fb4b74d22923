<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Search\Criteria;

abstract class AbstractCriteria implements CriteriaInterface, InternalCriteriaInterface
{
    protected string $name;
    protected string $operator;
    /**
     * @var mixed
     */
    protected $value;
    protected bool $isInternal = false;

    public function __construct(string $name, string $operator)
    {
        $this->name = $name;
        $this->operator = $operator;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getOperator(): string
    {
        return $this->operator;
    }

    public function setAsInternal(): self
    {
        $this->isInternal = true;

        return $this;
    }

    public function isInternal(): bool
    {
        return $this->isInternal;
    }
}
