<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Search\Criteria;

class KeyValueListCriteria extends AbstractCriteria
{
    /**
     * @var array[]
     */
    protected $values;

    public function __construct(string $name, string $operator, array $values)
    {
        parent::__construct($name, $operator);

        $this->values = $values;
    }

    public function getValues(): array
    {
        return $this->values;
    }

    public static function fromArrayLabels(string $name, string $operator, array $labels): self
    {
        $mapLabels = [];
        foreach ($labels as $label) {
            $mapLabels[] = [$label['key'] => $label['value'] ?? null];
        }

        return new self($name, $operator, $mapLabels);
    }
}
