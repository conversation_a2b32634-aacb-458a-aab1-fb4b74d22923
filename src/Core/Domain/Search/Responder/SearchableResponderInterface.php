<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Search\Responder;

use Generator;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\SearchableInterface;
use OpenLoyalty\Core\Domain\Search\SearchIterableInterface;

interface SearchableResponderInterface
{
    public function fromCriteria(
        SearchableInterface $searchable,
        CriteriaCollectionInterface $criteria,
        bool $estimatedCount = false
    ): SearchableResponse;

    public function fromCriteriaIterable(
        SearchIterableInterface $searchable,
        CriteriaCollectionInterface $criteria
    ): Generator;

    public function countByCriteria(
        SearchableInterface $searchable,
        CriteriaCollectionInterface $criteria
    ): int;
}
