<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;

class Store implements TimestampableInterface, BlameableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    private StoreId $storeId;
    private string $code;
    private string $currency;
    private string $name;
    private bool $active;

    public function __construct(StoreId $storeId, string $code, string $currency, string $name, bool $active = true)
    {
        $this->storeId = $storeId;
        $this->code = $code;
        $this->currency = $currency;
        $this->name = $name;
        $this->active = $active;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function setStoreId(StoreId $storeId): void
    {
        $this->storeId = $storeId;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): void
    {
        $this->code = $code;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): void
    {
        $this->currency = $currency;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): void
    {
        $this->active = $active;
    }

    public function __toString(): string
    {
        return (string) $this->getStoreId();
    }
}
