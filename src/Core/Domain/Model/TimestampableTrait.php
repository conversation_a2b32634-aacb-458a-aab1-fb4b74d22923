<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Model;

use DateTime;
use DateTimeImmutable;

trait TimestampableTrait
{
    protected DateTimeImmutable $createdAt;
    protected DateTimeImmutable $updatedAt;

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTimeImmutable|DateTime $createdAt): void
    {
        $this->createdAt = $createdAt instanceof DateTime ? DateTimeImmutable::createFromMutable($createdAt) : $createdAt;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(DateTimeImmutable|DateTime $updatedAt): void
    {
        $this->updatedAt = $updatedAt instanceof DateTime ? DateTimeImmutable::createFromMutable($updatedAt) : $updatedAt;
    }

    public function issetCreatedAt(): bool
    {
        return isset($this->createdAt);
    }

    public function issetUpdatedAt(): bool
    {
        return isset($this->updatedAt);
    }
}
