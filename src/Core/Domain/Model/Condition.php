<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain\Model;

use OpenLoyalty\Core\Domain\ValueObject\Condition\Operator;

class Condition
{
    private ?string $attribute;
    private Operator $operator;
    private array $data;

    public function __construct(
        ?string $attribute,
        Operator $operator,
        array $data
    ) {
        $this->attribute = $attribute;
        $this->operator = $operator;
        $this->data = $data;
    }

    public function getAttribute(): ?string
    {
        return $this->attribute;
    }

    public function getOperator(): Operator
    {
        return $this->operator;
    }

    public function getData(): array
    {
        return $this->data;
    }
}
