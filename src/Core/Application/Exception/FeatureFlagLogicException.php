<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Application\Exception;

use Exception;
use OpenLoyalty\Tools\FeatureFlag\FeatureFlag;

final class FeatureFlagLogicException extends Exception
{
    public static function createForFeatureFlag(string $className, FeatureFlag $featureFlag): self
    {
        return new self(sprintf('class %s should not be run without %s feature flag enabled', $className, $featureFlag->value));
    }
}
