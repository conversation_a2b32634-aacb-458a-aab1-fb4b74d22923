<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Application\UseCase;

use OpenLoyalty\Core\Application\Command\UpdateStore;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Infrastructure\Model\Store;

class UpdateStoreUseCase
{
    public function __construct(private CommandBusInterface $commandBus)
    {
    }

    public function execute(StoreId $storeId, Store $store): void
    {
        $command = new UpdateStore(
            $storeId,
            $store->getName(),
            $store->isActive()
        );
        $this->commandBus->dispatch($command);
    }
}
