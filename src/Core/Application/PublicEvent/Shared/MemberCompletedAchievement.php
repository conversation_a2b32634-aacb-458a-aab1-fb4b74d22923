<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Application\PublicEvent\Shared;

use DateTimeImmutable;
use OpenLoyalty\Core\Application\PublicEvent\Message\AbstractPublicEvent;
use OpenLoyalty\Core\Application\PublicEvent\PublicEventId;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\AdminId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;

final class MemberCompletedAchievement extends AbstractPublicEvent
{
    public function __construct(
        PublicEventId $publicEventId,
        public readonly StoreId $storeId,
        public readonly AchievementId $achievementId,
        public readonly CustomerId $customerId,
        public readonly DateTimeImmutable $completedAt,
        public readonly bool $manuallyEdited,
        public readonly ?string $achievementName = null,
        public readonly ?AdminId $adminId = null,
        public readonly ?int $previousCompletionCount = null,
        public readonly ?int $currentCompletionCount = null,
        public readonly ?DateTimeImmutable $createdAt = null,
    ) {
        parent::__construct($publicEventId);
    }
}
