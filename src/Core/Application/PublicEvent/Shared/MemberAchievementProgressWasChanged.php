<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Application\PublicEvent\Shared;

use DateTimeImmutable;
use OpenLoyalty\Core\Application\PublicEvent\Message\AbstractPublicEvent;
use OpenLoyalty\Core\Application\PublicEvent\PublicEventId;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\AdminId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\ValueObject\Achievement\RuleValuesDetails;

final class MemberAchievementProgressWasChanged extends AbstractPublicEvent
{
    /**
     * @param array<RuleValuesDetails>|null $rulesBeforeChanges
     * @param array<RuleValuesDetails>|null $rulesAfterChanges
     */
    public function __construct(
        PublicEventId $publicEventId,
        public readonly StoreId $storeId,
        public readonly AchievementId $achievementId,
        public readonly CustomerId $customerId,
        public readonly DateTimeImmutable $changedAt,
        public readonly ?string $achievementName = null,
        public readonly ?AdminId $adminId = null,
        public readonly ?array $rulesBeforeChanges = null,
        public readonly ?array $rulesAfterChanges = null
    ) {
        parent::__construct($publicEventId);
    }
}
