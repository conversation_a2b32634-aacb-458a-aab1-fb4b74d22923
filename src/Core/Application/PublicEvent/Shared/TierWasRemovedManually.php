<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Application\PublicEvent\Shared;

use DateTimeImmutable;
use OpenLoyalty\Core\Application\PublicEvent\Message\AbstractPublicEvent;
use OpenLoyalty\Core\Application\PublicEvent\PublicEventId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TierSetId;

class TierWasRemovedManually extends AbstractPublicEvent
{
    public function __construct(
        PublicEventId $publicEventId,
        public readonly CustomerId $customerId,
        public readonly StoreId $storeId,
        public readonly DateTimeImmutable $eventDate,
        public readonly ?TierSetId $tierSetId = null
    ) {
        parent::__construct($publicEventId);
    }
}
