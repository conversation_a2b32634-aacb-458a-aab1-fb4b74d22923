<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Application\JobHandler;

use OpenLoyalty\Core\Application\Job\RebuildAsyncCache;
use OpenLoyalty\Core\Domain\AsyncCache\AsyncCacheDispatcherInterface;
use OpenLoyalty\Core\Domain\Message\JobHandlerInterface;

final class RebuildAsyncCacheHandler implements JobHandlerInterface
{
    public function __construct(
        private readonly AsyncCacheDispatcherInterface $cacheDispatcher
    ) {
    }

    public function __invoke(RebuildAsyncCache $request): void
    {
        $this->cacheDispatcher->rebuild(
            $request->getStoreId(),
            $request->getCacheKey(),
            $request->getLockKey(),
            $request->getBuilderClass(),
            $request->getArguments()
        );
    }
}
