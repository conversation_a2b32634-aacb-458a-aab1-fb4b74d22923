<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Application\DataRetention;

use InvalidArgumentException;
use OpenLoyalty\Core\Domain\DataRetention\DataRetentionHandlerInterface;
use Psr\Log\LoggerInterface;
use Throwable;

readonly class DataRetentionExecutor implements DataRetentionExecutorInterface
{
    /**
     * @param iterable<DataRetentionHandlerInterface> $handlers
     */
    public function __construct(
        private iterable $handlers,
        private LoggerInterface $dataRetentionLogger
    ) {
    }

    public function cleanupData(): void
    {
        foreach ($this->handlers as $handler) {
            if (!$handler instanceof DataRetentionHandlerInterface) {
                throw new InvalidArgumentException('Data retention handler is not instance of DataRetentionHandler class');
            }

            $this->dataRetentionLogger->info(sprintf('Starting retention process for %s', get_class($handler)));
            try {
                $handler->handle();
            } catch (Throwable $e) {
                $this->dataRetentionLogger->error(
                    sprintf('Error during retention for %s: %s', get_class($handler), $e->getMessage()),
                    ['exception' => $e]
                );
            }
        }
    }
}
