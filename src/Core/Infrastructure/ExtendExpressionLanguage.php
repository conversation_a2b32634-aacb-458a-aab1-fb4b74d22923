<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure;

use Symfony\Component\ExpressionLanguage\ExpressionLanguage;
use Symfony\Component\HttpFoundation\Request;

/**
 * Class ExtendExpressionLanguage.
 */
class ExtendExpressionLanguage extends ExpressionLanguage
{
    /**
     * {@inheritdoc}
     */
    protected function registerFunctions(): void
    {
        parent::registerFunctions();

        $this->register('getRouteParameter', function ($name) {
            return sprintf('getRouteParameter(%s)', $name);
        }, function ($arguments, $name) {
            /** @var Request $request */
            $request = $arguments['request'];
            $routeParams = $request->attributes->get('_route_params');

            if (is_array($routeParams) && array_key_exists($name, $routeParams)) {
                return $routeParams[$name];
            }

            return null;
        });
    }
}
