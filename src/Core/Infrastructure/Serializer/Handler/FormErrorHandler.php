<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Serializer\Handler;

use <PERSON><PERSON>\Serializer\GraphNavigatorInterface;
use <PERSON><PERSON>\Serializer\Handler\SubscribingHandlerInterface;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\Visitor\SerializationVisitorInterface;
use OpenLoyalty\Core\Infrastructure\Model\ErrorResponse;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormErrorIterator;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Contracts\Translation\TranslatorInterface;

class FormErrorHandler implements SubscribingHandlerInterface
{
    /**
     * @var TranslatorInterface
     */
    private $translator;

    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    public static function getSubscribingMethods(): array
    {
        return [
            [
                'direction' => GraphNavigatorInterface::DIRECTION_SERIALIZATION,
                'type' => FormErrorIterator::class,
                'format' => 'json',
                'method' => 'serializeErrorIterator',
                'priority' => -255,
            ],
            [
                'direction' => GraphNavigatorInterface::DIRECTION_SERIALIZATION,
                'type' => Form::class,
                'format' => 'json',
                'method' => 'serializeForm',
                'priority' => -255,
            ],
            [
                'direction' => GraphNavigatorInterface::DIRECTION_SERIALIZATION,
                'type' => ErrorResponse::class,
                'format' => 'json',
                'method' => 'serializeErrorResponse',
            ],
        ];
    }

    /**
     * @param FormErrorIterator<FormError> $errorIterator
     */
    public function serializeErrorIterator(SerializationVisitorInterface $visitor, FormErrorIterator $errorIterator, array $type, SerializationContext $context): array
    {
        return $this->parseErrorIterator($errorIterator);
    }

    // @phpstan-ignore-next-line
    public function serializeForm(SerializationVisitorInterface $visitor, FormInterface $form, array $type, SerializationContext $context): array
    {
        return $this->parseErrorIterator($form->getErrors(true, true));
    }

    // @phpstan-ignore-next-line
    public function serializeErrorResponse(SerializationVisitorInterface $visitor, ErrorResponse $errorResponse, array $type, SerializationContext $context): array
    {
        return $this->parseErrorResponse($errorResponse);
    }

    private function parseErrorResponse(ErrorResponse $errorResponse): array
    {
        $serialized = [
            'code' => $errorResponse->getCode(),
            'message' => $errorResponse->getMessage(),
        ];

        if (null !== $errorResponse->getErrors()) {
            $serialized['errors'] = $errorResponse->getErrors();
        }

        return $serialized;
    }

    /* @phpstan-ignore-next-line */
    private function parseErrorIterator(FormErrorIterator $errorIterator): array
    {
        $errors = [];
        /** @var FormError $error */
        foreach ($errorIterator as $error) {
            $errors[] = $this->normalizeError($error);
        }

        return $this->parseErrorResponse(new ErrorResponse(
            Response::HTTP_BAD_REQUEST,
            $this->translator->trans('form.validation_failed'),
            $errors
        ));
    }

    private function normalizeError(FormError $error): array
    {
        return [
            'message' => $error->getMessage(),
            'parameters' => $error->getMessageParameters(),
            'plural' => $error->getMessagePluralization(),
            'code' => $error->getMessageTemplate(),
            'path' => $this->getPath($error),
        ];
    }

    private function getPath(FormError $error): ?string
    {
        if ($error->getCause() instanceof ConstraintViolation) {
            $propertyPath = (string) $error->getCause()->getPropertyPath();
        } elseif ($error->getOrigin() instanceof FormInterface) {
            $propertyPath = (string) $error->getOrigin()->getPropertyPath();
        }

        return (isset($propertyPath))
            ? $this->resolvePathName($propertyPath)
            : null;
    }

    private function resolvePathName(string $propertyPath): string
    {
        $parsed = preg_replace(
            [
                '/^data\.|\.data|children/',
                '/\[(.*)\]/',
                '/\[|\]/',
            ],
            [
                '',
                '$1',
                '',
            ],
            $propertyPath
        );

        return str_replace('..', '.', $parsed);
    }
}
