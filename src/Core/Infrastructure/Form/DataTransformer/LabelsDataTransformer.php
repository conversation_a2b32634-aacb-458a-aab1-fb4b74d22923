<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Form\DataTransformer;

use OpenLoyalty\Core\Domain\Model\Label;
use Symfony\Component\Form\DataTransformerInterface;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
class LabelsDataTransformer implements DataTransformerInterface
{
    private const ENTRIES_DELIMITER = ';';
    private const KEY_VALUE_DELIMITER = ':';

    public function transform(mixed $value): ?string
    {
        if (null == $value) {
            return null;
        }

        if (!is_array($value)) {
            throw new \InvalidArgumentException();
        }

        $values = array_map(function (Label $label): string {
            return $label->getKey().self::KEY_VALUE_DELIMITER.$label->getValue();
        }, $value);

        return implode(self::ENTRIES_DELIMITER, $values);
    }

    public function reverseTransform(mixed $values): array
    {
        if (null === $values) {
            return [];
        }

        return array_map(static function (array $label): Label {
            return new Label($label['key'] ?? '', $label['value'] ?? '');
        }, $values);
    }
}
