<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Form\Type;

use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\StoreRepository;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Class StoreChoicesType.
 */
class StoreChoicesType extends AbstractType
{
    /**
     * @var \OpenLoyalty\Core\Domain\StoreRepository
     */
    private $storeRepository;

    /**
     * StoreChoicesType constructor.
     */
    public function __construct(StoreRepository $storeRepository)
    {
        $this->storeRepository = $storeRepository;
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $storeChoices = array_map(
            function (Store $store) {
                return (string) $store->getCode();
            },
            $this->storeRepository->findAll()
        );

        $resolver->setDefaults([
            'choices' => array_combine($storeChoices, $storeChoices),
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function getParent(): string
    {
        return ChoiceType::class;
    }
}
