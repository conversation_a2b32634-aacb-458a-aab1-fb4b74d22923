<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Form\Type\Condition;

use OpenLoyalty\Core\Domain\Condition\Schema\AggregationType as AggregationTypeEnum;
use OpenLoyalty\Core\Domain\Condition\Schema\ContextSchemaDefinition;
use OpenLoyalty\Core\Domain\ValueObject\Condition\Aggregation;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
final class AggregationType extends AbstractType implements DataTransformerInterface
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /** @var ?ContextSchemaDefinition $schema */
        $schema = $options['condition_schema'] ?? null;

        $builder->add('type', EnumType::class, [
            'constraints' => [new NotBlank()],
            'class' => AggregationTypeEnum::class,
        ]);

        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) use ($schema): void {
            $form = $event->getForm();
            /** @var array<mixed> $data */
            $data = $event->getData();

            if (!isset($data['type']) || $data['type'] !== AggregationTypeEnum::Sum->value) {
                return;
            }

            $form->add('field', ChoiceType::class, [
                'constraints' => [new NotBlank()],
                'choices' => null !== $schema ? $schema->getFieldNames(true) : [],
            ]);
        });

        $builder->addModelTransformer($this);
    }

    /**
     * @param  ?array<mixed> $value
     * @return ?array<mixed>
     */
    public function transform(mixed $value): ?array
    {
        return $value;
    }

    /**
     * @param array{type: AggregationTypeEnum, field: ?string} $value
     */
    public function reverseTransform(mixed $value): ?Aggregation
    {
        if (empty($value) || !isset($value['type'])) {
            return null;
        }

        return new Aggregation(
            $value['type'],
            $value['field'] ?? null
        );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'condition_schema' => ContextSchemaDefinition::class,
        ]);
        $resolver->setRequired(['condition_schema']);
    }
}
