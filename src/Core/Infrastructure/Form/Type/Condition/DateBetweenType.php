<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Form\Type\Condition;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\NotBlank;

final class DateBetweenType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('from', DateTimeType::class, [
            'constraints' => [new NotBlank()],
            'format' => DateTimeType::HTML5_FORMAT,
        ]);
        $builder->add('to', DateTimeType::class, [
            'constraints' => [new NotBlank()],
            'format' => DateTimeType::HTML5_FORMAT,
        ]);
    }
}
