<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Form\EventSubscriber;

use OpenLoyalty\Core\Domain\Condition\Schema\ContextSchemaDefinition;
use OpenLoyalty\Core\Infrastructure\Form\Type\Condition\ConditionType;
use OpenLoyalty\Core\Infrastructure\Form\Type\Condition\ValueType;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Form\Event\PreSubmitEvent;
use Symfony\Component\Form\FormEvents;

final readonly class ConditionFieldsSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private ContextSchemaDefinition $contextSchemaDefinition
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [FormEvents::PRE_SUBMIT => 'onPreSubmit'];
    }

    public function onPreSubmit(PreSubmitEvent $event): void
    {
        $form = $event->getForm();
        /** @var array<mixed> $data */
        $data = $event->getData();

        if (!isset($data['value'])) {
            return;
        }

        /** @var array<mixed> $valueData */
        $valueData = $data['value'];

        /** @var string $field */
        $field = $valueData['field'] ?? null;
        $fieldSchema = $field ? $this->contextSchemaDefinition->getField($field) : null;

        $form->add('value', ValueType::class, [
            'condition_schema' => $this->contextSchemaDefinition,
            'field_schema' => $fieldSchema,
        ]);

        if (null !== $fieldSchema) {
            $form->add('condition', ConditionType::class, [
                'field_schema' => $fieldSchema,
                'for_aggregation' => $valueData['aggregation'] ?? false,
            ]);
        }
    }
}
