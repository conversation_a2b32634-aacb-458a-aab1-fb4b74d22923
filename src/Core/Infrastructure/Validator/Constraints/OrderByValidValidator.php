<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Validator\Constraints;

use Symfony\Component\Form\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

final class OrderByValidValidator extends ConstraintValidator
{
    public function __construct(
        private readonly TranslatorInterface $translator,
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof OrderByValid) {
            throw new UnexpectedTypeException($constraint, OrderByValid::class);
        }

        if (empty($value)) {
            return;
        }

        if (!is_array($value) || $this->isNumericArray($value)) {
            $this->context->buildViolation($this->translator->trans('core.order_by_validation.must_be_array'))->addViolation();
        }
    }

    /**
     * @param array<int|string, mixed> $value
     */
    private function isNumericArray(array $value): bool
    {
        return array_values($value) === $value;
    }
}
