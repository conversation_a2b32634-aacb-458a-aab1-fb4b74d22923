<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Validator\Constraints;

use OpenLoyalty\Core\Domain\Search\Criteria\IntegerCriteria;
use RuntimeException;
use Symfony\Component\Form\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

final class IntCriteriaRangeValidValidator extends ConstraintValidator
{
    public function __construct(
        private readonly TranslatorInterface $translator,
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof IntCriteriaRangeValid) {
            throw new UnexpectedTypeException($constraint, IntCriteriaRangeValid::class);
        }

        if (null === $value || '' === $value || [] === $value) {
            return;
        }

        if (!is_array($value)) {
            throw new UnexpectedTypeException($value, 'array');
        }

        foreach ($value as $item) {
            $this->validateItem($item, $constraint);
        }
    }

    private function validateItem(mixed $item, IntCriteriaRangeValid $constraint): void
    {
        if (!$item instanceof IntegerCriteria) {
            throw new UnexpectedTypeException($item, IntegerCriteria::class);
        }

        $minValue = $constraint->minValue;
        $maxValue = $constraint->maxValue;
        $value = $item->getValue();

        if (null === $minValue || null === $maxValue) {
            throw new RuntimeException('minValue and maxValue must be set');
        }

        if ($value < $minValue) {
            $this->context->buildViolation($this->translator->trans($constraint->minMessage))
                ->setParameter('{{ min_value }}', (string) $minValue)
                ->addViolation()
            ;
        }

        if ($value > $maxValue) {
            $this->context->buildViolation($this->translator->trans($constraint->maxMessage))
                ->setParameter('{{ max_value }}', (string) $maxValue)
                ->addViolation()
            ;
        }
    }
}
