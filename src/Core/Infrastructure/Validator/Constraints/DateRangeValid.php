<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Validator\Constraints;

use Symfony\Component\Validator\Constraint;

class DateRangeValid extends Constraint
{
    public string $minMessage = 'core.date_range.min_date';
    public string $maxMessage = 'core.date_range.max_date';
}
