<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Message;

use OpenLoyalty\Core\Domain\Message\JobBusInterface;
use OpenLoyalty\Core\Domain\Message\JobInterface;
use OpenLoyalty\Core\Domain\Message\NonTransactionalJobInterface;
use Symfony\Component\Messenger\Exception\HandlerFailedException;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\BusNameStamp;
use Symfony\Component\Messenger\Stamp\DispatchAfterCurrentBusStamp;

readonly class JobBus implements JobBusInterface
{
    public function __construct(
        private MessageBusInterface $jobBus,
        private StampProvider $stampProvider,
        private SenderBus $senderBus,
    ) {
    }

    public function dispatch(JobInterface $job, bool $waitForFinishProcess = true): void
    {
        try {
            $stamps = $this->stampProvider->provide($job);

            if (true === $waitForFinishProcess) {
                $stamps[] = new DispatchAfterCurrentBusStamp();
            }

            if ($job instanceof NonTransactionalJobInterface) {
                $stamps[] = new BusNameStamp('job.non_transaction.bus');
            }

            if ($this->senderBus->tryDispatch('job.bus', $job, $stamps)) {
                return;
            }

            $this->jobBus->dispatch($job, $stamps);
        } catch (HandlerFailedException $e) {
            while ($e instanceof HandlerFailedException) {
                $e = $e->getPrevious();
            }

            throw $e;
        }
    }
}
