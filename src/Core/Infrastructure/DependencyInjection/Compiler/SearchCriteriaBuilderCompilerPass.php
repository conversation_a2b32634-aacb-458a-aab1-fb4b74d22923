<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\DependencyInjection\Compiler;

use OpenLoyalty\Core\Domain\Search\ContextCriteriaBuilderProviderInterface;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;

class SearchCriteriaBuilderCompilerPass implements CompilerPassInterface
{
    private const TAG_NAME = 'search.criteria.builder';

    public function process(ContainerBuilder $container): void
    {
        if (!$container->has(ContextCriteriaBuilderProviderInterface::class)) {
            return;
        }

        $criteriaProvider = $container->findDefinition(ContextCriteriaBuilderProviderInterface::class);
        $criteriaBuilderServices = $container->findTaggedServiceIds(self::TAG_NAME);

        foreach ($criteriaBuilderServices as $id => $tags) {
            foreach ($tags as $tag) {
                $criteriaProvider->addMethodCall('addCriteriaBuilder', [$tag['context'], new Reference($id)]);
            }
        }
    }
}
