<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Cache;

use OpenLoyalty\Core\Domain\Cache\ResponseCacheInterface;
use Symfony\Contracts\Cache\CacheInterface;

class ResponseCache implements ResponseCacheInterface
{
    public function __construct(
        private readonly CacheInterface $responseCache,
    ) {
    }

    public function get(string $key, callable $callable): mixed
    {
        return $this->responseCache->get(
            $key,
            $callable
        );
    }

    public function delete(string $key): void
    {
        $this->responseCache->delete($key);
    }
}
