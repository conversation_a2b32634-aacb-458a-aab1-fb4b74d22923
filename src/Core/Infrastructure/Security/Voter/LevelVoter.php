<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Security\Voter;

use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Infrastructure\Security\Voter\LevelVoter as BaseLevelVoter;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\PermissionAccess;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use OpenLoyalty\User\Infrastructure\Security\Voter\CustomerVoter;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * Class LevelVoter.
 *
 * @extends Voter<string, mixed>
 */
class LevelVoter extends Voter
{
    public function __construct(protected UserPermissionCheckerInterface $permissionChecker)
    {
    }

    /**
     * {@inheritdoc}
     */
    public function supports(string $attribute, mixed $subject): bool
    {
        return $subject instanceof Level && BaseLevelVoter::LIST_CUSTOMERS === $attribute;
    }

    /**
     * {@inheritdoc}
     */
    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        /** @var User $user */
        $user = $token->getUser();

        $viewLevelAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionInCurrentStore(
            $user,
            BaseLevelVoter::PERMISSION_RESOURCE,
            [PermissionAccess::VIEW]
        );

        $viewCustomerAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionInCurrentStore(
            $user,
            CustomerVoter::PERMISSION_RESOURCE,
            [PermissionAccess::VIEW]
        );

        if (BaseLevelVoter::LIST_CUSTOMERS === $attribute) {
            return $viewLevelAdmin && $viewCustomerAdmin;
        }

        return false;
    }
}
