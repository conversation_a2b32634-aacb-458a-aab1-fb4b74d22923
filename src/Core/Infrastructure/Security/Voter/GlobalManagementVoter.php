<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Security\Voter;

use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\PermissionAccess;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * @extends Voter<string, mixed>
 */
class GlobalManagementVoter extends Voter
{
    public const PERMISSION_RESOURCE = 'GLOBAL_MANAGEMENT';
    public const VIEW_GLOBAL_MANAGEMENT = 'VIEW_GLOBAL_MANAGEMENT';
    public const EDIT_GLOBAL_MANAGEMENT = 'EDIT_GLOBAL_MANAGEMENT';

    public function __construct(private UserPermissionCheckerInterface $permissionChecker)
    {
    }

    public function supports(string $attribute, mixed $subject): bool
    {
        return null === $subject && in_array($attribute, [
            self::VIEW_GLOBAL_MANAGEMENT, self::EDIT_GLOBAL_MANAGEMENT,
        ]);
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        /** @var User $user */
        $user = $token->getUser();

        if (!$user instanceof User) {
            return false;
        }

        $fullAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionWithoutStore(
                $user, self::PERMISSION_RESOURCE, [PermissionAccess::VIEW]
        );

        return match ($attribute) {
            self::VIEW_GLOBAL_MANAGEMENT,self::EDIT_GLOBAL_MANAGEMENT => $fullAdmin,
            default => false,
        };
    }
}
