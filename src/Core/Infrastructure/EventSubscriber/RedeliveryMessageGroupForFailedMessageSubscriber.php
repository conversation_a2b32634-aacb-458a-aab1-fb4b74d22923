<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\EventSubscriber;

use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\Bridge\AmazonSqs\Transport\AmazonSqsFifoStamp;
use Symfony\Component\Messenger\Event\AbstractWorkerMessageEvent;
use Symfony\Component\Messenger\Event\WorkerMessageFailedEvent;

final readonly class RedeliveryMessageGroupForFailedMessageSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private UuidGeneratorInterface $uuidGenerator,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            WorkerMessageFailedEvent::class => [
                ['onWorkerMessageFailed', 102], // should be before \Symfony\Component\Messenger\EventListener\SendFailedMessageForRetryListener
            ],
        ];
    }

    public function onWorkerMessageFailed(WorkerMessageFailedEvent $event): void
    {
        $envelope = $event->getEnvelope();
        $mid = $this->uuidGenerator->generate();

        // add random messageGroupId for retry message. Without it, messageGroupId will be the same for all failed
        // messages and lead to the stuck of the consuming.
        $newEnvelope = $envelope->with(new AmazonSqsFifoStamp($mid, null));

        // we need to change it by reflection, instead of rewriting class SendFailedMessageForRetryListener (symfony upgrade problem)
        $reflection = new \ReflectionClass(AbstractWorkerMessageEvent::class);
        $property = $reflection->getProperty('envelope');
        $property->setAccessible(true);
        $property->setValue($event, $newEnvelope);
        $property->setAccessible(false);
    }
}
