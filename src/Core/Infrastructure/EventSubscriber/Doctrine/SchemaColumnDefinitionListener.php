<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\EventSubscriber\Doctrine;

use Doctrine\Bundle\DoctrineBundle\EventSubscriber\EventSubscriberInterface;
use Doctrine\DBAL\Event\SchemaColumnDefinitionEventArgs;
use Doctrine\DBAL\Events;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Types\Type;

/**
 * This class solves the problem with invalid DC2Type defined in the comment column definition.
 * if detects an unknown type then remove the column. It works only for a column that is not used by other relations.
 */
final class SchemaColumnDefinitionListener implements EventSubscriberInterface
{
    public function getSubscribedEvents(): array
    {
        return [
            Events::onSchemaColumnDefinition,
        ];
    }

    public function onSchemaColumnDefinition(SchemaColumnDefinitionEventArgs $eventArgs): void
    {
        $column = $eventArgs->getTableColumn();

        if (!empty($column['comment']) && preg_match('(\(DC2Type:(((?!\)).)+)\))', $column['comment'], $match)) {
            try {
                Type::getType($match[1]);
            } catch (Exception) {
                $eventArgs->preventDefault();
            }
        }
    }
}
