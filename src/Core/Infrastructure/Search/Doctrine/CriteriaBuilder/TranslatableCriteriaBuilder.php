<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Search\Doctrine\CriteriaBuilder;

use OpenLoyalty\Core\Domain\Search\Context\ContextInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\TranslatableCriteria;

final class TranslatableCriteriaBuilder extends BaseCriteriaBuilder
{
    public function build(ContextInterface $context, CriteriaInterface $criteria): void
    {
        if (!$criteria instanceof TranslatableCriteria) {
            return;
        }

        $queryBuilder = $context->getQueryBuilder();
        $parameterName = $this->getParameterName($criteria);
        $aliasName = $this->getParameterName($criteria, 'trans');
        $fieldName = $this->getFieldName($criteria);

        $fieldParts = explode('.', $fieldName);

        if (1 === count($fieldParts)) {
            $joinField = $fieldParts[0];
            $columnField = $fieldParts[0];
        } else {
            $joinField = $fieldParts[0];
            $columnField = $fieldParts[1];
        }

        $queryBuilder
            ->join(sprintf('%s.translations', $joinField), $aliasName)
            ->andWhere(sprintf('%s.locale = :locale', $aliasName))
            ->setParameter('locale', $criteria->getLocale());

        if (null === $criteria->getValue()) {
            $context->getQueryBuilder()
                ->andWhere(sprintf('%s.%s IS NULL', $aliasName, $columnField));

            return;
        }

        if (CriteriaInterface::EQUAL === $criteria->getOperator()) {
            $context->getQueryBuilder()
                ->andWhere(sprintf('%s.%s = :%s',
                    $aliasName,
                    $columnField,
                    $parameterName
                ))
                ->setParameter($parameterName, $criteria->getValue());

            return;
        }

        if (CriteriaInterface::LIKE === $criteria->getOperator()) {
            $context->getQueryBuilder()
                ->andWhere(sprintf('ILIKE(%s.%s, :%s) = TRUE', $aliasName, $columnField, $parameterName))
                ->setParameter($parameterName, sprintf('%%%s%%', $criteria->getValue()));
        }
    }

    public function allows(CriteriaInterface $criteria): bool
    {
        return $criteria instanceof TranslatableCriteria
            && in_array($criteria->getOperator(), [CriteriaInterface::EQUAL, CriteriaInterface::LIKE]);
    }
}
