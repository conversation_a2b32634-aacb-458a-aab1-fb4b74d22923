<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Search\Form\Symfony;

use OpenLoyalty\Core\Domain\Search\Criteria\NumericCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\OrderByCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\PaginationCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\StoreCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\OrderByValid;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Event\PreSubmitEvent;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Range;

class SearchType extends AbstractType
{
    private const DEFAULT_ITEMS_ON_PAGE = 10;
    private const ITEMS_ON_PAGE_LIMIT = 50;

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('_page', IntegerType::class, [
                'required' => false,
                'constraints' => [
                    new Range(['min' => 1]),
                ],
            ])
            ->add('_itemsOnPage', IntegerType::class, [
                'required' => false,
                'constraints' => [
                    new Range(['min' => 1, 'max' => self::ITEMS_ON_PAGE_LIMIT]),
                ],
            ])
            ->add('_orderBy', CollectionType::class, [
                'required' => false,
                'entry_type' => ChoiceType::class,
                'entry_options' => [
                    'choices' => [
                        OrderByCriteria::DESC,
                        OrderByCriteria::ASC,
                    ],
                ],
                'allow_add' => true,
                'allow_delete' => true,
                'error_bubbling' => false,
                'documentation' => [
                    'description' => 'An associative array in which the key is the name of the field on which we want to sort and the value is the sorting direction.',
                    'example' => [
                        'created_at' => 'desc',
                        'name' => 'asc',
                    ],
                ],
                'constraints' => [
                    new OrderByValid(),
                ],
            ])
            ->addEventListener(
                FormEvents::PRE_SUBMIT,
                [$this, 'onPreSubmit']
            );

        $builder->addModelTransformer(new CallbackTransformer(
            function ($value) {
                return $value;
            },
            function (array $data) use ($options): CriteriaCollectionInterface {
                $criteriaItems = new CriteriaCollection();
                $locale = $options['locale'];

            if (isset($data['_orderBy'])) {
                $fieldMappings = $options['field_mappings'];
                foreach ($data['_orderBy'] as $fieldName => $direction) {
                    if (array_key_exists($fieldName, $fieldMappings)) {
                        $fieldName = $fieldMappings[$fieldName];
                    }

                    $abs = false;
                    if (isset($data[$fieldName]) && reset($data[$fieldName]) instanceof NumericCriteria) {
                        $abs = reset($data[$fieldName])->isAbs();
                    }

                    $criteriaItems->add(new OrderByCriteria(
                        (string) $fieldName,
                        $direction ?? OrderByCriteria::DESC,
                        $locale,
                        $abs
                    ));
                }
            }

                if (isset($options['default_order_by']) && !empty($options['default_order_by']) && !isset($data['_orderBy'][$options['default_order_by']])) {
                    $criteriaItems->add(new OrderByCriteria(
                        $options['default_order_by'],
                        $options['default_sort_direction'] ?? OrderByCriteria::DESC //@phpstan-ignore-line
                    ));
                }

                if ($options['with_pagination']) {
                    $criteriaItems->add(new PaginationCriteria(
                        $data['_page'] ?? 1,
                        $data['_itemsOnPage'] ?? self::DEFAULT_ITEMS_ON_PAGE
                    ));
                }

                foreach (['_itemsOnPage', '_page', '_orderBy'] as $key) {
                    unset($data[$key]);
                }

                foreach ($data as $field) {
                    foreach ($field as $criteria) {
                        $criteriaItems->add($criteria);
                    }
                }

                if (isset($options['store'])) {
                    $criteriaItems->add(new StoreCriteria($options['store_field'], $options['store']));
                }

                return $criteriaItems;
            }
        ));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'method' => 'GET',
            'store' => null,
            'locale' => 'en',
            'field_mappings' => [],
            'with_pagination' => true,
            'store_field' => 'store',
            'default_order_by' => '',
            'default_sort_direction' => null,
        ]);

        $resolver->setRequired([
            'locale',
        ]);
    }

    /**
     * Event converts simple criteria (without []) to supported by this form.
     * "name=test" is the same like "name[eq]=test".
     */
    public function onPreSubmit(PreSubmitEvent $event): void
    {
        $data = $event->getData() ?? [];

        foreach ($data as $fieldName => $fieldValue) {
            // skip special fields
            if (str_starts_with((string) $fieldName, '_')) {
                continue;
            }
            if (!is_array($fieldValue)) {
                $data[$fieldName] = [
                    $fieldValue,
                ];
            }
        }

        $event->setData($data);
    }
}
