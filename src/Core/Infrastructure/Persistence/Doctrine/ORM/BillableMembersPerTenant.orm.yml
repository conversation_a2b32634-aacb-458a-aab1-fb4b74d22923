OpenLoyalty\Core\Domain\BillableMembersPerTenant:
    type: entity
    table: billable_members_per_tenant
    id:
        billableMembersPerTenantId:
            type: billable_members_per_tenant_id
            column: billable_members_per_tenant_id
    fields:
        period:
            type: date_immutable
        tenantId:
            type: store_id
        billableMembersCount:
            type: integer
        createdAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        updatedAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        createdBy:
            type: string
            nullable: true
        updatedBy:
            type: string
            nullable: true
        billableReportItemId:
            column: billable_report_item_id
            type: billable_report_item_id
    uniqueConstraints:
        billable_members_per_tenant_period_tenant_id_unique:
            columns: [ period, tenant_id ]