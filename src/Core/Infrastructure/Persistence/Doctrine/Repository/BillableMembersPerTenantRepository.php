<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\BillableMembersPerTenant;
use OpenLoyalty\Core\Domain\BillableMembersPerTenantRepositoryInterface;
use OpenLoyalty\Core\Domain\Id\StoreId;

final class BillableMembersPerTenantRepository extends DoctrineRepository implements BillableMembersPerTenantRepositoryInterface
{
    private const CACHE_TTL = 3600; // 1 hour

    protected function getClass(): string
    {
        return BillableMembersPerTenant::class;
    }

    /**
     * @return array<BillableMembersPerTenant>>
     */
    public function getBillableMembersReportData(?StoreId $tenantId = null): array
    {
        $qb = $this->repository->createQueryBuilder('a');
        if (null !== $tenantId) {
            $qb->andWhere('a.tenantId = :tenantId')->setParameter('tenantId', $tenantId);
        }
        $qb->andWhere('a.period >= :period')->setParameter('period', (new DateTimeImmutable())->modify('- 12 months'));
        $qb->orderBy('a.period', 'DESC');

        /* @phpstan-ignore-next-line */
        return $this->resultCacheResolver->resolveDefault($qb->getQuery(), self::CACHE_TTL)->getResult();
    }
}
