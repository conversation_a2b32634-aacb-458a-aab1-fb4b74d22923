<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository;

use DateTimeImmutable;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Query;
use Doctrine\ORM\Query\Parser;
use Doctrine\ORM\QueryBuilder;
use Exception;
use Generator;
use OpenLoyalty\Core\Domain\Exception\TooManyResultsException;
use OpenLoyalty\Core\Domain\Model\Identifier;
use OpenLoyalty\Core\Domain\Model\IntervalType;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Repository;
use OpenLoyalty\Core\Domain\Search\Context\ContextInterface;
use OpenLoyalty\Core\Domain\Search\ContextQueryBuilderInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\OrderByCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\PaginationCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\EmptyResultException;
use OpenLoyalty\Core\Infrastructure\Exception\NotSupportedUpdatedAtField;
use OpenLoyalty\Core\Infrastructure\Persistence\ResultCacheResolver;
use OpenLoyalty\Core\Infrastructure\Search\Doctrine\Context;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

abstract class DoctrineRepository implements Repository
{
    private int $estimationCountPrecision;
    private int $databaseCursorFetchSize;

    protected EntityRepository $repository;

    public function __construct(
        protected EntityManagerInterface $entityManager,
        protected readonly ContextQueryBuilderInterface $queryBuilder,
        protected readonly ResultCacheResolver $resultCacheResolver,
        protected readonly LoggerInterface $appLogger,
        protected readonly ParameterBagInterface $parameterBag
    ) {
        /* @phpstan-ignore-next-line */
        $this->repository = $entityManager->getRepository($this->getClass());
        $this->estimationCountPrecision = $this->parameterBag->get('estimation_count_precision');
        $this->databaseCursorFetchSize = $this->parameterBag->get('database_cursor_fetch_size');
    }

    abstract protected function getClass(): string;

    public function findByCriteria(CriteriaCollectionInterface $criteria, bool $useQuickResultCache = false): array
    {
        $projectionField = $criteria->getProjectionField();

        $context = $this->getBaseContext(
            [
                Context::CLASS_PARAM => $this->getClass(),
                Context::EXTRA_JOINS => !$criteria->isSkipExtraJoins(),
            ]
        );

        try {
            $this->queryBuilder->buildQuery($context, $criteria);
        } catch (EmptyResultException) {
            return [];
        }

        $queryBuilder = $context->getQueryBuilder();
        $queryBuilder->select(
            null === $projectionField ? Context::DEFAULT_ALIAS : $this->prepareFieldsToSelect($projectionField)
        );

        $query = $context->getQueryBuilder()->getQuery();
        $query = $useQuickResultCache ? $this->resultCacheResolver->resolveQuick($query) : $query;

        return $query->getResult();
    }

    /**
     * @throws TooManyResultsException
     */
    public function findOneByCriteria(CriteriaCollectionInterface $criteria, bool $useQuickResultCache = false): ?object
    {
        $result = $this->findByCriteria($criteria, $useQuickResultCache);

        if (empty($result)) {
            return null;
        }

        if (count($result) > 1) {
            throw new TooManyResultsException();
        }

        return $result[0];
    }

    public function countByCriteria(CriteriaCollectionInterface $criteria, bool $estimated = false): int
    {
        $context = $this->getBaseContext(
            [
                Context::CLASS_PARAM => $this->getClass(),
                Context::EXTRA_JOINS => !$criteria->isSkipExtraJoins(),
                ContextInterface::EXCLUDE_PARAM => [
                    PaginationCriteria::class,
                    OrderByCriteria::class,
                ],
            ]
        );

        try {
            $this->queryBuilder->buildQuery($context, $criteria);
        } catch (EmptyResultException) {
            return 0;
        }

        $queryBuilder = $context->getQueryBuilder();

        if (true === $estimated) {
            $queryBuilder->select(Context::DEFAULT_ALIAS);
            $countEstimator = new CountEstimator($this->appLogger);

            $estimatedCount = $countEstimator->estimate($queryBuilder->getQuery()) ?? 0;
            // estimated values below $this->estimatedPrecision may be inaccurate
            if ($estimatedCount > $this->estimationCountPrecision) {
                return $estimatedCount;
            }
        }

        $queryBuilder
            ->select(sprintf('COUNT(%s)', Context::DEFAULT_ALIAS));

        return $this->resultCacheResolver->resolveDefault($queryBuilder->getQuery())->getSingleScalarResult();
    }

    public function find(Identifier $id): ?object
    {
        return $this->repository->find($id);
    }

    public function findAll(): array
    {
        return $this->repository->findAll();
    }

    public function findBy(array $criteria, ?array $orderBy = null, int $limit = null, int $offset = null): array
    {
        return $this->repository->findBy($criteria, $orderBy, $limit, $offset);
    }

    public function findOneBy(array $criteria, array $orderBy = []): ?object
    {
        return $this->repository->findOneBy($criteria, $orderBy);
    }

    /**
     * @param  array<string,mixed> $params
     * @return ContextInterface
     */
    protected function getBaseContext(array $params): ContextInterface
    {
        return new Context(
            substr(strrchr($this->getClass(), '\\'), 1),
            $this->entityManager,
            $params
        );
    }

    protected function groupByInterval(QueryBuilder $queryBuilder, string $field, string $interval = IntervalType::DAY): QueryBuilder
    {
        switch ($interval) {
            case IntervalType::YEAR:
                $intervalPattern = 'YYYY';
                break;
            case IntervalType::MONTH:
                $intervalPattern = 'YYYY-MM';
                break;
            default:
                $intervalPattern = 'YYYY-MM-DD';
        }

        $queryBuilder
            ->addSelect(sprintf('DATE_FORMAT(%s, \'%s\') AS interval', $field, $intervalPattern))
            ->addGroupBy('interval');

        return $queryBuilder;
    }

    public function findAllIterable(): Generator
    {
        $context = $this->getBaseContext(
            [
                Context::CLASS_PARAM => $this->getClass(),
                Context::EXTRA_JOINS => false,
            ]
        );

        $this->queryBuilder->buildQuery($context, new CriteriaCollection());
        $queryBuilder = $context->getQueryBuilder();

        $queryBuilder->select(Context::DEFAULT_ALIAS);

        return $this->toIterableWithClearEntityManagerCache(
            $context->getQueryBuilder()->getQuery()
        );
    }

    public function findByCriteriaIterable(
        CriteriaCollectionInterface $criteria,
        bool $useQuickResultCache = false
    ): Generator {
        $projectionField = $criteria->getProjectionField();

        $context = $this->getBaseContext(
            [
                Context::CLASS_PARAM => $this->getClass(),
                Context::EXTRA_JOINS => !$criteria->isSkipExtraJoins(),
            ]
        );

        $this->queryBuilder->buildQuery($context, $criteria);
        $queryBuilder = $context->getQueryBuilder();

        $queryBuilder->select(
            null === $projectionField ? Context::DEFAULT_ALIAS : $this->prepareFieldsToSelect($projectionField)
        );

        $query = $context->getQueryBuilder()->getQuery();
        $query = $useQuickResultCache ? $this->resultCacheResolver->resolveQuick($query) : $query;

        return $this->toIterableWithClearEntityManagerCache(
        /* @phpstan-ignore-next-line */
            $query,
            $projectionField,
            $criteria->getScalarValue(),
        );
    }

    private function prepareFieldsToSelect(array $projectionFields): string
    {
        $preparedFields = '';
        foreach ($projectionFields as $key => $field) {
            if (!is_numeric($key)) {
                $preparedFields .= $field.' as '.$key.', ';
            } else {
                $preparedFields .= Context::DEFAULT_ALIAS.'.'.$field.', ';
            }
        }

        return rtrim($preparedFields, ', ');
    }

    protected function toIterableWithClearEntityManagerCache(
        Query $query,
        ?array $projectionField = null,
        bool $scalarValue = false
    ): Generator {
        $nativeQueryBuilder = new NativeQueryBuilder();

        $parser = new Parser($query);
        $parser->parse();
        $resultSetMapping = $parser->getParserResult()->getResultSetMapping();

        // name has to start from letter
        $cursorName = 'Q'.md5($query->getSQL());

        try {
            // cursor requires open transactions
            $query->getEntityManager()->beginTransaction();

            $nativeQuery = $nativeQueryBuilder->createCursor($query, $cursorName);
            $nativeQuery->execute();

            $this->appLogger->info(sprintf('Scroll query prepared (cursor = %s)', $cursorName), ['query' => $query->getSQL()]);

            do {
                $nativeQuery = $nativeQueryBuilder->fetchCursor($query, $this->databaseCursorFetchSize, $resultSetMapping, $cursorName);
                $results = $nativeQuery->execute();

                if (null !== $projectionField) {
                    if (1 !== count($projectionField)) {
                        $results = array_chunk($results, count($projectionField), true);
                    }

                    $results = array_map(function ($result) use ($scalarValue) {
                        $ret = [];
                        foreach ($result as $resultValue) {
                            if (!is_array($resultValue)) {
                                return $scalarValue ? (string) $resultValue : $resultValue;
                            }
                            $ret[array_key_first($resultValue)] = $resultValue[array_key_first($resultValue)];
                        }

                        return $ret;
                    }, $results);
                }
                // Because "yield from" returns array with the same keys per package. If someone use the result in the
                // iterator_to_array function then only first package will be used (by default preserve keys for the
                // iterator_to_array is disabled.
                foreach ($results as $item) {
                    yield $item;
                }

                //Not all cache is cleared here (e.g., embedded objects). If you load a lot of data, remember to customize your cache garbage collector.
                $this->entityManager->clear($this->getClass()); // @phpstan-ignore-line
            } while (!empty($results));

            $nativeQuery = $nativeQueryBuilder->closeCursor($query, $cursorName);
            $nativeQuery->execute();

            $query->getEntityManager()->commit();
        } catch (\Throwable $exception) {
            $this->appLogger->error(sprintf('Failed iterable query in %s: %s', get_class($this), $exception->getMessage()), ['query' => $query->getSQL()]);

            throw $exception;
        }
    }

    /**
     * @throws NotSupportedUpdatedAtField
     * @throws Exception
     */
    public function getMaxUpdatedAtDate(): ?DateTimeImmutable
    {
        if (false === is_subclass_of($this->getClass(), TimestampableInterface::class)) {
            throw new NotSupportedUpdatedAtField('The entity does not support the updatedAt field.');
        }

        $qb = $this->repository->createQueryBuilder(Context::DEFAULT_ALIAS);
        $qb->select(\sprintf('MAX(%s.updatedAt) as max_date', Context::DEFAULT_ALIAS));

        $result = $qb->getQuery()->getResult(AbstractQuery::HYDRATE_SINGLE_SCALAR);

        return null === $result ? $result : new DateTimeImmutable($result);
    }
}
