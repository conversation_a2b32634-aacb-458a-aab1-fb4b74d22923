<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository;

use Doctrine\ORM\Query;
use Psr\Log\LoggerInterface;

final readonly class CountEstimator
{
    public function __construct(
        private LoggerInterface $appLogger,
        private NativeQueryBuilder $nativeQueryBuilder = new NativeQueryBuilder()
    ) {
    }

    public function estimate(Query $query): ?int
    {
        $dql = null;
        try {
            $dql = $query->getDQL();
            $statement = $this->nativeQueryBuilder->countEstimate($query);

            return $statement->getSingleScalarResult();
        } catch (\Throwable $ex) {
            $this->appLogger->warning(sprintf('Estimation count failed %s', $ex->getMessage()), [
                'exception' => $ex->getTraceAsString(),
                'dql' => $dql,
            ]);

            return null;
        }
    }
}
