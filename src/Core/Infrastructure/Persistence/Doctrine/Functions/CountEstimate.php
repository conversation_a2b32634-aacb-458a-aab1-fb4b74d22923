<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Functions;

use Doctrine\ORM\Query\AST\Functions\FunctionNode;
use Doctrine\ORM\Query\Lexer;
use Doctrine\ORM\Query\Parser;
use Doctrine\ORM\Query\SqlWalker;

class CountEstimate extends FunctionNode
{
    public $subselect;

    public function getSql(SqlWalker $sqlWalker): string
    {
        return sprintf('count_estimate(\'%s\')', $sqlWalker->walkSelectStatement($this->subselect));
    }

    public function parse(Parser $parser): void
    {
        $parser->match(Lexer::T_IDENTIFIER);
        $parser->match(Lexer::T_OPEN_PARENTHESIS);

        $this->subselect = $parser->SelectStatement();

        $parser->match(Lexer::T_CLOSE_PARENTHESIS);
    }
}
