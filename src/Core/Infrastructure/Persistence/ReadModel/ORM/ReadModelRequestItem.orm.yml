OpenLoyalty\Core\Domain\ReadModel\Entity\ReadModelRequestItem:
    type: entity
    table: read_model_request_item
    indexes:
        createdAtReadModeRequestNewIdx:
            columns: [ store_id, created_at ]
        processingOnReadModeRequestNewIdx:
            columns: [ store_id, processing_on ]
        requestUniqueReadModeRequestNewIdx:
            columns: [ store_id, request_unique ]
        requestStartProcessingAfterIdx:
          columns: [ store_id, start_processing_after ]
    id:
        id:
            type: read_model_request_id
    fields:
        storeId:
            type: store_id
        requestUnique:
            type: string
            length: 128
        requestContent:
            type: text
        requestContentType:
            type: string
            length: 255
        createdAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        updatedAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        processingOn:
            type: datetime_immutable_microseconds
            nullable: true
        startProcessingAfter:
          type: datetime_immutable_microseconds
          nullable: true