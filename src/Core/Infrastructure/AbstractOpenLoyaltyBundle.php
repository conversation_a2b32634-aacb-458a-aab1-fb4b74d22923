<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure;

use Doctrine\DBAL\Types\Type;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\DoctrineTypeServiceProvider;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\SerializedJsonDoctrineType;
use Symfony\Component\HttpKernel\Bundle\Bundle;

abstract class AbstractOpenLoyaltyBundle extends Bundle
{
    /**
     * @var array<string, class-string<Type>>
     */
    private array $serializedDoctrineTypes;

    /**
     * @param array<string, class-string<Type>> $serializedDoctrineTypes
     */
    public function __construct(array $serializedDoctrineTypes)
    {
        $this->serializedDoctrineTypes = $serializedDoctrineTypes;

        foreach ($this->serializedDoctrineTypes as $type => $class) {
            if (!Type::hasType($type)) {
                Type::addType($type, $class);
            }
        }
    }

    public function boot(): void
    {
        parent::boot();

        if (null === $this->container) {
            throw new \RuntimeException('Can not initialize the bundle without a container');
        }

        foreach ($this->serializedDoctrineTypes as $type => $class) {
            /** @var SerializedJsonDoctrineType $type */
            $type = Type::getType($type);
            /** @var DoctrineTypeServiceProvider $doctrineTypeServiceProvider */
            $doctrineTypeServiceProvider = $this->container->get(DoctrineTypeServiceProvider::class);
            $type->setServiceProvider($doctrineTypeServiceProvider);
        }
    }
}
