<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Metrics;

final readonly class GrowingTablesMetricsGroup extends TablesMetricsGroup
{
    public function getGroup(): string
    {
        return 'table.growing';
    }

    protected function getName(): string
    {
        return 'growing';
    }

    protected function getTablesName(): array
    {
        /* candidates for retention policy */
        return [
            'account',
            'achievement_member_progress',
            'achievement_member_progress_rule_item',
            'activation_code',
            'campaign_code',
            'coupon',
            'custom_event',
            'customer',
            'customer_history',
            'export',
            'group_of_values',
            'value',
            'import',
            'issued_reward',
            'referral',
            'segment_customers',
            'tier_set_member_progress',
            'tier_set_member_progress_condition',
            'transaction',
            'transaction_item',
            'transfer',
            'transfer_expiring',
            'user',
            'wallet',
            'campaign_execution',
            'campaign_time_request',
            'import_item',
            'audit',
            'billable_report_item',
            'campaign_read_model',
            'member_campaign_usages_read_model',
            'transaction_read_model',
            'wallet_read_model',
            'failed_messages',
            'failed_webhook_messages',
            'migration_versions',
            'read_model_request',
            'read_model_request_item',
            'settings',
        ];
    }
}
