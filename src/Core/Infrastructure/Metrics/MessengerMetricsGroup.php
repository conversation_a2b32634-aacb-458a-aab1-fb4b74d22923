<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Metrics;

use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\MetricsRepository;

final readonly class MessengerMetricsGroup implements MetricsGroupInterface
{
    public function __construct(
        private MetricsRepository $metricsRepository
    ) {
    }

    public function getGroup(): string
    {
        return 'messenger';
    }

    public function getMetrics(): iterable
    {
        $failedMessages = $this->metricsRepository->getFailedMessages();
        $webhookFailedMessages = $this->metricsRepository->getFailedWebhookMessages();

        yield MetricItem::event('oloy_metric_messenger_failed_messages', [
            'default' => $failedMessages,
            'webhook' => $webhookFailedMessages,
        ]);
    }
}
