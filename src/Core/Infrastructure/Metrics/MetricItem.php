<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Infrastructure\Metrics;

final readonly class MetricItem
{
    protected function __construct(
        private array $attributes,
        private bool $metric,
        private string $name
    ) {
    }

    public static function metric(string $name, float $value): self
    {
        return new self([$name => $value], true, $name);
    }

    public static function event(string $name, array $attributes): self
    {
        return new self($attributes, false, $name);
    }

    public function isMetric(): bool
    {
        return $this->metric;
    }

    public function getAttributes(): array
    {
        return $this->attributes;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getValue(): float
    {
        if (!$this->isMetric()) {
            throw new \LogicException('Only metric is supported');
        }

        return $this->attributes[$this->name] ?? 0.0;
    }
}
