<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="en" target-language="en" datatype="plaintext" original="file.ext">
        <body>
            <trans-unit id="core.invalid_condition">
                <source>core.invalid_condition</source>
                <target>Invalid condition (operator or data).</target>
            </trans-unit>
            <trans-unit id="core.invalid_expression_function">
                <source>core.invalid_expression_function</source>
                <target>Function requires specific arguments which was not supplied.</target>
            </trans-unit>
            <trans-unit id="core.cache_is_not_ready">
                <source>core.cache_is_not_ready</source>
                <target>Cache is not ready, please try again in number_of_seconds seconds.</target>
            </trans-unit>
            <trans-unit id="core.order_by_validation.must_be_array">
                <source>core.order_by_validation.must_be_array</source>
                <target>The field must be an associative array.</target>
            </trans-unit>
            <trans-unit id="core.date_range.min_date">
                <source>core.date_range.min_date</source>
                <target>Date should not be before {{ min_date }}</target>
            </trans-unit>
            <trans-unit id="core.date_range.max_date">
                <source>core.date_range.max_date</source>
                <target>Date should not be after {{ max_date }}</target>
            </trans-unit>
            <trans-unit id="core.search.max_count_of_ids">
                <source>core.search.max_count_of_ids</source>
                <target>You can get max %maxCount% of ids in single request</target>
            </trans-unit>
            <trans-unit id="core.int_criteria_range.min_value">
                <source>core.int_criteria_range.min_value</source>
                <target>This value should be greater than or equal to {{ min_value }}</target>
            </trans-unit>
            <trans-unit id="core.int_criteria_range.max_value">
                <source>core.int_criteria_range.max_value</source>
                <target>This value should be less than or equal to {{ max_value }}</target>
            </trans-unit>
        </body>
    </file>
</xliff>
