services:

    _defaults:
        autowire: true
        autoconfigure: true
        public: true

    OpenLoyalty\Core\Infrastructure\Message\CommandBus: ~
    OpenLoyalty\Core\Infrastructure\Message\DomainEventBus:
    OpenLoyalty\Core\Infrastructure\Message\EventBus: ~
    OpenLoyalty\Core\Infrastructure\Message\JobBus: ~
    OpenLoyalty\DataAnalytics\Infrastructure\Shared\Message\AnalyticsEventBus: ~
    OpenLoyalty\Core\Infrastructure\PublicEvent\Message\PublicEventBus: ~

    OpenLoyalty\Core\Domain\Message\CommandBusInterface: '@OpenLoyalty\Core\Infrastructure\Message\CommandBus'
    OpenLoyalty\Core\Domain\Message\DomainEventBusInterface: '@OpenLoyalty\Core\Infrastructure\Message\DomainEventBus'
    OpenLoyalty\Core\Domain\Message\EventBusInterface: '@OpenLoyalty\Core\Infrastructure\Message\EventBus'
    OpenLoyalty\Core\Domain\Message\JobBusInterface: '@OpenLoyalty\Core\Infrastructure\Message\JobBus'
    OpenLoyalty\Core\Application\PublicEvent\Message\PublicEventBusInterface: '@OpenLoyalty\Core\Infrastructure\PublicEvent\Message\PublicEventBus'

    OpenLoyalty\Core\Infrastructure\EventSubscriber\LongPoolingSafeStopWorkerOnSignalSubscriber:
        arguments:
            $waitBeforeStopSec: '%env(int:MESSENGER_WAIT_BEFORE_STOP_WORKER)%'
        tags:
            - { name: 'kernel.event_subscriber' }