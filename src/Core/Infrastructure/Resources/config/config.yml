imports:
    - { resource: "@OpenLoyaltyAccountBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyAnalyticsBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyAuditBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyCampaignBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyRewardBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyMessagingBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyLevelBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyPointsBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyChannelBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltySegmentBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltySettingsBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyTransactionBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyTranslationBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyUserBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyImportBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyCustomEventBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyAchievementBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyDataAnalyticsBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyExportBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyGroupOfValuesBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyToolsBundle/Resources/config/config.yml"}
    - { resource: "@OpenLoyaltyLeaderboardBundle/Resources/config/config.yml"}

open_loyalty_core:
    page_field_name: page
    per_page_field_name: perPage
    sort_field_name: sort
    sort_direction_field_name: direction
    per_page_default: 10

doctrine:
    orm:
        entity_managers:
            default:
                dql:
                    string_functions:
                        TYPE: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Functions\Type
                        DATE: DoctrineExtensions\Query\Postgresql\Date
                        RANDOM: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Functions\Random
                        DATE_FORMAT: DoctrineExtensions\Query\Postgresql\DateFormat
                        DATE_PART: DoctrineExtensions\Query\Postgresql\DatePart
                        CAST: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Functions\Cast
                        COUNT_ESTIMATE: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Functions\CountEstimate
                        ILIKE: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Functions\ILike
                        GREATEST: DoctrineExtensions\Query\Postgresql\Greatest
                        JSON_GET_TEXT: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Postgresql\JsonGetText
            readmodel:
                dql:
                    string_functions:
                        TYPE: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Functions\Type
                        DATE: DoctrineExtensions\Query\Postgresql\Date
                        RANDOM: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Functions\Random
                        DATE_FORMAT: DoctrineExtensions\Query\Postgresql\DateFormat
                        DATE_PART: DoctrineExtensions\Query\Postgresql\DatePart
                        CAST: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Functions\Cast
                        COUNT_ESTIMATE: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Functions\CountEstimate
                        ILIKE: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Functions\ILike
                        GREATEST: DoctrineExtensions\Query\Postgresql\Greatest
                mappings:
                    OpenLoyaltyCore:
                        type: yml
                        dir: '%kernel.project_dir%/src/Core/Infrastructure/Persistence/Doctrine/ORM'
                        is_bundle: false
                        prefix: OpenLoyalty\Core\Domain
    dbal:
        types:
            datetime_immutable_microseconds: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\DateTimeImmutableMicrosecondsType
            datetime_microseconds: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\DateTimeMicrosecondsType
            sku: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\SKUDoctrineType
            labels_json_array: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\LabelsJsonArrayDoctrineType
            store_id: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\StoreIdDoctrineType
            points: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\PointsDoctrineType
            extended_json: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\ExtendedJsonDoctrineType
            billable_report_item_id: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\BillableReportItemIdDoctrineType
            billable_members_per_tenant_id: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\BillableMembersPerTenantIdDoctrineType
            billable_transactions_per_tenant_id: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\BillableTransactionsPerTenantIdDoctrineType
            json_t: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\JsonType
            read_model_request_id: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\ReadModelRequestIdDoctrineType
            uuid_store_id: OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\UuidStoreIdDoctrineType

jms_serializer:
    property_naming:
        id: 'JMS\Serializer\Naming\IdenticalPropertyNamingStrategy'
    metadata:
        directories:
            Domain:
                namespace_prefix: "OpenLoyalty\\Core\\Domain"
                path: "@OpenLoyaltyCoreBundle/Resources/config/serializer"

knp_gaufrette:
    adapters:
        import_file_s3:
            aws_s3:
                service_id: Aws\S3\S3Client
                bucket_name: "%amazon_s3.bucket_name_private%"
                detect_content_type: true
                options:
                    create: true
                    directory: "import_file_local"
        export_file_s3:
            aws_s3:
                service_id: Aws\S3\S3Client
                bucket_name: "%amazon_s3.bucket_name_private%"
                detect_content_type: true
                options:
                    create: true
                    directory: "export_file_local"
        archives_s3:
            aws_s3:
                service_id: Aws\S3\S3Client
                bucket_name: "%amazon_s3.bucket_name_archives%"
                detect_content_type: true
                options:
                    create: true
                    directory: "archives"
        analytics_s3:
            aws_s3:
                service_id: Aws\S3\S3Client
                bucket_name: "%amazon_s3.bucket_name_analytics%"
                detect_content_type: true
                options:
                    create: true
                    directory: "analytics"
        import_file_local:
            local:
                directory: "%kernel.project_dir%/var/import/"
                create:     true
        export_file_local:
            local:
                directory: "%kernel.project_dir%/var/export/"
                create:     true
        archives_local:
            local:
                directory: "%kernel.project_dir%/var/archives/"
                create:     true
        analytics_local:
            local:
                directory: "%kernel.project_dir%/var/analytics/"
                create: true
    filesystems:
        import:
            adapter: "%adapter_import_file%"
            alias: import_filesystem
        import_scheduled:
            adapter: "%adapter_import_scheduled%"
            alias: scheduled_import_filesystem
        export:
            adapter: "%adapter_export_file%"
            alias: export_filesystem
        archives:
            adapter: "%adapter_archives%"
            alias: archives_filesystem
        analytics:
            adapter: "%adapter_analytics%"
            alias: analytics_filesystem
        import_s3:
            adapter: "%adapter_import_file_s3%"
            alias: import_filesystem_s3
        import_scheduled_s3:
            adapter: "%adapter_import_scheduled_s3%"
            alias: scheduled_import_filesystem_s3
        export_s3:
            adapter: "%adapter_export_file_s3%"
            alias: export_filesystem_s3
        archives_s3:
            adapter: "%adapter_archives_s3%"
            alias: archives_filesystem_s3
        analytics_s3:
            adapter: "%adapter_analytics_s3%"
            alias: analytics_filesystem_s3
    stream_wrapper:
        filesystems:
            - export
            - import
            - import_scheduled
            - archives
            - analytics
            - analytics_s3
            - archives_s3
            - export_s3
