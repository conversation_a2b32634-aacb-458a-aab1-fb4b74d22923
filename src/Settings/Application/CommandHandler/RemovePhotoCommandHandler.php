<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Application\CommandHandler;

use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Settings\Application\Command\RemovePhoto;
use OpenLoyalty\Settings\Domain\Exception\InvalidPhotoNameException;
use OpenLoyalty\Settings\Domain\SystemEvent\PhotoRemovedSystemEvent;
use OpenLoyalty\Settings\Infrastructure\Entity\SettingsEntry;
use OpenLoyalty\Settings\Infrastructure\Model\Logo;
use OpenLoyalty\Settings\Infrastructure\Service\LogoUploader;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;
use Symfony\Contracts\Translation\TranslatorInterface;

class RemovePhotoCommandHandler implements CommandHandlerInterface
{
    private EventBusInterface $eventBus;
    private LogoUploader $uploader;
    private SettingsManager $settingsManager;
    private TranslatorInterface $translator;
    private StoreRepository $storeRepository;

    public function __construct(
        EventBusInterface $eventBus,
        LogoUploader $uploader,
        SettingsManager $settingsManager,
        TranslatorInterface $translator,
        StoreRepository $storeRepository
    ) {
        $this->eventBus = $eventBus;
        $this->uploader = $uploader;
        $this->settingsManager = $settingsManager;
        $this->translator = $translator;
        $this->storeRepository = $storeRepository;
    }

    public function __invoke(RemovePhoto $command): void
    {
        $store = $this->storeRepository->byCode($command->getStoreCode());
        $name = $command->getName();

        if (false === $this->uploader->isValidName($name)) {
            throw new InvalidPhotoNameException(sprintf($this->translator->trans('Invalid photo "%s" name'), $name));
        }

        $settings = $this->settingsManager->getSettings($store);
        $entry = $settings->getEntry($name);
        if ($entry instanceof SettingsEntry) {
            $photo = $entry->getValue();
            if ($photo instanceof Logo) {
                $this->uploader->remove($photo);
            }
            $this->settingsManager->removeSettingByKey($name, $store);
        }

        $this->eventBus->dispatch(
            new PhotoRemovedSystemEvent($name)
        );
    }
}
