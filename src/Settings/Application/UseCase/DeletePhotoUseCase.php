<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Application\UseCase;

use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Settings\Application\Command\RemovePhoto;

class DeletePhotoUseCase
{
    /**
     * @var \OpenLoyalty\Core\Domain\Message\CommandBusInterface
     */
    private $commandBus;

    public function __construct(CommandBusInterface $commandBus)
    {
        $this->commandBus = $commandBus;
    }

    public function execute(Store $store, string $name): void
    {
        $removePhotoCommand = new RemovePhoto($store->getCode(), $name);
        $this->commandBus->dispatch($removePhotoCommand);
    }
}
