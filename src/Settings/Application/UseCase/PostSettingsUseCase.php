<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Application\UseCase;

use OpenLoyalty\Audit\Application\Service\AuditManagerInterface;
use OpenLoyalty\Audit\Domain\SecurityAuditLog;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Settings\Infrastructure\Exception\AlreadyExistException;
use OpenLoyalty\Settings\Infrastructure\Model\Settings;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;

class PostSettingsUseCase
{
    /**
     * @var SettingsManager
     */
    private $settingsManager;

    /**
     * @var AuditManagerInterface
     */
    private $auditManager;

    public function __construct(
        SettingsManager $settingsManager,
        AuditManagerInterface $auditManager
    ) {
        $this->settingsManager = $settingsManager;
        $this->auditManager = $auditManager;
    }

    /**
     * @throws AlreadyExistException
     */
    public function execute(Settings $data, Store $store): void
    {
        $this->settingsManager->removeAll($store);
        $this->settingsManager->save($data);

        $this->auditManager->auditAdminEvent(
            SecurityAuditLog::UPDATE_SETTINGS_TYPE,
            SecurityAuditLog::SETTINGS_ENTITY_TYPE,
            null,
            $data->toArray()
        );
    }
}
