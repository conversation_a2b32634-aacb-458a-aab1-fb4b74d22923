<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Settings\Application\UseCase\PostSettingsUseCase;
use OpenLoyalty\Settings\Infrastructure\Exception\AlreadyExistException;
use OpenLoyalty\Settings\Infrastructure\Exception\CustomersIdentifierConfigurationInvalidException;
use OpenLoyalty\Settings\Infrastructure\Form\Type\SettingsFormType;
use OpenLoyalty\Settings\Infrastructure\Model\Settings;
use OpenLoyalty\Settings\Infrastructure\Security\Voter\TechnicalSettingsVoter;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;
use OpenLoyalty\Settings\Infrastructure\Validator\Constraints\CustomerIdentificationPriorityValidValidator;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

class PostSettings extends AbstractFOSRestController
{
    public function __construct(
        private readonly PostSettingsUseCase $useCase,
        private readonly FormFactoryInterface $formFactory,
        private readonly SettingsManager $settingsManager,
        private readonly StoreContextProviderInterface $storeContextProvider,
        private readonly TranslatorInterface $translator,
        private readonly CustomerIdentificationPriorityValidValidator $customerIdentificationPriorityValidValidator,
    ) {
    }

    /**
     * @Route(methods={"POST"}, name="oloy.settings.edit", path="/{storeCode}/settings")
     *
     * @Security("is_granted('EDIT_SETTINGS')")
     *
     * @Operation(
     *     tags={"Settings"},
     *     summary="Configure settings",
     *     operationId="settingsPost",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\RequestBody(
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="settings",
     *                     type="object",
     *                     @OA\Property(
     *                         property="programName",
     *                         type="string",
     *                         deprecated=true,
     *                     ),
     *                     @OA\Property(
     *                         property="programUrl",
     *                         type="string",
     *                         deprecated=true,
     *                     ),
     *                     @OA\Property(property="allowCustomersProfileEdits", type="boolean"),
     *                     @OA\Property(property="expirePointsNotificationDays", type="integer"),
     *                     @OA\Property(property="expireCouponsNotificationDays", type="integer"),
     *                     @OA\Property(property="expireLevelsNotificationDays", type="integer"),
     *                     @OA\Property(
     *                         property="customersIdentificationConfigurations",
     *                         type="array",
     *                         @OA\Items(ref=@Model(type=CustomersIdentificationConfiguration::class))
     *                     ),
     *                     @OA\Property(
     *                         property="tierAssignType",
     *                         type="string",
     *                         enum={"points", "transactions"}
     *                     ),
     *                     @OA\Property(
     *                         property="tierWalletCode",
     *                         type="string",
     *                     ),
     *                     @OA\Property(property="excludeDeliveryCostsFromTierAssignment", type="boolean"),
     *                     @OA\Property(
     *                         property="excludedDeliverySKUs",
     *                         type="array",
     *                         deprecated=true,
     *                         @OA\Items(type="string")
     *                     ),
     *                     @OA\Property(
     *                         property="excludedLevelSKUs",
     *                         type="array",
     *                         deprecated=true,
     *                         @OA\Items(type="string")
     *                     ),
     *                     @OA\Property(
     *                         property="excludedLevelCategories",
     *                         type="array",
     *                         @OA\Items(type="string")
     *                     ),
     *                     @OA\Property(
     *                         property="levelDowngradeMode",
     *                         type="string",
     *                         enum={"none", "automatic", "after_x_days"}
     *                     ),
     *                     @OA\Property(property="levelDowngradeDays", type="integer"),
     *                     @OA\Property(
     *                         property="levelDowngradeBase",
     *                         type="string",
     *                         enum={"none", "active_points", "earned_points", "earned_points_since_last_level_change"}
     *                     ),
     *                     @OA\Property(property="levelResetPointsOnDowngrade", type="boolean"),
     *                     @OA\Property(property="accountActivationRequired", type="boolean"),
     *                     @OA\Property(
     *                         property="identificationMethod",
     *                         type="string",
     *                         enum={"email", "phone", "loyaltyCardNumber"}
     *                     ),
     *                     @OA\Property(property="timezone", type="string", example="Europe/Warsaw"),
     *                     @OA\Property(property="rewardWalletCode", type="string"),
     *                     @OA\Property(
     *                         property="activeMember",
     *                         type="object",
     *                         @OA\Property(property="transactionInXDays", type="integer", description="Required if the customEventsInXDays field is not filled."),
     *                         @OA\Property(
     *                             property="customEventsInXDays",
     *                             type="object",
     *                             required={"days", "allEvents"},
     *                             description="Required if the transactionInXDays field is not filled.",
     *                             @OA\Property(property="days", type="integer"),
     *                             @OA\Property(property="allEvents", type="boolean"),
     *                             @OA\Property(
     *                                 property="eventTypes",
     *                                 type="array",
     *                                 @OA\Items(type="string")
     *                             )
     *                         ),
     *                         @OA\Property(
     *                             property="operator",
     *                             type="string",
     *                             description="Required if the customEventsInXDays and transactionInXDays fields are filled.",
     *                             enum={"and", "or"}
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Granted settings",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="settings",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\AdditionalProperties(type="string")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="400",
     *     description="Returned when form contains errors",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Request $request): View
    {
        $store = $this->storeContextProvider->getStore();
        $form = $this->formFactory->createNamed(
            'settings',
            SettingsFormType::class,
            $this->settingsManager->getSettings($store),
            [
                'method' => $request->getMethod(),
            ]
        );

        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        try {
            /** @var Settings $settings */
            $settings = $form->getData();
            $this->customerIdentificationPriorityValidValidator->validatePost($settings);
            $this->useCase->execute($settings, $store);

            $grantedSettings = array_filter($settings->toArray(), function ($code): bool {
                return $this->isGranted(TechnicalSettingsVoter::VIEW_SETTINGS_VALUE, $code);
            }, ARRAY_FILTER_USE_KEY);

            $this->settingsManager->flushCache();

            return $this->view(['settings' => $grantedSettings], Response::HTTP_OK);
        } catch (AlreadyExistException $exception) {
            $form->addError(new FormError($this->translator->trans($exception->getMessage())));
        } catch (CustomersIdentifierConfigurationInvalidException $exception) {
            $form->addError(new FormError($exception->getMessage()));
        }

        return $this->view($form, Response::HTTP_BAD_REQUEST);
    }
}
