<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Settings\Application\UseCase\GetPublicSettingsUseCase;
use OpenLoyalty\Settings\Infrastructure\Security\Voter\TechnicalSettingsVoter;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetList extends AbstractFOSRestController
{
    private SettingsManager $settingsManager;
    private GetPublicSettingsUseCase $useCase;
    private StoreContextProviderInterface $storeContextProvider;

    public function __construct(
        SettingsManager $settingsManager,
        GetPublicSettingsUseCase $useCase,
        StoreContextProviderInterface $storeContextProvider
    ) {
        $this->settingsManager = $settingsManager;
        $this->useCase = $useCase;
        $this->storeContextProvider = $storeContextProvider;
    }

    /**
     * @Route(methods={"GET"}, name="oloy.settings.get", path="/{storeCode}/settings")
     *
     * @Security("is_granted('VIEW_SETTINGS')")
     *
     * @Operation(
     *     tags={"Settings"},
     *     summary="Return settings",
     *     operationId="settingsGetList",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(
     *         name="public",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="boolean")
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="settings",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\AdditionalProperties(type="string")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Request $request): View
    {
        $store = $this->storeContextProvider->getStore();

        if (true === (bool) $request->get('public')) {
            $grantedSettings = $this->useCase->execute($store);
        } else {
            $settings = $this->settingsManager->getSettings($store)->toArray();
            $grantedSettings = array_filter($settings, function ($code): bool {
                return $this->isGranted(TechnicalSettingsVoter::VIEW_SETTINGS_VALUE, $code);
            }, ARRAY_FILTER_USE_KEY);
        }

        return $this->view([
            'settings' => $grantedSettings,
        ], Response::HTTP_OK);
    }
}
