<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Settings\Infrastructure\Provider\ChoicesProvider;

class GetChoices extends AbstractFOSRestController
{
    private ChoicesProvider $choicesProvider;

    public function __construct(ChoicesProvider $choicesProvider)
    {
        $this->choicesProvider = $choicesProvider;
    }

    /**
     * @Route(methods={"GET"}, name="oloy.settings.get_form_choices", path="/settings/choices/{type}")
     *
     * @Operation(
     *     tags={"Settings"},
     *     summary="Get available options",
     *     operationId="settingsGetChoices",
     *     @OA\Parameter(ref="#/components/parameters/choicesType"),
     *     @OA\Response(
     *         response="200",
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="choices",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\AdditionalProperties(type="string")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(string $type): View
    {
        $result = $this->choicesProvider->getChoices($type);

        if (empty($result)) {
            throw $this->createNotFoundException();
        }

        return $this->view($result);
    }
}
