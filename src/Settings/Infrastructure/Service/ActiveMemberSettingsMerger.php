<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Infrastructure\Service;

use Assert\Assertion as Assert;
use Assert\AssertionFailedException;
use OpenLoyalty\Settings\Infrastructure\Entity\JsonSettingEntry;
use OpenLoyalty\Settings\Infrastructure\Exception\ActiveMemberSettingNotSetException;
use OpenLoyalty\Settings\Infrastructure\Model\SettingsNames;

final class ActiveMemberSettingsMerger
{
    /**
     * @throws ActiveMemberSettingNotSetException
     */
    public function prepareActiveMemberSettings(JsonSettingEntry $currentSettings, JsonSettingEntry $newSetting): JsonSettingEntry
    {
        $currentJsonValue = $currentSettings->getValue();
        $newJsonValue = $newSetting->getValue();

        if (array_key_exists(SettingsNames::TRANSACTION_X_DAYS->value, $newJsonValue)) {
            return $newSetting;
        }

        $valueBody = [
            'days' => '',
            'eventTypes' => [],
            'allEvents' => false,
        ];

        try {
            if (array_key_exists(SettingsNames::CUSTOM_EVENTS_X_DAYS->value, $currentJsonValue)) {
                $currentActiveMemberSettingsBody = $currentJsonValue[SettingsNames::CUSTOM_EVENTS_X_DAYS->value];
                $newActiveMemberSettingBody = $newJsonValue[SettingsNames::CUSTOM_EVENTS_X_DAYS->value];

                $valueBody['days'] = array_key_exists('days', $newActiveMemberSettingBody) ? $newActiveMemberSettingBody['days'] : $currentActiveMemberSettingsBody['days'];
                $valueBody['eventTypes'] = array_key_exists('eventTypes', $newActiveMemberSettingBody) ? $newActiveMemberSettingBody['eventTypes'] : $currentActiveMemberSettingsBody['eventTypes'];
                $valueBody['allEvents'] = array_key_exists('allEvents', $newActiveMemberSettingBody) ? $newActiveMemberSettingBody['allEvents'] : $currentActiveMemberSettingsBody['allEvents'];
            }

            if (array_key_exists(SettingsNames::TRANSACTION_X_DAYS->value, $currentJsonValue)) {
                $newActiveMemberSettingBody = $newJsonValue[SettingsNames::CUSTOM_EVENTS_X_DAYS->value];

                Assert::keyExists($newActiveMemberSettingBody, 'days', 'Property Days, must be set for Customer events in X days setting');
                $valueBody['days'] = $newActiveMemberSettingBody['days'];

                Assert::keyExists($newActiveMemberSettingBody, 'allEvents', 'Property AllEvents, must be set for Customer events in X days setting');
                $valueBody['allEvents'] = $newActiveMemberSettingBody['allEvents'];

                if (true !== $valueBody['allEvents']) {
                    Assert::keyExists($newActiveMemberSettingBody, 'eventTypes', 'Property EventTypes, must be set for Customer events in X days setting');
                    $valueBody['eventTypes'] = $newActiveMemberSettingBody['eventTypes'];
                } else {
                    $valueBody['eventTypes'] = [];
                }
            }

            return new JsonSettingEntry(SettingsNames::ACTIVE_MEMBER->value, [SettingsNames::CUSTOM_EVENTS_X_DAYS->value => $valueBody], $currentSettings->getStore());
        } catch (AssertionFailedException $exception) {
            throw new ActiveMemberSettingNotSetException($exception->getMessage());
        }
    }
}
