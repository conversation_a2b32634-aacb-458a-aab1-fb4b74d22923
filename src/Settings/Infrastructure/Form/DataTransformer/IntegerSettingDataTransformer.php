<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Settings\Infrastructure\Form\DataTransformer;

use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Settings\Infrastructure\Entity\IntegerSettingEntry;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;
use Symfony\Component\Form\DataTransformerInterface;

/**
 * Class IntegerSettingDataTransformer.
 *
 * @implements DataTransformerInterface<mixed, mixed>
 */
class IntegerSettingDataTransformer implements DataTransformerInterface
{
    /**
     * @var string
     */
    protected $key;

    /**
     * @var SettingsManager
     */
    protected $settingsManager;

    /**
     * @var StoreContextProviderInterface
     */
    protected $storeContextProvider;

    public function __construct(string $key, SettingsManager $settingsManager, StoreContextProviderInterface $storeContextProvider)
    {
        $this->key = $key;
        $this->settingsManager = $settingsManager;
        $this->storeContextProvider = $storeContextProvider;
    }

    /**
     * {@inheritdoc}
     */
    public function transform(mixed $value)
    {
        if (null == $value) {
            return;
        }
        if (!$value instanceof IntegerSettingEntry) {
            throw new \InvalidArgumentException(sprintf('Value %s is not an instance of %s', $value, IntegerSettingEntry::class));
        }

        return $value->getValue();
    }

    /**
     * {@inheritdoc}
     */
    public function reverseTransform(mixed $value)
    {
        $store = $this->storeContextProvider->getStore();

        return new IntegerSettingEntry($this->key, $value, $store);
    }
}
