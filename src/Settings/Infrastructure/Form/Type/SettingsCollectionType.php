<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Settings\Infrastructure\Form\Type;

use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Settings\Infrastructure\Form\DataTransformer\ChoicesToJsonSettingDataTransformer;
use OpenLoyalty\Settings\Infrastructure\Form\DataTransformer\StringSettingDataTransformer;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Class SettingsCollectionType.
 */
class SettingsCollectionType extends AbstractType
{
    /**
     * @var SettingsManager
     */
    private $settingsManager;

    /**
     * @var StoreContextProviderInterface
     */
    protected $storeContextProvider;

    public function __construct(SettingsManager $settingsManager, StoreContextProviderInterface $storeContextProvider)
    {
        $this->settingsManager = $settingsManager;
        $this->storeContextProvider = $storeContextProvider;
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        if (array_key_exists('transformTo', $options)) {
            switch ($options['transformTo']) {
                case 'json':
                    $builder->addModelTransformer(
                        new ChoicesToJsonSettingDataTransformer($builder->getName(), $this->settingsManager, $this->storeContextProvider)
                    );
                    break;
                case 'string':
                default:
                    $builder->addModelTransformer(
                        new StringSettingDataTransformer($builder->getName(), $this->settingsManager, $this->storeContextProvider)
                    );
                    break;
            }
        }
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);

        $resolver->setDefined('transformTo');
        $resolver->setAllowedTypes('transformTo', ['null', 'string']);
        $resolver->addAllowedValues('transformTo', ['string', 'json']);
        $resolver->setDefaults(['transformTo' => 'string']);
    }

    /**
     * {@inheritdoc}
     */
    public function getParent()
    {
        return CollectionType::class;
    }
}
