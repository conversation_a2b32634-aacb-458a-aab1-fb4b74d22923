<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Settings\Infrastructure\Form\Type;

use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Settings\Infrastructure\Form\DataTransformer\StringSettingDataTransformer;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;

/**
 * Class SettingsTextType.
 */
class SettingsTextType extends AbstractType
{
    /**
     * @var SettingsManager
     */
    private $settingsManager;

    /**
     * @var StoreContextProviderInterface
     */
    protected $storeContextProvider;

    public function __construct(SettingsManager $settingsManager, StoreContextProviderInterface $storeContextProvider)
    {
        $this->settingsManager = $settingsManager;
        $this->storeContextProvider = $storeContextProvider;
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->addModelTransformer(new StringSettingDataTransformer(
            $builder->getName(),
            $this->settingsManager,
            $this->storeContextProvider
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getParent()
    {
        return TextType::class;
    }
}
