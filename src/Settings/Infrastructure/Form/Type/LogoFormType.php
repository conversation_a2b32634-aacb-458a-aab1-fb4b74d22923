<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Infrastructure\Form\Type;

use OpenLoyalty\Settings\Infrastructure\Model\Logo;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Constraints\NotBlank;

class LogoFormType extends AbstractType
{
    protected string $allowedMaxSize = '2M';
    protected array $allowedMimeTypes = [
        'image/png',
        'image/jpg',
        'image/jpeg',
        'image/gif',
    ];
    protected int $allowedMaxWidth = 2560;
    protected int $allowedMaxHeight = 1440;

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('file', FileType::class, [
            'required' => true,
            'documentation' => [
                'type' => 'object',
            ],
            'constraints' => [
                new NotBlank(),
                new Assert\File([
                    'maxSize' => $options['allowedMaxSize'],
                    'mimeTypes' => $options['allowedMimeTypes'],
                ]),
                new Assert\Image([
                    'maxWidth' => $options['allowedMaxWidth'],
                    'maxHeight' => $options['allowedMaxHeight'],
                    'mimeTypes' => $options['allowedMimeTypes'],
                ]),
            ],
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Logo::class,
            'allowedMimeTypes' => $this->allowedMimeTypes,
            'allowedMaxSize' => $this->allowedMaxSize,
            'allowedMaxHeight' => $this->allowedMaxHeight,
            'allowedMaxWidth' => $this->allowedMaxWidth,
        ]);
    }
}
