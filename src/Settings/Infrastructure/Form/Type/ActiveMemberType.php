<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Infrastructure\Form\Type;

use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Settings\Domain\ActiveMemberSettings;
use OpenLoyalty\Settings\Infrastructure\Form\DataTransformer\ChoicesToJsonSettingDataTransformer;
use OpenLoyalty\Settings\Infrastructure\Form\Type\ActiveMember\CustomEventType;
use OpenLoyalty\Settings\Infrastructure\Model\SettingsNames;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Exception\TransformationFailedException;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Range;

final class ActiveMemberType extends AbstractType
{
    public function __construct(
        private readonly SettingsManager $settingsManager,
        private readonly StoreContextProviderInterface $storeContextProvider
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->addEventListener(
            FormEvents::PRE_SUBMIT,
            function (FormEvent $event): void {
                $form = $event->getForm();
                $data = $event->getData();

                if (Request::METHOD_PATCH === $form->getConfig()->getOption('method')) {
                    $data = array_merge($form->getNormData() ?? [], $data);
                } else {
                    $form->setData(null);
                }

                if (
                    !$data &&
                    !isset($data[SettingsNames::TRANSACTION_X_DAYS->value]) &&
                    !isset($data[SettingsNames::CUSTOM_EVENTS_X_DAYS->value])
                ) {
                    throw new TransformationFailedException('Either '.SettingsNames::TRANSACTION_X_DAYS->value.' or '.SettingsNames::CUSTOM_EVENTS_X_DAYS->value.' must be set', 0, null, 'settings.active_member.not_empty', ['{{ transactionInXDays }}' => SettingsNames::TRANSACTION_X_DAYS->value, '{{ customEventsInXDays }}' => SettingsNames::CUSTOM_EVENTS_X_DAYS->value]);
                }

                if (isset($data[SettingsNames::TRANSACTION_X_DAYS->value])) {
                    $form->add(SettingsNames::TRANSACTION_X_DAYS->value, IntegerType::class, [
                        'required' => true,
                        'constraints' => [
                            new Range(['min' => 1, 'max' => ActiveMemberSettings::FIVE_YEARS_IN_DAYS]),
                            new NotBlank(),
                        ],
                    ]);
                }

                if (isset($data[SettingsNames::CUSTOM_EVENTS_X_DAYS->value])) {
                    $form->add(SettingsNames::CUSTOM_EVENTS_X_DAYS->value, CustomEventType::class, [
                        'required' => false,
                    ]);
                }

                if (isset(
                    $data[SettingsNames::TRANSACTION_X_DAYS->value],
                    $data[SettingsNames::CUSTOM_EVENTS_X_DAYS->value]
                )) {
                    if (!isset($data['operator'])) {
                        $data['operator'] = null;
                        $event->setData($data);
                    }

                    $form->add('operator', ChoiceType::class, [
                        'required' => true,
                        'choices' => [
                            'and' => 'and',
                            'or' => 'or',
                        ],
                        'constraints' => [
                            new NotBlank(),
                        ],
                    ]);
                }
            }
        );

        $builder->addModelTransformer(
            new ChoicesToJsonSettingDataTransformer(
                SettingsNames::ACTIVE_MEMBER->value,
                $this->settingsManager,
                $this->storeContextProvider
            )
        );
    }
}
