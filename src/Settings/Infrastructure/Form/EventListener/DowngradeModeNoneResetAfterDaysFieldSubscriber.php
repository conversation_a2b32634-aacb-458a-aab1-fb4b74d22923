<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Infrastructure\Form\EventListener;

use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Settings\Infrastructure\Entity\BooleanSettingEntry;
use OpenLoyalty\Settings\Infrastructure\Entity\IntegerSettingEntry;
use OpenLoyalty\Settings\Infrastructure\Entity\StringSettingEntry;
use OpenLoyalty\Settings\Infrastructure\Model\Settings;
use OpenLoyalty\Settings\Infrastructure\Model\SettingsNames;
use OpenLoyalty\User\Domain\Provider\TierAssignTypeProvider;
use OpenLoyalty\User\Infrastructure\LevelDowngradeModeProvider;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;

/**
 * Class DowngradeModeNoneResetAfterDaysFieldSubscriber.
 */
class DowngradeModeNoneResetAfterDaysFieldSubscriber implements EventSubscriberInterface
{
    /**
     * @var StoreContextProviderInterface
     */
    private $storeContextProvider;

    public function __construct(StoreContextProviderInterface $storeContextProvider)
    {
        $this->storeContextProvider = $storeContextProvider;
    }

    /**
     * {@inheritdoc}
     */
    public static function getSubscribedEvents(): array
    {
        return [
            FormEvents::SUBMIT => '__invoke',
        ];
    }

    public function __invoke(FormEvent $event): void
    {
        $store = $this->storeContextProvider->getStore();
        $data = $event->getData();
        if (!$data instanceof Settings) {
            return;
        }

        $tierAssignType = $data->getEntry(SettingsNames::TIER_ASSIGN_TYPE_SETTINGS_KEY->value);

        if (TierAssignTypeProvider::TYPE_TRANSACTIONS === $tierAssignType?->getValue()) {
            $data->addEntry(
                new StringSettingEntry(
                    SettingsNames::LEVEL_DOWNGRADE_MODE->value,
                    LevelDowngradeModeProvider::MODE_NONE,
                    $store
                )
            );
            $this->resetSettings($data, $store);
        }

        $levelDowngradeMode = $data->getEntry(SettingsNames::LEVEL_DOWNGRADE_MODE->value);
        $value = $levelDowngradeMode?->getValue();

        if (LevelDowngradeModeProvider::MODE_X_DAYS !== $value) {
            $this->resetSettings($data, $store);
        }

        $event->setData($data);
    }

    private function resetSettings(Settings $data, Store $store): void
    {
        $data->addEntry(
            new IntegerSettingEntry(
                SettingsNames::LEVEL_DOWNGRADE_DAYS->value,
                '',
                $store
            )
        );
        $data->addEntry(
            new StringSettingEntry(
                SettingsNames::LEVEL_DOWNGRADE_BASE->value,
                LevelDowngradeModeProvider::BASE_ACTIVE_POINTS,
                $store
            )
        );
        $data->addEntry(
            new BooleanSettingEntry(
                SettingsNames::LEVEL_RESET_POINTS_ON_DOWNGRADE->value,
                false,
                $store
            )
        );
    }
}
