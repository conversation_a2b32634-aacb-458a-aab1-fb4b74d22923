<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Infrastructure\Model;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Validator\Constraints as Assert;

class Logo implements FileInterface
{
    /**
     * @var string
     */
    protected $path;

    /**
     * @var string
     */
    protected $originalName;

    /**
     * @var string
     */
    protected $mime;

    /**
     * @var UploadedFile
     * @Assert\NotBlank()
     * @Assert\File(
     *     mimeTypes={"image/png", "image/jpeg", "image/svg+xml", "text/html"},
     *     maxSize="2M"
     * )
     */
    protected $file;

    public function getFile(): ?UploadedFile
    {
        return $this->file;
    }

    public function setFile(UploadedFile $file): void
    {
        $this->file = $file;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function setPath(string $path): void
    {
        $this->path = $path;
    }

    public function getOriginalName(): ?string
    {
        return $this->originalName;
    }

    public function setOriginalName(string $originalName): void
    {
        $this->originalName = $originalName;
    }

    public function getMime(): ?string
    {
        return $this->mime;
    }

    public function setMime(string $mime): void
    {
        $this->mime = $mime;
    }

    /**
     * {@inheritdoc}
     */
    public static function deserialize(array $data = []): \OpenLoyalty\Core\Infrastructure\FileInterface
    {
        $obj = new self();
        foreach ($data as $k => $v) {
            if (empty($v)) {
                continue;
            }
            switch ($k) {
                case 'originalName':
                    $obj->setOriginalName($v);
                    break;
                case 'path':
                    $obj->setPath($v);
                    break;
                case 'mime':
                    $obj->setMime($v);
                    break;
            }
        }

        return $obj;
    }
}
