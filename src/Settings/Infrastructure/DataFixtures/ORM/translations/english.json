{"-": "-", "+": "+", "10PercentValueProductsWithSku": "10% of the value of products with a specific SKU", "10PointsForEveryProductWithSpecificSku": "10 points for every product with specific SKU", "10UnitsForEveryProductMatchesCriteria": "10 units for every product that matches selected criteria", "10UnitsForEveryUnitCurrencySpentOnProductMatchesCriteria": "10 units for every unit of currency spent on a product that matches selected criteria", "15DaysFromEvent": "15 days from event", "15DaysFromTransaction": "15 days from transaction", "15PercentValueExcludeProductsWithSku": "15% of the transaction value, exclude products with a specific SKU", "1PointFor10UnitsCurrencySpentAndRoundupPoints": "1 point for every 10 units of currency spent and roundup the value of points", "1PointFor1UnitCurrencySpent": "1 point for every 1 unit of currency spent", "1PointFor1UnitCurrencySpent2xPointsEveryProductFromCategory": "1 point for every 1 unit of currency spent, 2x points for every product from the specific category", "1YearFromEvent": "1 year from event", "1YearFromTransaction": "1 year from transaction", "20PointsForEveryProductFromSpecificBrand": "20 points for every product from the specific brand", "2PercentTransactionValue": "2% of the transaction value", "2PointsFor1UnitCurrencySpentExcludeProductFromBrand": "2 points for every 1 unit of currency spent, exclude products from the specific brand", "3MonthsFromEvent": "3 months from event", "3MonthsFromTransaction": "3 months from transaction", "3PercentTransactionValueRoundedUp": "3% of the transaction value, rounded up", "3purchasesInLast14Days": "3 purchases in the last 14 days", "404Header": "404. Page not found", "404Info": "The page you are looking for does not exist.\nPlease make sure the URL is correct.", "4TransactionsInRow": "4 transactions in a row", "50CurrencyUnitsEachWeek": "50 currency units each week", "5PercentValueProductsInCategory": "5% of the value from products in a specific category", "5PointsForEvery1UnitCurrencySpentOnProductFromBrand": "5 points for every 1 unit of the currency spent on the product from the specific brand", "5SeasonalTransactions": "5 seasonal transactions", "7DayLoginStreak": "7-day login streak", "7PercentValueProductsOfBrand": "7% of the value from products of a specific brand", "acceptedFormats": "Accepted formats: jpg, png, gif", "access": "Access", "accessReward": "Access reward", "accessToastDescription": "You don't have permission to access all features on this page. Please refer to your system administrator.", "accessToastTitle": "Access information", "accessToken": "Access token", "accountActivated": "Activated account", "accountDeactivated": "Deactivated account", "accountId": "Account ID", "accountStatus": "Account status", "achievement": "Achievement", "achievementActionInfo": "Choose the action that will trigger your achievement", "achievementActivated": "Achievement activated", "achievementActivation": "Achievement activation", "achievementActivationHelpInfo": "Once the achievement is activated, it will immediately become available to members according to its settings.\n\nProgress can be monitored from the member's profile, enabling you to track how close a member is to completing the achievement.", "achievementActivationWarnInfo": "Remember! Editing certain elements of the achievement after it's created will reset member progress.", "achievementAdded": "New achievement was successfully added.", "achievementAdjustment": "Achievement adjustment", "achievementAmountAndOccurrenceSummary": "Need to perform an action at least { goal } { timePluralized } per { periodSingle } for { value } consecutive { periodPluralized }.", "achievementAvailability": "Achievement availability", "achievementAvailabilityHelpInfo": "Availability defines <span>when</span> members\ncan work toward completing the achievement.\nFor example, you might make it available only during a special event, like a summer sale or Black Friday, or keep it open all year round.", "achievementAvailabilityInfo": "Define when this achievement will be available to members and when they can make progress", "achievementCompleted": "Achievement completed", "achievementCompletion": "Achievement completion", "achievementCompletionCountWasDecreased": "Achievement completion count was decreased", "achievementCompletionWasManuallyChanged": "Achievement completion for \"{ achievementName }\" was manually changed", "achievementCompletionWasManuallyChangedFromTo": "Achievement completion for \"{ achievementName }\" was manually changed from { previousCompletionCount } to { currentCompletionCount }", "achievementConditionInfo": "Set the conditions that must be met along with the event.", "achievementConsecutiveSummary": "Sum event attribute action at least { goal } { timePluralized } over { value } consecutive { periodPluralized }.", "achievementCounting": "Achievement counting", "achievementDeactivated": "Achievement deactivated", "achievementDescription": "Achievement description", "achievementDescriptionLocale": "Achievement description ({ locale })", "achievementDetails": "Achievement details", "achievementEdited": "Achievement edited successfully", "achievementEventOccurenciesCustomEvent": "Examples:\nAdd a minimum of 10 product reviews\nShare this post 5 times in June\nWatch 7 ads in a week", "achievementEventOccurenciesTransaction": "Examples:\nPlace 5 transactions above $50 each.\nAdd a minimum of 10 products reviews.\nPlace 5 transactions between June and August.", "achievementGoal": "Achievement goal", "achievementGoalAndFormula": "Achievement goal and formula", "achievementGoalString": "{ periodGoal } { pluralizedTime } over { lastOperator } { periods } { consecutiveOperator } { pluralizedPeriodType }", "achievementId": "Achievement ID", "achievementInfo": "Achievement info", "achievementInformation": "Achievement information", "achievementInfoStepDescription": "Name the achievement, describe it,\nset availability and visibility", "achievementLimitInfoBox": "Limits for specific rules are defined in the 'Rules' step.", "achievementLimits": "Achievement limits", "achievementLimitsInfo": "Set how many times a member can reach this achievement within a specific period", "achievementName": "Achievement name", "achievementNameCompleted": "Completed achievement \"{ achievementName }\"", "achievementNameLocale": "Achievement name ({ locale })", "achievementOverallSummary": "Sum event attribute action at least { goal } { timePluralized }.", "achievementProgressed": "Achievement progressed", "achievementProgression": "Achievement progression", "achievementProgressionRulesText": "rule \"{ ruleName }\" from { previousPeriodValue } to { currentPeriodValue }", "achievementProgressionWasManuallyChanged": "Achievement progression for \"{ achievementName }\" was manually changed in { rules }", "achievementProgressWasChanged": "Achievement progress was changed", "achievementReachLimitMemberBased": "Achievement reach limit (Member based)", "achievementRule": "Achievement rule", "achievements": "Achievements", "achievementsImportReview": "Achievements import review", "achievementSpecificEventAttributesCustomEvent": "Examples:\nAdd 5 product reviews longer than 50 characters\nShare posts from 5 categories in February\nWatch ads for 30 minutes a week", "achievementSpecificEventAttributesTransaction": "Examples:\nSpend $10 in February.\nPurchase 8 items from the shoes category.\nSpend $100 on products with a specific SKU.", "achievementsStatus": "Achievements status", "achievementStartWhenActive": "This achievement is only available to members when active", "achievementStatus": "Achievement status", "achievementTrigger": "Achievement trigger", "achievementType": "Achievement type", "achievementTypeLinksText": "Learn more about { achievementTypes } and see { exampleAchievementConfiguration }", "achievementTypesLabel": "achievement types", "achievemetLimitHelpInfo": "Limits let you decide <span>how many times</span> members can complete the achievement (by progressing through all the rules).\n\nYou can limit by:\n<span>Unlimited</span> (members can earn it as often as possible)\n<span>Specific time periods</span> (e.g., per hour, day, month, or year)\n<span>A total number</span> of times it can be earned", "achivementCompletedByMember": "Achievement successfully completed by the member", "aclDescription": "Grants access to view and/or manage all roles in the system, including editing permissions and adding new roles.", "action": "Action", "actionHasStartedInfo": "Action has started. You can view progress on the list of mass actions log", "actionID": "Action ID", "actionItemID": "Action item ID", "actionRequired": "Action required", "actions": "Actions", "actionStartDate": "Action start date", "actionType": "Action type", "activate": "Activate", "activateAccount": "Activate account", "activateAccountHeader": "This account will be activated", "activateAccountInfo": "Do you want to activate this account?", "activateAchievement": "Activate achievement", "activateAutomationCampaign": "Activate automation campaign", "activateCampaign": "Activate campaign", "activateFilteredTransfers": "Activate filtered transfers", "activateHeader": "Activate", "activateRewardCategoryHeader": "This reward category will be activated", "activateRewardCategoryInfo": "Activate this reward category?", "activateSchema": "Activate schema", "activateSchemaInfo": "Activate this schema?", "activateSegment": "Activate segment", "activateSegmentHeader": "Are you sure you want to activate the \"{ name }\" segment?", "activateTier": "Activate tier", "activateTierInfo": "Activate this tier?", "activateTransferInfo": "Are you sure you want to activate this transfer?", "activateTransfers": "Activate transfers", "activateWalletTypeInfo": "Activate wallet type to allow members to start earning and spending units", "active": "Active", "activeFrom": "Active from", "activeMember": "Active member", "activeMemberCustomEventCount": "Member made a custom event in the last X days", "activeMembers": "Active members", "activeMemberSettings": "Active member settings", "activeMemberSettingsChangeWarning": "You are changing a member identifier from unique to non-unique.\nPlease note that this change cannot be reversed.\n\nOther changes can be reversed.", "activeMemberSettingsInfo": "Set a condition that needs to be met to consider a member active.\nIt might take up to 24 hours to see configuration changes in the dashboard since recalibration happens once a day. { link }.", "activeMembersTooltip": "Total number of active members on the last day of the selected date range. This metric can take up to 60 seconds to recalculate.\n\n{ configuration } changes can take up to 24 hours to recalculate. Recalculation only considers historical data for the specified period in active member settings. Changes will be visible on the chart from the day the adjustments are made.", "activeMemberTransactionCount": "Member made a transaction in the last X days", "activePoints": "Active points", "activeTo": "Active to", "activeUnits": "Active units", "activeUnitsBecomeNegative": "Active units can become negative", "activeUnitSingularOrPlural": "Active { units }", "activeUnitsWalletBalanceAttributes": "Active units wallet balance attributes", "activeWalletUnits": "Active { units } ({ walletName })", "activeWebhooks": "Active webhooks", "activity": "Activity", "add": "Add", "add2ProductReviews": "Add 2 product reviews", "add2ProductReviewsInLast60Days": "Add 2 product reviews in the last 60 days", "add2ProductReviewsInLast60DaysDescription": "Encourage members to contribute two product reviews within the last 60 days. This engagement helps enhance product insights.", "addAchievement": "Add achievement", "addAdmin": "Add admin", "addAtLeastOneCriterion": "Missing criteria: At least one criterion must be added to this condition.", "addAttribute": "Add attribute", "addAttributeKey": "Add attribute key", "addAttributeKeyAndValue": "Add attribute key and value", "addAutomation": "Add automation campaign", "addBenefit": "Add benefit", "addCampaign": "Add campaign", "addCategory": "Add category", "addChannel": "Add channel", "addCollection": "Add collection", "addCondition": "Add condition", "addCoupon": "Add coupon", "addCriteria": "Add criteria", "addCustomAttribute": "Add custom attribute", "addCustomEvent": "Add custom event", "adddNewRule": "Add new rule", "addedApiKeyConfirmation": "The API key has been successfully added.", "addedCustomEvent": "Custom event \"{ type }\" added", "addedUnits": "Earned { value } { units } in { walletName }", "addedUnitsWithComment": "Earned { value } { units } in { walletName } with comment: \"{ comment }\"", "addEffect": "Add effect", "addEventSchema": "Add event schema", "addFilter": "Add filter", "addFirstAttribute": "Add first attribute", "addFirstCondition": "Add first condition", "addFirstCustomAttribute": "Add first custom attribute", "addFirstMembersToSegment": "Add first members to this segment", "addFirstPermission": "Add first permission", "addFirstProduct": "Add first product", "addFirstTransactionFilter": "Add first transaction filter", "addFirstTransactionItemFilters": "Add first transaction item filters", "addGender": "Add gender", "addHeaderName&Value": "Add header name & value", "addHeaderNameAndValue": "Add header name and value", "addIdentificationFactor": "Add identification factor", "adding": "Adding", "addingUnitsTransfers": "Adding units transfers", "additionalDetails": "Additional details", "additionalInformation": "Additional information", "additionalMetrics": "Additional metrics", "additionalSettings": "Additional settings", "addLanguage": "Add language", "addLeaderboard": "Add leaderboard", "addMember": "Add member", "addNewAchievementRule": "Add new achievement rule", "addNewAttribute": "Add new attribute", "addNewCampaign": "New campaign", "addNewCondition": "Add new condition", "addNewDate": "Add new date", "addNewTransactionFilter": "Add new transaction filter", "addPermission": "Add permission", "addPermissionDescription": "You can restrict the permissions for other roles who work with you.\nFor example customer service may only have access to the\nmembers section, but not to areas with settings.", "addProduct": "Add product", "addProductToSimulatedCampaign": "Add products to the simulated campaign", "addReferralCampaign": "Add referral campaign", "addReport": "Add report", "address1": "Address 1", "address2": "Address 2", "addressDetails": "Address details", "addReward": "Add reward", "addRole": "Add role", "addRule": "Add rule", "addRulesAndConfigureConditionsAndEffects": "Add rules and configure conditions and effects", "addRulesDescription": "You can add more than one rule with different conditions and effects by clicking on the purple button \"Add rule\".", "addSegment": "Add segment", "addSingleItem": "Add single item", "addSingleMember": "Add single member", "addSKU": "Add SKU", "addSpecialReward": "Add special reward", "addTenant": "Add tenant", "addTier": "Add tier", "addTiers": "Add tiers", "addTierSet": "Add tier set", "addTiersSet": "Add tiers set", "addTiersToThisSet": "Add tiers to this set", "addTransaction": "Add transaction", "addTransactionManually": "Add transaction manually", "addTranslation": "Add translation", "addUnits": "Add units", "addUnitsToTheMembersAccount": "Add units to the member's account", "addUnitsTransfer": "Add units transfer", "addUnitTransfer": "Add unit transfer", "addWalletType": "Add wallet type", "addWebhook": "Add webhook", "adjustSearchCriteriaToFindTemplate": "Try adjusting your search criteria to find the templates you are looking for.", "adminAdded": "New admin was successfully added.", "adminDefault": "Admin default", "administrator": "Administrator", "admins": "Admins", "adminsDescription": "Grants access to view and/or manage the list of admins including editing details and changing passwords. 'ACL' permission is required to modify an admin's role.", "adminSettingsEdited": "Admin settings edited", "adminStatus": "Admin status", "adminWithValue": "Admin: { value }", "advancedSettings": "Advanced settings", "after": "After", "afterDate": "After { date }", "afterNDays": "After { count } days", "afterNYears": "After { count } years", "afterOrEqual": "After or equal", "afterXDays": "After X days", "age": "Age", "ageFrom": "Age from", "ageTo": "Age to", "agreement1": "Agreement 1", "agreement2": "Agreement 2", "agreement3": "Agreement 3", "agreementFieldError": "This agreement is required", "agreements": "Agreements", "agreeToParticipateInLoyaltyProgram": "I agree to participate in the Loyalty Program and accept its terms and conditions", "agreeToProcessingDataCollectedFromLoyaltySystems": "I agree to the processing of data collected from other loyalty systems.", "agreeToReceiveMarketingCommunications": "I agree to receive marketing communications.", "all": "All", "allMembers": "All members", "allPossiblePermissions": "This role has all possible permissions", "allRegisteredReturnTransactionsByMember": "The amount of all registered return transactions realized by member", "allTime": "All time", "alpha": "Alpha", "alphanum": "Alphanumeric", "always_available": "Always available", "alwaysAvailable": "Always available", "amountAndOccurrence": "Amount and occurrence", "amountExcludedForTier": "Amount excluded for tier", "analytics": "Analytics", "and": "And", "andCondition": "‘AND’ Condition", "annualExpirationOnChosenDate": "Annual expiration on chosen date", "annualExpirationOnChosenDateExplanation": "New unit transfers will be expired on the chosen date annually after midnight, respecting the time zone set in the settings at the time of creating this transfer.", "annualRecalculationOnChosenDate": "Annual recalculation on the chosen dates", "annualRecalculationOnChosenDateInfo": "The tier set will be recalculated on the chosen dates annually right after midnight, respecting the time zone set in the settings.", "anonymize": "Anonymize", "anonymizedAccount": "Anonymized account", "anonymizedProfile": "Anonymized profile", "anonymizeProfile": "Anonymize profile", "anyCustomEvent": "Any custom event", "anyoneCanRedeemReward": "Anyone can redeem reward", "apiDefault": "Api default", "apiDocumentation": "API documentation", "apiKey": "API Key", "apiKeySuccessfullyDeleted": "The API key has been successfully deleted.", "approved": "Approved", "april": "April", "areYouSure": "Are you sure?", "areYouSureYouWantToContinue": "Are you sure you want to continue?", "areYouSureYouWantToDeactivateSegment": "Are you sure you want to deactivate the\n\"{name}\" segment?", "areYouSureYouWantToDeleteThat": "Are you sure you want to delete that?", "areYouSureYouWantToExit": "Are you sure you want to exit?", "areYouSureYouWantToPerformThisChange": "Are you sure you want to perform this change?", "areYouSureYouWantToSaveChanges": "Are you sure you want to save changes?", "assign_member_custom_attribute": "Set Member Custom Attribute", "assignCodeToMember": "Assign code to member", "assignedCustomEventToCustomer": "Assigned custom event to customer", "assignedTransactionToCustomer": "Assigned transaction to customer", "assignment": "Assignment", "assignToTier": "Assign to tier", "associatedAchievements": "Associated achievements", "associatedCampaigns": "Associated campaigns", "associatedResourcesHere": "Associated resources here", "associatedRewards": "Associated rewards", "atStaticDate": "At static date", "atTheEndOfNYear": "At the end of { n } year(s)", "atTheEndOfTheMonth": "At the end of the month", "atTheEndOfTheXthYear": "At the end of the X-th year", "atTheEndOfTheYear": "At the end of the year", "atTheEndOfThisYear": "At the end of this year", "attribute": "Attribute", "attributeEventName": "Attribute event name", "attributeName": "Attribute name", "attributeNameInfo": "The name visible to you in dropdown menus within campaigns, achievements, and other loyalty modules.", "attributes": "Attributes", "attributeSystemIdentifier": "Attribute system identifier", "attributeSystemIdentifierInfo": "A unique value behind the scenes for technical purposes (API integration). Share this with your technical team if needed.", "attributeType": "Attribute type", "attributeValue": "Attribute value", "auditLog": "Audit log", "auditLogDescription": "Grants access to view and/or manage and export system logs by date range.", "august": "August", "autocompleteLinksText": "Click the rule chip once to open a pop-up. Click it twice to edit its value without opening a pop-up. You can use operators like: +,-,/,.  See {listOfOperators} Learn more about {availableVariables} and see {exampleFormulas}", "autocompletePlaceholder": "Start typing or choose from the list", "autoGenerateCouponCodes": "Auto-generate coupon codes", "autoGenerateCouponCodesInfo": "After the creation of a Reward, you need to upload coupons", "autogeneratedCoupons": "Autogenerated coupons", "automatic": "Automatic", "automaticLogout": "You have been automatically logged out due to inactivity for security reasons. To continue using our website, please log back in again.", "automation": "Automation campaign", "automationActivated": "Automation campaign activated", "automationBudget": "Automation campaign budget", "automationCampaigns": "Automation campaigns", "automationCompletionPerMember": "Automation campaign completion per member", "automationCreated": "Automation campaign created", "automationDeactivated": "Automation campaign deactivated", "automationDescLocale": "Automation campaign description ({ locale })", "automationDetails": "Automation campaign details", "automationDisplayOrder": "Automation campaign display order", "automationId": "Automation campaign ID", "automationLimit": "Automation campaign limit", "automationName": "Automation campaign name", "automationNameLocale": "Automation campaign name ({ locale })", "automationOverview": "Automation campaign overview", "automations": "Automations", "automationsImportReview": "Automation campaigns import review", "automationStatus": "Automation campaign status", "automationStatusDesc": "The automation campaign will start only if it is active", "automationsThatCouldNotBeRemoved": "Automation campaigns that could not be removed", "automationSuccessfullyDeleted": "The automation campaign has been successfully deleted", "automationTrigger": "Automation campaign trigger", "automationTriggeredEveryMonthOnSelectedDays": "Automation campaign triggered every month on selected days", "automationTriggeredEveryWeekOnSelectedDays": "Automation campaign triggered every week on selected days", "automationTriggeredOnceEveryDay": "Automation campaign triggered once every day", "automationTriggeredOnMembersBirthday": "Automation campaign triggered on a member's birthday", "automationTriggeredOnMembersRegistrationDate": "Automation campaign triggered annually on the member's registration date", "automationTriggersLimitation": "Automation campaign triggers limitation", "automationType": "Automation campaign type", "automationUpdated": "Automation campaign was successfully updated", "automationVisibility": "Automation campaign visibility", "automationVisibilityInfo": "Automation campaign visibility is responsible for determining the visibility of the automation campaign to the targeted members in segments. The precise targeting of the automation campaigns depends on the conditions specified in the rules section.", "automatycalyGenerated": "Number of signs in automatycaly generated coupon.", "availability": "Availability", "availabilityInfo": "The availability column displays the remaining redemption limit and the total redemption limit.", "available": "Available", "availableCoupons": "Available coupons", "availableForEveryone": "Available for everyone", "availableForSpecificSegment": "Available for specific segment", "availableForSpecificTier": "Available for specific tier", "availableFrom": "Available from", "availableRewards": "Available rewards", "availableStock": "Available stock", "availableTo": "Available to", "availableVariablesLinkLabel": "available variables", "averagePurchaseTransactionValueRange": "Avg. purchase transaction value range", "averageTransactionValue": "Average transaction value", "averageValueOfTransactions": "The average value of transactions", "avgNumberOfTransactions": "Avg. number of transactions", "avgNumberOfTransactionsTooltip": "Average number of transactions per member within the set date range. { link } about how the metric is calculated.", "avgPurchaseTransactionValueWithCurrency": "Avg. purchase transaction value ({ currency })", "avgReturnTransactionValue": "Avg. return transaction value", "avgReturnTransactionValueWithCurrency": "Avg. return transaction value ({ currency })", "avgReturnValue": "Avg. return value", "avgSpending": "Avg. spending", "avgSpendingTooltip": "Average spending per member within the set date range. { link } about how the metric is calculated.", "avgSpendingWithCurrency": "Avg. spending ({ currency })", "avgTransactionValue": "Avg. transaction value", "avgTransactionValueFrom": "Avg. transaction value from", "avgTransactionValueTo": "Avg. transaction value to", "avgTransactionValueTooltip": "Average transaction value within the set date range. { link } about how the metric is calculated.", "avgTransactionValueWithCurrency": "Avg. transaction value ({ currency })", "avgTransAmount": "Avg. trans. amount", "avgValueOfReturnTransactions": "The average value of return transactions", "awaiting_shipping": "Waiting for shipping", "back": "Back", "backToDashboard": "Back to dashboard", "backToEdit": "Back to edit", "backToLoginPage": "Back to login page", "badCredentials": "Bad credentials", "badge": "Badge", "badgeAchievementDesc": "This field is optional. If you leave it blank, it will take the achievement name as its value. { userGuide }", "badgeAclDescription": "Grants access to view and/or manage badge information, including viewing badge counts, editing badge names, and modifying badge completion counts for members. Requires 'VIEW' permission to access badge information in achievement and member profiles.", "badgeCompletedCountUpdated": "Badge completed count updated.", "badgeCount": "Badge count", "badgeCountManuallyAssigned": "Badge count for \"{ badge<PERSON>ame }\" was manually changed from  { from } to { to }", "badgeName": "Badge name", "badgeNameEarned": "Badge \"{ name }\" earned", "badgeNameUpdated": "Badge name successfully updated", "badgeReachedDate": "Badge reached date", "badges": "Badges", "badgesLimitHelpInfo": "When a member completes an achievement they automatically receive the corresponding badge in their profile. \n\nWith badges, you can easily identify engaged community members and adjust campaigns or benefits for the most active users.", "basicConfiguration": "Basic configuration", "basicInfo": "Basic info", "basicInformation": "Basic information", "basicInfoSummary": "Basic info summary", "basicLegalAgreement": "Basic legal agreement", "basicSettings": "Basic settings", "basicSettingsInfo": "A channel represents a specific transaction source or location within your loyalty program, such as an online store, physical location, or mobile app. Channels are created individually for each tenant. ", "before": "Before", "beforeDate": "Before { date }", "beforeDeactivatingSegmentReviewItemsInfo": "Before deactivating the “{name}” segment, please review any associated campaigns and rewards listed below:", "beforeDeactivationPleaseReview": "Before deactivation, please review { link }.", "beforeOrEqual": "Before or equal", "behaviourBased": "Behaviour based", "benefit": "Benefit", "benefitCode": "Benefit code", "benefitName": "Benefit name", "between": "Between", "betweenDates": "Between { dateFrom } - { dateTo }", "billableMembers": "Billable members", "billableMembersTabTooltipDescription": "Total number of members who interacted with the loyalty program at least once within a calendar month.\nThese interactions include any of the following: Submitting a purchase, a return transaction, or a custom event; transferring, spending, or earning units; redeeming or using a reward; or referring a member.", "billableReportsDescription": "Grants access to view usage charts, including the total number of transactions and billable members across all tenants.", "birthdayCampaign": "Birthday campaign", "birthdayCampaignInfo": "Campaign based on members birthday", "birthdayDate": "Birthday date", "birthdayInXNumberOfDays": "Birthday in X number of days", "blocked": "Blocked", "blockedPoints": "Blocked points", "blockedUnits": "Blocked units", "blockedUnitsWalletBalanceAttributes": "Blocked units wallet balance attributes", "blockedWalletUnits": "Blocked { units } ({ walletName })", "boolean": "Boolean", "boughtInSpecificChannel": "Bought in specific channel", "boughtProductsWithCustomAttributes": "Bought products with custom attributes", "boughtSpecificBrands": "Bought specific brands", "boughtSpecificSKU": "Bought specific SKU", "brand": "Brand", "brandDescription": "Brand description", "brandName": "Brand name", "breakageRate": "Breakage rate", "breakageRateTooltip": "The percent of issued { units } that have expired within the set date range.", "browse": "browse", "buildingName": "Building name", "buildingNameNumber": "Building name number", "bulkActions": "Bulk actions", "businessPerformance": "Business Performance", "buy3Products": "Buy 3 products", "buyProductFromBrandX": "Buy a product from brand X", "buyProductFromBrandXDescription": "Encourage purchases from brand X to enhance its visibility, strengthen customer loyalty, and increase transaction value.", "buyProductFromCategoryYAndX": "Buy a product from category Y and X", "buyProductFromCategoryYAndXDescription": "Encourage members to add complementary products to their shopping cart, accelerating cross-sales and increasing transaction value", "buyProductWithSpecificSKU": "Buy a product with a specific SKU", "buyProductWithSpecificSKUDescription": "Motivate members to purchase a designated SKU item, fostering sales growth and elevating the visibility of targeted products.", "buyXAndY": "Buy X and Y", "calendar": "Calendar", "calendarDays": "Calendar days", "calendarMonths": "Calendar months", "calendarWeeks": "Calendar weeks", "calendarYears": "Calendar years", "campaign": "Campaign", "campaignActivated": "Campaign activated", "campaignAttributes": "Campaign attributes", "campaignBudget": "Campaign budget", "campaignCompletion": "Campaign completion", "campaignCompletionInCurrentPeriodInfo": "On the current { timeframe }, the member completed the campaign { completionNumber }.", "campaignCompletionInfo": "Over the last { timeframe }, the member completed the campaign { completionNumber }.", "campaignCompletionNumber": "Campaign completion number", "campaignCompletionPerMember": "Campaign completion per member", "campaignCompletionTimeframe": "Campaign completion timeframe", "campaignCompletionTimeframeNumberInfo": "Member completed the campaign in the last x { timeframe }", "campaignCreated": "Campaign was successfully created", "campaignDates": "Campaign dates", "campaignDatesDesc": "Campaigns will be based on business dates. If a campaign has no end date, do not set one.", "campaignDeactivated": "Campaign deactivated", "campaignDescLocale": "Campaign description ({ locale })", "campaignDescription": "Campaign description", "campaignDetails": "Campaign details", "campaignDisplayOrder": "Campaign display order", "campaignId": "Campaign ID", "campaignInfo": "Campaign info", "campaignIssuance": "Campaign issuance", "campaignLimit": "Campaign limit", "campaignName": "Campaign name", "campaignNameLocale": "Campaign name ({ locale })", "campaignOrder": "Campaign order", "campaignOrderDisplay": "Campaign order display", "campaignOverview": "Campaign overview", "campaignOverviewDesc": "Campaigns are structured around a sequence of triggers, conditions, and effects. Upon the occurrence of a trigger, its attributes are assessed against predefined conditions. If these conditions are fulfilled, an effect is applied. This mechanism enables rewarding customers who satisfy the criteria outlined in a campaign.", "campaigns": "Campaigns", "campaignsDescription": "Grants access to view and/or manage the list of campaigns, including adding, editing, and duplicating campaigns. Requires 'SEGMENT' and 'TIERS' permission to display correctly.", "campaignsImportReview": "Campaigns import review", "campaignSimulator": "Campaign simulator", "campaignsStatus": "Campaigns status", "campaignStatus": "Campaign status", "campaignsThatCouldNotBeRemoved": "Campaigns that could not be removed", "campaignsTransactionFiltersDescription": "Use transaction filters to narrow down specific products in customers' carts, based on the products' size, quantity, price, or other relevant factors. You can combine multiple rules for more precise filtering.", "campaignSuccessfullyDeleted": "The campaign has been successfully deleted", "campaignTrigger": "Campaign trigger", "campaignTriggerHelpDesc": "A campaign trigger is the event that starts a campaign. When this event takes place, it is checked against all active campaigns using the same trigger. If the conditions are met, the campaign's effect is applied.", "campaignTriggerInfo": "Select trigger that will initiate the campaign", "campaignTriggers": "Campaign triggers", "campaignTriggersLimitation": "Campaign triggers limitation", "campaignType": "Campaign type", "campaignUnitsLimitation": "Campaign units limitation", "campaignUnitsLimitationInfo": "Overall units issued by the campaign up to a specified limit, over a defined period. The limit for units issued by the campaign is set in the campaign limitation settings.", "campaignUpdated": "Campaign was successfully updated", "campaignVisibility": "Campaign visibility", "campaignVisibilityDesc": "Campaign visibility only determines to whom the challenge is presented, not who can complete it (It doesn't affect the campaign conditions), and receives points. The condition must be specified in the rules. \n\nVisibility controls who can see the campaign. It could be visible to all members or just specific groups, such as segments or tiers.", "campaignVisibilityInfo": "Campaign visibility is responsible for determining the visibility of the campaign to the targeted members in segments. The precise targeting of the campaign depends on the conditions specified in the rules section.", "campaignWillStartOnlyIfActive": "The campaign will start only if it is active.", "cancel": "Cancel", "canceled": "Canceled", "cancelFilteredTransfers": "Cancel filtered transfers", "cancelled": "Cancelled", "cancelledUnits": "Cancelled { value } { units } in { walletName }", "cancelTransferInfo": "Are you sure you want to cancel this transfer?", "cancelTransfers": "Cancel transfers", "categories": "Categories", "category": "Category", "categoryName": "Category name", "change": "Change", "changeAPIHeader": "Change API key", "changeAPIInfo": "Are you sure you want to update API key?", "changeBadgeName": "Change badge name", "changeBadgeNameDesc": "You can change the default badge name (which is the achievement name) to a custom name. { learnMore }", "changePassword": "Change password", "changePasswordInfo": "Enter your new password", "changeStatus": "Change status", "changesWillBeLost": "Changes will be lost", "changeTriggerToDaily": "Change trigger to daily", "changingFieldWillResetProgress": "Changing this field will reset progress of all members.", "channel": "Channel", "channelAdded": "Channel was successfully created", "channelCode": "Channel code", "channelDeleted": "The channel has been successfully deleted", "channelDescription": "Channel description", "channelId": "Channel ID", "channelInfo": "A channel represents a specific sales medium or platform where customers make purchases", "channelName": "Channel name", "channels": "Channels", "channelsDescription": "Grants access to view and/or manage sales channels, including filtering, modifying transactions, and applying channel-related conditions.", "channelUpdated": "Channel updated", "chooseChannel": "Choose channel", "chooseConditionsForThisSet": "Choose conditions for this set", "chooseConditionsInfo": "Members will progress within the chosen conditions. These conditions and their values will be applicable to every tier.", "chooseDateRange": "Choose date range", "chooseEventTargetAttribute": "Choose event target attribute", "chooseExpirationDateFormula": "Choose expiration date formula", "chooseFromTheList": "Choose from the list", "choosePendingDateFormula": "Choose pending date formula", "chooseTemplate": "Choose template", "chooseTemplatesFromTheList": "Start typing or choose templates from the list", "chooseWebhookEvent": "Choose webhook event", "city": "City", "claimFor": "Claim for", "claimReward": "Claim reward", "clearAll": "Clear all", "clearFilters": "Clear filters", "clickHere": "Click here", "clickToUpload": "Click to upload", "close": "Close", "code": "Code", "codeID": "Code ID", "codeLocale": "Code (locale)", "codePreview": "Code preview", "collapseMenu": "Collapse menu", "collection": "Collection", "collectionAdded": "Collection added successfully", "collectionDescription": "Collection description", "collectionFirstValuesInfo": "Add your first values to this collection", "collectionImportValuesInfo": "Now you can import values into the collection", "collectionName": "Collection name", "collections": "Collections", "collectionsDescription": "Grants access to view and/or manage the collections list and allows adding new collections. Importing new values to existing collections requires 'IMPORT' permissions.", "collectionStatus": "Collection status", "collectionStatusInfo": "The collection will be usable as soon as it is active", "collectionUpdated": "Collection updated successfully", "comment": "Comment", "communicationSettingsInfo": "Add days before which we notify a user about expiring units, coupons or recalculations.", "companyDetails": "Company details", "companyName": "Company name", "comparisonPeriod": "Comparison period", "complete5SeasonalTransactions": "Complete 5 seasonal transactions", "complete5SeasonalTransactionsDescription": "Drive engagement by completing five transactions in any season. This strategy encourages consistent activity and supports robust seasonal sales.", "completeCustomEvent": "Complete custom event", "completeCustomEventBuy3Products": "Complete custom event & buy 3 products", "completeCustomEventBuy3ProductsDescription": "Encourage members to engage with an external event and purchase three products to fulfill multiple achievement rules.", "completed": "Completed", "completedCount": "Completed count", "completedPeriods": "Completed periods", "completedPeriodsFrom": "Completed periods from", "completedPeriodsInfo": "Number of completed periods to the number of time units in which the event has to be executed", "completedPeriodsTo": "Completed periods to", "completion": "Completion", "completionCount": "Completion count", "completionNumber": "Completion number", "completionNumberFrom": "Completion number from", "completionNumberTo": "Completion number to", "completionStatus": "Completion status", "condition": "Condition", "conditions": "Conditions", "conditionsDescription": "Conditions description", "conditionsIn": "Conditions in", "conditionType": "Condition type", "conditionValue": "Condition value", "configDuplication": "Config duplication", "configDuplicationInformation": "Copied configurations will be disabled by default. Custom events and elements used in the configurations won't be copied automatically - please transfer them manually.", "configuration": "Configuration", "configureSettings": "Configure settings", "confirm": "Confirm", "confirmation": "Confirmation", "connectReferrerTokenToUser": "To connect this user to a specific referrer, paste the token in this field.", "consecutive": "Consecutive", "consecutiveCustomEventExpressionGoal": "The member must log { event } custom events with the attribute { expressionFormula } summing up to <span>{ periodGoal } or more every { periodType }</span> for <span>{ consecutiveElement } { pluralizedConsecutiveType } in a row</span>", "consecutiveCustomEventQuantityGoal": "The member must log a <span>{ periodGoal } custom events</span> { event } <span>every { periodType }, for { consecutiveElement } { pluralizedConsecutiveType } in a row</span>", "consecutiveDay": "Consecutive day", "consecutiveMonth": "Consecutive month", "consecutiveTransactionExpressionGoal": "The member must place transactions with the attribute { expressionFormula } summing up to <span>{ periodGoal } or more every { periodType } </span> for <span>{ consecutiveElement } { pluralizedConsecutiveType } in a row</span>", "consecutiveTransactionQuantityGoal": "The member must place <span>{ periodGoal } { pluralizedTransaction } every { periodType }</span>, for <span>{ consecutiveElement } { pluralizedConsecutiveType } in a row</span>", "consecutiveWeek": "Consecutive week", "consecutiveYear": "Consecutive year", "consents": "Consents", "contains": "Contains", "containsOneOf": "Contains one of", "continue": "Continue", "continueWithAuth0": "Continue with Auth0", "continueWithSSO": "Continue with SSO", "controlsTotalAvailabilityOfCampaignRewards": "Controls the total availability of campaign rewards", "conversion_coupon": "Conversion coupon", "copied": "<PERSON>pied", "copied!": "Copied!", "copiedAchievementsInfo": "Copied achievements will be disabled by default. Custom events and elements used in the achievement won't be copied automatically – please transfer them manually.", "copiedAutomationsInfo": "Copied automation campaigns will be disabled by default. Custom events and elements used in the automation campaigns won't be copied automatically – please transfer them manually.", "copiedCampaignsInfo": "Copied campaigns will be disabled by default. Custom events and elements used in the campaign won't be copied automatically – please transfer them manually.", "copiedCustomEventSchemasInfo": "Copied custom event schemas will be disabled by default.", "copiedReferralsInfo": "Copied referral campaigns will be disabled by default. Custom events and elements used in the referral campaign won't be copied automatically – please transfer them manually.", "copiedRewardsInfo": "Copied rewards will be disabled by default. Custom events and elements used in the reward won't be copied automatically – please transfer them manually.", "copiedWalletTypesInfo": "Copied wallet types will be disabled by default. Custom events and elements used in the wallet type won't be copied automatically – please transfer them manually.", "copy": "Copy", "copyAchievementsAndViewTargetTenant": "Copy achievement(s) and view target tenant", "copyAchievementsToTargetTenant": "Copy { count } achievement(s) to target tenant", "copyApiKeyInfo": "The API key is ready. Copy the code below and save it.\nYou won't be able to view it again once you close this window.", "copyAutomationsAndViewTargetTenant": "Copy automation campaign(s) and view target tenant", "copyAutomationsToTargetTenant": "Copy { count } automation campaigns to target tenant", "copyCampaignAndViewTargetTenant": "Copy campaign and view target tenant", "copyCampaignsAndViewTargetTenant": "Copy campaigns and view target tenant", "copyCampaignsToTargetTenant": "Copy { count } campaigns to target tenant", "copyCustomEventSchemaAndViewTargetTenant": "Copy custom event schema and view target tenant", "copyCustomEventSchemasAndViewTargetTenant": "Copy custom event schemas and view target tenant", "copyCustomEventSchemasToTargetTenant": "Copy { count } custom event schemas to target tenant", "copyOneAutomationToTargetTenant": "Copy 1 automation campaign to target tenant", "copyOneCampaignToTargetTenant": "Copy 1 campaign to target tenant", "copyOneCustomEventSchemaToTargetTenant": "Copy 1 custom event schema to target tenant", "copyOneReferralToTargetTenant": "Copy 1 referral campaign to target tenant", "copyOneRewardToTargetTenant": "Copy 1 reward to target tenant", "copyOneWalletTypeToTargetTenant": "Copy 1 wallet type to target tenant", "copyReferralAndViewTargetTenant": "Copy referral campaign and view target tenant", "copyReferralsAndViewTargetTenant": "Copy referral campaigns and view target tenant", "copyReferralsToTargetTenant": "Copy { count } referral campaigns to target tenant", "copyRewardAndViewTargetTenant": "Copy reward and view target tenant", "copyRewardsAndViewTargetTenant": "Copy rewards and view target tenant", "copyRewardsToTargetTenant": "Copy { count } rewards to target tenant", "copyToDifferentTenant": "Copy to different tenant", "copyWalletTypeAndViewTargetTenant": "Copy wallet type and view target tenant", "copyWalletTypesAndViewTargetTenant": "Copy wallet types and view target tenant", "costWithCurrency": "Cost ({ currency })", "countOnlyUniqueCustomAttributesOfTransaction": "Count only unique custom attributes of transaction products", "country": "Country", "countUniqueCustomEventAttributes": "Count only unique custom event attributes", "coupon": "Coupon", "couponActiveFrom": "Coupon active from", "couponActiveTo": "Coupon active to", "couponAdded": "Coupon added", "couponCharacterSet": "Coupon character set", "couponCharacterSetInfo": "Types of characters used while generating the coupon.", "couponCharset": "Coupon charset", "couponCode": "Coupon code", "couponConfiguration": "Coupon configuration", "couponDetails": "Coupon details", "couponExpirationDate": "Coupon expiration date", "couponGeneratorCharacterSet": "Coupon generator character set", "couponGeneratorLength": "Coupon generator length", "couponGeneratorPrefix": "Coupon generator prefix", "couponInfo": "Reward in the form of a digital coupon", "couponLength": "Coupon length", "couponPendingAndExpirationSettings": "Coupon pending and expiration settings", "couponPrefix": "Coupon prefix", "couponPrefixInfo": "Prefix added at the beginning of the coupon.", "couponRemoved": "The coupon was successfully removed", "couponReward": "Coupon reward", "coupons": "Coupons", "couponsCount": "Coupons count", "couponsGeneratedInfo": "Configure how coupons will be generated and all details of the coupons", "couponStatus": "Coupon status", "couponSuccesfullyMarkedAsUnused": "Coupon succesfully marked as unused", "couponSuccesfullyMarkedAsUsed": "Coupon succesfully marked as used", "couponsUploadedFromTheExternalSource": "Coupons uploaded from the external source", "couponType": "Coupon type", "couponValidDays": "Coupon will be valid on specific days.", "couponValidPeriod": "The period of time when the coupon will be valid.", "couponValue": "Coupon value", "couponValueSettings": "Coupon value settings", "createAchievement": "Create achievement", "createAchievementInfo": "Create achievements from scratch or start from ready-made templates while still having the flexibility to customize. { link }", "createCustomAchievementInfo": "Create a custom achievement tailored to your loyalty program's needs, with full control over every detail.", "createDate": "Create date", "createdAtFrom": "createdAtFrom", "createdAtTo": "createdAtTo", "createdOn": "Created on", "createdOnInfo": "The date when the units transfer was created in our system.", "csvSizeInfo": "CSV (max. {size} MB)", "cumulativeEarnedUnits": "Cumulative earned units", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currentDate": "Current date", "currentEarnings": "Current earnings", "currentPassword": "Current password", "currentPeriod": "Current period", "currentProgress": "Current progress", "currentProgressDescription": "Progress bar displays the current tier status. Member must meet all criteria to move up a tier.", "currentProgressFrom": "Current progress from", "currentProgressInfo": "Current progress is related to the number of events in a time period.\n\nIn multi-dimensional achievements, the current progress can exceed the goal. Progress increases (up to the set limit) in each rule until all the rules are completed.", "currentProgressTo": "Current progress to", "currentScore": "Current score", "currentTier": "Current tier", "currentTime": "Current time", "currentValue": "Current value", "customAttribute": "Custom attribute", "customAttributeAdded": "New custom attribute was successfully added", "customAttributeDescription": "Custom attributes are data fields used to store additional details.", "customAttributeKey": "Key: { value }", "customAttributeKeyLabel": "Custom attribute key", "customAttributes": "Custom attributes", "customAttributesAutomationDescription": "Custom attributes are data fields used to store additional details about automation campaigns.", "customAttributesUsingPatternInfo": "Custom attributes using pattern e.g. (key1;value1),(key2;value2),...", "customAttributesWereDeleted": "Custom attributes were deleted", "customAttributesWereUpdated": "Custom attributes were updated", "customAttributeValue": "Value: { value }", "customAttributeWasAdded": "Custom attribute was added", "customConfiguration": "Custom configuration", "customCouponsAttributes": "Custom attributes to the coupons", "customDateRange": "Custom date range", "customerDetailCustomEventInfo": "The details refer to the customer engaged in the custom event and can vary from the information of an existing member in the system", "customerDetailsFromCustomEvent": "Customer details from the custom event", "customerDetailsFromTheTransaction": "Customer details from the transaction", "customerDetailsFromTheTransactionInfo": "The details refer to the customer engaged in the transaction and can vary from the information of an existing member in the system", "customerId": "Customer ID", "customerProfileRecurringDates": "Customer profile recurring dates", "customEvent": "Custom event", "customEventAttribute": "Custom event attribute", "customEventAttributes": "Custom event attributes", "customEventDate": "Custom event date", "customEventDetails": "Custom event details", "customEventId": "Custom event ID", "customEventName": "Custom event name", "customEvents": "Custom events", "customEventSchemaAdded": "New event schema was successfully added", "customEventSchemaImportReview": "Custom event schema import review", "customEventSchemas": "Custom event schemas", "customEventSchemaUpdated": "Event schema was successfully updated", "customEventsDescription": "Grants access to view and/or manage the list of custom events and event schemas, using custom events across the platform e.g achievements and campaigns.", "customEventWithinTheLastXNumberOfDays": "Custom event within the last X number of days", "customEventWithSchema": "Custom event: { schema }", "customMembersList": "Custom members list", "customMetric": "Custom metric", "customRewardAttributes": "Custom reward attributes", "customSchemaDesc": "Build your customized event schemas which can be used later to set {eventBasedCampaign}", "customTiers": "Custom tiers", "customTiersDesc": "Tiers can have their own conditions, allowing easy customization of loyalty programs. { link }", "daily": "Daily", "dailyGrossValueFrom": "Daily gross value from", "dailyGrossValueInGivenPeriod": "Daily gross value in given period", "dailyGrossValueInLastXDays": "Daily gross value in last X days", "dailyGrossValueTo": "Daily gross value to", "dailyStreak": "Daily streak", "dailyTriggerSuggestion": "Selecting all days of the week will make the automation campaign run every day – if that is the intention, we suggest using the Daily trigger.", "dashboard": "Dashboard", "dashboardGeneralOverview": "Dashboard (General overview)", "dataProcessingAgreement": "Data processing agreement", "dataProcessingConsent": "Data processing consent", "date": "Date", "dateFrom": "Date from", "dateOfBirth": "Date of birth", "dateOfRegistration": "Date of registration", "datetime": "Datetime", "dateTo": "Date to", "day": "Day", "day(s)": "Day(s)", "dayAgo": "Day ago", "dayOfEachMonth": "Day { days } of each month", "dayOfMonth": "Day of month", "days": "Days", "daysAgo": "Days ago", "daysFrom": "Days from", "daysOfEachMonth": "Days { days } of each month", "daysOfEachMonthHelper": "This function triggers a campaign on the last day of each month, right after midnight.", "daysSinceLastTransaction": "Days since last transaction", "daysSinceTheLastTransaction": "Days since the last transaction", "daysTo": "Days to", "daysToExpireCoupon": "Days to expire coupon", "daysToExpireCouponInfo": "Number of days until a coupon will expire", "deactivate": "Deactivate", "deactivateAccount": "Deactivate account", "deactivateAchievement": "Deactivate achievement", "deactivateAutomationCampaign": "Deactivate automation campaign", "deactivateCampaign": "Deactivate campaign", "deactivatedMember": "Deactivated member", "deactivateMemberHeader": "This member will be deactivated", "deactivateSchema": "Deactivate schema", "deactivateSchemaInfo": "Deactivate this schema?", "deactivateSegment": "Deactivate segment", "deactivateSegmentHeader": "Deactivate segment", "deactivateSegmentInfo": "Deactivating a segment will freeze its member list. Members will not be added or removed from it dynamically, even if members meet the conditions. Deactivated segments can still be used in campaigns.", "deactivateSegmentInfoBox": "If campaigns or rewards are linked to this segment, they will remain visible to its current members.", "deactivateSegmentReview": "Deactivate segment review", "deactivateSegmentReviewInfoBox": "Removing these relations <span>is not required</span> to deactivate the segment.", "deactivateThisAccount": "Do you want to deactivate this account?", "deactivateThisRewardCategory": "Deactivate this reward category?", "deactivateThisSegment": "Deactivate this segment?", "deactivateThisTier": "Deactivate this tier?", "deactivateTier": "Deactivate tier", "deactivateUserHeader": "Deactivate user", "december": "December", "deduct": "Deduct", "deduct_unit": "Deduct units", "deducting": "Deducting", "deductingUnitsTransfers": "Deducting units transfers", "deductUnits": "Deduct units", "deductUnitsFromTheMembersAccount": "Deduct units from the member's account", "deductUnitsTransfer": "Deduct units transfer", "default": "<PERSON><PERSON><PERSON>", "defaultLanguageValue": "Default language value", "defaultRole": "Default role", "defaultRoleDescription": "If a role is set as default, it will be assigned to any admin user created by logging in by LDAP.", "defaultWalletConfiguration": "Default wallet configuration", "defineLimitation": "Define the limitation", "defineRules&effectDesc": "Define the rules & effects", "delete": "Delete", "deleteAutomation": "Delete automation campaign", "deleteAutomationQuestion": "Are you sure you want to delete this automation campaign?", "deleteAutomations": "Delete automation campaigns", "deleteAutomationsQuestion": "Are you sure you want to delete those automation campaigns?", "deleteBatchAutomationsError": "Unfortunately, not all automation campaigns have been removed due to existing dependencies.", "deleteBatchCampaignError": "Unfortunately, not all campaigns have been removed due to existing dependencies.", "deleteBatchReferralError": "Unfortunately, not all referral campaigns have been removed due to existing dependencies.", "deleteCampaign": "Delete campaign", "deleteCampaignQuestion": "Are you sure you want to delete this campaign?", "deleteCampaigns": "Delete campaigns", "deleteCampaignsQuestion": "Are you sure you want to delete those campaigns?", "deleteChannel": "Delete channel", "deleteCondition": "Delete condition", "deleteConditionConfirm": "Are you sure you want to delete this condition?", "deleteCouponInfo": "Are you sure to delete this coupon? You will not be able to undo this operation.", "deletedApiKeyConfirmation": "Are you sure you want to delete this API key?", "deleteEffect": "Delete effect", "deleteEffectConfirm": "Are you sure you want to delete this effect?", "deleteFormula": "Delete formula", "deleteHeader": "Delete confirmation", "deleteInfo": "This operation cannot be undone.", "deleteItem": "Delete item", "deleteItemQuestion": "Are you sure you want to delete this item?", "deleteMemberInfo": "Are you sure to delete this member? You will not be able to undo this operation.", "deleteReferral": "Delete referral campaign", "deleteReferralQuestion": "Are you sure you want to delete this referral campaign?", "deleteReferrals": "Delete referral campaigns", "deleteReferralsQuestion": "Are you sure you want to delete those referral campaigns?", "deleteRule": "Delete rule", "deleteRuleConfirm": "Are you sure you want to delete this rule? All conditions and effects within this rule will be removed", "deleteTranslation": "Delete translation", "deleteTranslationHeader": "Are you sure you want to delete this translation?", "deleteTranslationInfo": "Please note that deleting a translation is permanent and cannot be undone.", "deleteWebhookInfo": "Delete this webhook?", "deliveryCity": "Delivery city", "deliveryCosts": "Delivery costs", "deliveryCostsDescription": "Delivery costs will not be generating points.", "desciptionOfTheRewardIn": "Desciption of the reward in", "desciptionOfTheRewardInInfo": "Desciption of the reward in default language (English).", "description": "Description", "design": "Design", "details": "Details", "dialogDescriptionBrand": "Type <span>one brand</span> that you want to use in the points calculation formula.", "dialogDescriptionCategory": "Type <span>one category</span> that you want to use in the points calculation formula.", "dialogDescriptionCustomAttribute": "Type <span>one custom attribute</span> that you want to use in the points calculation formula.", "dialogDescriptionItemsCriteria": "Add at least one 'AND' criterion to this condition", "dialogDescriptionMemberCustomAttributes": "Type one member custom attribute <span>key</span> that you want to use in the formula.", "dialogDescriptionSku": "Type <span>one SKU</span> that you want to use in the points calculation formula.", "dialogDescriptionTransactionCustomAttributes": "Type one transaction custom attribute <span>key</span> that you want to use in the formula.", "differentTenant": "Different tenant", "direct": "Direct", "directAchievement": "Direct achievement", "directAchievementInfo": "An achievement where rules are set directly for members", "directCampaign": "Direct campaign", "directCampaignInfo": "A one-sided campaign with rules for a member", "directRuleType": "Direct rule type", "disabledCampaignTypeInfo": "You can't change the type in an existing campaign", "disabledCampaignTypeTriggerInfo": "You can't change the trigger in an existing { type }", "disableNotifications": "Disable notifications", "discardChangesAndExit": "Discard changes & exit", "displayAll": "Display all", "displayOrder": "Display order", "documentation": "Documentation", "documentDate": "Document date", "documentId": "Document ID", "documentNumber": "Document number", "documentType": "Document type", "doesNotContain": "Does not contain", "done": "Done", "downgradeBasedOn": "Downgrade based on", "downgradeEvery": "Downgrade every", "downgradeOption": "Downgrade option", "download": "Download", "downloadExportInfo": "Your download will start soon", "downloadJsonFile": "Download .JSON file", "downloadMembersList": "Download members list", "downloadSampleFiles": "Download the sample files { link }.", "dragDropImageOr": "Drag and drop image or", "duplicate": "Duplicate", "duplicateAchievement": "Duplicate achievement", "duplicateAndViewTargetTenant": "Duplicate and view target tenant", "duplicateAutomation": "Duplicate automation campaign", "duplicateCampaign": "Duplicate campaign", "duplicateConfigurationInProgress": "Duplicate configurations in progress", "duplicateConfigurationToDiffTenant": "Duplicate configurations to different tenant", "duplicatedSKUError": "The same SKU can't be used twice", "duplicateFormula": "Duplicate formula", "duplicateReferral": "Duplicate referral campaign", "duplicateReward": "Duplicate reward", "duplicateRole": "Duplicate role", "duplicateRule": "Duplicate rule", "duplicateSelectedRecords": "Duplicate selected records ({ count })", "dynamic_coupon": "Dynamic value coupon", "dynamicCoupon": "Dynamic coupon", "dynamicPointsEarned": "{value} points earned", "dynamicPointsSpent": "{value} points spent", "dynamicRewardRedeemed": "Reward {value} redeemed", "dynamicSegment": "Dynamic segment", "dynamicSegmentDescription": "Segment automatically recalculates based on applied conditions", "dynamicValue": "Dynamic value", "dynamicValueInfo": "The value calculated based on the formula defined when creating a campaign", "earnedPoints": "Earned points", "earnedRewards": "Earned rewards", "earnedUnits": "Earned units", "earnedUnitsSinceLastTierChange": "{ units } earned since the last tier change", "earnedUnitsWithinLastXDays": "{ units } earned within the last X days", "edit": "Edit", "editAchievement": "Edit achievement", "editAchievementModalInfo": "Resetting this progress ensures that all members meet the achievement criteria fairly under the updated rules.", "editAchievementModalWarningBoxInfo": "You have made changes to one or more fields that <span>will reset members' progress</span>. { link }.", "editAchievementRuleWarningBoxInfo": "Changes to the type, trigger, Progress tracking, goals, trigger condition or event limit <span>will reset member progress</span>. This also applies when rules are added, deleted, or duplicated. { link }.", "editAdmin": "Edit admin", "editApiKey": "Edit API key", "editAttribute": "Edit attribute", "editAutomation": "Edit automation campaign", "editBadgeCount": "Edit badge count", "editCampaign": "Edit campaign", "editCategory": "Edit category", "editChannel": "Edit channel", "editCollection": "Edit collection", "editCompletionCountDesc": "When a member completes an achievement they automatically receive the corresponding badge in their profile. Here you can manually adjust how many of these badges the member has earned.", "editCompletionCountWarn": "Changing the completed count on a badge in a member's profile does not affect the completed count of the related achievement.", "editCondition": "Edit condition", "editCustomEvent": "Edit custom event", "editedApiKeyConfirmation": "The API key has been successfully edited.", "editEffect": "Edit effect", "editEventSchema": "Edit event schema", "editFormula": "Edit formula", "editLanguage": "Edit language", "editLeaderboard": "Edit leaderboard", "editMember": "Edit member", "editProfile": "Edit profile", "editReferral": "Edit referral campaign", "editReward": "Edit reward", "editRole": "Edit role", "editRule": "Edit rule", "editRules": "Edit rules", "editSegment": "Edit segment", "editTenant": "Edit tenant", "editTier": "Edit tier", "editTiers": "Edit tiers", "editTiersSet": "Edit tiers set", "editTransaction": "Edit transaction", "editTranslation": "Edit translation", "editWallet": "Edit wallet", "effects": "Effects", "email": "Email", "emailAddress": "Email address", "emailOrPhoneNumber": "Email or phone number", "emptySKUError": "SKU can't be blank", "enableNegativeUnitssBalance": "Enable negative units balance", "enableNotifications": "Enable notifications", "enableWebhooks": "Enable webhooks", "endAt": "End at", "endDate": "End date", "endDateAndTime": "End date and time", "ends_with": "Ends with", "endsWith": "Ends with", "engagedMembers": "Engaged members", "engagedMembersTooltip": "Total number of members engaged with the campaigns at least once within the set date range.", "enterHeaderNameValue": "Enter header name & value", "enterWebhookUrl": "Enter webhook url", "entityID": "Entity ID", "entityType": "Entity type", "eq": "Equals", "error": "Error", "errorDetails": "Error details", "estimatedResults": "Estimated results", "estimatedResultsInfo": "Due to performance optimization, the number of results is displayed as an estimate", "event": "Event", "eventAttributePerXDays": "Event attribute per x days", "eventBased": "Event based", "eventBasedCampaign": "Event-based Campaigns", "eventBasedOnRedemptionCodes": "Events based on generating redemption codes", "eventDate": "Event date", "eventDetails": "Event details", "eventFrequencyLimit": "Event frequency limit", "eventFrequencyLimitPerMember": "Event frequency limit per member", "eventID": "Event ID", "eventLimit": "Event limit", "eventLimitInfo": "Restrict how many times an event can count toward progress within a specified timeframe.", "eventMatching": "Event matching", "eventMatchingInfo": "Choose which identifiers can be used for transaction matching and in which order.\n\nWhen transactions are registered, the system checks identifiers from top to bottom to determine which member the transaction belongs to.\n\nYou can drag items up or down to change this priority order.", "eventName": "Event name", "events": "Events", "eventSchemasImportReview": "Event schemas import review", "eventSourcedFromExternalSystemOrApp": "Events sourced from an external system or application", "eventStatus": "Event status", "eventSystemIdentifier": "Event system identifier", "eventTrackedOnMemberBehaviour": "Event tracked based on members behaviour", "eventType": "Event type", "everyAnniversaryOfRegistrationDate": "Every anniversary of the registration date", "everyDay": "Every day", "everyMonth": "Every month", "everyone": "Everyone", "everyWeek": "Every week", "everyXMonthsAfterReachingCurrentTier": "Every X months after reaching current tier", "everyXNumberOfDays": "Every X number of days", "everyYear": "Every year", "example": "Example", "exampleAchievementConfigurationLabel": "example achievement configuration", "exampleCalculationFormulasLinkLabel": "example calculation formulas", "exampleCoupon": "Example coupon", "excludedSKUs": "Excluded SKUs", "excludedTierSKUs": "SKUs excluded from tiers", "exit": "Exit", "exitDiscardChanges": "Exit & discard changes", "expandMore": "Expand more", "expiration": "Expiration", "expirationDate": "Expiration date", "expirationSettings": "Expiration settings", "expire": "Expire", "expired": "Expired", "expiredOn": "Expired on", "expiredPoints": "Expired points", "expiredUnits": "Expired { units }", "expiredUnitsConst": "Expired units", "expiredUnitsValue": "Expired { value } { units } in { walletName }", "expiredUnitsWalletBalanceAttributes": "Expired units wallet balance attributes", "expiredWalletUnits": "Expired { units } ({ walletName })", "expireFilteredTransfers": "Expire filtered transfers", "expiresAnnually": "Expires annually", "expiresOn": "Expires on", "expireTransferInfo": "Are you sure you want to expire this transfer?", "expireTransfers": "Expire transfers", "expiringCouponsNotification": "Expiring coupons notification", "expiringNotifications": "Expiring notifications", "expiringUnitsConfiguration": "Expiring units configuration", "expiringUnitsNotification": "Expiring units notification", "export": "Export", "exportAchievementsTo": "Export achievements to", "exportAllData": "Export all data", "exportAsCSV": "Export as .CSV", "exportAsJPG": "Export as .JPG", "exportAsPNG": "Export as .PNG", "exportChart": "Export this chart", "exportChartError": "We cannot export the chart right now, try again", "exportConfirmation": "Are you sure you want to export?", "exportDate": "Export date", "exportEndDate": "Export end date", "exportEventSchemaTo": "Export event schema to", "exportId": "Export ID", "exportImagesWarning": "Please note that exporting images within a reward to the JSON file is not supported", "exportingToCSV": "Exporting to CSV", "exportingToCSVInfo": "Select specified date ranges to export your CSV file", "exportInProgress": "Export in progress", "exportIsUnavailable": "Export is unavailable", "exportIsUnavailableInfo": "Export to CSV is unavailable when the list is empty or when no records match your filters.", "exportMembersList": "Export members list", "exportRewardsTo": "Export rewards to", "exports": "Exports", "exportsDescription": "Grants access to view and/or manage the exports list, allowing the export of member lists, including those from tiers and segments, but requires the admin to have permissions to view or modify the corresponding resources.", "exportsImports": "Exports/Imports", "exportSnackbarInfo": "Export has started. You can view progress on the list of exports", "exportStartDate": "Export start date", "exportToCSV": "Export to CSV", "exportToCSVFile": "Export to .CSV file ({value})", "exportToJsonFile": "Export to .json file ({ count })", "exportType": "Export type", "exportWalletTypeTo": "Export wallet type to", "expression": "Expression", "expressions": "Expressions", "external": "External", "externalLogin": "External login via API KEY", "externalTransferId": "External Transfer ID", "failed": "Failed", "failedExport": "Failed export", "failedToImport": "Failed to import", "false": "False", "featured": "Featured", "february": "February", "female": "Female", "fileCannotByImported": "The file cannot be imported - its type is not compatible.", "fileHasUnupportedFieldsAndCouldNotBeImported": "The file has unsupported fields and could not be imported.", "filesFormats": "files from your computer. Accepted formats: jpg, png, gif.", "fileTooLarge": "File too large", "filterAdded": "New filter was successfully added", "findByDocumentNumber": "Find transaction by document number", "findByRewardName": "Find by reward name", "finish": "Finish", "firstName": "First name", "firstSegmentConditionInfo": "Members are added to the segment automatically if they meet the applied conditions", "firstTransactionDate": "First transaction date", "fixedAmountOf100Points": "Fixed amount of 100 points", "fixedValue": "Fixed value", "fixedValueInfo": "Fixed value predefined while adding reward", "flatUnitName": "Flat/Unit name", "flatUnitNameNumber": "Flat unit name number", "focusOnParticipantRestrictions": "Focuses on participant restrictions", "forgotPassword": "Forgot password?", "forgotPasswordInfo": "Enter your login, we will send you recovery password", "format": "Format", "formula": "Formula", "formulaPreview": "Formula preview", "fraudTransactionInfo": "We observed that the behavior during the execution of this transaction was suspicious, take a closer look at it", "friday": "Friday", "friendlyReminder": "Friendly reminder", "friendlyReminderHelpInfo": "Once you've created the achievement, you can easily duplicate it to quickly make a similar one!", "from": "From", "fulfillmentTracking": "Fulfillment tracking", "fulfillmentTrackingProcess": "Fulfillment tracking process", "fullName": "Full name", "fullProfileOfTheMember": "Full profile of the member", "gender": "Gender", "general": "General", "generalInfo": "General info", "generalOverview": "General overview", "generalOverviewAnalyticsDescription": "Grants access to view and filter general overview chart from the dashboard.", "generateApiKey": "Generate API key", "generateApiKeyTooltip": "An API key can be generated after creating the admin.", "generateCodes": "Generate codes", "generatedOn": "Generated on", "generateKey": "Generate key", "generateNewKey": "Generate new key", "generateNewKeyForAuthentication": "Generate a new key for authentication of your external API requests", "generateRedemptionCodes": "Generate Redemption codes", "giftReward": "Gift reward", "give_points": "Add units", "give_reward": "Give reward", "givePointsAutocompleteHint": "Type a formula or click '+' button to choose a template.", "givePointsAutocompleteLink": "Learn more about formulas and see examples.", "globalAdmin": "Global Admin", "globalManagement": "Global management", "globalManagementDescription": "Grants access to view and/or manage global settings, including viewing key metrics and managing loyalty features across multiple tenants and environments.", "globalOverview": "Global overview", "globalSearch": "Global search", "globalUnitsLimitation": "Global units limitation", "goal": "Goal", "goalInfo": "The specification of whether an event should occur consecutively or overall", "goals": "Goals", "goBack": "Go back", "graphicFileMaxSize": ".jpg .png (max. { size }MB)", "grossValue": "Gross value", "grossValueOfProductsFromSpecificBrand": "Gross value of products from specific brand", "grossValueOfProductsFromSpecificCategory": "Gross value of products from specific category", "grossValueOfProductsWithSpecificCustomAttributes": "Gross value of products with specific custom attributes", "grossValueOfProductsWithSpecificSku": "Gross value of products with specific SKU", "gt": "Greater than", "gte": "Greater than or equal to", "guideTour": "Guide tour", "guideTourInfo": "Step-by-step walkthrough", "has_at_least_one_label": "Has at least one label", "hasAtLeastOneCustomAttribute": "Has at least one custom attribute", "header": "Header", "headers": "Headers", "headersInfo": "Specify the custom headers you want to send with webhook.", "headerValue": "Header Value", "help": "Help", "here": "Here", "hidden": "Hidden", "hide": "<PERSON>de", "hideCustomAttributes": "Hide custom attributes", "highestTier": "Highest tier", "homeAnalyticsDescription": "Grants access to additional dashboard metrics, including total members, members without transactions, members with transactions, and members by tiers.", "hourly": "Hourly", "hours": "Hours", "howToImagesOfTheReward": "on how to use images of the ", "howToRedeemReward": "Instruction on how to redeem reward", "howToRedeemRewardIn": "Instruction on how to redeem reward in", "id": "ID", "identificationFactor": "Identification factor", "identificationFactorInfo": "The member’s unique identifier that will be used to match the member with their transactional and behavioral data.", "identifier": "Identifier", "import": "Import", "importAchievements": "Import achievements", "importAutomations": "Import automation campaigns", "importAutomationsSimulateInfo": "Remember you can simulate automation campaigns to ensure they work as intended.", "importCampaigns": "Import campaigns", "importCampaignsSimulateInfo": "Remember you can simulate campaigns to ensure they work as intended.", "importCollection": "Import collection", "importCoupons": "Import coupons", "importCouponsInfo": "Importing coupons to a reward can only be done after creating the reward. { link }", "importCsv": "Import .CSV", "importCsvConfirmationInfo": "The current records will be updated.", "importCsvConfirmationQuestion": "Are you sure you want update your current values?", "importCsvModalLinksText": "Read { importGuide } and see { sampleCsvFile }", "importEndDate": "Import end date", "importErrorDetails": "Import error details", "importEventSchemas": "Import event schemas", "importFileName": "Import file name", "importGuide": "Import guide", "importHasStarted": "Import has started", "importHasStartedInfo": "You can view progress on the list of imports. This can take up to a few minutes", "importHasStartedViewProgress": "Import has started. You can view progress on the list of imports", "importId": "Import ID", "importMatches": "Import matches", "importMatchesWithErrors": "Items has been imported with some errors (Processed {processed}, Success {success}, Failed {failed}", "importMembers": "Import members", "importMembersInCsvInfo": "Import the list of members in CSV format into this segment", "importMembersInfo": "Upload any XML file with members details formatted according to the requirements", "importNewValues": "Import new values", "importReferrals": "Import referral campaigns", "importReferralsSimulateInfo": "Remember you can simulate referral campaigns to ensure they work as intended.", "importRewards": "Import rewards", "imports": "Imports", "importsDescription": "Grants access to import data, including members, members to segments, and unit transfers.", "importsExports": "Imports / Exports", "importStartDate": "Import start date", "importTransactions": "Import transactions", "importTransactionsWithErrors": "Items has been imported with some errors (Processed {processed}, Success {success}, Failed {failed}", "importTransfersType": "Import transfers: {importType} units", "importType": "Import type", "importUnitsTransfers": "Import units transfers", "importUnitsTransfersInfo": "You are about to {currentType} units to members listed in the file.\n<span>Ensure the file only contains transfers to {currentType} units.</span>\n{clickHere} to upload transfers for {importType} units. {learnMore}", "importValues": "Import values", "importWalletType": "Import wallet type", "importWalletTypes": "Import wallet types", "in": "In", "in_last": "In last", "inactive": "Inactive", "inFiltersInfo": "One or few values separated by comma", "information": "Information", "inPeriodFrom": "In period from", "inprogress": "In progress", "inProgress": "In progress", "integrations": "Integrations", "inTemplates": "In templates", "internalEvent": "Internal event", "internalEventName": "Internal event name", "inTotal": "In total", "introConfigDuplicationDescription": "You can duplicate an entire loyalty program to another tenant with just a few clicks.\n Our new feature allows you to effortlessly replicate your campaign settings, achievements, or wallets across different tenants.\n\nSimplify your workflow and ensure consistency in your loyalty programs with ease. Try it now and streamline your business operations like never before!", "introConfigDuplicationTargetTenant": "After this step you will be transferred to the target tenant to which you want to transfer configurations", "introducingConfigDuplication": "Introducing config duplication", "invalidJsonFile": "Invalid JSON file", "inVariables": "In variables", "invitaitons": "Invitaitons", "IP": "IP", "is": "is", "is_after": "Is after", "is_before": "Is before", "is_between": "Is between", "is_day_of_month": "Is nth day of the month", "is_day_of_week": "Is day of week", "is_equal": "Is equal", "is_greater_or_equal": "Is greater or equal", "is_greater_than": "Is greater than", "is_less_or_equal": "Is less or equal", "is_less_than": "Is less than", "is_month_of_year": "Is month of year", "is_not_between": "Is not between", "is_not_equal": "Is not equal", "is_not_one_of": "Is not one of", "is_not_one_of_collections": "Is not one of collections", "is_one_of": "Is one of", "is_one_of_collections": "Is one of collections", "is_time_between": "Is time between", "isActive": "Active", "isAllTimeActive": "Is all time active", "isBetween": "Is between", "isEqual": "Is equal", "isGreaterOrEqual": "Is greater or equal", "isGreaterThan": "Is greater than", "isInCollections": "Is in collections", "isLessOrEqual": "Is less or equal", "isLessThan": "Is less than", "isNotBetween": "Is not between", "isNotEqual": "Is not equal", "isNotInCollections": "Is not in collections", "isNotOneOf": "Is not one of", "isNotOneOfCollections": "Is not one of collections", "isOneOf": "Is one of", "isOneOfCollections": "Is one of collections", "issuanceRate": "Issuance rate", "issued": "Issued", "issuedRewards": "Issued rewards", "issuedRewardsDescription": "Grants access to view and/or manage the list of reward fulfillments, changing reward status. Viewing members profiles linked to rewards fulfillments requires 'MEMBERS' permission.", "issuedUnitsTooltip": "Total number of issued { units } at the end of selected period of time.", "issuer": "Issuer", "isType": "Is type", "itemAdded": "The item has been successfully added", "itemBrand": "Item brand", "itemCategory": "Item category", "itemCustomAttributeValue": "Item custom attribute value", "itemDetails": "Item details", "itemName": "Item name", "itemPrice": "Item price", "itemSku": "Item SKU", "itemSuccessfullyDeleted": "The item has been successfully deleted", "itemUpdated": "The item has been successfully updated", "january": "January", "joinedLoyaltyProgram": "Joined loyalty program", "jsonFile": ".JSON file", "jsonFileMaxSize": ".JSON (max. { size } MB)", "jsonXmlFileMaxSize": ".JSON, .XML (max. { size } MB)", "july": "July", "june": "June", "keepMeLoggedIn": "Keep me logged in", "key": "Key", "keyName": "Key name", "language": "Language", "languageAdded": "Language added", "languageDescription": "Grants access to view and/or manage language settings, including editing, adding, or removing language options, as well as changing the display language from the top navbar.", "languageUpdated": "Language updated", "last": "Last", "last12months": "Last 12 months", "last30Days": "Last 30 days", "lastCustomEventWithinTheXToYNumberOfDays": "Last custom event within the X to Y number of days", "lastDay": "Last day", "lastDayCustomEventExpressionGoal": "In the last { periodValue } { pluralizedDay } the member must log a custom event { event } with the attribute { expressionFormula } summing up to { periodGoal }", "lastDayCustomEventQuantityGoal": "In the last { periodValue } { pluralizedDay } the member must log a custom event { event } at least { periodGoal } { pluralizedTime }", "lastDayOfTheMonth": "Last day of the month", "lastDayTransactionExpressionGoal": "In the last { periodValue } { pluralizedDay }, the member must place transactions with the attribute { expressionFormula } summing up to { periodGoal }", "lastDayTransactionQuantityGoal": "In the last { periodValue } { pluralizedDay }, the member must place { periodGoal } { pluralizedTransaction }", "lastDowngrade": "Last downgrade", "lastMonth": "Last month", "lastName": "Last name", "lastPromotion": "Last promotion", "lastPurchaseTransactionWithinTheXToYNumberOfDays": "Last purchase transaction within the X to Y number of days", "lastRecalculation": "Last recalculation", "lastRecalculationInfo": "The last recalculation column shows when the list was last updated by the system. The system regularly updates lists to add members who match the segment criteria and remove those who no longer match it.", "lastSearch": "Last search", "lastTransactionDate": "Last transaction date", "lastTransactionWithinTheXToYNumberOfDays": "Last transaction within the X to Y number of days", "lastUse": "Last use", "lastWeek": "Last week", "lastxDays": "Last x days", "lastXDays": "Last X days", "lastxDaysHelpInfo": "Tracks how many times an event occurred <span>within the past X days.\nMissing a day does not reset the count.</span>", "lastXMonths": "Last X months", "lastXWeeks": "Last X weeks", "lastYear": "Last year", "latestActivity": "Latest activity", "leaderboardBasicDescription": "Name, describe, and configure the leaderboard", "leaderboardBasicInfo": "Leaderboard basic info", "leaderboardBasicInfoSummary": "Leaderboard basic info", "leaderboardCreated": "Leaderboard created", "leaderboardDescLocale": "Leaderboard description ({ locale })", "leaderboardDetails": "Leaderboard details", "leaderboardInfoDescription": "Set up the essential information and parameters for this leaderboard.", "leaderboardName": "Leaderboard name", "leaderboardNameLocale": "Leaderboard name ({ locale })", "leaderboards": "Leaderboards", "leaderboardsHelpInfo": "Leaderboards rank members by a metric you choose, high to low. \n\nLeaderboards create healthy competition by showing members how they compare to others.\n\n Example", "leaderboardsStatusHelpInfo": "A leaderboard is a dynamic ranking system that displays members in order of their performance based on a specific metric, such as number of custom events, units earned, or spending amount. \n\nLeaderboards create healthy competition by showing members how they compare to others.\n\n Example", "leaderboardStatus": "Set whether the leaderboard is active and collecting data.", "leaderboardUpdated": "Leaderboard updated", "leaderboardVisibility": "Leaderboard visibility", "leaderboardVisibilityHelp": "Leaderboard visibility", "leaderboardVisibilityHelpInfo": "Leaderboard visibility only determines <span>who can see</span> the leaderboard, not who can participate in it.\n\n<span>Visible to Everyone:</span> The leaderboard is visible to all members.\n<span>Tiers Visibility:</span> The leaderboard is visible only to the specific tiers that you choose.\n<span>Segment Visibility:</span> The leaderboard is visible only to the specific segments that you choose.\n<span>Hidden:</span> The leaderboard remains hidden from all members.", "leaderboardVisibilitySubtitle": "Control which members can see your leaderboard in the program.", "learnMore": "Learn more", "learnMoreAboutAchievements": "Learn more about achievements.", "learnMoreAboutBadges": "Learn more about badges", "learnMoreAboutCoupons": "Learn more about coupons", "learnMoreAboutCouponTypes": "{ learnMore } about coupon types", "learnMoreAboutSegments": "Learn more about segments", "learnMoreAboutTierConfiguration": "Learn more about tiers configuration", "learnMoreAboutTiers": "Learn more about tiers.", "learnMoreTimeTriggers": "Learn more how {link} work and see configuration examples.", "left": "Left", "leftToAchieveLimit": "Left to achieve limit", "legalConsent": "Legal consent", "lifecycleReportConfiguration": "Lifecycle report configuration", "like": "Contains", "limit": "Limit", "limitAndBudget": "Limit & budget", "limitation": "Limitation", "limitationAndBudget": "Campaign limitation & budget is one of the ways to incorporate anti-fraud into loyalty programs. \n\n<span>Campaign completion</span> If at least one effect of the campaign is triggered (e.g., out of 5 possible effects), it is counted as a completion. \n\nThis limit ensures that members cannot repeatedly benefit from the campaign beyond the defined threshold.", "limitations": "Limitations", "limitPerMember": "Limit per member", "limits": "Limits", "limitsMemberInfo": "This limit is member based", "limmitation&budget": "Limitation & Budget", "listOfAchievements": "List of achievements", "listOfCampaigns": "List of campaigns", "listOfCollections": "List of collections", "listOfColumns": "List of columns", "listOfExports": "List of exports", "listOfImports": "List of imports", "listOfMembers": "List of members", "listOfOperators": "List of operators", "listOfRewards": "List of rewards", "listOfSelectedFilters": "List of selected filters", "listOfTiers": "List of tiers", "listOfTierSets": "List of tier sets", "listOfTransactions": "List of transactions", "liveVersion": "Live version", "liveVersionDesc": "The live environment where the app is fully available to users.", "loading": "Loading", "lockedPoints": "Locked points", "lockedUntil": "Locked until", "logDetails": "Log details", "logID": "Log ID", "login": "<PERSON><PERSON>", "loginSuccessful": "Login successful", "loginToYourAccount": "Login to your account", "logo": "Logo", "logout": "Logout", "logs": "Logs", "lowAvailability": "Low availability", "lowAvailabilityInfo": "This icon appears when there are 20% or less remaining uses of a particular reward.", "loyaltyCardNumber": "Loyalty card number", "loyaltyModules": "Loyalty modules", "lt": "Less than", "lte": "Less than or equal to", "madeTransaction": "Made transaction \"{ documentNumber }\"", "maintainA7DayLoginStreak": "Maintain a 7-day login streak", "maintainA7DayLoginStreakDescription": "Challenge members to achieve milestones within a custom event. This is a non-transactional achievement that engages customers through gamification elements.", "make3PurchasesInLast14Days": "Make 3 purchases in the last 14 days", "make3PurchasesInLast14DaysDescription": "Activate customers to increase the volume and value of sales by encouraging them to make 3 purchases within the last 14 days.", "make4TransactionsInRow": "Make 4 transactions in a row", "make4TransactionsInRowDescription": "Encourage members to make four consecutive transactions to boost engagement and enhance their ongoing commitment to the brand.", "maker": "Maker", "male": "Male", "manageColumns": "Manage columns", "manageImages": "Manage images", "manageProgress": "Manage progress", "manageProgressInfo": "Increasing the progress of this achievement will trigger the associated campaign(s) once. { seeExample } or { learnMore }.", "manageProgressInfoTooltip": "When you manually increase the progress of an achievement, the system checks if the new progress meets the campaign conditions.\n\nExample: With the achievement '1 point for each transaction,' manually changing the progress from 0 to 5 awards the member 1 point, not 5.", "manageRoleTenantInfo": "Select at least one tenant that the admin will have access to", "manageTenants": "Manage tenants", "manageTimezone": "Manage timezone", "manuallyAssigned": "Manually assigned", "manuallyAssignedTier": "Manually assigned tier", "manuallyAssignedTierDescription": "The member will not drop below the current tier unless assigned to a lower tier or if the current assignment is removed. The member remains locked in this tier until they reach a higher tier, at which point the lock is removed.", "manuallyAssignedTierInfo": "Do you want to remove manually assigned tier?", "manuallyChangedProgress": "Manually changed progress", "manuallyChangedProgressInfo": "Progress of the achievement was manually changed on { date }", "march": "March", "markAllAsRead": "Mark all as read", "markAsUnused": "<PERSON> as unused", "markAsUnusedInfo": "Mark this coupon as unused?", "markAsUsed": "Mark as used", "markAsUsedInfo": "Mark this coupon as used?", "marketingAgreement": "Marketing agreement", "marketingConsent": "Marketing consent", "massActionConfirmation": "Mass action confirmation", "massActionConfirmationInfo": "This action will affect {count}. Processing may take longer depending on how many records are filtered.", "massActions": "Mass actions", "massActionsAreUnavailable": "Mass actions are unavailable", "massActionsAreUnavailableInfo": "Mass actions are unavailable when the list is empty or when no records match your filters.", "massActionsDescription": "Grants access to view and/or manage mass actions, including viewing the mass actions log and performing mass actions across the system.", "massActionsLog": "Mass actions log", "master": "Master", "match": "Match", "matched": "Matched", "matchedMember": "Matched member", "matches_regex": "Matches regex", "matchesImported": "Matches were successfully imported.", "matchesInfo": "Upload any XML file with full matches details formatted according to the requirements", "matchesRegex": "Matches regex", "matchTransaction": "Match transaction with a member", "matchWithMember": "Match with a member", "material": "Material", "materialReward": "Material reward", "materialRewardInfo": "Reward in the form of a material gift", "maxImportAchievementsCountReached": "You've reached the 50-achievement limit. Consider importing fewer at once.", "maxImportAutomationsCountReached": "You've reached the 50-automation campaign limit. Consider importing fewer at once.", "maxImportCampaignCountReached": "You've reached the 50-campaign limit. Consider importing fewer at once.", "maxImportEventSchemaCountReached": "You've reached the 50-event schema limit. Consider importing fewer at once.", "maxImportReferralCountReached": "You've reached the 50-referral campaigns limit. Consider importing fewer at once.", "maxImportRewardCountReached": "You've reached the 50-reward limit. Consider importing fewer at once.", "maxImportWalletTypesCountReached": "You've reached the 50-wallet type limit. Consider importing fewer at once.", "maxNumberOfChars": "The maximum number of characters is {maxNumber}", "may": "May", "member": "Member", "memberActivated": "The member was successfully activated", "memberAdded": "New member was successfully added", "memberAddressChanged": "Member address details updated", "memberAnonymizationHeader": "Anonymization confirmation", "memberAnonymizationInfo": "Are you sure to anonymize this member? You will not be able to undo this operation.", "memberAnonymized": "The member was successfully anonymized", "memberAreAddedAutomaticallyIfMeetAppliedConditions": "Member are added to the tier automatically if they meet the applied conditions.", "memberBadges": "Member badges", "memberCampaignCompletion": "Member campaign completion", "memberCampaignCompletionInfo": "Current campaign completions by a member, up to a specified limit, over a defined period. The limit for campaign completion per member is set in the campaign limitations settings.", "memberCompanyDetailsChanged": "Member company details updated", "memberCustomAttribute": "Member custom attribute", "memberCustomAttributes": "Member custom attributes", "memberCustomAttributeWasRemoved": "Member custom attribute was removed", "memberCustomAttributeWasSet": "Member custom attribute was set", "memberCustomAttributeWasUpdated": "Member custom attribute was updated", "memberDeactivated": "The member was successfully deactivated", "memberDetails": "Member details", "memberDetailsChanged": "Member profile attribute updated", "memberDoesNotExist": "Member doesn't exist", "memberID": "Member ID", "memberIdentifierConfiguration": "Member identifier configuration", "memberIdentifierConfigurationInfo": "<span>Member identifiers</span> help identify each user, contributing to a healthy database.\n\nThe identifiers are <span>email, phone number,</span> and <span>loyalty card number</span>.\n\nEvery loyalty program requires <span>one unique identifier</span> to prevent duplicates.\n\nWhen the system matches transaction with members, it checks all selected member identifiers <span>following the order</span> set on the table to the left.", "memberIdentifierConfigurationWarning": "Required registration fields must be unique.\n\nOnly unique identifiers can be used for matching events.\n\nThis is a tenant-based setting.", "memberIdentifiers": "Member identifiers", "memberIdentifiersConfigurationInfo": "Configure how the system distinguishes each user.", "memberIdentifiersConfigurationWarning": "Please note that once identifier is set as non-unique and saved this change cannot be reversed.", "memberIdSimulation": "When you use the ID of an existing member, the fields above in the member details section will be filled with the member's data for simulation purposes", "memberLifetimeValue": "Member lifetime value", "memberLimitation": "Member limitation", "memberMatchedWithCustomEvent": "Member matched with the custom event", "memberMatchedWithTheTransaction": "Member matched with the transaction", "memberName": "Member name", "memberOperation": "Member operation", "memberPosition": "Member position", "memberProfile": "Member profile", "memberProfileCreated": "Member profile created", "memberProfileUpdated": "Member profile was successfully updated", "memberRegistrationDate": "Member registration date", "memberRemoved": "The member was successfully removed", "members": "Members", "membersBirthday": "Member's birthday", "membersByStatus": "Members by status", "membersByTiers": "Members by tiers", "membersDescription": "Grants access to view and/or manage the members list, single member details, account management add, activate, deactivate, configuration, and transaction matching.", "memberSegments": "Member segments", "membershipAnniversary": "Membership anniversary", "membersImported": "Members were successfully imported.", "membersInSegment": "Members in segment", "membersLifecycleOverview": "Members lifecycle overview", "membersOverview": "Members overview", "membersSegment": "Members / Segment", "membersTier": "Members / Tier", "membersToSegment": "Members to segment", "membersTotal": "Members total", "membersWithCustomAttribute": "Members with custom attribute", "membersWithCustomAttributeValue": "Members with custom attribute value", "membersWithoutTransaction": "Members without transaction", "membersWithTransaction": "Members with transaction", "memberTierChanged": "Member tier updated", "memberTiers": "Member tiers", "memberUnitsLimitation": "Member units limitation", "memberUnitsLimitationInfo": "Current amount of units earned by a member, up to a specified limit, over a defined period. The limit for units issued per member is set in the campaign limitation settings.", "memberWalletUnitsLimitation": "Member { units } limitation", "memberWasActivated": "Member was activated", "memberWasAnonymized": "Member was anonymized", "memberWasDeactivated": "Member was deactivated", "memberWasMovedToLevel": "Member was moved to tier", "memberWasRegistered": "Member was registered", "metric": "Metric", "metrics": "Metrics", "modify": "Modify", "modules": "<PERSON><PERSON><PERSON>", "monday": "Monday", "month": "Month", "monthly": "Monthly", "monthlyStreak": "Monthly streak", "monthlyTriggerDescription": "The automation campaign will trigger in the chosen days every month right after midnight, respecting the time zone set in the settings.", "months": "Months", "monthsSinceJoiningProgram": "Months since joining the program", "monthsSinceJoiningTheProgram": "Months since joining the program", "more": "More", "moreElements": "+ { count } more", "movedFromTierToTier": "Changed tier from \"{ oldTierName }\" to \"{ newTierName }\"", "movedToTier": "Reached first tier \"{ newTierName }\"", "multiInputHelper": "Type and press enter to add", "multiLevelReferral": "Multi level referral", "multiReferralDescription": "Number of levels counted in the multi level campaign configuration", "multiReferralExampleCampaign": "example multi level campaign configuration", "multiReferralLearnMore": "Learn more about {referralLevel} and see {referralExample}", "multiReferralLevel": "Referral level", "multiReferralLevelCondition": "Current referral level for multi level campaign", "multiReferralLevelLink": "referrals levels", "multiReferralMaxLevel": "Referral max levels", "multiWithSelectHelper": "Type the name of the item you are looking for", "name": "Name", "nameIn": "Name in", "nameInfo": "Name of the reward in the default language (English).", "negativeBalanceInfo": "Allows the wallet units to go below zero units, for example in the case of returns. This option is disabled by default. { warning } the misuse of this setting can misrepresent the company's financial position and performance. { learnMore }", "neverExpires": "Never expires", "new": "New", "newAchievement": "New Achievement", "newBadgeName": "New badge name", "newCollection": "New collection", "newCondition": "New condition", "newCustomAttribute": "New custom attribute", "newEffect": "New Effect", "newFilter": "New filter", "newGoal": "New goal", "newMembers": "New members", "newMembersWhoJoinedTheLoyaltyProgram": "The number of new members who joined the loyalty program", "newPassword": "New password", "newPermission": "New permission", "newPointsTransfer": "New points transfer", "newReturnTransactions": "Return \"{ documentNumber }\" placed", "newReward": "New reward", "newTierSet": "New tier set", "newValue": "New value", "next": "Next", "nextRecalculation": "Next recalculation", "nextStep": "Next step", "nip": "<PERSON><PERSON>", "no": "No", "noActivityYet": "No activity yet", "noActivityYetInfo": "Any member activity that impacts this achievement (such as transactions or custom events) will be listed here.", "noCondition": "No condition", "noData": "No data to display", "noDetailsForChosenRecord": "No details for chosen record", "noExpiration": "No expiration", "noLimit": "No limit", "none": "None", "noneResultsInTableMatchTheFilter": "None of the results in this table match the filter that’s been set.", "noOptions": "No options", "noPending": "No pending", "noPermissionInfo": "You don't have permission to access requested page.", "noResultsFor": "No results for \"{ value }\"", "noResultsFound": "No results found", "noResultsMatchThatFilter": "No results match that filter", "noSpacesOrSpecialCharactersAreAllowed": "No spaces or special characters are allowed", "not_disclosed": "Not disclosed", "notBetween": "Not between", "notBetweenDates": "Not between { dateFrom } - { dateTo }", "notContainsOneOf": "Not contains one of", "notDisclosed": "Not disclosed", "notificationsEnabled": "Notifications enabled", "notificationsSettings": "Notifications settings", "notIn": "Not in", "notMatched": "Not matched", "notMatchesRegex": "Not matches regex", "notSavedConditionWarning": "You must complete editing the condition to proceed to the next step", "notSavedGoalWarning": "You must complete editing the goal to proceed to the next step", "notSet": "Not set", "notVisibleForEveryone": "Not visible for everyone", "november": "November", "nthDayOfTheMonth": "Day { number } of the month", "num": "Numeric", "number": "Number", "number/value": "Number/value", "numberOf": "Number of", "numberOfActiveUnitsInWallet": "Number of active units in a wallet", "numberOfCustomEvents": "Number of custom events", "numberOfCustomEventsGoal": "Number of custom events (Goal)", "numberOfDays": "Number of days", "numberOfDaysThisCouponWillBeInThePendingStage": "Number of days this coupon will be in the pending stage", "numberOfEventOccurrences": "Number of event occurrences", "numberOfGeneratedCodes": "Number of generated codes", "numberOfGeneratedRedemptionCodes": "Number of generated Redemption Codes", "numberOfHours": "Number of hours", "numberOfMonths": "Number of months", "numberOfObjects": "Number of objects", "numberOfPurchases": "Number of purchases", "numberOfPurchaseTransactionsInGivenPeriod": "Number of purchase transactions in given period", "numberOfRecords": "Number of records", "numberOfSpecificEventAttributes": "Number of specific event attributes", "numberOfTiers": "Number of tiers", "numberOfTimesTheCampaignIsTriggered": "Number of times the campaign is triggered", "numberOfTransactions": "Number of transactions", "numberOfTransactionsFrom": "Number of transactions from", "numberOfTransactionsGoal": "Number of transactions (Goal)", "numberOfTransactionsTo": "Number of transactions to", "numberOfUnits": "Number of units", "numberOfWeeks": "Number of weeks", "numberOfYears": "Number of years", "numberShort": "No", "objectToProcessID": "Object to process ID", "occurrence": "Occurrence", "october": "October", "of": "of", "ofAttrValue": "Of { value } attr value", "ofEventAttrPerPeriod": "Of { value } event attr per { period }", "offers": "Offers", "offersOverview": "Offers overview", "offersTriggered": "Offers triggered", "offersTriggeredRate": "Offers triggered rate", "oopsSomethingWentWrong": "Oops, something went wrong", "openLoyaltyDocumentation": "Open Loyalty Documentation", "orCondition": "‘OR’ Condition", "order": "Order", "orDragAndDropFile": "or drag and drop file.", "orDragAndDropFileWithTransfers": "or drag and drop a file with transfers", "otherModules": "Other modules", "over": "Over", "overall": "Overall", "overallCustomEventExpressionGoal": "{ goal } the member must log a custom event { event } with the attribute { expressionFormula } summing up to { periodGoal }", "overallCustomEventQuantityGoal": "{ goal } the member must log custom events { event } at least { periodGoal } { pluralizedTime }", "overallGoalHelpInfo": "Counts every occurrence of the event, regardless of timing or frequency.\n<span>Missing any time interval does not affect the count.</span>", "overallTransactionExpressionGoal": "{ goal } the member must place transactions with the attribute { expressionFormula } summing up to { periodGoal }", "overallTransactionQuantityGoal": "{ goal } the member must place { periodGoal } { pluralizedTransaction }", "overrideTheExpirationDate": "Override the expiration date", "overrideUnitsExpirationRules": "Override units expiration rules", "overrideUnitsSettings": "Override units settings", "overrideUnitsSettingsRules": "Override units settings rules", "overrideUnitsSettingsRulesInfo": "Please note that this change applies only to this transfer and does not change wallet settings.", "overTheLast": "Over the last", "p2p_adding": "P2P Adding", "p2p_spending": "P2P Spending", "packing": "Packing", "password": "Password", "passwordSent": "Your password and further instructions have been sent to your email", "passwordSentInfo": "If user exists in our database, we will send next instruction to connected email", "passwordsNotEqualError": "Passwords are not equal", "passwordUpdated": "Password updated", "pause": "Pause", "pending": "Pending", "pendingForNDays": "Pending for { count } days", "pendingForXDays": "Pending for X days", "pendingPoints": "Pending points", "pendingPointsWereAdded": "Pending units were added", "pendingPointsWereCanceled": "Pending units were canceled", "pendingSettings": "Pending settings", "pendingUnits": "Pending { units }", "pendingUnitsAdded": "Activated { value } pending { units } in { walletName }", "pendingUnitsCanceled": "Cancelled { value } pending { units } in { walletName }", "pendingUnitsConst": "Pending units", "pendingUnitsTooltip": "Total number of pending { units } at the end of selected period of time.", "pendingUnitsWalletBalanceAttributes": "Pending units wallet balance attributes", "pendingWalletUnits": "Pending { units } ({ walletName })", "percent": "Percent", "percentageCoupon": "Percentage coupon", "percentageCouponInfo": "Coupon with a fixed percentage value", "percentOfPurchaseTransactionsInChannel": "% of purchase transactions in channel ", "perDay": "Per day", "perHour": "Per hour", "periodInARow": "{ period } in a row", "permanentUserToken": "Permanent user token", "permissions": "Permissions", "permissionsInfo": "Control what admins can view and modify within the system. { learnMore } about permissions", "permissionTooltip": "The 'Modify' permission includes 'View'.\nWhen 'Modify' is selected, 'View' is also enabled and\ncannot be turned off separately.", "perMonth": "Per month", "personalDashboard": "Personal dashboard", "perWeek": "Per week", "perYear": "Per year", "phone": "Phone", "phoneNumber": "Phone number", "plainPassword": "Password", "pleaseSelectDateOnOrAfter": "Please select a date on or after {date}", "plusMore": "+ { more }", "point": "Point", "points": "Points", "pointsBalance": "Points balance", "pointsBlocked": "{ points } points blocked", "pointsByType": "Points by type", "pointsCalculationFormula": "Points calculation formula", "pointsEarned": "Points earned", "pointsIssued": "Points issued", "pointsOverview": "Points overview", "pointsPlural": "Points plural", "pointsRedeemed": "Points redeemed", "pointsSingular": "Points singular", "pointsToTheNextTier": "Points to the next tier", "pointsWereAdded": "Units were added", "pointsWereBlocked": "Units were blocked", "pointsWereCanceled": "Units were canceled", "pointsWereExpired": "Units were expired", "pointsWereSpent": "Units were spent", "pointsWereTransferred": "Units were transferred", "pointsWereUnblocked": "Units were unblocked", "pointsWereUnlocked": "Units were unlocked", "pointsWillBeLockedUntil": "Points will be locked until", "pointValue": "Point value", "popular": "Popular", "popularConditions": "Popular conditions", "popularOffers": "Popular offers", "popularRewards": "Popular rewards", "position": "Position", "postalCode": "Postal code", "predictedSpendingAiInfo": "Predicted Spending is an Artificial Intelligence estimation of member's total transaction value within the next 7 days.", "predictedSpendingWithCurrency": "Predicted spending ({currency})", "preferredMemberRegistrationMethod": "Preferred Member registration method", "previewCode": "Preview code", "previousStep": "Previous step", "price": "Price", "priceInCurrency": "Price in ({ currency })", "priceInPoints": "Price in points", "priceInUnits": "Price in { units }", "probabilityOfNextPurchase": "Probability of the next purchase", "probabilityOfNextPurchaseAiInfo": "Probability of the next purchase, is an Artificial Intelligence estimation of the likelihood that the individual will transact in the next 7 days.", "probabilityOfPurchase": "Probability of purchase", "probabilityOfPurchaseAiInfo": "Probability of purchase is an Artificial Intelligence estimation of the likelihood that suggests how similar the product is to that member usually buy", "product": "Product", "productBrand": "Product brand", "productBrands": "Product brands", "productCategories": "Product categories", "productCategory": "Product category", "productCustomAttribute": "Product custom attribute", "productCustomAttributes": "Product custom attributes", "productFromCategoryX": "Product from category X", "productName": "Product name", "productQuantity": "Product quantity", "products": "Products", "productsInTheTransaction": "Products in the transaction", "productSKU": "Product SKU", "productSKUs": "Product SKUs", "productValueWithCurrency": "Product value ({ currency })", "productWithSpecificSKU": "Product with a specific SKU", "profile": "Profile", "profileBased": "Profile based", "profileEditedSuccessfully": "Profile edited successfully!", "programName": "Program name", "programURL": "Program URL", "progressManuallyChanged": "Progress for \"{ achievementName }\" manually changed", "progressTracking": "Progress tracking", "progressTrackingInfo": "Select how progress is tracked: by the number of times a member performs an action or by the value of specific\nevent attributes.", "progressUpdated": "Progress successfully updated.", "promotionDate": "Promotion date", "property": "Property", "provideCode": "Please provide a code", "provideCurrency": "Please select a currency", "provideName": "Please provide a name", "providePassword": "Please provide a password", "province": "Province", "public": "Public", "purchase": "Purchase", "purchasedAt": "Purchased at", "purchasedProducts": "Purchased products", "purchasePlace": "Purchase place", "purchasePlaceInfo": "A purchase place refers to the specific physical or virtual location where a customer completes a transaction, indicating the precise place or address of the purchase", "purchaseTransaction": "Purchase transaction", "purchaseTransactionCountRange": "Purchase transaction count range", "purchaseTransactionDateRange": "Purchase transaction date range", "purchaseTransactionMatchedWithMember": "Purchase transaction matched with a member", "purchaseTransactions": "Purchase transactions", "purchaseTransactionSuccessfullyMatchedWithMember": "Purchase transaction successfully matched with a member", "purchaseTransactionValueRange": "Purchase transaction value range", "purchaseTransactionWithinTheLastXNumberOfDays": "Purchase transaction within the last X number of days", "pushyApiKey": "Pushy API secret key", "quantity": "Quantity", "quantityOfFilteredTransaction": "Quantity of filtered transaction", "quantityOfProductsMatchCriteria": "Quantity of products that match selected criteria", "queryFilter": "Query filter", "queryFilterInfo": "Regex filter for URL request", "queued": "Queued", "rangePickerApply": "Apply", "rangePickerCancel": "Cancel", "rangePickerDateRange": "Date Range", "rangePickerFrom": "From", "rangePickerFromTo": "Date range: <span>{from} - {to}</span>", "rangePickerInvalidDate": "Invalid date", "rangePickerInvalidRangePeriod": "Invalid date range period", "rangePickerRequired": "Required", "rangePickerTo": "To", "reachedTier": "Reached tier", "readImportGuide": "Read {importGuide}", "readMore": "Read more", "recalculationSettings": "Recalculation settings", "recentDays": "Recent days", "recentMonths": "Recent months", "recentYears": "Recent years", "recipientName": "Recipient Name", "recommendedProducts": "Recommended products", "recordImportStatus": "Record import status", "records": "records", "recordsFailedToImport": "Records failed to import", "recordsInCollection": "Records in collection", "recordStatus": "Record status", "recoverPassword": "Recover password", "redeemed": "Redeemed", "redeemedReward": "Redeemed reward \"{ name }\"", "redeemedRewardId": "Redeemed reward ID", "redeemedRewards": "Redeemed rewards", "redemptionCode": "Redemption code", "redemptionCodeCharacterSet": "Redemption code character set", "redemptionCodeInfo": "Allows you to create a campaign based on codes for later use by members in the form of a bar code, QR code, or code to be entered in the app or on the website.", "redemptionCodeLength": "Redemption code length", "redemptionCodePrefix": "Redemption code prefix", "redemptionCodes": "Redemption codes", "redemptionCodesWithNumber": "Redemption codes ({codesCount})", "redemptionDate": "Redemption date", "redemptionHistory": "Reward redemption's status change history", "redemptionLimit": "Redemption limit", "redemptionLimitInfo": "Leave empty for unlimited redemptions", "redemptionLimitPerMember": "Redemption limit per member", "redemptionLimitPerMemberInfo": "Leave empty for unlimited redemptions per member", "redemptionRate": "Redemption rate", "redemptionRateTooltip": "The percent of issued { units } that have been spent within the set date range.", "refer5Friends": "<PERSON><PERSON> 5 friends", "refer5FriendsDescription": "Enhance the loyalty program by encouraging members to refer five friends, fostering a larger, more engaged community.", "referee": "Referee", "refereeActiveUnits": "Referee active units", "refereeActiveWalletUnits": "Referee active { units } ({ walletName })", "refereeAvgTransactionValue": "Referee avg. transaction value ({currency})", "refereeBadges": "Referee badges", "refereeBlockedUnits": "Referee blocked units", "refereeBlockedWalletUnits": "Referee blocked { walletName } { units }", "refereeBuildingName": "Referee building name", "refereeCity": "Referee city", "refereeCountry": "Referee country", "refereeCustomAttributes": "Referee custom attributes", "refereeDataProcessingConsent": "Referee data processing consent", "refereeDateOfBirth": "Referee birth date", "refereeDateOfRegistration": "Referee date of registration", "refereeEmail": "Referee email", "refereeExpiredUnits": "Referee expired units", "refereeExpiredWalletUnits": "Referee expired { units } ({ walletName })", "refereeExpression": "Referee expression", "refereeFirstName": "Referee first name", "refereeFirstTransactionDate": "Referee first transaction", "refereeFlatUnitName": "Referee flat/unit name", "refereeGender": "Referee gender", "refereeLastName": "Referee last name", "refereeLastTransactionDate": "Referee last transaction date", "refereeLegalConsent": "Referee legal consent", "refereeLockedWalletUnits": "Referee locked { walletName } { units }", "refereeLoyaltyCardNumber": "Referee loyalty card number", "refereeMarketingConsent": "Referee marketing consent", "refereeName": "Referee name", "refereeNumberOfPurchases": "Referee number of purchases", "refereePendingUnits": "Referee pending units", "refereePhoneNumber": "Referee phone number", "refereePostalCode": "Referee postal code", "refereeProvince": "Referee province", "refereeRuleTypeAndGoal": "Referee rule type and goal", "refereeSegments": "Referee segments", "refereeStreet": "Referee street", "refereeTier": "Referee tier", "refereeTierFromDefaultTierset": "Referee tier from Default Tier set", "refereeTierReachedDate": "Referee tier from Default Tier set reached date", "refereeTiers": "Referee tiers", "refereeTotalEarnedUnits": "Referee total earned units", "refereeTotalEarnedWalletUnits": "Referee total earned { units } ({ walletName })", "refereeTotalSpending": "Referee total spending", "refereeUsedUnits": "Referee used units", "refereeUsedWalletUnits": "Referee used { units } ({ walletName })", "refereeWalletBalanceAttributes": "Referee wallet balance attributes", "referral": "Referral", "referralAchievement": "Referral achievement", "referralAchievementInfo": "An achievement where rules are set for members (referrers) and non-members (referees)", "referralBudget": "Referral campaign budget", "referralCampaign": "Referral campaign", "referralCampaignCreated": "Referral campaign was successfully created", "referralCampaignInfo": "A two-sided campaign with rules for referrer and referee", "referralCampaigns": "Referral campaigns", "referralCampaignUpdated": "Referral campaign was successfully updated", "referralCompletionPerMember": "Referral campaign completion per member", "referralDescLocale": "Referral campaign description ({ locale })", "referralDescription": "Referral campaign description", "referralDetails": "Referral campaign details", "referralDisplayOrder": "Referral campaign display order", "referralId": "Referral campaign ID", "referralLimit": "Referral campaign limit", "referralMaxLevels": "Referral max levels", "referralName": "Referral campaign name", "referralNameLocale": "Referral campaign name ({ locale })", "referralOverview": "Referral campaign overview", "referralRate": "Referral rate", "referralRuleType": "Referral rule type", "referrals": "Referrals", "referralsImportReview": "Referral campaigns import review", "referralsOverview": "Referrals overview", "referralStatus": "Referral campaign status", "referralsThatCouldNotBeRemoved": "Referral campaigns that could not be removed", "referralSuccessfullyDeleted": "The referral campaign has been successfully deleted", "referralToken": "Referral token", "referralTrigger": "Referral campaign trigger", "referralTriggersLimitation": "Referral campaign triggers limitation", "referralType": "Referral campaign type", "referralVisibility": "Referral campaign visibility", "referralVisibilityInfo": "Referral campaign visibility is responsible for determining the visibility of the referral campaign to the targeted members in segments. The precise targeting of the referral depends on the conditions specified in the rules section. { link }", "referralWillStartOnlyIfActive": "The referral campaign will start only if it is active", "referredMembers": "Referred members", "referrer": "<PERSON><PERSON><PERSON>", "referrerActiveUnits": "Referrer active units", "referrerActiveWalletUnits": "Referrer active { units } ({ walletName })", "referrerAvgTransactionValue": "Referrer avg. transaction value ({currency})", "referrerBadges": "Referrer badges", "referrerBlockedUnits": "Referrer blocked units", "referrerBlockedWalletUnits": "Referrer blocked { walletName } { units }", "referrerBuildingName": "Referrer building name", "referrerCity": "Referrer city", "referrerCountry": "Referrer country", "referrerCustomAttributes": "Referrer custom attributes", "referrerDataProcessingConsent": "Referrer data processing consent", "referrerDateOfBirth": "<PERSON><PERSON><PERSON> birth date", "referrerDateOfRegistration": "Referrer date of registration", "referrerDetails": "Referrer details", "referrerEmail": "Referrer email", "referrerExpiredUnits": "Referrer expired units", "referrerExpiredWalletUnits": "Referrer expired { units } ({ walletName })", "referrerExpression": "Referrer expression", "referrerFirstName": "<PERSON><PERSON><PERSON> first name", "referrerFirstTransactionDate": "Referrer first transaction", "referrerFlatUnitName": "Referrer flat/unit name", "referrerGender": "Referrer gender", "referrerLastName": "<PERSON><PERSON><PERSON> last name", "referrerLastTransactionDate": "Referrer last transaction date", "referrerLegalConsent": "Referrer legal consent", "referrerLockedWalletUnits": "Referrer locked { walletName } { units }", "referrerLoyaltyCardNumber": "Referrer loyalty card number", "referrerMarketingConsent": "Referrer marketing consent", "referrerName": "Referrer name", "referrerNumberOfPurchases": "Referrer number of purchases", "referrerPendingUnits": "Referrer pending units", "referrerPhoneNumber": "Referrer phone number", "referrerPostalCode": "Referrer postal code", "referrerProvince": "Referrer province", "referrerSegments": "Referrer segments", "referrerStreet": "Referrer street", "referrerTier": "Referrer tier", "referrerTierReachedDate": "Referrer tier from Default Tier set reached date", "referrerTiers": "Referrer tiers", "referrerToken": "Referrer token", "referrerTotalEarnedUnits": "Referrer total earned units", "referrerTotalEarnedWalletUnits": "Referrer total earned { units } ({ walletName })", "referrerTotalSpending": "Referrer total spending", "referrerUsedUnits": "Referrer used units", "referrerUsedWalletUnits": "Referrer used { units } ({ walletName })", "referrerWalletBalanceAttributes": "Referrer wallet balance attributes", "referToAdministratorInfo": "Please refer to your system administrator.", "refreshBrowserInfo": "This page didn't load Open Loyalty correctly, please try refreshing your browser", "refreshPage": "Refresh page", "registered": "Registered", "registeredDate": "Registered date", "registeredMembers": "Registered members", "registeredMembersTooltip": "Total number of new members who activated an account within the set date range. { link } about how the metric is calculated.", "registeredOn": "Registered on", "registeredOnInfo": "The actual date when the units transfer occurred.", "registration": "Registration", "registrationDate": "Registration date", "registrationDateInXNumberOfDays": "Registration date in X number of days", "rejected": "Rejected", "relatedDocumentNumber": "Related document number of purchase transaction", "relatedTransactions": "Related transactions", "relatedTransferID": "Related transfer ID", "relations": "Relations", "remainingCodesInTheCampaign": "Remaining codes in the campaign", "remainingCodesInTheReferral": "Remaining codes in the referral campaign", "remainingUnitsInTheAutomation": "Remaining units in the automation campaign", "remainingUnitsInTheCampaign": "Remaining units in the campaign", "remainingUnitsInTheReferral": "Remaining units in the referral campaign", "remove_member_custom_attribute": "Remove Member Custom Attribute", "removeChannelHeader": "Are you sure you want to delete this channel?", "removeCoupon": "Remove coupon", "removeHeader": "Do you want to remove this translation?", "removeManuallyAssignedTier": "Remove manually assigned tier", "removeProfile": "Remove profile", "removeSegment": "Remove segment", "removeTier": "Remove tier", "removeTranslation": "Remove translation", "repeatNewPassword": "Repeat new password", "repeatOn": "Repeat on", "repeatPassword": "Repeat password", "replaceMembersByCsv": "Replace members by CSV", "replaceMembersInfo": "All current members will be removed from the list and replaced with a new file.", "replaceMembersTitle": "Are you sure you want to replace the list?", "reports": "Reports", "required": "Required", "requiredArray": "This collection should contain 1 element or more.", "requiredField": "Required field", "requiredFieldError": "This field is required", "requiredFieldInfo": "Choose which identifier (only one) is mandatory when creating new members.\n\nRequired registration fields must be unique.", "resetUnits": "Reset all active { units } and move them to the expired { units } pool every { numberOfDays }.", "resimulateCampaign": "Resimulate campaign", "resource": "Resource", "results": "Results", "return": "Return", "returned": "Returned", "returns": "Returns", "returnTransaction": "Return transaction", "returnTransactionMatchedWithMember": "Return transaction matched with a member", "returnTransactions": "Return transactions", "revenue": "Revenue ({ currency })", "revenueTooltip": "Total value of transactions matched with members within the set date range. { link } about how the metric is calculated.", "reviewConfiguration": "Review the configuration", "reward": "<PERSON><PERSON>", "rewardAdded": "The reward has been successfully added", "rewardAvailability": "Reward availability", "rewardCategoryActivated": "Reward category activated", "rewardCategoryAdded": "New reward category was successfully added", "rewardCategoryDeactivated": "Reward category deactivated", "rewardCategoryFormDescription": "Use Reward Categories to group rewards by criteria such as provider, reward type or store", "rewardCategoryUpdated": "Reward category was successfully updated", "rewardCode": "Reward code", "rewardDetails": "Reward details", "rewardFulfillment": "Reward fulfillment", "rewardId": "Reward ID", "rewardImages": "Reward images", "rewardIsAlwaysAvailable": "Reward is always available", "rewardIsAlwaysVisible": "Reward is always visible", "rewardIsAvailabileAllTime": "<PERSON><PERSON> is availabile all time", "rewardIsAvailableInSpecificPeriodOfTime": "Reward is available in specific period of time", "rewardIsVisibleInSpecificPeriodOfTime": "Reward is visible in specific period of time", "rewardName": "Reward name", "rewardOverview": "Reward overview", "rewardPriceInUnits": "Reward price in { units }", "rewards": "Rewards", "rewardsCategories": "Rewards categories", "rewardsCategoriesDescription": "Grants access to view and/or manage the list of rewards categories and add new categories. Selecting a reward categories during reward creation requires 'REWARD' permission.", "rewardsConfiguration": "Rewards configuration", "rewardsDescription": "Grants access to view and/or manage the rewards list, adding, editing, managing reward's images, duplicating rewards, and configuring rewards setting. Claiming rewards requires 'MEMBERS' permission.", "rewardsEarnedFromThisEvent": "Rewards earned from this event", "rewardsEarnedFromThisTransaction": "Rewards earned from this transaction", "rewardsFulfillment": "Rewards fulfillment", "rewardsImportReview": "Rewards import review", "rewardsLimit": "Rewards limit", "rewardsOverview": "Rewards overview", "rewardsRedeemed": "Rewards redeemed", "rewardStatus": "Reward status", "rewardStatusInfo": "Members can redeem the reward once it is active.", "rewardSuccesfullyPurchased": "<PERSON><PERSON> succesfully purchased", "rewardsValue": "<PERSON><PERSON>’s value", "rewardTargeting": "Reward targeting", "rewardType": "Reward type", "rewardUpdated": "<PERSON><PERSON> was successfully updated", "rewardVisibilityInCatalog": "Reward visibility in catalog", "rewardWalletTypeClaimInfo": "By default, units will be deducted from the wallet type selected in your reward configuration", "rewardWalletTypeInfo": "By default, units will be taken from your reward configuration wallet", "rewardWasBought": "Reward was bought", "role": "Role", "roleAdded": "Role has been added", "roleDeleted": "Role deleted", "roleEdited": "Role has been edited", "roles": "Roles", "rounddown": "Rounddown", "roundup": "Roundup", "rows": "Rows", "rowsPerPage": "Rows per page", "rule": "Rule", "ruleConditionsHelpInfo": "Optional, extra conditions that an event must follow to count toward an achievement.\n• <span>For transactions</span>, conditions are based on details like where the purchase was made.\n• <span>For custom events</span>, conditions depend on the custom event's specific parameters.\n• You can also use <span>Expression.</span>\n\nIf no conditions are set, all transactions or chosen custom events will count toward progress.", "ruleDescription": "Rule description", "ruleGoal": "Rule goal", "ruleGoalHelpInfo": "A goal defines how often an event needs to happen to make progress or complete the achievement.\n\nOccurrence:\n• <span>Overall:</span> Anytime.\n• <span>Last X days:</span> Within a set number of days.\n• <span>Streak:</span> In a row (daily, weekly, etc.).", "ruleGoalToggleHelpInfo": "The rule progresses when a referred member (referee) performs the triggers chosen below.\n\nIf you want to reward a member for bringing an active friend (referee) to the loyalty program:\n• <span>Toggle enabled:</span> The member's progress will advance only when each unique referee completes a transaction.\n• <span>Toggle disabled:</span> The member's progress will advance whenever a referee completes a transaction.", "ruleID": "Rule ID", "ruleLimit": "Rule limit", "ruleLimitHelpInfo": "The event limit sets how many times an event can count within a set time period.\n\nFor example, if the goal is <span>4 transactions in 1 month</span> but only <span>1 per week</span>, this limit makes sure only one transaction per week is counted, even if more happen.", "ruleLimitWarnBox": "Limit applies to <span>each individual rule</span>, not to the entire achievement itself.", "ruleName": "Rule name", "ruleNumber": "Rule #{ number }", "ruleProgressTrackingExampleHelpInfo": "<span>Number of event occurrences</span>\nPlace 5 transactions above $50 each.\nAdd 10 products reviews.\nPlace 5 transactions in June.\n\n<span>Value of event attributes</span>\nSpend $10 in February.\nSpend $100 on 'ACME' products.\nWalk 1,000 steps.", "ruleProgressTrackingWarnBox": "If you want to track the <span>value of custom event attributes</span>, ensure the attribute is a number type so it can be summed or counted towards progress.", "rules": "Rules", "rules&effects": "Rules & Effects", "rulesAndEffectDescription": "<span>Rules</span> define the conditions that must be met for participants to have an effect in the campaign. \n\n<span>Effects</span> specify what happens when the rules are met, i.e., give reward for participants.", "rulesAndEffectSummary": "Rules & Effects summary", "ruleSettings": "Rule settings", "ruleSettingsInfo": "Remember that you can add more than one rule.", "rulesForReferee": "Rules for referee (user who was invited)", "rulesForRefereeInfo": "Configure conditions and effects for referee", "rulesForReferrer": "Rules for referrer (user who invites)", "rulesForReferrerInfo": "Configure conditions and effects for referrer", "rulesInAchievement": "Rules in achievement", "rulesStepDescription": "Add rules, set their triggers\nand limits", "rulesSummary": "Rules summary", "ruleTitle": "Rule title", "ruleTitleAndDescriptionHelpInfo": "An achievement can have up to six rules, each with its own conditions, triggers, and types.\n\nEach rule tracks its own progress, and once <span>all rules</span> are fulfilled, the achievement's completion count increases.", "ruleTrigger": "Rule trigger", "ruleTriggerHelpInfo": "A trigger defines <span>which</span> event activates the achievement.\n\nThere are two types of triggers:\n\n<span>Purchase transaction</span> - The rule is triggered automatically based on purchase transactions matched with member.\n\n<span>Custom event</span> - The achievement is triggered by a custom event performed by a member.", "ruleType": "Rule type", "ruleTypeHelpInfo": "Rule type defines <span>who</span> can progress it.\n\nThere are two types:\n\n<span>Direct rule type</span>, where a member progresses their achievement.\n\n<span>Referral rule type</span>, where a referee (user referred by a member) progresses the member's achievement.", "sampleCsvFile": "Sample .csv file", "sampleCsvFileLink": "sample CSV file", "sampleJsonFile": "sample JSON file", "saturday": "Saturday", "save": "Save", "saveAchievement": "Save achievement", "saveAsADraft": "Save as a draft", "saveAttribute": "Save attribute", "saveChanges": "Save changes", "saveCode": "Save code", "saveCondition": "Save condition", "saveConfiguration": "Save configuration", "saveCustomVariable": "Save custom variable", "saveEffect": "Save effect", "saveFilter": "Save filter", "saveGoal": "Save goal", "saveTemplate": "Save template", "saveTransactionFilters": "Save transaction filters", "saveWallet": "Save wallet", "scenario": "<PERSON><PERSON><PERSON>", "schemaActivated": "Schema activated", "schemaAttributeComplex": "This code definition is too complex to be displayed here.", "schemaCodeLearnMore": "Learn more about { link } or check { apiDocs }", "schemaDeactivated": "Schema deactivated", "schemas": "<PERSON><PERSON><PERSON>", "score": "Score", "search": "Search", "searchAttributes": "Search attributes", "searchForCampaign": "Search for campaign...", "searchForConditions": "Search for conditions", "searchForGoals": "Search for goals", "searchForResources": "Search for resources", "searchForRole": "Search for role", "searchForTenant": "Search for tenant", "searchForTenants": "Search for tenants...", "searchForTiers": "Search for tiers", "searchInTable": "Search in table...", "see": "See", "seeActionsRecords": "See actions records", "seeAll": "See all", "seeAllCampaignDetails": "See all campaign details", "seeDetails": "See details", "seeFullProfile": "See full profile", "seeImportedRecords": "See imported records", "seeMemberDetails": "See member details", "seeMemberProfile": "See member profile", "seeReward": "See reward", "seeRewardDetails": "See reward details", "seeTheDashboard": "See the dashboard", "seeTheExample": "See the example", "seeTheListOfMembers": "See the list of members", "seeTheListOfTiers": "See the list of tiers", "seeTheListOfUnitsTransfers": "See the list of { units } transfers", "seeTriggersHistory": "See triggers history", "seeXRules": "See { x } rules", "segment": "Segment", "segmentActivated": "The segment was successfully activated", "segmentActivityInfo": "The segment dynamically recalculates members, but only when it's active.", "segmentAdded": "New segment was successfully added", "segmentDeactivated": "The segment was successfully deactivated", "segmentDescription": "Segment description", "segmentDetails": "Segment details", "segmentEdited": "Segment edited successfully", "segmentMembers": "Segment / Members", "segmentName": "Segment name", "segmentRelationsEmptyStateDesc": "Segment hasn't been used in a campaign, reward or achievement yet.", "segmentRelationsEmptyStateTitle": "No associated campaigns, rewards or\nachievements", "segmentRemoved": "The segment was successfully removed", "segments": "Segments", "segmentsDescription": "Grants access to view and/or manage the segment list, adding segments, and using segments in achievements, campaigns, and rewards. Viewing members in segments requires 'MEMBERS' permission.", "segmentsLimit": "Segments limit", "segmentStatus": "Segment status", "segmentType": "Segment type", "selectAchievementTrigger": "Select achievement trigger", "selectAchievementType": "Select achievement type", "selectAchievementTypeAndTriggerInfo": "Select the achievement type and the event that triggers it.", "selectAllEvents": "Select all events", "selectAutomationTrigger": "Select automation campaign trigger", "selectCampaignTrigger": "Select campaign trigger", "selectCampaignType": "Select campaign type", "selectedLanguage": "Selected language", "selectEventThatStartsCampaign": "Select event that starts campaign", "selectMember": "Select member", "selectMemberInfo": "Type member ID, e-mail, phone number or loyalty card number", "selectReferralTrigger": "Select referral campaign trigger", "selectSetOfTransactionFilters": "Select set of transaction filters", "selectTierSetToRemove": "Select a tier set to remove", "selectToExport": "Select to export", "selectWalletType": "Select wallet type", "selectWalletTypeInfo": "Choose the type of wallet to add or deduct assigned units to.", "sell": "<PERSON>ll", "september": "September", "setAutomationLimitsAndBudget": "Set automation campaign limits & budget", "setCampaignLimitAndBudget": "Set campaign limit & budget", "setCampaignStartAndEndDates": "Set campaign start & end dates.", "setExpirationDate": "Set expiration date", "setGoal": "Set goal", "setLeaderboardTimeframe": "Set when your leaderboard starts and ends to define the competition period.", "setReferralLimitAndBudget": "Set referral campaign limit & budget", "setTargetToCompleteThisRule": "Set the target to complete this rule.", "settings": "Settings", "settingsDescription": "Grants access to view and/or manage various system settings, including wallet types, activation settings, expiring units configuration, units wallet overview on dashboard.", "settingsSaved": "Settings were saved.", "shipped": "Shipped", "shippingAddress": "Shipping address", "shortDescription": "Short description", "showAllElements": "Show all ({ elements })", "showCoupon": "Show coupon", "showDetails": "Show details", "showFromAllTenants": "Show from all tenants", "showFromTenant": "Show from tenant", "showRedemptionCodes": "Show redemption codes", "signIn": "Sign In", "simulateCampaign": "Simulate campaign", "simulateCampaignMemberSectionDescription": "For the referral campaigns, the rules for referees will be applied to this member", "simulateCampaigns": "Simulate campaigns", "simulatedCampaigns": "Simulated campaigns", "simulatedResults": "Simulated results", "simulationComplete": "Simulation complete", "singleAchievementInfo": "Conditions that must be met along with the event", "singleCampaignOverview": "Single campaign view (Campaign overview)", "singleCampaignOverviewDescription": "Grants access to a view and filter campaign specific analytics.", "sku": "SKU", "SKU": "SKU", "sortOrder": "Sort order", "sourceOfCoupons": "Source of coupons", "sourceOfTheCoupons": "Source of the coupons", "sourceOfTheCouponsInfo": "The way of how coupons are generated.", "specificDays": "Specific days", "specificExpiringDate": "Specific expiring date", "spend1000CurrencyUnits": "Spend 1,000 currency units", "spend1000CurrencyUnitsIn2Months": "Spend 1,000 currency units in 2 months", "spend1000CurrencyUnitsIn2MonthsDescription": "Target high-value customers by incentivizing them to spend 1,000 currency units within a 2-month timeframe, maximizing revenue.", "spend100CurrencyUnits": "Spend 100 currency units", "spend100CurrencyUnitsDescription": "Encourage members to spend 100 currency units overall, boosting their transaction count and maximizing their contribution to revenue.", "spend50CurrencyUnitsEachWeekOfMarch": "Spend 50 currency units each week of March", "spend50CurrencyUnitsEachWeekOfMarchDescription": "Encourage members to spend 50 currency units each week, promoting consistent engagement and boosting monthly revenue.", "spendInFebruary": "Spend $10 in February.", "spending": "Spending", "spendingToTheNextTier": "Spending to the next tier", "spendPoints": "Spend points", "spendUnits": "Spend units", "spent": "Spent", "spentUnits": "Spent { units }", "spentUnitsFromWallet": "Spent { value } { units } from { walletName }", "spentUnitsWithComment": "Spent { value } { units } from { walletName } with comment: \"{ comment }\"", "stageVersion": "Stage version", "stageVersionDesc": "A pre-live environment used for testing and final checks. Not accessible to end users.", "starred": "Starred", "startAndEndCampaignDate": "Start & end campaign date", "startAt": "Start at", "startDate": "Start date", "startDateAndTime": "Start date and time", "startFromScratch": "Start from scratch", "starts_with": "Starts with", "startsWith": "Starts with", "state": "State", "stateProvince": "State/Province", "static_coupon": "Fixed value coupon", "staticCoupon": "Static coupon", "staticDate": "Static date", "staticSegment": "Static segment", "staticSegmentDescription": "Segment based on an imported CSV file with a list of members", "staticSegmentInfo": "Importing members from a CSV file can be done after creating the segment. { link }", "status": "Status", "statusEventInfo": "Status refers to the current state of a custom event, indicating whether it has been matched with an existing member or not", "statusInfo": "Status refers to the current state of a transaction, indicating whether it has been matched with an existing member or not.", "statusUpdated": "Status was successfully updated", "storesDescription": "This permission is selected by default to ensure that admins can view content they have access to. Removing it will cause issues with displaying data for the subpages.", "streak": "Streak", "streakHelpInfo": "How many <span>times in a row</span> a member completes an action over a set period (daily, weekly, yearly).\n<span>Missing any required interval resets the streak.</span>", "streakInfo": "In streaks, attributes of an event happen repeatedly in a row, without any gaps.\n\nProgress indicates how many intervals in a row have been completed and the current value of a specific event attribute.", "streakPeriodValue": "Streak period (Value)", "street": "Street", "streetName": "Street name", "succeed": "Succeed", "successActions": "Configure actions that will be taken if the conditions are met.", "successfullyExported": "Successfully exported", "successfullyImported": "Successfully imported", "successfullyImportedRecords": "Successfully imported records", "successfullyImportedToTargetTenant": "Successfully imported to { tenant } tenant", "summary": "Summary", "summaryHelpDesc": "This step provides an overview of your campaign setup, including all key details like rules, effects. Review the configuration to ensure everything is correct. \n\nIf needed, you can return to any step to make changes before finalizing and activating the campaign.", "summaryLeaderboardsHelpDesc": "A leaderboard is a dynamic ranking system that displays members in order of their performance based on a specific metric.\n\nLeaderboards create healthy competition by showing members how they compare to others.\n\n Example", "summaryStepDescription": "Review the\nconfiguration", "sunday": "Sunday", "superadmin": "Superadmin", "surname": "Surname", "switchedOffOverrideUnitsInfo": "Units earned in this { type } will follow the wallet configuration regarding expiration and pending rules", "switchedOnOverridePendingInfo": "Units earned by participantion in this { type } will be pending according to these settings.", "switchedOnOverrideUnitsInfo": "Units earned in this { type } will expire according to these settings, overriding wallet expiration settings.\nPlease note that this change applies only to this effect and does not change wallet settings.", "switchToInactiveTenant": "Switch to inactive tenant", "systemId": "System ID", "systemIdentifier": "System identifier", "systemLogs": "System logs", "target": "Target", "targetAttribute": "Target attribute", "targetedSegments": "Targeted segments", "targetedSegmentsPlaceholder": "Start typing or choose from the list of segments...", "targetedTiers": "Targeted tiers", "targetedTiersPlaceholder": "Start typing or choose from the list of tiers...", "targetTenant": "Target tenant", "targetTenantTransferInfo": "After this step, you will be transferred to the target tenant", "tax": "Tax", "taxID": "TAX ID", "taxIdentificationNumber": "Tax Identification Number", "technicalSettings": "Technical settings", "templates": "Templates", "tenant": "Tenant", "tenantAdded": "New tenant was successfully added", "tenantId": "Tenant ID", "tenants": "Tenants", "tenantUpdated": "Tenant updated", "text": "Text", "thereIsAnErrorInRule": "It looks like there is an error in conditions or effects, edit it to be able to save { type }", "theSegmentWillBeRemoved": "This segment will be removed", "theValueShouldBeGreaterThanZero": "The value should be greater than 0.", "thisAccountWillBeDeactivated": "This account will be deactivated", "thisActionCannotBeUndone": "This action cannot be undone.", "thisActionWillResetProgress": "I understand that this action will reset members progress", "thisMonth": "This month", "thisRewardCategoryWillBeDeactivated": "This reward category will be deactivated", "thisSchemaWillBeActivated": "This schema will be activated", "thisSchemaWillBeDeactivated": "This schema will be deactivated", "thisSegmentWillBeDeactivated": "This segment will be deactivated", "thisTierWillBeDeactivated": "This tier will be deactivated", "thisTierWillBeRemoved": "This tier will be removed", "thisValueShouldNotBeBlank": "This value should not be blank.", "thisWeek": "This week", "thisYear": "This year", "thursday": "Thursday", "tier": "Tier", "tierActivated": "The tier was successfully activated", "tierAdded": "New tier was successfully added", "tierAssignedManually": "Tier assigned manually", "tierAssignedToMember": "<PERSON> was successfully assigned to the member", "tierAssignType": "Tier will be calculated with", "tierBenefits": "Tier benefits", "tierCalculatedWithTransactionsInfo": "Tiers will be calculated based on the member's total spending amount.", "tierConditionsChangeWarning": "Changing the conditions will reset all tiers' threshold values to 0. Remember, after making this change, you'll need to set new values for each tier.", "tierConfigurationInfo": "Every configuration change will apply to each member individually for new unit transfers or transactions. { link }.", "tierDeactivated": "The tier was successfully deactivated", "tierDescription": "Tier description", "tierDowngradeDate": "Tier downgrade date", "tierEdited": "<PERSON> edited successfully", "tierFromDefaultTierset": "Tier from Default Tier set", "tierFromDefaultTierSetReachedDate": "Tier from Default Tier set reached date", "tierID": "Tier ID", "tierName": "Tier name", "tierPhoto": "Tier photo", "tierPromotionDate": "Tier promotion date", "tierReachedDate": "Tier reached date", "tierRecalculationNotification": "Tier recalculation notification", "tierRemoved": "The tier was successfully removed", "tierResetDate": "Tier reset date", "tiers": "Tiers", "tiersConditions": "Tiers conditions", "tiersConfiguration": "Tiers configuration", "tiersDescription": "Grants access to the tiers list, tier sets, and editing tiers. Exporting members from tiers requires 'EXPORT' permission.", "tierSet": "Tier set", "tierSetConditions": "Tier set conditions", "tierSetCreated": "Tier set was successfully created", "tierSetDescription": "Tier set description", "tierSetName": "Tier set name", "tierSetRemoved": "The tier set was successfully removed", "tierSetSettings": "Tier set settings", "tierSetStatusInfo": "Members will be able to join tiers only when it’s active", "tierSetUpdated": "Tier set was successfully updated", "tiersLimit": "Tiers limit", "tiersOverview": "Tiers overview", "tiersSet": "Tier set", "tiersSetConditions": "Tiers set conditions", "tiersSetDowngradeConditions": "Tiers set downgrade conditions", "tiersSetDowngradeConditionsInfo": "Select the recalculation settings for downgrading members between tiers. This happens when members don't fulfill the required conditions to remain in their current tier. { link }", "tiersSetSettings": "Tiers set settings", "tiersSetStatus": "Tiers set status", "time": "Time", "timeBased": "Time-based", "timeBasedCampaign": "Time-based campaign", "timeBasedCampaignInfo": "Campaign based on specific time periods", "timeframe": "Timeframe", "timeframeHelpInfo": "The timeframe defines <span>when</span> the leaderboard counts member activities.\n\n<span>Start date</span> (required): When the leaderboard begins tracking member activity. \n<span>End date</span> (optional): When the leaderboard stops tracking activity. ", "timeframeInfo": "The time when the achievement is available", "timeline": "Timeline", "times": "Times", "timezone": "Timezone", "timezoneInfo": "Determine the timezone to be used for date and time calculations", "timezoneSaved": "Timezone was successfully saved", "title": "Title", "titleAndDescription": "Title and description", "to": "To", "toAddEventSchemaFinisheEditing": "To add an event schema, you must finish editing attributes.", "today": "Today", "token": "Token", "tooltipInformation": "Tooltip information", "tooManyFailedLoginAttempts": "Too many failed login attempts, please try again in 5 minute", "top1000Ranking": "Top 1000 ranking", "toSaveAutomationFinishEditingRules": "To save a automation campaign, you must finish editing conditions and effects", "toSaveCampaignFinishEditingRules": "To save a campaign, you must finish editing conditions and effects", "toSaveFinishEditingRules": "To save, you must finish editing conditions", "totalAmountOfAllRegisteredTransactionsByMember": "Total amount of all registered transactions realized by member", "totalEarned": "Total earned", "totalEarnedPoints": "Total earned points", "totalEarnedUnits": "Total earned { units }", "totalEarnedUnitsConst": "Total earned units", "totalEarnedUnitsWalletBalanceAttributes": "Total earned units wallet balance attributes", "totalEarnedUnitsInWallet": "Total earned units in a wallet", "totalEarnedWalletUnits": "Total earned { units } ({ walletName })", "totalEngagement": "Total engagement", "totalEngagementTooltip": "Total number of times the campaigns engaged members within the set date range.", "totalGrossValueFrom": "Total gross value from", "totalGrossValueTo": "Total gross value to", "totalNumberOfItemsInTransaction": "Total number of items in transaction", "totalNumberOfMembers": "Total number of members", "totalNumberOfProductsWithSpecificCustomAttribute": "Total number of products with specific custom attribute", "totalNumberOfReturnTransactions": "Total number of return transactions", "totalNumberOfTransactions": "Total number of transactions", "totalNumberOfUnits": "Total number of { units } in the market", "totalNumberOfUnitsEarned": "Total number of { units } earned by members", "totalNumberOfUnitsExpired": "Total number of { units } which are expired and can’t be used by members", "totalNumberOfUnitsPending": "Total number of { units } which soon will become active", "totalNumberOfUnitsSpent": "Total number of { units } used by members", "totalReturns": "Total returns", "totalReturnsWithCurrency": "Total returns ({ currency })", "totalSpending": "Total spending", "totalSpendingInGivenPeriod": "Total spending in given period", "totalSpendingInLastXDays": "Total spending in last x days", "totalSpendingWithCurrency": "Total spending ({ currency })", "totalTransactionValueWithCurrency": "Total transaction value ({ currency })", "track3RefereesTransactions": "Track 3 referees' transactions", "track3RefereesTransactionsDescription": "Achievement progresses each time a referee completes their transaction, with all progress tracked on the referrer's account.", "trackedMetric": "Tracked metric", "trackedMetricDescription": "Select what type of activity will be measured and ranked on the leaderboard.", "trackedMetrics": "Tracked metrics", "trackedMetricsEditDesc": "The 'tracked metric' can't be changed in an existing leaderboard.", "trackedMetricsHelpInfo": "Determine <span>which activity</span> will be measured and ranked in this leaderboard.\n\nSelect the metric that best aligns with your program goals.", "trackedMetricsHelpInfoMembers": "Members with the same tracked metric value will tie and appear in the same position on the leaderboard.", "traditionalTiers": "Traditional tiers", "traditionalTiersDesc": "Continue with the classic tier system, where tiers are calculated based on units or transactions.", "transaction": "Transaction", "transaction_amount": "Sum of Transactions gross value", "transactionAdded": "New transaction was successfully added", "transactionalHistory": "Transactional history", "transactionBased": "Transaction based", "transactionBrandGrossValue": "Value of products from a specific brand", "transactionBrandQty": "Quantity of products from a specific brand", "transactionCategoryGrossValue": "Value of products from a specific category", "transactionCategoryQty": "Quantity of products from a specific category", "transactionChannel": "Transaction channel", "transactionChannels": "Transaction channels", "transactionCountFrom": "Transaction count from", "transactionCountTo": "Transaction count to", "transactionCustomAttribute": "Transaction custom attribute", "transactionCustomAttributes": "Transaction custom attribute", "transactionDate": "Transaction date", "transactionDeliveryCity": "Transaction delivery city", "transactionDetails": "Transaction details", "transactionDetailsFraudScoreInfo": "This is a metric that tracks user behavior and indicates any deviation from the norm in behavior, which helps detect potential fraud", "transactionDocumentDate": "Transaction document date", "transactionDocumentNumber": "Transaction document number", "transactionEdited": "Transaction edited successfully", "transactionFilters": "Transaction filters", "transactionFiltersDesc": "Transaction filters are powerful tools that allow you to refine the products in customers transactions. \n\nBy applying specific criteria, such as size, quantity, price, and other relevant factors, you can effectively narrow down the selection to meet your needs.", "transactionFiltersSummary": "Transaction filters summary", "transactionFraudScore": "Transaction fraud score", "transactionGrossValue": "Transaction gross value", "transactionId": "Transaction ID", "transactionItemFilters": "Transaction item filters", "transactionMatched": "Transaction was successfully matched", "transactionPurchasePlace": "Transaction purchase place", "transactionQty": "Quantity of products in the transaction", "transactionQuantity": "Transaction quantity", "transactions": "Transactions", "transactionsAmount": "Transactions amount", "transactionsAmountWithoutDeliveryCosts": "Transactions amount without delivery costs", "transactionsCount": "Transactions count", "transactionsDescription": "Grants access to view and/or manage the transactions list, transaction details, editing and adding transactions, viewing transactions on member profiles, and matching transactions with members (requires 'MEMBERS' permission).", "transactionShippingCity": "Transaction shipping city", "transactionsImported": "Transactions were successfully imported.", "transactionsInfo": "Upload any XML file with full transactions details formatted according to the requirements", "transactionSKUGrossValue": "Value of products with a specific SKU", "transactionSKUQty": "Quantity of products with a specific SKU", "transactionsLabels": "Transaction's labels", "transactionsMatchedWithMembers": "Transactions matched with members", "transactionsRelated": "Transactions related with this { type }", "transactionsTabTooltipDescription": "Total number of purchase and return events generated by the active members and sent to Open Loyalty within a calendar month.", "transactionsTooltip": "Total number of transactions within the set date range. { link } about how the metric is calculated.", "transactionSystemId": "Transaction system ID", "transactionTargetAttribute": "Transaction target attribute", "transactionValue": "Transaction value", "transactionValueFrom": "Transaction value from", "transactionValueInfo": "The transaction value can vary depending on data export configuration in your system, including pricing, discounts, taxes, and extra charges.", "transactionValueTo": "Transaction value to", "transactionValueWithCurrency": "Transaction value ({ currency })", "transferActivated": "Transfer activated", "transferCanceled": "Transfer canceled", "transferExpired": "Transfer expired", "transferID": "Transfer ID", "transferredUnits": "Transferred { value } { units } from { walletName }", "translationDeleted": "Translation has been successfully deleted.", "translations": "Translations", "translationsDescription": "Grants access to view and/or manage translations for all language keys, ensuring that system text can be accurately localized and customized across different languages.", "trigersInTimeBasedAutomationsLink": "triggers in time-base automation campaigns", "trigger": "<PERSON><PERSON>", "triggerBasedConditions": "Trigger-based conditions", "triggerConditions": "Trigger conditions", "triggerConditionsInfo": "Set detailed conditions for the chosen trigger to determine when the achievement progresses. If no conditions are set, all transactions or selected custom events will count toward achievement progress.", "triggerConditionsOptional": "Trigger conditions <span>(Optional)</span>", "triggeredAchievements": "Triggered achievements", "triggeredConditions": "Configure conditions based on which the campaign will be triggered.", "triggersAggregationInfo": "Choose how you want to sum up the triggers", "triggersHistory": "Triggers history", "triggersHistoryInfo": "Transactions or custom events that impacted the progress or completions of this achievement", "triggersLimit": "Triggers limit", "triggerStrategyType": "Time: {type}", "true": "True", "tuesday": "Tuesday", "type": "Type", "typeAndTrigger": "Type and trigger", "typeCondition": "Type condition", "typeEffect": "Type effect", "typeIdentificationFactor": "Type identification factor", "typeInToSearch": "Type in to search", "typeOfReward": "Type of reward", "typeProperty": "Type property...", "typesOfWallets": "Types of wallets", "typeTrigger": "Type & trigger", "undefined": "Undefined", "uniqueCustomEventAttributes": "Unique custom event attributes", "uniqueCustomEventAttributesHelperText": "When enabled, progress increases only for unique values. Example considering the attribute \"brand\":\nA member purchases products from three different brands: will advance progress three times.\nA member purchases three products from the same brand: will advance progress only once.", "uniqueCustomEventAttributesHelpPanel": "Enabling \"Count <span>only unique</span> custom event attributes\" makes progress towards the achievement only increase when a custom event contains an attribute with a new, unique value. Repeated values are not counted towards progress.\n\n<span>Example use case</span>\nEnsure achievement will only progress if member buys from different brands.", "uniqueCustomEventAttributesInfo": "Count only <span>unique</span> custom event attributes", "uniqueField": "Unique field", "uniqueFieldInfo": "Choose whether this identifier must be unique across all members.", "uniqueRefereeHelperText": "If the toggle is enabled, it will take transactions from 10 unique referees to progress the member's achievement.\nIf disabled, a single referee can complete all 10 transactions to progress the member's achievement.", "uniqueRefereeInfo": "Count only <span>one</span> transaction from each referee.", "unit": "Unit", "unitExpirationMethod": "Unit expiration method", "unitExpirationPeriod": "Unit expiration period", "unitLimitiations": "Units limitations", "unitName": "Unit name", "unitPendingMethod": "Unit pending method", "unitPendingPeriod": "Unit pending period", "unitPluralName": "Unit plural name", "units": "Units", "unitsActive": "Active { units } (Cumulative)", "unitsActiveTooltip": "Total number of active { units } at the end of selected period of time.", "unitsBlocked": "{ value } { units } blocked in { walletName }", "unitsConversionCoupon": "Units conversion coupon", "unitsConversionCouponInfo": "Value coupon based on converted loyalty units", "unitsConversionRatio": "{ units } conversion ratio", "unitsConversionRatioInfo": "The conversion rate of { units } into 1 { currency }", "unitsConversionRounding": "{ units } conversion rounding", "unitsEarnedFromThisEvent": "Units earned from this event", "unitsEarnedFromThisTransaction": "Units earned from this transaction", "unitsEarnedStatus": "Units earned status", "unitSettings": "Unit settings", "unitsExpirationPeriod": "Units expiration period", "unitsExpired": "Expired { units }", "unitsExpiredTooltip": "Total number of { units } expired in selected period of time.", "unitsForEveryProductThatMatchesSelectedCriterial": "Units for every product that matches selected criteria", "unitSingularName": "Unit singular name", "unitsIssued": "Issued { units }", "unitsIssuedByAutomation": "Unit issued by the automation campaign", "unitsIssuedByTheAutomation": "Units issued by the automation campaign", "unitsIssuedByTheCampaign": "Units issued by the campaign", "unitsIssuedByTheReferral": "Units issued by the referral campaign", "unitsIssuedPerMember": "Units issued per member", "unitsLeftToAchieveLimit": "{ units } left to achieve limit", "unitsLimit": "Units limit", "unitsLimitations": "Units limitations", "unitsPending": "Pending { units } (Cumulative)", "unitsPendingInWallet": "Pending { value } { units } in { walletName }", "unitsPendingPeriod": "Units pending period", "unitsPendingTooltip": "Total number of pending { units } at the end of selected period of time.", "unitsSpent": "Spent { units }", "unitsSpentTooltip": "Total number of { units } spent in selected period of time.", "unitsToConvert": "Units to convert", "unitsTransfer": "Units transfer", "unitsTransferAdded": "New units transfer was successfully added.", "unitsTransfers": "Units transfers", "unitsTransfersDescription": "Grants access to view and/or manage the unit transfers list, including adding, deducting, canceling, expiring, and activating unit transfers, as well as viewing unit transfer details.", "unitsTransfersType": "Units transfers: {importType} units", "unitsTransferType": "Units transfer type", "unitsUnblocked": "{ value } { units } unblocked in { walletName }", "unitsWalletOverview": "{ units } wallet overview", "unitsYearsActiveCountHelperText": "0 - for the end of this year", "unitTransferInfo": "You are about to { transferType } { operator } the selected member's account. If you want to { alternativeType } instead, { link }.", "unitTransfers": "Unit transfers", "unknownErrorDuringLogin": "Unknown error during login. Please try again.", "unlimited": "Unlimited", "unlimitedNumber": "Unlimited number of redemtions per member", "unlimitedNumberOfCoupons": "Unlimited number of coupons", "unlockedOn": "Unlocked on", "unpaidInvoiceToastDescription": "Your invoice payment is overdue. To avoid any service interruption, please complete your payment as soon as possible.", "unsupportedDeviceHeader": "Your browser width is too small.", "unsupportedDeviceInfo": "Resize your browser to be at least 1000px wide to get back to the dashboard", "unused": "Unused", "updatedAt": "Updated at", "updatedOn": "Updated on", "updateValues": "Update values", "upload": "Upload", "uploadCsvInfo": "Click to upload or drag and drop a file.", "uploadFile": "Upload file", "uploadFileForTransfers": "Upload file for transfers: {currentType} units", "uploadFileFromComputer": "Upload file from computer", "uploadImages": "Upload images", "uploadProfileCover": "Upload profile cover", "uploadProfilePicture": "Upload profile picture", "uriWebhooks": "URI", "url": "Url", "usage": "Usage", "usageChart": "Usage chart", "usageChartDescription": "The chart shows the total number of events created in the system from all tenants within a specified period. As an example, transactions from May that were sent to Open Loyalty in June are displayed on this chart in June, regardless of when they occurred. For a business overview, { link }.", "usageDate": "Usage date", "usageInstruction": "Usage instruction", "useATemplate": "Use a template", "used": "Used", "usedOn": "Used on", "usedPoints": "Used points", "usedRedemptionCodes": "Used redemption codes", "usedUnits": "Used { units }", "usedUnitsConst": "Used units", "usedUnitsWalletBalanceAttributes": "Used units wallet balance attributes", "usedWalletUnits": "Used { units } ({ walletName })", "useOfCouponsCount": "Use of coupons count", "user": "User", "userGuide": "User guide", "userID": "User ID", "username": "Username", "userType": "User type", "useTemplate": "Use template", "validationDays": "Validation on a specific days", "validationEndDate": "Validation end date", "validationStartDate": "Validation start date", "validationTime": "Validation time", "value": "Value", "valueAndFormula": "Value and formula", "valueCoupon": "Value coupon", "valueCouponInfo": "Coupon with a fixed or dynamic currency value", "valueFrom": "Value from", "valueOfCustomEvents": "Value of custom events", "valueOfEventAttributes": "Value of event attributes", "valueOfFilteredTransaction": "Value of filtered transaction", "valueOfProductsMatchCriteria": "Value of products that match selected criteria ({ currency })", "valueOfTax": "Value of tax", "valueOfTransactions": "Value of transactions", "values": "Values", "valueTo": "Value to", "variables": "Variables", "version": "Version", "view": "View", "viewAll": "View all", "viewCode": "View code", "viewEventSchemas": "View event schemas", "viewFullFormula": "View full formula", "viewImportGuide": "View import guide", "viewList": "View list", "viewListOfAchievements": "View list of achievements", "viewListOfAutomations": "View list of automation campaigns", "viewListOfCampaigns": "View list of campaigns", "viewListOfImports": "View list of imports", "viewListOfReferrals": "View list of referral campaigns", "viewListOfRewards": "View list of rewards", "viewListOfWalletTypes": "View list of wallet types", "viewMemberProfile": "View member profile", "visibility": "Visibility", "visibleCampaigns": "Visible campaigns", "visibleForEveryone": "Visible for everyone", "visibleForSegment": "Visible for a specific segment", "visibleForTier": "Visible for a specific tier", "visibleFrom": "Visible from", "visibleTo": "Visible to", "waitingForShipping": "Waiting for shipping", "walk10000StepsADay": "Walk 10,000 steps a day", "walk10000StepsADayDescription": "Challenge members to achieve milestones within a custom event. This is a non-transactional achievement that engages customers through gamification elements.", "wallet": "Wallet", "walletBalanceAttributes": "Wallet balance attributes", "walletCode": "Wallet code", "walletCodeInfo": "This field is mainly used in the API to indicate to which wallet some operations are to be performed, e.g. add, spend units.", "walletConfiguration": "Wallet configuration", "walletDescription": "Wallet Description", "walletDetails": "Wallet details", "walletName": "Wallet name", "walletOverview": "Wallet overview", "wallets": "Wallets", "walletsDescription": "Grants access to view member's wallets within the single member view.", "walletsInfo": "Click on the wallet to see more details", "walletType": "Wallet type", "walletTypeAdded": "Wallet type saved successfully", "walletTypeAllowedForRedemption": "Wallet type allowed for redemption", "walletTypeDoesNotExist": "Wallet Type with such code doest not exist.", "walletTypesImportReview": "Wallet types import review", "walletTypeStatus": "Wallet type status", "walletTypeUpdated": "Wallet type edited successfully", "walletUnitsByType": "{ walletName } { unitsPluralName } by type", "walletUnitsLimit": "{ units } limit", "walletUsedForRewardRedemptions": "Wallet used for reward redemptions", "warning": "Warning", "warningEditTiers": "In the case of editing any of the conditions, the progress of each member will be recalculated individually for the new events and activities.", "weAreSorry": "We are sorry...", "webhookAdded": "Webhook successfully created", "webhookConfiguration": "Webhook configuration", "webhookDescription": "Grants access to view and/or manage the list of active webhooks, including adding and deleting webhooks subscriptions.", "webhookHeaderName": "Request header name", "webhookHeaderValue": "Request header value", "webhookName": "Webhook name", "webhookRemoved": "Webhook successfully removed", "webhooks": "Webhooks", "webhookSubscriptions": "Webhook subscriptions", "webhookUpdated": "Webhook successfully updated", "webhookUrl": "Webhook URL", "weDetectedUnsupportedFields": "We detected unsupported fields", "wednesday": "Wednesday", "week": "Week", "weekly": "Weekly", "weeklyStreak": "Weekly streak", "weeks": "Weeks", "whatsNew": "What’s new", "whatsNewInfo": "To see the complete list of released features, click here", "where": "Where", "whereIn": "Where in", "withAKey": "With a key", "withFormula": "With formula", "withoutEndDate": "Without end date", "withoutPoints": "Without points", "year": "Year", "yearly": "Yearly", "yearlyStreak": "Yearly streak", "years": "Years", "yes": "Yes", "yesActivate": "Yes, activate", "yesCancel": "Yes, cancel", "yesContinue": "Yes, continue", "yesDeactivate": "Yes, deactivate", "yesDelete": "Yes, delete", "yesExpire": "Yes, expire", "yesSaveChanges": "Yes, save changes", "yesterday": "Yesterday", "youCantUndoThisAction": "You can't undo this action"}