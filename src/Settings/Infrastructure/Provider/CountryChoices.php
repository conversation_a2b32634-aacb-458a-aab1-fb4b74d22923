<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Infrastructure\Provider;

use Symfony\Component\Intl\Countries;

class CountryChoices implements ChoiceProvider
{
    public function getChoices(): array
    {
        $choices = [];
        $choiceList = Countries::getNames();

        foreach ($choiceList as $code => $name) {
            $choices[] = [
                'code' => $code,
                'name' => $name,
            ];
        }

        return ['choices' => $choices];
    }

    public function getType(): string
    {
        return 'country';
    }
}
