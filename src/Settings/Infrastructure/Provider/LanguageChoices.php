<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Infrastructure\Provider;

use Symfony\Component\Intl\Languages;

class LanguageChoices implements ChoiceProvider
{
    public function getChoices(): array
    {
        $choices = [];
        $choiceList = Languages::getNames();

        foreach ($choiceList as $code => $name) {
            $choices[] = [
                'code' => $code,
                'name' => $name,
            ];
        }

        return ['choices' => $choices];
    }

    public function getType(): string
    {
        return 'language';
    }
}
