<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="en" target-language="en" datatype="plaintext" original="file.ext">
        <body>
            <trans-unit id="settings.get_manifest.not_found">
                <source>settings.get_manifest.not_found</source>
                <target>Not found</target>
            </trans-unit>
            <trans-unit id="store.code_must_be_unique">
                <source>store.code_must_be_unique</source>
                <target>Store code must be unique</target>
            </trans-unit>
            <trans-unit id="store.code_invalid">
                <source>store.code_invalid</source>
                <target>Invalid store code (related objects are not assigned to the same store)</target>
            </trans-unit>
            <trans-unit id="settings.choices.marketing_vendors.disabled">
                <source>settings.choices.marketing_vendors.disabled</source>
                <target>Disabled</target>
            </trans-unit>
            <trans-unit id="settings.point_expire_after.all_time_active">
                <source>settings.point_expire_after.all_time_active</source>
                <target>Points never expire</target>
            </trans-unit>
            <trans-unit id="settings.point_expire_after.after_x_days">
                <source>settings.point_expire_after.after_x_days</source>
                <target>Points expire after x days</target>
            </trans-unit>
            <trans-unit id="settings.point_expire_after.at_the_end_of_the_month">
                <source>settings.point_expire_after.at_the_end_of_the_month</source>
                <target>Points expire on month's end</target>
            </trans-unit>
            <trans-unit id="settings.point_expire_after.at_the_end_of_the_x_th_year">
                <source>settings.point_expire_after.at_the_end_of_the_x_th_year</source>
                <target>Points expire on the X-th year's end</target>
            </trans-unit>
            <trans-unit id="settings.tier_mode_already_exist">
                <source>settings.settings.tier_mode_already_exist</source>
                <target>Tier mode is already set</target>
            </trans-unit>
        </body>
    </file>
</xliff>
