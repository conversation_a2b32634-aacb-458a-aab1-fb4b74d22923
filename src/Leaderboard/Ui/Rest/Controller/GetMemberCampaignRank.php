<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Core\Domain\Exception\InvalidTriggerException;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use OpenLoyalty\Leaderboard\Application\DataMapper\LeaderboardDataMapper;
use OpenLoyalty\Leaderboard\Infrastructure\Facade\CampaignFacade;
use OpenLoyalty\User\Domain\Customer;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

final class GetMemberCampaignRank extends AbstractFOSRestController
{
    private const MAX_SURROUNDING = 12;
    private const DEFAULT_SURROUNDING = 5;

    public function __construct(
        private readonly LeaderboardDataMapper $leaderboardDataMapper,
        private readonly TranslatorInterface $translator,
        private readonly CampaignFacade $campaignFacade
    ) {
    }

    /**
     * @Route(methods={"GET"}, name="oloy.campaign.leaderboard.member.campaign.get", path="/{storeCode}/campaign/{campaignId}/leaderboard/member/{member}", requirements={"campaignId"="%routing.uuid%"})
     *
     * @Security("is_granted('GET_CAMPAIGN')")
     * @throws InvalidTriggerException
     */
    public function __invoke(Request $request, string $campaignId, Customer $member): View
    {
        $storeId = $member->getStoreId();
        if (null === $storeId) {
            return $this->view(['message' => 'Store not found'], Response::HTTP_NOT_FOUND);
        }

        if (!$this->campaignFacade->isCampaignTriggeredBy(
            new CampaignId($campaignId),
            $storeId,
            Trigger::create(Trigger::LEADERBOARD))
        ) {
            return $this->view([
                'message' => $this->translator->trans('leaderboard.not_leaderboard'),
                'code' => Response::HTTP_NOT_FOUND,
            ],
                Response::HTTP_NOT_FOUND
            );
        }

        // Get surrounding parameter
        $surrounding = $request->query->getInt('surrounding', self::DEFAULT_SURROUNDING);
        if ($surrounding > self::MAX_SURROUNDING) {
            return $this->view([
                'message' => $this->translator->trans('leaderboard.max_surrounding', ['count' => self::MAX_SURROUNDING]),
                'code' => Response::HTTP_BAD_REQUEST,
            ],
                Response::HTTP_BAD_REQUEST
            );
        }

        $response = $this->leaderboardDataMapper->mapMemberLeaderboardRank($member, $surrounding);

        return $this->view($response);
    }
}
