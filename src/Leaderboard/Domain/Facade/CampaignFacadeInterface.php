<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain\Facade;

use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;

interface CampaignFacadeInterface
{
    public function isCampaignTriggeredBy(CampaignId $campaignId, StoreId $storeId, Trigger $triggerType): bool;
}
