<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain\Facade;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\ValueObject\Leaderboard;

interface LeaderboardFacadeInterface
{
    public function createLeaderboardForCampaign(Campaign $campaign, Leaderboard $leaderboardConfig): void;
}
