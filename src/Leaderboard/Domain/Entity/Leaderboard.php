<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain\Entity;

use DateTimeImmutable;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Core\Domain\Id\LeaderboardId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Leaderboard\Domain\ValueObject\Metric;

class Leaderboard
{
    private LeaderboardId $leaderboardId;
    private string $iterPeriod;
    private string $periodFrom;
    private string $periodTo;
    private ?DateTimeImmutable $recalculatedAt = null;
    private ?DateTimeImmutable $completedAt = null;
    private Store $store;
    private Campaign $campaign;
    private Metric $metric;
    
    public function __construct(
        LeaderboardId $leaderboardId,
        string $iterPeriod,
        string $periodFrom,
        string $periodTo,
        Store $store,
        Campaign $campaign,
        Metric $metric
    ) {
        $this->leaderboardId = $leaderboardId;
        $this->iterPeriod = $iterPeriod;
        $this->periodFrom = $periodFrom;
        $this->periodTo = $periodTo;
        $this->store = $store;
        $this->campaign = $campaign;
        $this->metric = $metric;
    }
    
    public function getLeaderboardId(): LeaderboardId
    {
        return $this->leaderboardId;
    }
    
    public function getIterPeriod(): string
    {
        return $this->iterPeriod;
    }
    
    public function getPeriodFrom(): string
    {
        return $this->periodFrom;
    }
    
    public function getPeriodTo(): string
    {
        return $this->periodTo;
    }
    
    public function getRecalculatedAt(): ?DateTimeImmutable
    {
        return $this->recalculatedAt;
    }
    
    public function setRecalculatedAt(DateTimeImmutable $recalculatedAt): void
    {
        $this->recalculatedAt = $recalculatedAt;
    }
    
    public function getCompletedAt(): ?DateTimeImmutable
    {
        return $this->completedAt;
    }
    
    public function setCompletedAt(DateTimeImmutable $completedAt): void
    {
        $this->completedAt = $completedAt;
    }
    
    public function isCompleted(): bool
    {
        return $this->completedAt !== null;
    }
    
    public function getStore(): Store
    {
        return $this->store;
    }
    
    public function getCampaign(): Campaign
    {
        return $this->campaign;
    }
    
    public function getMetric(): Metric
    {
        return $this->metric;
    }
}
