<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain\ValueObject;

final readonly class Metric
{
    public const TYPE_EARNED_UNITS_CUMULATIVE = 'earned_units_cumulative';

    public function __construct(
        private string $type,
        private string $walletTypeCode
    ) {
        if ($type !== self::TYPE_EARNED_UNITS_CUMULATIVE) {
            throw new \InvalidArgumentException(sprintf('Invalid metric type: %s', $type));
        }
        
        if (empty($walletTypeCode)) {
            throw new \InvalidArgumentException('Wallet type code cannot be empty');
        }
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getWalletTypeCode(): string
    {
        return $this->walletTypeCode;
    }
}
