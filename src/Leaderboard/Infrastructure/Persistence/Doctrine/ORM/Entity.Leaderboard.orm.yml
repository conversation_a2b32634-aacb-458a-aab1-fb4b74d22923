OpenLoyalty\Leaderboard\Domain\Entity\Leaderboard:
    type: entity
    table: leaderboard
    id:
        leaderboardId:
            type: leaderboard_id
    cache:
        usage: NONSTRICT_READ_WRITE
        region: campaign
    fields:
        iter_period:
            type: string
            nullable: false
        period_from:
            type: string
            nullable: false
        period_to:
            type: string
            nullable: false
        recalculatedAt:
            type: datetime_immutable_microseconds
        completedAt:
            type: datetime_immutable_microseconds

    manyToOne:
        store:
            targetEntity: OpenLoyalty\Core\Domain\Store
            joinColumn:
                nullable: false
                name: store_id
                referencedColumnName: id
        campaign:
            targetEntity: OpenLoyalty\Campaign\Domain\Campaign
            joinColumn:
                nullable: false
                name: campaign_id
                referencedColumnName: campaign_id
                onDelete: CASCADE
    embedded:
        metric:
            class: OpenLoyalty\Leaderboard\Domain\ValueObject\Metric
    uniqueConstraints:
        walletReadModelIdx:
            columns: [ store_id, leaderboard_id, iter_period ]