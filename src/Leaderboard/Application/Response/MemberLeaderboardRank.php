<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Application\Response;

final readonly class MemberLeaderboardRank
{
    public function __construct(
        public float $score,
        public \DateTimeInterface $recalculatedAt,
        public int $position,
        public \DateTimeInterface $changedPositionAt,
        public Progress $progress,
        public Surrounding $surrounding
    ) {
    }
}
