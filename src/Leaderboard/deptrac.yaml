deptrac:
    paths:
        - ./src
    layers:
        -   name: LeaderboardDomain
            collectors:
                -   type: directory
                    regex: src/Leaderboard/Domain/*
        -   name: LeaderboardApplication
            collectors:
                -   type: directory
                    regex: src/Leaderboard/Application/*
        -   name: LeaderboardInfrastructure
            collectors:
                -   type: directory
                    regex: src/Leaderboard/Infrastructure/*
        -   name: LeaderboardUi
            collectors:
                -   type: directory
                    regex: src/Leaderboard/Ui/*
    ruleset:
        LeaderboardDomain:
            - CoreDomain
            - UserDomain
        LeaderboardApplication:
            - LeaderboardDomain
            - CoreDomain
            - CoreApplication
            - UserDomain
            - CampaignDomain # dependency to remove
        LeaderboardInfrastructure:
            - LeaderboardDomain
            - LeaderboardApplication
            - CoreDomain
            - CoreInfrastructure
        LeaderboardUi:
            - LeaderboardDomain
            - LeaderboardApplication
            - LeaderboardInfrastructure
            - CoreDomain
            - CoreInfrastructure
            - UserDomain
    skip_violations:
        OpenLoyalty\Leaderboard\Infrastructure\Facade\CampaignFacade:
            - OpenLoyalty\Campaign\Domain\CampaignGateInterface
