<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\CustomEvent\Infrastructure\Security\Voter;

use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\PermissionAccess;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * @extends Voter<string, mixed>
 */
class CustomEventVoter extends Voter
{
    public const PERMISSION_RESOURCE = 'CUSTOM_EVENT';

    public const LIST_CUSTOM_EVENT = 'LIST_CUSTOM_EVENT';
    public const SEND_CUSTOM_EVENT = 'SEND_CUSTOM_EVENT';
    public const MANAGE_CUSTOM_EVENT_SCHEMA = 'MANAGE_CUSTOM_EVENT_SCHEMA';
    public const GET_CUSTOM_EVENT_SCHEMA = 'GET_CUSTOM_EVENT_SCHEMA';

    /**
     * @var UserPermissionCheckerInterface
     */
    private $permissionChecker;

    public function __construct(UserPermissionCheckerInterface $permissionChecker)
    {
        $this->permissionChecker = $permissionChecker;
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return null === $subject && in_array($attribute, [
            self::LIST_CUSTOM_EVENT,
            self::SEND_CUSTOM_EVENT,
            self::MANAGE_CUSTOM_EVENT_SCHEMA,
            self::GET_CUSTOM_EVENT_SCHEMA,
        ]);
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        /** @var User $user */
        $user = $token->getUser();

        if (!$user instanceof User) {
            return false;
        }

        $viewAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionInCurrentStore(
                $user,
                self::PERMISSION_RESOURCE,
                [PermissionAccess::VIEW]
            );

        $fullAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionInCurrentStore(
            $user,
            self::PERMISSION_RESOURCE,
            [PermissionAccess::VIEW, PermissionAccess::MODIFY]
        );

        return match ($attribute) {
            self::MANAGE_CUSTOM_EVENT_SCHEMA, self::SEND_CUSTOM_EVENT => $fullAdmin,
            self::LIST_CUSTOM_EVENT, self::GET_CUSTOM_EVENT_SCHEMA => $viewAdmin,
            default => false,
        };
    }
}
