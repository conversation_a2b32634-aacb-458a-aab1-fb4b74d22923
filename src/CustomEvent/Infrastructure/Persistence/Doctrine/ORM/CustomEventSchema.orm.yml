OpenLoyalty\CustomEvent\Domain\CustomEventSchema:
    type: entity
    table: custom_event_schema
    cache:
        usage: NONSTRICT_READ_WRITE
        region: custom_event_schema
    indexes:
        customEventSchemaCreatedAtIdx:
            columns: [ created_at ]
    id:
        id:
            type: integer
            generator:
                strategy: AUTO
    fields:
        eventType:
            type: string
        name:
            type: string
        schema:
            type: custom_event_schema
        active:
            type: boolean
        createdAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        updatedAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        createdBy:
            type: string
            nullable: true
        updatedBy:
            type: string
            nullable: true
    manyToOne:
        store:
            targetEntity: OpenLoyalty\Core\Domain\Store
            joinColumn:
                name: store_id
                nullable: false
                referencedColumnName: id
    uniqueConstraints:
        primary_idx:
            columns: [ event_type, store_id ]
