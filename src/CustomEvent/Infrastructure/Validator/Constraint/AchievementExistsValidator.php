<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\CustomEvent\Infrastructure\Validator\Constraint;

use OpenLoyalty\Achievement\Domain\AchievementRepositoryInterface;
use <PERSON><PERSON>fony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

class AchievementExistsValidator extends ConstraintValidator
{
    public function __construct(
        private readonly AchievementRepositoryInterface $achievementRepository,
        private readonly TranslatorInterface $translator
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (null === $value) {
            return;
        }

        $exists = $this->achievementRepository->byId($value);

        if (null === $exists) {
            $this->context
                ->buildViolation($this->translator->trans('campaign.achievement_not_exist'))
                ->setParameter('{{ achievementId }}', $value)
                ->addViolation();
        }
    }
}
