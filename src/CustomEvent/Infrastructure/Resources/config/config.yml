doctrine:
    dbal:
        types:
            custom_event_id: OpenLoyalty\CustomEvent\Infrastructure\Persistence\Doctrine\Type\CustomEventIdDoctrineType
            custom_event_schema: OpenLoyalty\CustomEvent\Infrastructure\Persistence\Doctrine\Type\CustomEventSchemaDoctrineType
            custom_event_body: OpenLoyalty\CustomEvent\Infrastructure\Persistence\Doctrine\Type\CustomEventBodyDoctrineType
    orm:
        entity_managers:
            readmodel:
                mappings:
                    OpenLoyaltyCustomEvent:
                        type: yml
                        dir: '%kernel.project_dir%/src/CustomEvent/Infrastructure/Persistence/Doctrine/ORM'
                        is_bundle: false
                        prefix: OpenLoyalty\CustomEvent\Domain
jms_serializer:
    metadata:
        directories:
            CustomEvent:
                namespace_prefix: "OpenLoyalty\\CustomEvent\\Domain"
                path: "@OpenLoyaltyCustomEventBundle/Resources/config/serializer"
