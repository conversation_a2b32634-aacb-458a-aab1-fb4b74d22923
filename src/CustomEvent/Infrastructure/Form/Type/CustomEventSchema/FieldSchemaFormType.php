<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\CustomEvent\Infrastructure\Form\Type\CustomEventSchema;

use OpenLoyalty\CustomEvent\Domain\ValueObject\Schema;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Event\PreSubmitEvent;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Count;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Contracts\Translation\TranslatorInterface;

class FieldSchemaFormType extends AbstractType
{
    public function __construct(
        private readonly TranslatorInterface $translator,
        private readonly int $maxCustomEventSchemaFields,
        private readonly int $stringFieldMaxLength
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('type', ChoiceType::class, [
            'required' => true,
            'choices' => Schema::FIELD_TYPES,
            'constraints' => [new NotBlank()],
        ]);

        if (true === $options['name_required']) {
            $builder->add('description', TextType::class, [
                'required' => true,
            ]);
            $builder->add('name', TextType::class, [
                'required' => true,
                'constraints' => [
                    new NotBlank(),
                    new Length(['max' => $this->stringFieldMaxLength]),
                    new Regex([
                        'message' => $this->translator->trans('Space or special chars are not allowed'),
                        'pattern' => '/^[a-zA-Z_][a-zA-Z_0-9]*$/',
                    ]),
                ],
            ]);
        }

        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (PreSubmitEvent $event): void {
            $form = $event->getForm();
            $data = $event->getData();

            if (!isset($data['type'])) {
                return;
            }

            if (Schema::OBJECT_FIELD_TYPE === $data['type']) {
                $form->add('fields', CollectionType::class, [
                    'allow_add' => true,
                    'allow_delete' => true,
                    'entry_type' => FieldSchemaFormType::class,
                    'error_bubbling' => false,
                    'constraints' => [
                        new Count(['min' => 1, 'max' => $this->maxCustomEventSchemaFields])],
                ]);

                return;
            }

            if (Schema::COLLECTION_FIELD_TYPE === $data['type']) {
                $form->add('itemType', FieldSchemaFormType::class, [
                    'required' => true,
                    'constraints' => [new NotBlank()],
                    'name_required' => false,
                ]);
            }
        });
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefault('name_required', true);
    }
}
