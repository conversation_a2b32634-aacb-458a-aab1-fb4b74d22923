<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\CustomEvent\Infrastructure\Form\Type;

use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\DateTimeCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\TextCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\UuidCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\UuidListCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchType;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\DateTimeCriteriaRangeValid;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\MaxCountOfIdsInSearchField;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CustomEventSearchType extends AbstractType
{
    private const MAX_COUNT_OF_IDS_IN_SEARCH_FIELD = 50;

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('customerId', UuidCriteriaType::class);
        $builder->add('eventId', TextCriteriaType::class);

        $builder->add('customEventId', UuidListCriteriaType::class, [
            'default_operator' => CriteriaInterface::IN,
            'allowed_operators' => [
                CriteriaInterface::IN,
            ],
            'constraints' => [
                new MaxCountOfIdsInSearchField(['maxCount' => self::MAX_COUNT_OF_IDS_IN_SEARCH_FIELD]),
            ],
        ]);

        $builder->add('type', TextCriteriaType::class);
        $builder->add('eventName', TextCriteriaType::class);
        $builder->add('eventDate', DateTimeCriteriaType::class,
            ['constraints' => [new DateTimeCriteriaRangeValid()]]
        );
        $builder->add('customerData:email', TextCriteriaType::class);
        $builder->add('customerData:phone', TextCriteriaType::class);
        $builder->add('customerData:loyaltyCardNumber', TextCriteriaType::class);
    }

    public function getParent(): string
    {
        return SearchType::class;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'default_order_by' => 'createdAt',
        ]);
    }
}
