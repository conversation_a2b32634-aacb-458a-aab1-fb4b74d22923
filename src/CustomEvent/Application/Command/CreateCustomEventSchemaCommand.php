<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\CustomEvent\Application\Command;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\CommandInterface;
use OpenLoyalty\CustomEvent\Domain\ValueObject\Active;
use OpenLoyalty\CustomEvent\Domain\ValueObject\Schema;

readonly class CreateCustomEventSchemaCommand implements CommandInterface
{
    public function __construct(
        private string $eventType,
        private StoreId $storeId,
        private string $name,
        private Schema $schema,
        private DateTimeImmutable $createdAt,
        private ?Active $active = null
    ) {
    }

    public function getEventType(): string
    {
        return $this->eventType;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getSchema(): Schema
    {
        return $this->schema;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getActive(): ?Active
    {
        return $this->active ?? null;
    }

    public function getIsActive(): bool
    {
        return !$this->getActive() || $this->getActive()->isActive();
    }
}
