<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\CustomEvent\Application\Command;

use OpenLoyalty\Core\Domain\Message\CommandInterface;
use OpenLoyalty\Core\Domain\Store;

final class ChangeCustomEventSchemaActivityCommand implements CommandInterface
{
    private Store $store;
    private string $eventType;
    private bool $active;

    public function __construct(
        Store $store,
        string $eventType,
        bool $active
    ) {
        $this->store = $store;
        $this->eventType = $eventType;
        $this->active = $active;
    }

    public function getStore(): Store
    {
        return $this->store;
    }

    public function getEventType(): string
    {
        return $this->eventType;
    }

    public function getActive(): bool
    {
        return $this->active;
    }
}
