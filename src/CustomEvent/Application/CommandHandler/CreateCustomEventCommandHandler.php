<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\CustomEvent\Application\CommandHandler;

use OpenLoyalty\Core\Application\TimezoneResolverInterface;
use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\CustomEvent\Application\Command\CreateCustomEventCommand;
use OpenLoyalty\CustomEvent\Domain\CustomEventCreatorInterface;
use Psr\Log\LoggerInterface;

final readonly class CreateCustomEventCommandHandler implements CommandHandlerInterface
{
    public function __construct(
        private StoreRepository $storeRepository,
        private TimezoneResolverInterface $timezoneResolver,
        private LoggerInterface $customEventLogger,
        private CustomEventCreatorInterface $customEventCreator,
    ) {
    }

    public function __invoke(CreateCustomEventCommand $command): void
    {
        $this->timezoneResolver->setDefaultTimezoneByStoreId($command->getStoreId());
        $store = $this->storeRepository->byId($command->getStoreId());

        if (null === $store) {
            $this->customEventLogger->warning(
                sprintf('Store with id %s does not exist. Custom event skipped.', $command->getStoreId())
            );

            return;
        }

        $this->customEventCreator->registerCustomEvent(
            $store,
            $command->getType(),
            $command->getCustomEventId(),
            $command->getCustomerData(),
            $command->getBody(),
            $command->getEventDate(),
            $command->getCreatedAt(),
            $command->getEventId()
        );
    }
}
