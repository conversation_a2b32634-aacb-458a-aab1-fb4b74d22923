<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\CustomEvent\Application\UseCase;

use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\CustomEvent\Application\Command\CreateCustomEventCommand;

final readonly class CreateCustomEventUseCase
{
    public function __construct(
        private CommandBusInterface $commandBus
    ) {
    }

    public function execute(CreateCustomEventCommand $command): void
    {
        $this->commandBus->dispatch($command);
    }
}
