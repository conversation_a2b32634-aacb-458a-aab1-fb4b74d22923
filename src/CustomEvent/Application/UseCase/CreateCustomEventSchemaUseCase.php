<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\CustomEvent\Application\UseCase;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\CustomEvent\Application\Command\CreateCustomEventSchemaCommand;

class CreateCustomEventSchemaUseCase
{
    /**
     * @var CommandBusInterface
     */
    private $commandBus;

    public function __construct(CommandBusInterface $commandBus)
    {
        $this->commandBus = $commandBus;
    }

    public function execute(Store $store, array $data): void
    {
        $this->commandBus->dispatch(new CreateCustomEventSchemaCommand(
            $data['eventType'],
            $store->getStoreId(),
            $data['name'],
            $data['schema'],
            new DateTimeImmutable(),
            $data['active'] ?? null
        ));
    }
}
