<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\CustomEvent\Ui\Rest\Controller\Schema;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View as FosView;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableTotalResponse;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchFormFactoryInterface;
use OpenLoyalty\CustomEvent\Application\UseCase\GetCustomEventSchemaListUseCase;
use OpenLoyalty\CustomEvent\Domain\CustomEventSchema;
use OpenLoyalty\CustomEvent\Infrastructure\Form\Type\CustomEventSchemaSearchType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetList extends AbstractFOSRestController
{
    private GetCustomEventSchemaListUseCase $useCase;
    private SearchFormFactoryInterface $searchFormFactory;

    public function __construct(GetCustomEventSchemaListUseCase $useCase, SearchFormFactoryInterface $searchFormFactory)
    {
        $this->useCase = $useCase;
        $this->searchFormFactory = $searchFormFactory;
    }

    /**
     * @Route(methods={"GET"}, name="oloy.custom_event.schema.list", path="/{storeCode}/customEvent/schema")
     * @Security("is_granted('GET_CUSTOM_EVENT_SCHEMA')")
     *
     * @Operation(
     *     tags={"Custom Event"},
     *     summary="Get custom event schema list",
     *     operationId="customEventSchemaGetList",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(
     *         name="eventType",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="name",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="active",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="boolean")
     *     ),
     *     @OA\Parameter(ref="#/components/parameters/page"),
     *     @OA\Parameter(ref="#/components/parameters/itemsOnPage"),
     *     @OA\Parameter(ref="#/components/parameters/orderBy"),
     *     @OA\Response(
     *         response="200",
     *         description="Returned when successful",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="items",
     *                     type="array",
     *                     @OA\Items(ref=@Model(type=CustomEventSchema::class))
     *                 ),
     *                 @OA\Property(property="total", ref=@Model(type=SearchableTotalResponse::class))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="400",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="401",
     *         ref="#/components/responses/Unauthorized"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     )
     * )
     */
    public function __invoke(Request $request): FOSView
    {
        $form = $this->searchFormFactory->createAndHandle(
            CustomEventSchemaSearchType::class,
            $request->query->all(),
            $request->getLocale()
        );

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $result = $this->useCase->execute($form->getData());

        return $this->view($result, Response::HTTP_OK);
    }
}
