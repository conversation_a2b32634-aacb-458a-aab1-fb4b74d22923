<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\CustomEvent\Domain;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\CustomEventId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Search\SearchableInterface;
use OpenLoyalty\Core\Domain\Search\SearchIterableInterface;

interface CustomEventRepositoryInterface extends SearchableInterface, SearchIterableInterface
{
    public function byId(CustomEventId $customEventId): ?CustomEvent;

    public function byEventId(string $eventId, StoreId $storeId): ?CustomEvent;

    public function save(CustomEvent $customEvent): void;

    /**
     * @return CustomEvent[]
     */
    public function findAllByType(StoreId $storeId, ?string $type = null): array;

    public function getMinCustomEventDateInPeriod(
        StoreId $storeId,
        CustomerId $customerId,
        array $eventTypes,
        DateTimeImmutable $startPeriodDate,
        DateTimeImmutable $endPeriodDate
    ): ?DateTimeImmutable;
}
