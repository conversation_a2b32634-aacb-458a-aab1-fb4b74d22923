<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\CustomEvent\Domain\ValueObject;

class Schema
{
    public const TEXT_FIELD_TYPE = 'text';
    public const NUMBER_FIELD_TYPE = 'number';
    public const DATETIME_FIELD_TYPE = 'datetime';
    public const BOOLEAN_FIELD_TYPE = 'boolean';

    public const OBJECT_FIELD_TYPE = 'object';
    public const COLLECTION_FIELD_TYPE = 'collection';

    public const FIELD_TYPES = [
        self::TEXT_FIELD_TYPE,
        self::NUMBER_FIELD_TYPE,
        self::DATETIME_FIELD_TYPE,
        self::BOOLEAN_FIELD_TYPE,
        self::OBJECT_FIELD_TYPE,
        self::COLLECTION_FIELD_TYPE,
    ];

    private array $rawData;

    public function __construct(array $rawData)
    {
        $this->rawData = $rawData;
    }

    public function getRawData(): array
    {
        return $this->rawData;
    }

    public function getFields(): array
    {
        return $this->rawData['fields'] ?? [];
    }
}
