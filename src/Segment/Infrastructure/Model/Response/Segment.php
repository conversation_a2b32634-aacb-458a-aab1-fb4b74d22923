<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Infrastructure\Model\Response;

use DateTime;
use DateTimeImmutable;
use OpenApi\Annotations as OA;

class Segment
{
    /**
     * @OA\Property(property="segmentId", type="string",  format="uuid")
     */
    private string $segmentId;
    private string $name;
    private ?string $description;
    private bool $active;
    /**
     * @var SegmentPart[]
     */
    private array $parts;
    private DateTime $createdAt;
    private int $customersCount;
    private float $averageTransactionAmount;
    private float $averageTransactions;
    private float $averageClv;
    private ?string $currency;
    /**
     * @OA\Property(
     *     property="calculatedAt",
     *     description="The field is added to the segment when the segment is recalculated."
     * )
     */
    private ?DateTimeImmutable $calculatedAt;

    /**
     * @param SegmentPart[] $parts
     */
    public function __construct(
        string $segmentId,
        string $name,
        ?string $description,
        bool $active,
        array $parts,
        DateTime $createdAt,
        int $customersCount,
        float $averageTransactionAmount,
        float $averageTransactions,
        float $averageClv,
        ?string $currency,
        ?DateTimeImmutable $calculatedAt
    ) {
        $this->segmentId = $segmentId;
        $this->name = $name;
        $this->description = $description;
        $this->active = $active;
        $this->parts = $parts;
        $this->createdAt = $createdAt;
        $this->customersCount = $customersCount;
        $this->averageTransactionAmount = $averageTransactionAmount;
        $this->averageTransactions = $averageTransactions;
        $this->averageClv = $averageClv;
        $this->currency = $currency;
        $this->calculatedAt = $calculatedAt;
    }

    public function getSegmentId(): string
    {
        return $this->segmentId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    /**
     * @return SegmentPart[]
     */
    public function getParts(): array
    {
        return $this->parts;
    }

    public function getCreatedAt(): DateTime
    {
        return $this->createdAt;
    }

    public function getCustomersCount(): int
    {
        return $this->customersCount;
    }

    public function getAverageTransactionAmount(): float
    {
        return $this->averageTransactionAmount;
    }

    public function getAverageTransactions(): float
    {
        return $this->averageTransactions;
    }

    public function getAverageClv(): float
    {
        return $this->averageClv;
    }

    public function getCalculatedAt(): ?DateTimeImmutable
    {
        return $this->calculatedAt;
    }
}
