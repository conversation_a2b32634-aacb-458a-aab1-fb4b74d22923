<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Infrastructure\SystemEvent\Listener;

use OpenLoyalty\Core\Domain\Message\EventHandlerInterface;
use OpenLoyalty\Core\Domain\Message\JobBusInterface;
use OpenLoyalty\Segment\Application\Job\RecreateSegmentJob;
use OpenLoyalty\Segment\Domain\SystemEvent\SegmentChangedSystemEvent;

final class RecalculateSegmentListener implements EventHandlerInterface
{
    protected JobBusInterface $jobBus;

    public function __construct(JobBusInterface $jobBus)
    {
        $this->jobBus = $jobBus;
    }

    public function __invoke(SegmentChangedSystemEvent $event): void
    {
        $this->jobBus->dispatch(new RecreateSegmentJob(
            $event->getStore()->getStoreId(),
            $event->getSegmentId(),
            new \DateTimeImmutable())
        );
    }
}
