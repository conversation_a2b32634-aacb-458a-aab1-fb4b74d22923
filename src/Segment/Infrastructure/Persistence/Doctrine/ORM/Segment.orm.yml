OpenLoyalty\Segment\Domain\Segment:
    type: entity
    table: segment
    indexes:
        segmentCreatedAtIdx:
            columns: [ created_at ]
    id:
        segmentId:
            type: segment_id
            column: segment_id
    fields:
        name:
            type: string
            column: name
        description:
            type: text
            nullable: true
            column: description
        active:
            type: boolean
            options:
                default: 0
        customersCount:
            type: integer
            options:
                default: 0
            column: customers_count
        averageTransactionAmount:
            type: decimal
            scale: 2
            precision: 14
            options:
                default: 0.00
            column: average_transaction_amount
        averageClv:
            type: decimal
            scale: 2
            precision: 14
            options:
                default: 0.00
            column: average_clv
        averageTransactions:
            type: decimal
            scale: 2
            precision: 14
            options:
                default: 0
            column: average_transactions
        createdAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        updatedAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        createdBy:
            type: string
            nullable: true
        updatedBy:
            type: string
            nullable: true
        calculatedAt:
            type: datetime_immutable
            column: calculated_at
            nullable: true
    oneToMany:
        parts:
            targetEntity: OpenLoyalty\Segment\Domain\Model\SegmentPart
            mappedBy: segment
            cascade: [ "all" ]
            orphanRemoval: true
    manyToOne:
        store:
            targetEntity: OpenLoyalty\Core\Domain\Store
            joinColumn:
                name: store_id
                nullable: false
                referencedColumnName: id
