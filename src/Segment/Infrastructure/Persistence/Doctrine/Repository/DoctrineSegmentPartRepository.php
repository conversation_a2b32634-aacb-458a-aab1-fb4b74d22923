<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Infrastructure\Persistence\Doctrine\Repository;

use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\SegmentPartId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\Segment\Domain\Model\Criteria\CampaignCompletion;
use OpenLoyalty\Segment\Domain\Model\SegmentPart;
use OpenLoyalty\Segment\Domain\SegmentPartRepository;

class DoctrineSegmentPartRepository extends DoctrineRepository implements SegmentPartRepository
{
    protected function getClass(): string
    {
        return SegmentPart::class;
    }

    public function byId(SegmentPartId $segmentPartId): ?object
    {
        return parent::find($segmentPartId);
    }

    public function save(SegmentPart $segmentPart): void
    {
        $this->entityManager->persist($segmentPart);
        $this->entityManager->flush();
    }

    public function remove(SegmentPart $segmentPart): void
    {
        $this->entityManager->remove($segmentPart);
        $this->entityManager->flush();
    }

    public function isCampaignExistsInSegmentPartCriteria(CampaignId $campaignId, StoreId $storeId): bool
    {
        $qb = $this->entityManager->createQueryBuilder();
        $qb->select('sp')
            ->from(SegmentPart::class, 'sp')
            ->join('sp.criteria', 'c')
            ->join('sp.segment', 's')
            ->join(CampaignCompletion::class, 'cc', 'WITH', 'cc = c')
            ->where('cc.campaignId = :campaignId')
            ->andWhere('s.store = :storeId')
            ->setParameter('campaignId', (string) $campaignId)
            ->setParameter('storeId', (string) $storeId)
            ->setMaxResults(1);

        $result = $qb->getQuery()->getOneOrNullResult();

        return null !== $result;
    }
}
