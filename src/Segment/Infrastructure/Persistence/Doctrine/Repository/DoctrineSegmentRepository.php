<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Infrastructure\Persistence\Doctrine\Repository;

use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\Segment\Domain\Segment;
use OpenLoyalty\Segment\Domain\SegmentRepository;

class DoctrineSegmentRepository extends DoctrineRepository implements SegmentRepository
{
    protected function getClass(): string
    {
        return Segment::class;
    }

    public function byId(SegmentId $segmentId): ?Segment
    {
        return $this->find($segmentId);
    }

    public function findAllActive(): array
    {
        $qb = $this->repository->createQueryBuilder('e');
        $qb->andWhere('e.active = :true')->setParameter('true', true);

        return $qb->getQuery()->getResult();
    }

    public function save(Segment $segment): void
    {
        $this->entityManager->persist($segment);
        $this->entityManager->flush();
    }

    public function remove(Segment $segment): void
    {
        $this->entityManager->remove($segment);
        $this->entityManager->flush();
    }

    public function countAllActiveInAllStores(?SegmentId $excludedId = null): int
    {
        $qb = $this->repository->createQueryBuilder('e');
        $qb->select('count(e.segmentId)');
        $qb->andWhere('e.active = :active')->setParameter('active', true);
        if ($excludedId) {
            $qb->andWhere('e.segmentId != :excludedId')->setParameter('excludedId', $excludedId);
        }

        return $this->resultCacheResolver->resolveQuick($qb->getQuery())->getSingleScalarResult();
    }
}
