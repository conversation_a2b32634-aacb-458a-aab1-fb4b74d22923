<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Infrastructure\DataFixtures\ORM;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Faker\Factory;
use OpenLoyalty\Channel\Infrastructure\DataFixtures\ORM\LoadChannelData;
use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Segment\Application\Command\ActivateSegment;
use OpenLoyalty\Segment\Application\Command\CreateSegment;
use OpenLoyalty\Segment\Domain\Model\Criteria\Anniversary;
use OpenLoyalty\Segment\Domain\Model\Criterion;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\User\Infrastructure\DataFixtures\ORM\LoadUserData;

class LoadSegmentData extends Fixture implements OrderedFixtureInterface, FixtureGroupInterface
{
    public const SEGMENT_ID = '29ae4a98-2315-40c2-9e0f-96e370ed5b1c';
    public const SEGMENT2_ID = '006e08f0-38ec-4f74-9309-ba55f2e461b0';
    public const SEGMENT3_ID = 'c6d19576-7083-4ab5-ac6a-1563008c4d99';
    public const SEGMENT4_ID = '0f12ba53-adcb-4ed8-8b6d-e3fda4306b11';
    public const SEGMENT5_ID = 'aa65076d-55b9-44fe-964b-06c9e51d8308';
    public const SEGMENT6_ID = '247fe74f-52e9-4b48-ba1c-580d55afa5c8';
    public const SEGMENT7_ID = '4cd81e1b-78bf-4f45-92ab-3f8124630fc6';
    public const SEGMENT8_ID = '9766b577-a84c-4a5b-ab0b-9252d9778b0c';
    public const SEGMENT9_ID = '07fafeb4-fb5c-496f-92c3-9a9d30689b3c';
    public const SEGMENT10_ID = 'f71a7464-f04a-43e1-bf37-119f9f5eb517';
    public const SEGMENT11_ID = 'f1d5ea1a-373b-4b77-a3df-695e850e9aeb';
    public const SEGMENT12_ID = '992a1035-9df9-4ad3-8722-6e4344ca5802';
    public const SEGMENT13_ID = 'e49db235-b7e8-49ea-981b-158fd205800a';
    public const SEGMENT14_ID = 'c2310d4e-c300-49f8-acba-f127866040d8';
    public const SEGMENT15_ID = '317fa5e7-292e-4ee2-9ce3-277493f98e08';

    public const DATA = [
        LoadSettingsData::DEFAULT_STORE_CODE => [
            'segmentId' => self::SEGMENT_ID,
            'segment2Id' => self::SEGMENT2_ID,
            'segment3Id' => self::SEGMENT3_ID,
            'segment4Id' => self::SEGMENT4_ID,
            'segment5Id' => self::SEGMENT5_ID,
            'segment6Id' => self::SEGMENT6_ID,
            'segment7Id' => self::SEGMENT7_ID,
            'segment8Id' => self::SEGMENT8_ID,
            'segment9Id' => self::SEGMENT9_ID,
            'segment10Id' => self::SEGMENT10_ID,
            'segment11Id' => self::SEGMENT11_ID,
            'segment12Id' => self::SEGMENT12_ID,
            'segment13Id' => self::SEGMENT13_ID,
            'segment14Id' => self::SEGMENT14_ID,
            'segment15Id' => self::SEGMENT15_ID,
        ],
        LoadSettingsData::STORE_A_CODE => [
            'segmentId' => '8ad2c915-fb98-4048-8a81-2dcacacc4ed6',
            'segment2Id' => 'c29c9c06-e8b4-4e86-b10b-bf13d6f7cedc',
            'segment3Id' => '14440317-c378-4ca4-b30f-b6c0b2b19f6d',
            'segment4Id' => '1ab4fe41-770e-4b97-8241-43cdf9773de3',
            'segment5Id' => '3f2669fe-8f98-4c16-918a-bff88d728c67',
            'segment6Id' => '0883bf6b-a393-4959-91cb-13ca4c42a539',
            'segment7Id' => 'da6807d6-0eae-4325-9929-89d921022e0f',
            'segment8Id' => '996331aa-6534-44b3-9797-4d0fb3fb564e',
            'segment9Id' => '5a75c98d-8127-4c1b-8e6e-69a96e9a7d37',
            'segment10Id' => 'c744ed96-59ee-40bd-b7b3-712fd98f0dc7',
            'segment11Id' => '6ac6e4c1-3331-407f-a3ea-19f43941d792',
            'segment12Id' => 'ed252a27-2abf-41ee-8f53-8999f4f98167',
            'segment13Id' => '14b0a1ab-06a0-4363-b122-f12a04d70e66',
            'segment14Id' => '1b28259e-ecd2-45b2-ad34-c88461b45237',
            'segment15Id' => '137fdafa-abb2-4317-9660-c7728c09d566',
        ],
        LoadSettingsData::STORE_B_CODE => [
            'segmentId' => '1507d3f2-9d05-4362-9f7e-b06e84c79b20',
            'segment2Id' => '858e73d2-1a09-4d97-a478-5f624c6ee7b7',
            'segment3Id' => '503aa9c2-a2e4-4d10-a348-ae9f2426ca6d',
            'segment4Id' => '22671f35-2360-47df-b6b7-9e360cc9f97f',
            'segment5Id' => 'ed6634f2-e21f-488a-a46f-d9941c53e26f',
            'segment6Id' => 'cfa07645-8636-4e60-84f8-220567ac8303',
            'segment7Id' => '5da13d2f-a584-4fd8-8bf9-a9272d004f13',
            'segment8Id' => 'bb7c8515-f07c-4508-b8a6-31bcaf201f7b',
            'segment9Id' => 'aa5db201-e7c0-4680-8834-31f5bf16e667',
            'segment10Id' => 'd76bfb64-da77-404c-8f4f-18e15019dedb',
            'segment11Id' => '573d8ab6-cabb-49c9-96b2-204d591f455f',
            'segment12Id' => '122e48c1-12c6-47b9-bb6c-1a5be496ad21',
            'segment13Id' => 'cf258c0e-6431-40a8-918a-9a91be080d75',
            'segment14Id' => '615ae87a-690b-4603-84eb-a9cc5e4e59e0',
            'segment15Id' => 'ef05ad22-3e1d-41b1-a793-5e6bff5a608e',
        ],
    ];

    public function __construct(
        private readonly CommandBusInterface $commandBus
    ) {
    }

    /**
     * {@inheritdoc}
     */
    public static function getGroups(): array
    {
        return ['setup'];
    }

    public function load(ObjectManager $manager): void
    {
        $this->loadData(LoadSettingsData::DEFAULT_STORE_CODE);
        $this->loadData(LoadSettingsData::STORE_A_CODE);
    }

    public function getOrder(): int
    {
        return 99;
    }

    protected function loadData(string $storeCode): void
    {
        $faker = Factory::create();

        /** @var Store $store */
        $store = $this->getReference($storeCode);

        $this->commandBus->dispatch(
            new CreateSegment(
                new SegmentId(self::DATA[$store->getCode()]['segmentId']), $store->getStoreId(), [
                    'name' => 'test',
                    'description' => 'desc',
                    'parts' => [
                        [
                            'segmentPartId' => $faker->uuid,
                            'criteria' => [
                                [
                                    'type' => Criterion::TYPE_BOUGHT_THROUGH_CHANNEL,
                                    'criterionId' => $faker->uuid,
                                    'channelIds' => [LoadChannelData::DATA[$store->getCode()]['channelId']],
                                ],
                                [
                                    'type' => Criterion::TYPE_AVERAGE_TRANSACTION_AMOUNT,
                                    'criterionId' => $faker->uuid,
                                    'fromAmount' => 1,
                                    'toAmount' => 10000,
                                ],
                                [
                                    'type' => Criterion::TYPE_TRANSACTION_COUNT,
                                    'criterionId' => $faker->uuid,
                                    'min' => 10,
                                    'max' => 20,
                                ],
                            ],
                        ],
                    ],
                ]
            )
        );
        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment2Id']), $store->getStoreId(), [
                'name' => 'anniversary',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_ANNIVERSARY,
                                'criterionId' => $faker->uuid,
                                'days' => 10,
                                'anniversaryType' => Anniversary::TYPE_BIRTHDAY,
                            ],
                        ],
                    ],
                ],
            ])
        );
        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment3Id']), $store->getStoreId(), [
                'name' => 'purchase period',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_PURCHASE_PERIOD,
                                'criterionId' => $faker->uuid,
                                'fromDate' => new \DateTime('2014-12-01'),
                                'toDate' => new \DateTime('2015-01-01'),
                            ],
                        ],
                    ],
                ],
            ])
        );
        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment4Id']), $store->getStoreId(), [
                'name' => 'last purchase 10 days ago',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_LAST_PURCHASE_N_DAYS_BEFORE,
                                'criterionId' => $faker->uuid,
                                'days' => 10,
                            ],
                        ],
                    ],
                ],
            ])
        );
        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment5Id']), $store->getStoreId(), [
                'name' => 'transaction amount 10-50',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_TRANSACTION_AMOUNT,
                                'criterionId' => $faker->uuid,
                                'fromAmount' => 10,
                                'toAmount' => 50,
                            ],
                        ],
                    ],
                ],
            ])
        );
        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment6Id']), $store->getStoreId(), [
                'name' => '10 percent of transactions were performed in channel',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_TRANSACTION_PERCENT_IN_CHANNEL,
                                'criterionId' => $faker->uuid,
                                'percent' => 0.10,
                                'channelId' => LoadChannelData::DATA[$store->getCode()]['channelId'],
                            ],
                        ],
                    ],
                ],
            ])
        );
        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment7Id']), $store->getStoreId(), [
                'name' => 'bought skus',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_BOUGHT_SKUS,
                                'criterionId' => $faker->uuid,
                                'skuIds' => ['SKU1'],
                            ],
                        ],
                    ],
                ],
            ])
        );
        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment8Id']), $store->getStoreId(), [
                'name' => 'bought makers',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_BOUGHT_MAKERS,
                                'criterionId' => $faker->uuid,
                                'makers' => ['company'],
                            ],
                        ],
                    ],
                ],
            ])
        );
        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment9Id']), $store->getStoreId(), [
                'name' => 'bought labels',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_BOUGHT_LABELS,
                                'criterionId' => $faker->uuid,
                                'labels' => [
                                    ['key' => 'test', 'value' => 'label'],
                                ],
                            ],
                        ],
                    ],
                ],
            ])
        );
        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment10Id']), $store->getStoreId(), [
                'name' => 'customer list',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_CUSTOMER_LIST,
                                'criterionId' => $faker->uuid,
                                'customers' => [
                                    LoadUserData::DATA[$store->getCode()]['user1UserId'],
                                ],
                            ],
                        ],
                    ],
                ],
            ])
        );

        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment11Id']), $store->getStoreId(), [
                'name' => 'customer list with label',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_CUSTOMER_LIST,
                                'criterionId' => $faker->uuid,
                                'customers' => [
                                    LoadUserData::DATA[$store->getCode()]['user1UserId'],
                                ],
                            ],
                        ],
                    ],
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_CUSTOMER_HAS_LABELS,
                                'criterionId' => $faker->uuid,
                                'labels' => [
                                    ['key' => 'test'],
                                ],
                            ],
                        ],
                    ],
                ],
            ])
        );

        $this->commandBus->dispatch(new ActivateSegment(new SegmentId(self::DATA[$store->getCode()]['segment11Id'])));

        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment12Id']), $store->getStoreId(), [
                'name' => 'customer list with label test-segment',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_CUSTOMER_HAS_LABELS,
                                'criterionId' => $faker->uuid,
                                'labels' => [
                                    ['key' => 'test-for-segment'],
                                ],
                            ],
                        ],
                    ],
                ],
            ])
        );

        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment13Id']), $store->getStoreId(), [
                'name' => 'Customer with transaction between',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_LAST_TRANSACTION_BETWEEN_DAYS,
                                'criterionId' => $faker->uuid,
                                'fromDays' => 10,
                                'toDays' => 60,
                            ],
                        ],
                    ],
                ],
            ])
        );

        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment14Id']), $store->getStoreId(), [
                'name' => 'last custom event n days before',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_LAST_CUSTOM_EVENT_N_DAYS_BEFORE,
                                'criterionId' => $faker->uuid,
                                'days' => 60,
                            ],
                        ],
                    ],
                ],
            ])
        );

        $this->commandBus->dispatch(
            new CreateSegment(new SegmentId(self::DATA[$store->getCode()]['segment15Id']), $store->getStoreId(), [
                'name' => 'custom event between',
                'description' => 'desc',
                'parts' => [
                    [
                        'segmentPartId' => $faker->uuid,
                        'criteria' => [
                            [
                                'type' => Criterion::TYPE_LAST_CUSTOM_EVENT_BETWEEN_DAYS,
                                'criterionId' => $faker->uuid,
                                'fromDays' => 12,
                                'toDays' => 62,
                            ],
                        ],
                    ],
                ],
            ])
        );
    }
}
