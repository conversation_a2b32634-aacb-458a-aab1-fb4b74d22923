<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Infrastructure;

use DateTimeImmutable;
use Generator;
use OpenLoyalty\Campaign\Domain\CampaignGateInterface;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Segment\Domain\CampaignFacadeInterface;
use OpenLoyalty\Segment\Domain\ValueObject\ResourceAssociatedToSegment;

final class CampaignFacade implements CampaignFacadeInterface
{
    public function __construct(private readonly CampaignGateInterface $campaignGate)
    {
    }

    public function isSegmentUsedInCampaigns(SegmentId $segmentId): bool
    {
        return $this->campaignGate->isSegmentUsedInCampaigns($segmentId);
    }

    public function findCustomersByCampaignCompletion(
        StoreId $storeId,
        CampaignId $campaignId,
        int $campaignCompletionNumber,
        DateTimeImmutable $fromDate,
        DateTimeImmutable $toDate,
    ): Generator {
        yield from $this->campaignGate->findCustomersByCampaignCompletion(
            $storeId,
            $campaignId,
            $campaignCompletionNumber,
            $fromDate,
            $toDate
        );
    }

    public function isCampaignExists(CampaignId $campaignId, StoreId $storeId): bool
    {
        return null !== $this->campaignGate->getCampaign($campaignId, $storeId);
    }

    /**
     * @return array<ResourceAssociatedToSegment>
     */
    public function findCampaignsAssociatedToSegment(SegmentId $segmentId, StoreId $storeId): array
    {
        $resources = [];

        foreach ($this->campaignGate->findCampaignsAssociatedToSegment($segmentId, $storeId) as $resource) {
            $resources[] = new ResourceAssociatedToSegment(
                (string) $resource['id'],
                (string) $resource['name'],
                (bool) $resource['active'],
                [
                    'campaign_type' => isset($resource['type']) ? (string) $resource['type'] : '',
                    'campaign_trigger' => isset($resource['trigger']) ? (string) $resource['trigger'] : '',
                ]
            );
        }

        return $resources;
    }
}
