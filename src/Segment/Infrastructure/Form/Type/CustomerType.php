<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Segment\Infrastructure\Form\Type;

use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Segment\Infrastructure\Form\Transformer\CustomerTransformer;
use OpenLoyalty\Segment\Infrastructure\Provider\CustomerIdProvider;
use OpenLoyalty\Segment\Infrastructure\Validator\Constraints\Customer;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CustomerType extends TextType
{
    /**
     * @var CustomerIdProvider
     */
    private $customerIdProvider;

    /**
     * @var StoreContextProviderInterface
     */
    private $storeContextProvider;

    public function __construct(CustomerIdProvider $customerIdProvider, StoreContextProviderInterface $storeContextProvider)
    {
        $this->customerIdProvider = $customerIdProvider;
        $this->storeContextProvider = $storeContextProvider;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->addModelTransformer(new CustomerTransformer($this->customerIdProvider, $this->storeContextProvider));
    }

    public function configureOptions(OptionsResolver $options): void
    {
        $options->setDefaults([
            'compound' => false,
            'constraints' => [new Customer()],
        ]);
    }
}
