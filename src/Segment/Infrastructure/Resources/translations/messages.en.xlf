<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="en" target-language="en" datatype="plaintext" original="file.ext">
        <body>
            <trans-unit id="segment.parts.customer_not_found">
                <source>segment.parts.customer_not_found</source>
                <target>Customer not found: %data%</target>
            </trans-unit>
            <trans-unit id="segment.parts.too_many_customers_found">
                <source>segment.parts.too_many_customers_found</source>
                <target>Too many customers found: %data%</target>
            </trans-unit>
            <trans-unit id="segment.cannot_delete_used_segment">
                <source>segment.cannot_delete_used_segment</source>
                <target>To delete the segment, it must first be removed from all associated campaigns, members or tiers.</target>
            </trans-unit>
            <trans-unit id="segment.cannot_deactivate_used_segment">
                <source>segment.cannot_deactivate_used_segment</source>
                <target>To deactivate the segment, it must first be removed from all associated campaigns, members or tiers.</target>
            </trans-unit>
            <trans-unit id="segment.criteria.achievement.invalid_operator">
                <source>segment.criteria.achievement.invalid_operator</source>
                <target>It is not a valid operator, choose one of: {{ operators }}</target>
            </trans-unit>
            <trans-unit id="segment.criteria.achievement.invalid_achievement_id">
                <source>segment.criteria.achievement.invalid_achievement_id</source>
                <target>Does not exist achievement with provided id: {{ achievementId }}</target>
            </trans-unit>
            <trans-unit id="segment.criteria.achievement.invalid_achievement_rule_id">
                <source>segment.criteria.achievement.invalid_achievement_rule_id</source>
                <target>Provided achievement rule is not from provided achievement.</target>
            </trans-unit>
            <trans-unit id="segment.criteria.achievement.required_for_consecutive_type_roles">
                <source>segment.criteria.achievement.required_for_consecutive_type_roles</source>
                <target>Required for consecutive type rules.</target>
            </trans-unit>
            <trans-unit id="segment.criteria.achievement.period_goal_in_rule_greater_than_limit">
                <source>segment.criteria.achievement.period_goal_in_rule_greater_than_limit</source>
                <target>This value should be less than or equal to {{ maxValue }}</target>
            </trans-unit>
            <trans-unit id="segment.criteria.achievement.not_handled_achievement_rule">
                <source>segment.criteria.achievement.not_handled_achievement_rule</source>
                <target>This is not handled achievement rule for creating segment.</target>
            </trans-unit>
            <trans-unit id="segment.active.cannot_be_active">
                <source>segment.active.cannot_be_active</source>
                <target>You have reached the global limit of {{ limit }} active segments. To activate this segment, please deactivate existing ones or contact your Customer Success Manager or the support <NAME_EMAIL>.</target>
            </trans-unit>
            <trans-unit id="segment.associations.notSupportedResource">
                <source>segment.associations.notSupportedResource</source>
                <target>Invalid resource type, choose supported type.</target>
            </trans-unit>
        </body>
    </file>
</xliff>
