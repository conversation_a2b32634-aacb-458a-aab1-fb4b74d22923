imports:
    - { resource: services/*.yml }

services:
    _defaults:
        autoconfigure: true
        autowire: true
        public: false

    _instanceof:
        OpenLoyalty\Core\Infrastructure\Message\Lock\LockItemPropertiesProviderInterface:
            tags: [ 'oloy.core.message.lock_item_properties_provider' ]

    OpenLoyalty\Segment\Infrastructure\DataFixtures\ORM\:
        resource: '../../DataFixtures/ORM/*'

    OpenLoyalty\Segment\Infrastructure\Form\EventSubscriber\:
        resource: '../../Form/EventSubscriber/*'

    OpenLoyalty\Segment\Infrastructure\Validator\Constraints\:
        resource: '../../Validator/Constraints/*'

    OpenLoyalty\Segment\Infrastructure\Form\Type\:
        resource: '../../Form/Type/*'
        tags:
            - { name: 'form.type' }

    OpenLoyalty\Segment\Infrastructure\Form\Type\LabelFormType:
        arguments:
            $customAttributeKeyMaxLength: '%form_custom_attribute_key_max_length%'
            $customAttributeValueMaxLength: '%form_custom_attribute_value_max_length%'

    OpenLoyalty\Segment\Infrastructure\Form\Type\LabelWithoutValueFormType:
        arguments:
            $customAttributeKeyMaxLength: '%form_custom_attribute_key_max_length%'
            $customAttributeValueMaxLength: '%form_custom_attribute_value_max_length%'

    OpenLoyalty\Segment\Infrastructure\Form\Type\SegmentSearchType:
        arguments:
            $maxNumberValue: '%form_number_field_max_value%'

    OpenLoyalty\Segment\Infrastructure\ParamConverter\SegmentParamConverter:
        tags:
            - { name: 'request.param_converter', priority: '2', converter: 'segment_converter' }

    OpenLoyalty\Segment\Infrastructure\Event\Listener\SegmentSerializationListener:
        public: true
        tags:
            - { name: 'jms_serializer.event_subscriber' }

    OpenLoyalty\Segment\Infrastructure\Transformer\SegmentReadModelTransformer: ~

    OpenLoyalty\Segment\Infrastructure\Provider\CustomerDetailsProviderInterface: '@OpenLoyalty\Segment\Infrastructure\Provider\CustomerDetailsProvider'
    OpenLoyalty\Segment\Infrastructure\Provider\CustomerDetailsProvider: ~

    OpenLoyalty\Segment\Infrastructure\Provider\CustomerIdProvider: ~

    OpenLoyalty\Segment\Infrastructure\Persistence\Doctrine\Repository\DoctrineSegmentRepository: ~
    OpenLoyalty\Segment\Infrastructure\Persistence\Doctrine\Repository\DoctrineSegmentPartRepository: ~
    OpenLoyalty\Segment\Infrastructure\Persistence\Doctrine\Repository\DoctrineSegmentCustomerRepository: ~

    OpenLoyalty\Segment\Application\DataMapper\SegmentDataMapper: ~

    OpenLoyalty\Segment\Infrastructure\SegmentGate: ~
    OpenLoyalty\Segment\Domain\SegmentGateInterface: '@OpenLoyalty\Segment\Infrastructure\SegmentGate'

    OpenLoyalty\Segment\Infrastructure\CampaignFacade: ~
    OpenLoyalty\Segment\Domain\CampaignFacadeInterface: '@OpenLoyalty\Segment\Infrastructure\CampaignFacade'

    OpenLoyalty\Segment\Infrastructure\ImportFacade: ~
    OpenLoyalty\Segment\Domain\ImportFacadeInterface: '@OpenLoyalty\Segment\Infrastructure\ImportFacade'

    OpenLoyalty\Segment\Infrastructure\AchievementFacade: ~
    OpenLoyalty\Segment\Domain\AchievementFacadeInterface: '@OpenLoyalty\Segment\Infrastructure\AchievementFacade'

    OpenLoyalty\Segment\Domain\CampaignCompletionDateRangeCalculator: ~

    OpenLoyalty\Segment\Infrastructure\Message\Lock\RecreateSegmentLockItemPropertiesProvider:
        arguments:
            $lockTtl: '%messenger.segment_lock_ttl%'

    OpenLoyalty\Segment\Infrastructure\CustomEventSchemaFacade: ~
    OpenLoyalty\Segment\Domain\CustomEventSchemaFacadeInterface: '@OpenLoyalty\Segment\Infrastructure\CustomEventSchemaFacade'

    OpenLoyalty\Segment\Infrastructure\MemberFacade: ~
    OpenLoyalty\Segment\Domain\MemberFacadeInterface: '@OpenLoyalty\Segment\Infrastructure\MemberFacade'

    OpenLoyalty\Segment\Infrastructure\TierFacade: ~
    OpenLoyalty\Segment\Domain\TierFacadeInterface: '@OpenLoyalty\Segment\Infrastructure\TierFacade'

    OpenLoyalty\Segment\Infrastructure\RewardFacade: ~
    OpenLoyalty\Segment\Domain\RewardFacadeInterface: '@OpenLoyalty\Segment\Infrastructure\RewardFacade'

    OpenLoyalty\Segment\Infrastructure\TransactionFacade: ~
    OpenLoyalty\Segment\Domain\TransactionFacadeInterface: '@OpenLoyalty\Segment\Infrastructure\TransactionFacade'
    
    OpenLoyalty\Segment\Infrastructure\Validator\Constraints\CanBeActiveValidator: ~
