<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Segment\Infrastructure\Validator\Constraints;

use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Segment\Domain\AchievementFacadeInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

final class AchievementExistsInStoreValidator extends ConstraintValidator
{
    public function __construct(
        private readonly AchievementFacadeInterface $achievementFacade,
        private readonly TranslatorInterface $translator,
        private readonly StoreContextProviderInterface $contextProvider
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof AchievementExistsInStore) {
            return;
        }

        if (null === $value) {
            return;
        }

        $storeId = $this->contextProvider->getStore()->getStoreId();

        if (!$this->achievementFacade->isAchievementExistsInStore(new AchievementId($value), $storeId)) {
            $this->context
                ->buildViolation($this->translator->trans(AchievementExistsInStore::MESSAGE))
                ->setParameter('{{ achievementId }}', $value)
                ->addViolation()
            ;
        }
    }
}
