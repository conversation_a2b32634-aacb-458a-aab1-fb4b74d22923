<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Infrastructure\Validator\Constraints;

use Symfony\Component\Validator\Constraint;

final class AchievementRulePeriodGoalInLimit extends Constraint
{
    public const MESSAGE = 'segment.criteria.achievement.period_goal_in_rule_greater_than_limit';

    public ?string $achievementRuleId;
    public ?string $achievementGoal;

    public function getRequiredOptions(): array
    {
        return ['achievementRuleId', 'achievementGoal'];
    }
}
