<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Segment\Infrastructure\Validator\Constraints;

use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Segment\Domain\CampaignFacadeInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints\Uuid;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Validator\ValidatorInterface;

final class CampaignValidator extends ConstraintValidator
{
    public function __construct(
        private readonly StoreContextProviderInterface $storeContextProvider,
        private readonly CampaignFacadeInterface $campaignFacade,
        private readonly ValidatorInterface $validator,
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof Campaign) {
            throw new UnexpectedTypeException($constraint, Campaign::class);
        }

        $storeId = $this->storeContextProvider->getStore()->getStoreId();

        $uuidValidatorErrors = $this->validator->validate(
            $value,
            new Uuid()
        );

        if (
            null === $value
            || 0 !== $uuidValidatorErrors->count()
            || false === $this->campaignFacade->isCampaignExists(new CampaignId($value), $storeId)
        ) {
            $this->context->buildViolation($constraint->message)
                ->setParameter('{{ campaignId }}', $this->formatValue($value))
                ->setCode(Campaign::CAMPAIGN_NOT_FOUND_ERROR_CODE)
                ->addViolation();
        }
    }
}
