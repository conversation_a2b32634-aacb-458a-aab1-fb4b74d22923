<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Infrastructure\Validator\Constraints;

use OpenLoyalty\Core\Domain\Id\AchievementRuleId;
use OpenLoyalty\Segment\Domain\AchievementFacadeInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

final class RequiredForConsecutiveTypeRulesValidator extends ConstraintValidator
{
    public const YEAR = 'year';
    public const MONTH = 'month';
    public const WEEK = 'week';
    public const DAY = 'day';
    public const CONSECUTIVE_RULE_TYPES = [
        self::YEAR,
        self::MONTH,
        self::WEEK,
        self::DAY,
    ];

    public function __construct(
        private readonly AchievementFacadeInterface $achievementFacade,
        private readonly TranslatorInterface $translator,
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof RequiredForConsecutiveTypeRules) {
            return;
        }
        if (!$constraint->achievementRuleId) {
            return;
        }

        $periodType = $this->achievementFacade->getAchievementRuleDetails(
            new AchievementRuleId($constraint->achievementRuleId)
        )?->periodType;

        if (!in_array($periodType, self::CONSECUTIVE_RULE_TYPES)) {
            return;
        }
        if (empty($value)) {
            $this->context
                ->buildViolation($this->translator->trans(RequiredForConsecutiveTypeRules::MESSAGE))
                ->addViolation()
            ;
        }
    }
}
