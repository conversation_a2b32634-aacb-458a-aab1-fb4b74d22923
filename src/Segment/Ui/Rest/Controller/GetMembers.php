<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableTotalResponse;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchFormFactoryInterface;
use OpenLoyalty\Segment\Application\Response\SegmentCustomer;
use OpenLoyalty\Segment\Application\UseCase\GetSegmentCustomersUseCase;
use OpenLoyalty\Segment\Infrastructure\Form\Type\SegmentedCustomerSearchType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetMembers extends AbstractFOSRestController
{
    private GetSegmentCustomersUseCase $useCase;
    private SearchFormFactoryInterface $searchFormFactory;

    public function __construct(GetSegmentCustomersUseCase $useCase, SearchFormFactoryInterface $searchFormFactory)
    {
        $this->useCase = $useCase;
        $this->searchFormFactory = $searchFormFactory;
    }

    /**
     * @Route(methods={"GET"}, name="oloy.segment.get_members", path="/{storeCode}/segment/{segment}/members", requirements={"segment"="%routing.uuid%"})
     *
     * @Security("is_granted('LIST_CUSTOMERS', segment)")
     *
     * @Operation(
     *     tags={"Segment"},
     *     summary="Get segment’s members",
     *     operationId="segmentGetMembers",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(ref="#/components/parameters/segment"),
     *     @OA\Parameter(
     *         name="customerId",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="firstName",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="lastName",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="email",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="phone",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(ref="#/components/parameters/page"),
     *     @OA\Parameter(ref="#/components/parameters/itemsOnPage"),
     *     @OA\Parameter(ref="#/components/parameters/orderBy"),
     *     @OA\Response(
     *         response="200",
     *         description="List of segmented members",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="items",
     *                     type="array",
     *                     @OA\Items(ref=@Model(type=SegmentCustomer::class))
     *                 ),
     *                 @OA\Property(property="total", ref=@Model(type=SearchableTotalResponse::class))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="400",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Request $request, \OpenLoyalty\Segment\Domain\Segment $segment): View
    {
        $form = $this->searchFormFactory->createAndHandle(
            SegmentedCustomerSearchType::class,
            $request->query->all(),
            $request->getLocale(),
            [
                'with_store_context' => false,
            ]
        );

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $result = $this->useCase->execute($segment->getSegmentId(), $form->getData());

        return $this->view($result, Response::HTTP_OK);
    }
}
