<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Segment\Application\Exception\SegmentNameAlreadyExistsException;
use OpenLoyalty\Segment\Application\UseCase\CreateSegmentUseCase;
use OpenLoyalty\Segment\Infrastructure\Form\Type\CreateSegmentFormType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class Post extends AbstractFOSRestController
{
    public function __construct(
        private readonly FormFactoryInterface $formFactory,
        private readonly CreateSegmentUseCase $useCase,
        private readonly StoreContextProviderInterface $storeContextProvider,
    ) {
    }

    /**
     * @Route(methods={"POST"}, name="oloy.segment.create", path="/{storeCode}/segment")
     *
     * @Security("is_granted('CREATE_SEGMENT')")
     *
     * @Operation(
     *     tags={"Segment"},
     *     summary="Add a new segment",
     *     operationId="segmentPost",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\RequestBody(
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(property="segment", ref=@Model(type=CreateSegmentFormType::class))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Return created segment ID.",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="segmentId",
     *                     type="string",
     *                     description="Created segment ID",
     *                     example="00000000-0000-0000-0000-000000000000"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="400",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Request $request): View
    {
        $form = $this->formFactory->createNamed('segment', CreateSegmentFormType::class);
        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        try {
            $segmentId = $this->useCase->execute(
                $this->storeContextProvider->getStore(),
                $form->getData()
            );

            return $this->view(['segmentId' => (string) $segmentId], Response::HTTP_OK);
        } catch (SegmentNameAlreadyExistsException) {
            $form->get('name')->addError(new FormError('Segment with this name already exists'));
        }

        return $this->view($form, Response::HTTP_BAD_REQUEST);
    }
}
