<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Ui\Rest\Controller;

use Exception;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Segment\Application\UseCase\ActivateSegmentUseCase;
use OpenLoyalty\Segment\Domain\Checker\ActiveSegmentLimitReachedCheckerInterface;
use OpenLoyalty\Segment\Domain\Provider\EnvironmentVariableProviderInterface;
use OpenLoyalty\Ui\Rest\Responder\ErrorResponderInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

final class PostActivate extends AbstractFOSRestController
{
    public function __construct(
        private readonly ActivateSegmentUseCase $useCase,
        private readonly ActiveSegmentLimitReachedCheckerInterface $activeSegmentLimitReachedChecker,
        private readonly ErrorResponderInterface $formErrorResponder,
        private readonly EnvironmentVariableProviderInterface $environmentVariableProvider,
        private readonly TranslatorInterface $translator,
    ) {
    }

    /**
     * @Route(methods={"POST"}, name="oloy.segment.activate", path="/{storeCode}/segment/{segment}/activate", requirements={"segment"="%routing.uuid%"})
     *
     * @Security("is_granted('ACTIVATE', segment)")
     *
     * @Operation(
     *     tags={"Segment"},
     *     summary="Activate a segment",
     *     operationId="segmentPostActivate",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(ref="#/components/parameters/segment"),
     *     @OA\Response(
     *         response="204",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     *
     * @throws Exception
     */
    public function __invoke(\OpenLoyalty\Segment\Domain\Segment $segment): View
    {
        if ($this->activeSegmentLimitReachedChecker->isGlobalLimitReached()) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans(
                    'segment.active.cannot_be_active',
                    ['{{ limit }}' => $this->environmentVariableProvider->getGlobalActiveSegmentLimit()]
                ),
            );
        }
        $this->useCase->execute($segment->getSegmentId());

        return $this->view(null, Response::HTTP_NO_CONTENT);
    }
}
