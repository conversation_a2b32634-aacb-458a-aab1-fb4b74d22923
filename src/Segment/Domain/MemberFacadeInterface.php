<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain;

use DateTimeImmutable;
use Generator;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\ValueObject\Condition\Operator;

interface MemberFacadeInterface
{
    public function getAllMemberIdsWithSpecificCustomEventType(
        StoreId $storeId,
        DateTimeImmutable $from,
        DateTimeImmutable $to,
        ?string $customEventType
    ): Generator;

    public function getAllMemberIdsRegisteredInDateFrom(StoreId $storeId, Operator $operator, ?int $days, ?int $fromDays, ?int $toDays): Generator;

    public function getCustomerIdsByAge(StoreId $storeId, int $from, int $to): Generator;

    public function getCustomerIdsByBirthdayAnniversary(
        StoreId $storeId,
        DateTimeImmutable $from,
        DateTimeImmutable $to
    ): Generator;

    public function getCustomerIdsByRegistrationAnniversary(
        StoreId $storeId,
        DateTimeImmutable $from,
        DateTimeImmutable $to
    ): Generator;

    public function findByCriteriaReadContext(CriteriaCollectionInterface $criteriaCollection): Generator;

    /**
     * @param array<string> $cities
     */
    public function findByAddressFields(StoreId $storeId, string $string, array $cities): Generator;

    /**
     * @param array<string> $genderOptions
     */
    public function findByGenders(StoreId $storeId, array $genderOptions): Generator;

    public function getAllActiveIds(StoreId $storeId): Generator;
}
