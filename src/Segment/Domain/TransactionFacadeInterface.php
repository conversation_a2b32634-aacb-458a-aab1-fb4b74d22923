<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain;

use DateTimeImmutable;
use Generator;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;

interface TransactionFacadeInterface
{
    public function findByCriteriaReadContext(CriteriaCollectionInterface $criteriaCollection): Generator;

    /**
     * @return array<string>
     */
    public function findCustomersWithTransactionAmountEachDayInPeriod(
        StoreId $storeId,
        DateTimeImmutable $purchaseDateFrom,
        DateTimeImmutable $purchaseDateTo,
        float $fromAmount,
        float $toAmount
    ): array;

    /**
     * @return array<string>
     */
    public function findCustomersWithTransactionAmountInAtLeastOneDayExceeding(
        StoreId $storeId,
        DateTimeImmutable $purchaseDateFrom,
        DateTimeImmutable $purchaseDateTo,
        float $toAmount
    ): array;

    public function findCustomersWithTransactionAmountInPeriod(
        StoreId $storeId,
        DateTimeImmutable $purchaseDateFrom,
        DateTimeImmutable $purchaseDateTo,
        float $fromAmount,
        float $toAmount
    ): Generator;

    public function findCustomersWithTransactionAmountInPeriodExceeding(StoreId $storeId, DateTimeImmutable $purchaseDateFrom, DateTimeImmutable $purchaseDateTo, float $toAmount): Generator;

    public function findCustomersWithTransactionCountInPeriod(StoreId $storeId, DateTimeImmutable $purchaseDateFrom, DateTimeImmutable $purchaseDateTo, string $condition): Generator;
}
