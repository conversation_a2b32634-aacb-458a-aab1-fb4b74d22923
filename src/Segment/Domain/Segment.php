<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain;

use DateTime;
use DateTimeImmutable;
use Doctrine\Common\Collections\Collection;
use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Segment\Domain\Model\SegmentPart;

class Segment implements TimestampableInterface, BlameableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    protected SegmentId $segmentId;
    protected string $name;
    protected ?string $description;
    protected bool $active = false;
    protected int $customersCount = 0;
    protected Store $store;
    protected ?DateTimeImmutable $calculatedAt = null;

    /**
     * @var numeric
     *
     * @see why not float: https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/types.html#decimal
     */
    protected $averageTransactionAmount = 0.00;

    /**
     * @var numeric
     *
     * @see why not float: https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/types.html#decimal
     */
    protected $averageTransactions = 0.00;

    /**
     * @var numeric
     *
     * @see why not float: https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/types.html#decimal
     */
    protected $averageClv = 0.00;

    /**
     * @var SegmentPart[]|Collection<\OpenLoyalty\Segment\Domain\Model\SegmentPart>
     */
    protected $parts;

    public function __construct(SegmentId $segmentId, Store $store, string $name, string $description = null)
    {
        $this->segmentId = $segmentId;
        $this->name = $name;
        $this->description = $description;
        $this->parts = [];
        $this->createdAt = new DateTimeImmutable();
        $this->store = $store;
    }

    public function getSegmentId(): SegmentId
    {
        return $this->segmentId;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): void
    {
        $this->active = $active;
    }

    /**
     * @return SegmentPart[]|Collection<SegmentPart>
     */
    public function getParts()
    {
        return $this->parts;
    }

    /**
     * @param SegmentPart[] $parts
     */
    public function setParts(array $parts): void
    {
        $this->parts = $parts;
    }

    public function addPart(SegmentPart $part): void
    {
        $part->setSegment($this);
        $this->parts[(string) $part->getSegmentPartId()] = $part;
    }

    public function removePart(SegmentPart $part): void
    {
        $part->setSegment(null);
        unset($this->parts[(string) $part->getSegmentPartId()]);
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getCustomersCount(): int
    {
        return $this->customersCount;
    }

    public function setCustomersCount(int $customersCount): void
    {
        $this->customersCount = $customersCount;
    }

    public function getCreatedAt(): DateTime
    {
        return DateTime::createFromInterface($this->createdAt);
    }

    public function getAverageTransactionAmount(): float
    {
        return (float) $this->averageTransactionAmount;
    }

    public function setAverageTransactionAmount(float $averageTransactionAmount): void
    {
        $this->averageTransactionAmount = $averageTransactionAmount;
    }

    public function getAverageTransactions(): float
    {
        return (float) $this->averageTransactions;
    }

    public function setAverageTransactions(float $averageTransactions): void
    {
        $this->averageTransactions = $averageTransactions;
    }

    public function getAverageClv(): float
    {
        return (float) $this->averageClv;
    }

    public function setAverageClv(float $averageClv): void
    {
        $this->averageClv = $averageClv;
    }

    public function getStore(): Store
    {
        return $this->store;
    }

    public function setCalculatedAt(?DateTimeImmutable $calculatedAt): void
    {
        $this->calculatedAt = $calculatedAt;
    }

    public function getCalculatedAt(): ?DateTimeImmutable
    {
        return $this->calculatedAt;
    }
}
