<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\ValueObject;

final class CampaignCompletionTimeframe
{
    public const LAST_X_DAYS = 'last_x_days';
    public const LAST_X_WEEKS = 'last_x_weeks';
    public const LAST_X_MONTHS = 'last_x_months';

    public const ALLOWED_TYPES = [
        self::LAST_X_DAYS,
        self::LAST_X_WEEKS,
        self::LAST_X_MONTHS,
    ];

    public const ALLOWED_VALUES = [
        self::LAST_X_DAYS => 62,
        self::LAST_X_WEEKS => 8,
        self::LAST_X_MONTHS => 2,
    ];

    public function __construct(
        public readonly string $type,
        public readonly int $number,
    ) {
    }
}
