<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\Model\Criteria;

use Assert\Assertion as Assert;
use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Id\ChannelId;
use OpenLoyalty\Core\Domain\Id\CriterionId;
use OpenLoyalty\Segment\Domain\Model\Criterion;

class TransactionPercentInChannel extends Criterion
{
    private const PERCENT_MULTIPLIER = 100;
    protected string $channelId;

    /**
     * @var numeric
     *
     * @see why not float: https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/types.html#decimal
     */
    protected $percent;

    /**
     * @throws AssertionFailedException
     */
    public function getChannelId(): ChannelId
    {
        return new ChannelId($this->channelId);
    }

    public function setChannelId(ChannelId $channelId): void
    {
        $this->channelId = (string) $channelId;
    }

    public function getPercent(): float
    {
        return (float) $this->percent;
    }

    public function setPercent(float $percent): void
    {
        $this->percent = $percent;
    }

    /**
     * @throws AssertionFailedException
     */
    public static function fromArray(array $data): self
    {
        $criterion = new self(new CriterionId($data['criterionId']));
        $criterion->setChannelId(new ChannelId($data['channelId']));
        $criterion->setPercent($data['percent']);

        return $criterion;
    }

    /**
     * @throws AssertionFailedException
     */
    public static function validate(array $data): void
    {
        parent::validate($data);
        Assert::keyIsset($data, 'channelId');
        Assert::keyIsset($data, 'percent');
        Assert::notBlank($data, 'channelId');
        Assert::string($data['channelId']);
        Assert::numeric($data['percent']);
        Assert::range($data['percent'], 0, 1);
    }

    /**
     * @throws AssertionFailedException
     */
    public function getDataAsArray(): array
    {
        return [
            'channelId' => (string) $this->getChannelId(),
            'percent' => round($this->getPercent() * self::PERCENT_MULTIPLIER, 1),
        ];
    }

    public function getType(): string
    {
        return Criterion::TYPE_TRANSACTION_PERCENT_IN_CHANNEL;
    }
}
