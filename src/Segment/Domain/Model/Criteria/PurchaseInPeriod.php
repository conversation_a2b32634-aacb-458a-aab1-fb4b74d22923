<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\Model\Criteria;

use Assert\Assertion as Assert;
use Assert\AssertionFailedException;
use DateTimeInterface;
use OpenLoyalty\Core\Domain\Id\CriterionId;
use OpenLoyalty\Segment\Domain\Model\Criterion;

class PurchaseInPeriod extends Criterion
{
    protected DateTimeInterface $fromDate;
    protected DateTimeInterface $toDate;

    public function getFromDate(): DateTimeInterface
    {
        return $this->fromDate;
    }

    public function setFromDate(DateTimeInterface $fromDate): void
    {
        $this->fromDate = $fromDate;
    }

    public function getToDate(): DateTimeInterface
    {
        return $this->toDate;
    }

    public function setToDate(DateTimeInterface $toDate): void
    {
        $this->toDate = $toDate;
    }

    /**
     * @throws AssertionFailedException
     */
    public static function fromArray(array $data): self
    {
        $criterion = new self(new CriterionId($data['criterionId']));
        $criterion->setFromDate($data['fromDate']);
        $criterion->setToDate($data['toDate']);

        return $criterion;
    }

    /**
     * @throws AssertionFailedException
     */
    public static function validate(array $data): void
    {
        parent::validate($data);
        Assert::keyIsset($data, 'fromDate');
        Assert::keyIsset($data, 'toDate');
        Assert::notBlank($data, 'fromDate');
        Assert::notBlank($data, 'toDate');
    }

    public function getDataAsArray(): array
    {
        return [
            'fromDate' => $this->getFromDate(),
            'toDate' => $this->getToDate(),
        ];
    }

    public function getType(): string
    {
        return Criterion::TYPE_PURCHASE_PERIOD;
    }
}
