<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\Model\Criteria;

use Assert\Assertion as Assert;
use OpenLoyalty\Core\Domain\Id\CriterionId;
use OpenLoyalty\Segment\Domain\Model\Criterion;

class BoughtMakers extends Criterion
{
    protected array $makers = [];

    public function getMakers(): array
    {
        return $this->makers;
    }

    public function setMakers(array $makers): void
    {
        $this->makers = $makers;
    }

    public static function fromArray(array $data): self
    {
        $criterion = new self(new CriterionId($data['criterionId']));
        $criterion->setMakers($data['makers']);

        return $criterion;
    }

    public static function validate(array $data): void
    {
        parent::validate($data);
        Assert::keyIsset($data, 'makers');
        Assert::notBlank($data, 'makers');
        Assert::isArray($data['makers']);
    }

    public function getDataAsArray(): array
    {
        return [
            'makers' => $this->getMakers(),
        ];
    }

    public function getType(): string
    {
        return Criterion::TYPE_BOUGHT_MAKERS;
    }
}
