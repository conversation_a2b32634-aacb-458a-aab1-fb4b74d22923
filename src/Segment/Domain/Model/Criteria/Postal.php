<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Segment\Domain\Model\Criteria;

use OpenLoyalty\Core\Domain\Id\CriterionId;
use OpenLoyalty\Segment\Domain\Model\Criterion;

class Postal extends Criterion
{
    private const INDEX_NAME = 'postal';

    protected array $postal = [];

    public function getPostal(): array
    {
        return $this->postal;
    }

    public function setPostal(array $postal): void
    {
        $this->postal = $postal;
    }

    public static function fromArray(array $data): self
    {
        $criterion = new self(new CriterionId($data['criterionId']));
        $criterion->setPostal($data['postal']);

        return $criterion;
    }

    public function getDataAsArray(): array
    {
        return [
            self::INDEX_NAME => $this->getPostal(),
        ];
    }

    public function getType(): string
    {
        return Criterion::TYPE_CUSTOMER_POSTAL;
    }
}
