<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\Model\Criteria;

use Assert\Assertion as Assert;
use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Id\CriterionId;
use OpenLoyalty\Segment\Domain\Model\Criterion;

class LastCustomEventBetweenDays extends Criterion
{
    protected int $fromDays;
    protected int $toDays;
    protected ?string $customEventType = null;

    /**
     * @throws AssertionFailedException
     */
    public static function fromArray(array $data): self
    {
        $criterion = new self(new CriterionId($data['criterionId']));
        $criterion->setToDays($data['toDays']);
        $criterion->setFromDays($data['fromDays']);
        $criterion->setCustomEventType($data['customEventType'] ?? null);

        return $criterion;
    }

    public function setFromDays(int $fromDays): void
    {
        $this->fromDays = $fromDays;
    }

    public function setToDays(int $toDays): void
    {
        $this->toDays = $toDays;
    }

    public function setCustomEventType(?string $customEventType): void
    {
        $this->customEventType = $customEventType;
    }

    public function getFromDays(): int
    {
        return $this->fromDays;
    }

    public function getToDays(): int
    {
        return $this->toDays;
    }

    public function getCustomEventType(): ?string
    {
        return $this->customEventType;
    }

    /**
     * @throws AssertionFailedException
     */
    public static function validate(array $data): void
    {
        parent::validate($data);
        Assert::keyIsset($data, 'toDays');
        Assert::notBlank($data, 'toDays');
        Assert::integer($data['toDays']);
        Assert::keyIsset($data, 'fromDays');
        Assert::notBlank($data, 'fromDays');
        Assert::integer($data['fromDays']);
        Assert::greaterOrEqualThan($data['toDays'], $data['fromDays']);
    }

    public function getDataAsArray(): array
    {
        return [
            'fromDays' => $this->getFromDays(),
            'toDays' => $this->getToDays(),
            'customEventType' => $this->customEventType,
        ];
    }

    public function getType(): string
    {
        return Criterion::TYPE_LAST_CUSTOM_EVENT_BETWEEN_DAYS;
    }
}
