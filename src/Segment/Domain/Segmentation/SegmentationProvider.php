<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\Segmentation;

use OpenLoyalty\Segment\Domain\Model\SegmentPart;
use OpenLoyalty\Segment\Domain\Segment;
use OpenLoyalty\Segment\Domain\Segmentation\CriteriaEvaluators\Evaluator;
use Psr\Log\LoggerInterface;
use ReflectionClass;

class SegmentationProvider
{
    /**
     * @var \OpenLoyalty\Segment\Domain\Segmentation\CriteriaEvaluators\Evaluator[]
     */
    protected array $evaluators;
    protected ?LoggerInterface $logger = null;
    protected int $maxExecutionTimeBeforeWarning = 3;

    public function evaluateSegment(Segment $segment): array
    {
        $customers = null;

        foreach ($segment->getParts() as $part) {
            if (null === $customers) {
                $customers = $this->getCustomersForPart($part);
            } else {
                $customers = !empty($customers) ? array_intersect($customers, $this->getCustomersForPart($part)) : [];
            }
        }

        return $customers ?? [];
    }

    public function addEvaluator(Evaluator $evaluator): void
    {
        $this->evaluators[] = $evaluator;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public function setMaxExecutionTimeBeforeWarning(int $maxExecutionTimeBeforeWarning): void
    {
        $this->maxExecutionTimeBeforeWarning = $maxExecutionTimeBeforeWarning;
    }

    protected function getCustomersForPart(SegmentPart $part): array
    {
        $customers = [];

        foreach ($part->getCriteria() as $criterion) {
            foreach ($this->evaluators as $evaluator) {
                if (!$evaluator->support($criterion)) {
                    continue;
                }
                $timeStart = microtime(true);
                $customers = array_merge($customers, $evaluator->evaluate($criterion));
                $timeEnd = microtime(true);
                $executionTimeInMinutes = ($timeEnd - $timeStart) / 60;
                if ($this->logger && $executionTimeInMinutes >= $this->maxExecutionTimeBeforeWarning) {
                    $refl = new ReflectionClass($evaluator);
                    $this->logger->alert($refl->getShortName().' needs too much time', [
                        'evaluator' => $refl->getShortName(),
                        'evaluator_class' => $refl->getName(),
                        'segment' => (string) $part->getSegment()->getSegmentId(),
                        'segment_part' => (string) $part->getSegmentPartId(),
                        'criterion' => (string) $criterion->getCriterionId(),
                        'execution_time_in_minutes' => $executionTimeInMinutes,
                    ]);
                }
            }
        }

        return array_unique($customers);
    }
}
