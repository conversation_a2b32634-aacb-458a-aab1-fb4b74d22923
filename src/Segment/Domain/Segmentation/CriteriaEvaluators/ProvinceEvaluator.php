<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\Segmentation\CriteriaEvaluators;

use OpenLoyalty\Segment\Domain\MemberFacadeInterface;
use OpenLoyalty\Segment\Domain\Model\Criteria\Province;
use OpenLoyalty\Segment\Domain\Model\Criterion;

final readonly class ProvinceEvaluator implements Evaluator
{
    public function __construct(
        private MemberFacadeInterface $memberFacade
    ) {
    }

    public function evaluate(Criterion $criterion): array
    {
        if (!$this->support($criterion)) {
            return [];
        }

        $storeId = $criterion->getStore()->getStoreId();

        return iterator_to_array(
            $this->memberFacade->findByAddressFields($storeId, 'province', $criterion->getProvinces())
        );
    }

    public function support(Criterion $criterion): bool
    {
        return $criterion instanceof Province;
    }
}
