<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\Segmentation\CriteriaEvaluators;

use OpenLoyalty\Core\Domain\Search\Criteria\BooleanCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\NumericCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\StoreCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\Segment\Domain\MemberFacadeInterface;
use OpenLoyalty\Segment\Domain\Model\Criteria\TransactionAmount;
use OpenLoyalty\Segment\Domain\Model\Criterion;

final readonly class TransactionAmountEvaluator implements Evaluator
{
    public function __construct(
        private MemberFacadeInterface $memberFacade
    ) {
    }

    public function evaluate(Criterion $criterion): array
    {
        if (!$criterion instanceof TransactionAmount) {
            return [];
        }

        $criteria = new CriteriaCollection();
        $criteria->add(new StoreCriteria('storeId', $criterion->getStore()));
        $criteria->add(new BooleanCriteria('active', CriteriaInterface::EQUAL, true));
        $criteria->add(new NumericCriteria('transactionsAmount', CriteriaInterface::GREATER_THAN_EQUAL, $criterion->getFromAmount()));
        $criteria->add(new NumericCriteria('transactionsAmount', CriteriaInterface::LESS_THAN_EQUAL, $criterion->getToAmount()));
        $criteria->setProjectionField('id', true);
        $criteria->skipExtraJoins();

        return iterator_to_array(
            $this->memberFacade->findByCriteriaReadContext($criteria)
        );
    }

    public function support(Criterion $criterion): bool
    {
        return $criterion instanceof TransactionAmount;
    }
}
