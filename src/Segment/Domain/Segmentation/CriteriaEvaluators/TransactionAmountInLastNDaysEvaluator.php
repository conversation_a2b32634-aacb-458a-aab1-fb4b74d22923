<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\Segmentation\CriteriaEvaluators;

use DateTimeImmutable;
use OpenLoyalty\Segment\Domain\MemberFacadeInterface;
use OpenLoyalty\Segment\Domain\Model\Criteria\TransactionAmountInLastNDays;
use OpenLoyalty\Segment\Domain\Model\Criterion;
use OpenLoyalty\Segment\Domain\TransactionFacadeInterface;

final readonly class TransactionAmountInLastNDaysEvaluator implements Evaluator
{
    public function __construct(
        private TransactionFacadeInterface $transactionFacade,
        private MemberFacadeInterface $memberFacade
    ) {
    }

    public function evaluate(Criterion $criterion): array
    {
        if (!$criterion instanceof TransactionAmountInLastNDays) {
            return [];
        }

        $storeId = $criterion->getStore()->getStoreId();
        $purchaseDateFrom = new DateTimeImmutable(sprintf('-%d days midnight', $criterion->getDays()));
        $purchaseDateTo = new DateTimeImmutable('midnight');

        $allCustomerIds = iterator_to_array($this->memberFacade->getAllActiveIds($storeId));

        if ($criterion->getFromAmount() > 0.0) {
            $segmentCustomerIds = iterator_to_array($this->transactionFacade->findCustomersWithTransactionAmountInPeriod(
                $storeId,
                $purchaseDateFrom,
                $purchaseDateTo,
                $criterion->getFromAmount(),
                $criterion->getToAmount()
            ));

            $customerIds = array_intersect($allCustomerIds, $segmentCustomerIds);
        } else {
            $notCustomerIds = iterator_to_array($this->transactionFacade->findCustomersWithTransactionAmountInPeriodExceeding(
                $storeId,
                $purchaseDateFrom,
                $purchaseDateTo,
                $criterion->getToAmount()
            ));

            $customerIds = array_diff($allCustomerIds, $notCustomerIds);
        }

        return $customerIds;
    }

    public function support(Criterion $criterion): bool
    {
        return $criterion instanceof TransactionAmountInLastNDays;
    }
}
