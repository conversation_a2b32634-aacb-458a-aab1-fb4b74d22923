<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\Segmentation\CriteriaEvaluators;

use OpenLoyalty\Segment\Domain\MemberFacadeInterface;
use OpenLoyalty\Segment\Domain\Model\Criteria\MemberRegistrationDate;
use OpenLoyalty\Segment\Domain\Model\Criterion;

final readonly class MemberRegistrationDateEvaluator implements Evaluator
{
    public function __construct(
        private MemberFacadeInterface $memberFacade,
    ) {
    }

    /**
     * @return array<string>
     */
    public function evaluate(Criterion $criterion): array
    {
        if (!$this->support($criterion)) {
            return [];
        }

        $storeId = $criterion->getStore()->getStoreId();
        $days = $criterion->getDays();
        $fromDays = $criterion->getFromDays();
        $toDays = $criterion->getToDays();
        $operator = $criterion->getOperator();

        /* @phpstan-ignore-next-line  */
        return iterator_to_array($this->memberFacade->getAllMemberIdsRegisteredInDateFrom($storeId, $operator, $days, $fromDays, $toDays));
    }

    public function support(Criterion $criterion): bool
    {
        return $criterion instanceof MemberRegistrationDate;
    }
}
