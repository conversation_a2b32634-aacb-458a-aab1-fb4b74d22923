<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\Segmentation\CriteriaEvaluators;

use OpenLoyalty\Core\Domain\Search\Criteria\Criteria;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\DateTimeCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\StoreCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\Segment\Domain\Model\Criteria\LastPurchaseNDaysBefore;
use OpenLoyalty\Segment\Domain\Model\Criterion;
use OpenLoyalty\Segment\Domain\TransactionFacadeInterface;

final readonly class LastPurchaseNDaysBeforeEvaluator implements Evaluator
{
    public function __construct(
        private TransactionFacadeInterface $transactionFacade
    ) {
    }

    public function evaluate(Criterion $criterion): array
    {
        if (!$criterion instanceof LastPurchaseNDaysBefore) {
            return [];
        }

        $from = new \DateTime();
        $from->setTime(0, 0, 0);
        $from->modify('-'.$criterion->getDays().' days');

        $criteria = new CriteriaCollection();
        $criteria->add(new DateTimeCriteria('header:purchasedAt', CriteriaInterface::GREATER_THAN_EQUAL, $from));
        $criteria->add(new DateTimeCriteria('header:purchasedAt', CriteriaInterface::LESS_THAN_EQUAL, new \DateTime()));
        $criteria->add(new StoreCriteria('storeId', $criterion->getStore()));
        $criteria->add(new Criteria('customerId', CriteriaInterface::HAS_VALUE));
        $criteria->setProjectionField('customerId', true);

        return iterator_to_array($this->transactionFacade->findByCriteriaReadContext($criteria));
    }

    public function support(Criterion $criterion): bool
    {
        return $criterion instanceof LastPurchaseNDaysBefore;
    }
}
