<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\Segmentation\CriteriaEvaluators;

use OpenLoyalty\Core\Domain\Search\Criteria\BooleanCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\StoreCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\UuidCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\Segment\Domain\MemberFacadeInterface;
use OpenLoyalty\Segment\Domain\Model\Criteria\CustomerInLevel;
use OpenLoyalty\Segment\Domain\Model\Criterion;
use Ramsey\Uuid\Uuid;

final readonly class CustomerInLevelEvaluator implements Evaluator
{
    public function __construct(
        private MemberFacadeInterface $memberFacade
    ) {
    }

    public function evaluate(Criterion $criterion): array
    {
        if (!$criterion instanceof CustomerInLevel) {
            return [];
        }

        $customers = [];
        $levels = $criterion->getLevels();
        foreach ($levels as $level) {
            $criteria = new CriteriaCollection();
            $criteria->add(new StoreCriteria('storeId', $criterion->getStore()));
            $criteria->add(new BooleanCriteria('active', CriteriaInterface::EQUAL, true));
            $criteria->add(new UuidCriteria(
                'levelId',
                CriteriaInterface::EQUAL,
                Uuid::fromString((string) $level)
            ));
            $criteria->setProjectionField('id', true);
            $criteria->skipExtraJoins();

            $customers = array_merge(
                $customers,
                iterator_to_array(
                    $this->memberFacade->findByCriteriaReadContext($criteria)
                )
            );
        }

        return $customers;
    }

    public function support(Criterion $criterion): bool
    {
        return $criterion instanceof CustomerInLevel;
    }
}
