<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

/*
 * Copyright Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain\Segmentation\CriteriaEvaluators;

use OpenLoyalty\Segment\Domain\MemberFacadeInterface;
use OpenLoyalty\Segment\Domain\Model\Criteria\Anniversary;
use OpenLoyalty\Segment\Domain\Model\Criterion;

final readonly class AnniversaryEvaluator implements Evaluator
{
    public function __construct(
        private MemberFacadeInterface $memberFacade
    ) {
    }

    public function evaluate(Criterion $criterion): array
    {
        if (!$criterion instanceof Anniversary) {
            return [];
        }
        $from = new \DateTime('today');
        $to = new \DateTime(sprintf('today +%d days', $criterion->getDays()));

        if (Anniversary::TYPE_BIRTHDAY === $criterion->getAnniversaryType()) {
            return iterator_to_array(
                $this->memberFacade->getCustomerIdsByBirthdayAnniversary(
                    $criterion->getStore()->getStoreId(),
                    \DateTimeImmutable::createFromMutable($from),
                    \DateTimeImmutable::createFromMutable($to)
                )
            );
        }

        if (Anniversary::TYPE_REGISTRATION === $criterion->getAnniversaryType()) {
            return iterator_to_array(
                $this->memberFacade->getCustomerIdsByRegistrationAnniversary(
                    $criterion->getStore()->getStoreId(),
                    \DateTimeImmutable::createFromMutable($from),
                    \DateTimeImmutable::createFromMutable($to)
                )
            );
        }

        return [];
    }

    public function support(Criterion $criterion): bool
    {
        return $criterion instanceof Anniversary;
    }
}
