<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Application\UseCase;

use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Segment\Application\Command\CreateSegment;
use OpenLoyalty\Segment\Application\Exception\SegmentNameAlreadyExistsException;
use OpenLoyalty\Segment\Domain\SegmentValidatorInterface;

class CreateSegmentUseCase
{
    private SegmentValidatorInterface $segmentValidator;
    private UuidGeneratorInterface $uuidGenerator;
    private CommandBusInterface $commandBus;

    public function __construct(
        SegmentValidatorInterface $segmentValidator,
        UuidGeneratorInterface $uuidGenerator,
        CommandBusInterface $commandBus
    ) {
        $this->segmentValidator = $segmentValidator;
        $this->uuidGenerator = $uuidGenerator;
        $this->commandBus = $commandBus;
    }

    /**
     * @throws SegmentNameAlreadyExistsException
     * @throws AssertionFailedException
     */
    public function execute(Store $store, array $data): SegmentId
    {
        if (!isset($data['name'])) {
            throw new \InvalidArgumentException('Name is not defined');
        }

        if (!isset($data['parts'])) {
            throw new \InvalidArgumentException('Parts are not defined');
        }

        if (!array_key_exists('active', $data)) {
            throw new \InvalidArgumentException('Active is not defined');
        }

        if ($this->segmentValidator->exists($store, $data['name'])) {
            throw new SegmentNameAlreadyExistsException();
        }

        $data = $this->generateUuidForSegmentPartsAndCriteria($data);
        $segmentId = new SegmentId($this->uuidGenerator->generate());

        $this->commandBus->dispatch(
            new CreateSegment($segmentId, $store->getStoreId(), $data)
        );

        return $segmentId;
    }

    private function generateUuidForSegmentPartsAndCriteria(array $data): array
    {
        foreach ($data['parts'] as $partIndex => $part) {
            $data['parts'][$partIndex]['segmentPartId'] = $this->uuidGenerator->generate();
            if (isset($part['criteria'])) {
                foreach ($part['criteria'] as $criterionIndex => $criterion) {
                    $data['parts'][$partIndex]['criteria'][$criterionIndex]['criterionId'] = $this->uuidGenerator->generate();
                }
            }
        }

        return $data;
    }
}
