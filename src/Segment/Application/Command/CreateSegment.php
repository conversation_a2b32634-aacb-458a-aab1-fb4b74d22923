<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Application\Command;

use Assert\Assertion as Assert;
use Exception;
use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Segment\Domain\Model\Criterion;
use OpenLoyalty\Segment\Domain\Model\SegmentPart;

class CreateSegment extends SegmentCommand
{
    private array $segmentData;
    private StoreId $storeId;

    public function __construct(SegmentId $segmentId, StoreId $storeId, array $segmentData)
    {
        $this->validate($segmentData);
        parent::__construct($segmentId);
        $this->segmentData = $segmentData;
        $this->storeId = $storeId;
    }

    protected function validate(array $segmentData): void
    {
        Assert::keyIsset($segmentData, 'name');
        Assert::notBlank($segmentData['name']);
        Assert::keyIsset($segmentData, 'parts');
        Assert::notBlank($segmentData['parts']);
        Assert::greaterOrEqualThan(count($segmentData['parts']), 0);
        foreach ($segmentData['parts'] as $part) {
            SegmentPart::validate($part);
            foreach ($part['criteria'] as $criterion) {
                /** @var Criterion[] $map */
                $map = Criterion::TYPE_MAP;
                if (!isset($map[$criterion['type']])) {
                    throw new Exception('type '.$criterion['type'].' does not exists');
                }
                $map[$criterion['type']]::validate($criterion);
            }
        }
    }

    public function getSegmentData(): array
    {
        return $this->segmentData;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }
}
