<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Application\Command;

use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Domain\Message\CommandInterface;
use OpenLoyalty\Core\Domain\Message\ThreadSafeTransportableInterface;

abstract class SegmentCommand implements CommandInterface, ThreadSafeTransportableInterface
{
    protected SegmentId $segmentId;

    public function __construct(SegmentId $segmentId)
    {
        $this->segmentId = $segmentId;
    }

    public function getSegmentId(): SegmentId
    {
        return $this->segmentId;
    }
}
