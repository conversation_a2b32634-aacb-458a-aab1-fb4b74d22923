<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Analytics\Ui\Rest\Controller;

use Exception;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Analytics\Application\UseCase\GetTransactionsStatsUseCase;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;

class GetTransactions extends AbstractFOSRestController
{
    private GetTransactionsStatsUseCase $useCase;
    private StoreContextProviderInterface $storeContextProvider;

    public function __construct(GetTransactionsStatsUseCase $useCase, StoreContextProviderInterface $storeContextProvider)
    {
        $this->useCase = $useCase;
        $this->storeContextProvider = $storeContextProvider;
    }

    /**
     * @Route(methods={"GET"}, name="oloy.analytics.transactions", path="/{storeCode}/analytics/transactions")
     * @Security("is_granted('VIEW_STATS')")
     *
     * @Operation(
     *     tags={"Analytics"},
     *     summary="Transactions analytics",
     *     operationId="transactionsStatsGet",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Response(
     *         response="200",
     *         description="Returned when successful",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/TransactionsStats")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="401",
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 oneOf={
     *                     @OA\Schema(ref="#/components/schemas/ExpiredToken"),
     *                     @OA\Schema(ref="#/components/schemas/InvalidToken"),
     *                     @OA\Schema(ref="#/components/schemas/Unauthorized")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     )
     * )
     *
     * @throws Exception
     */
    public function __invoke(): View
    {
        return $this->view($this->useCase->execute($this->storeContextProvider->getStoreOrNull()));
    }
}
