<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Analytics\Application\UseCase;

use DateTimeImmutable;
use OpenLoyalty\Core\Application\DateRange\DateRangeBuilderInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\DateTimeCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\StoreCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Transaction\Domain\TransactionRepositoryInterface;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;

class GetTransactionsStatsUseCase
{
    public function __construct(
        private CustomerRepositoryInterface $customerRepository,
        private TransactionRepositoryInterface $transactionRepository,
        private DateRangeBuilderInterface $dateRangeBuilder
    ) {
    }

    public function execute(?Store $store): array
    {
        $intervals = $this->dateRangeBuilder->createDaysFromIntervals(new DateTimeImmutable(), [1, 7, 30, 365]);
        $intervalResult = [];

        foreach ($intervals as $days => $from) {
            $criteria = new CriteriaCollection();
            $criteria->add(new StoreCriteria('storeId', $store));
            $criteria->add(new DateTimeCriteria('header.purchasedAt', CriteriaInterface::GREATER_THAN_EQUAL, $from));
            $intervalResult['in_'.$days.'_days'] = $this->transactionRepository->countByCriteria($criteria, true);
        }

        $criteria = new CriteriaCollection();
        $criteria->add(new StoreCriteria('storeId', $store));

        $result = [
            'total' => $this->transactionRepository->countByCriteria($criteria, true),
            'countIntervals' => $intervalResult,
        ];

        if ($store) {
            $result['amount'] = $this->customerRepository->getTransactionTotalAmountBy($store->getStoreId(), false);
            $result['amountWithoutDeliveryCosts'] = $this->customerRepository->getTransactionTotalAmountBy(
                $store->getStoreId(),
                true
            );
            $result['currency'] = $store->getCurrency();
        }

        return $result;
    }
}
