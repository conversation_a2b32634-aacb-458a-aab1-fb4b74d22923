<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Wallet\Repository;

use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\DataAnalytics\Domain\Wallet\Entity\WalletType;

interface WalletTypeRepositoryInterface
{
    public function save(WalletType $walletType): void;

    public function byId(WalletTypeId $walletTypeId): ?WalletType;
}
