<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Segment\Factory;

use OpenLoyalty\DataAnalytics\Domain\Segment\Entity\Segment;
use OpenLoyalty\DataAnalytics\Domain\Segment\Event\SegmentWasCreated;

final class SegmentFactory
{
    public function createFromEvent(SegmentWasCreated $event): Segment
    {
        return Segment::create(
            $event->getSegmentId(),
            $event->getSegmentName(),
            $event->getSegmentCreationDate(),
            $event->getTenantId()
        );
    }
}
