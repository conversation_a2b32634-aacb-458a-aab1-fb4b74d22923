<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject;

use OpenLoyalty\DataAnalytics\Domain\Shared\Exception\VersionValueIsInvalid;

final class Version
{
    private const ALLOWED_VERSIONS_VALUE = [1];

    /**
     * @throws VersionValueIsInvalid
     */
    public function __construct(private readonly int $value)
    {
        if (!in_array($value, self::ALLOWED_VERSIONS_VALUE)) {
            throw new VersionValueIsInvalid();
        }
    }

    public function isEqual(self $version): bool
    {
        return $version->getValue() === $this->value;
    }

    public function getValue(): int
    {
        return $this->value;
    }

    public function __toString(): string
    {
        return (string) $this->value;
    }
}
