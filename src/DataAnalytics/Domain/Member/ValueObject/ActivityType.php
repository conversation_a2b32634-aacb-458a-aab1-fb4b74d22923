<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Member\ValueObject;

use OpenLoyalty\DataAnalytics\Domain\Member\Exception\ActivityTypeIsInvalid;

final class ActivityType
{
    public const ACTIVE = 'active';
    public const INACTIVE = 'inactive';

    /**
     * @throws ActivityTypeIsInvalid
     */
    public function __construct(
        private readonly string $type
    ) {
        if (!in_array($type, [self::ACTIVE, self::INACTIVE])) {
            throw new ActivityTypeIsInvalid();
        }
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function isEqual(self $gender): bool
    {
        return $gender->getType() === $this->type;
    }

    public function __toString(): string
    {
        return $this->type;
    }
}
