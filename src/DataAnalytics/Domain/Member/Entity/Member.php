<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Member\Entity;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\DataAnalytics\Domain\Member\ValueObject\Gender;

class Member
{
    private ?DateTimeImmutable $updateDate = null;
    private ?DateTimeImmutable $deletionDate = null;

    private function __construct(
        private readonly CustomerId $memberId,
        private Gender $gender,
        private DateTimeImmutable $registrationDate,
        private readonly StoreId $tenantId
    ) {
    }

    public static function create(
        CustomerId $memberId,
        Gender $gender,
        DateTimeImmutable $registrationDate,
        StoreId $tenantId
    ): self {
        return new self(
            $memberId,
            $gender,
            $registrationDate,
            $tenantId
        );
    }

    public function getMemberId(): CustomerId
    {
        return $this->memberId;
    }

    public function getGender(): Gender
    {
        return $this->gender;
    }

    public function getRegistrationDate(): DateTimeImmutable
    {
        return $this->registrationDate;
    }

    public function getTenantId(): StoreId
    {
        return $this->tenantId;
    }

    public function update(
        Gender $gender,
        DateTimeImmutable $updateDate
    ): void {
        $this->gender = $gender;
        $this->updateDate = $updateDate;
    }

    public function getUpdateDate(): ?DateTimeImmutable
    {
        return $this->updateDate;
    }

    public function getDateOfDeletion(): ?DateTimeImmutable
    {
        return $this->deletionDate;
    }

    public function delete(DateTimeImmutable $deletionDate): void
    {
        $this->deletionDate = $deletionDate;
    }

    public function isDeleted(): bool
    {
        return null !== $this->deletionDate;
    }

    public function restore(DateTimeImmutable $registrationDate): void
    {
        $this->deletionDate = null;
        $this->registrationDate = $registrationDate;
    }
}
