<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Member\Repository;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\AggregationType;

interface MemberTransactionAggregatedRepositoryInterface
{
    public function getRevenueGroupedByDateInterval(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array;

    public function getAverageSpendingGroupedByDateInterval(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array;

    public function getTransactionsCountGroupedByDateInterval(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array;

    public function getAverageTransactionValueGroupedByDateInterval(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array;

    public function getAverageNumberOfTransactionsGroupedByDateInterval(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array;
}
