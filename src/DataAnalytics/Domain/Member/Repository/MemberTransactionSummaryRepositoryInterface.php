<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Member\Repository;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;

interface MemberTransactionSummaryRepositoryInterface
{
    public function getRevenueForDateInterval(
        StoreId $tenantId,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): float;

    public function getAverageSpendingForDateInterval(
        StoreId $tenantId,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): float;

    public function getTransactionsCountForDateInterval(
        StoreId $tenantId,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): int;

    public function getAverageTransactionValueForDateInterval(
        StoreId $tenantId,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): float;

    public function getAverageNumberOfTransactionsForDateInterval(
        StoreId $tenantId,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): float;
}
