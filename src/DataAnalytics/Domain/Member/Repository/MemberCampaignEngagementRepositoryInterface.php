<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Member\Repository;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\DataAnalytics\Domain\Member\Entity\MemberCampaignEngagement;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\AggregationType;
use OpenLoyalty\DataAnalytics\Domain\Tenant\Entity\Tenant;

interface MemberCampaignEngagementRepositoryInterface
{
    public function save(MemberCampaignEngagement $memberCampaignEngagement): void;

    public function getOldestEventDate(Tenant $tenant, CampaignId $campaignId): DateTimeImmutable;

    public function getTotalEngagementCountForDateInterval(
        StoreId $tenantId,
        CampaignId $campaignId,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): int;

    public function getTotalEngagementGroupedByDateInterval(
        StoreId $tenantId,
        CampaignId $campaignId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array;

    public function getEngagedMembersCountForDateInterval(
        StoreId $tenantId,
        CampaignId $campaignId,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): int;

    public function getEngagedMembersGroupedByDateInterval(
        StoreId $tenantId,
        CampaignId $campaignId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array;
}
