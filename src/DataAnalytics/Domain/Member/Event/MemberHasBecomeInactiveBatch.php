<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Member\Event;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AbstractAnalyticsEvent;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsBatchEventsInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;

final class MemberHasBecomeInactiveBatch extends AbstractAnalyticsEvent implements AnalyticsBatchEventsInterface
{
    private array $events;

    public function __construct(
        AnalyticsEventId $analyticsEventId,
        Version $eventVersion,
        StoreId $tenantId
    ) {
        parent::__construct($analyticsEventId, $eventVersion, $tenantId);
        $this->events = [];
    }

    public function addEvent(MemberHasBecomeInactive $event): void
    {
        $this->events[] = $event;
    }

    public function getEvents(): array
    {
        return $this->events;
    }
}
