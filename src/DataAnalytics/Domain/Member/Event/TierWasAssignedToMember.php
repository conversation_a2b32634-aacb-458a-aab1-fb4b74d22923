<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Member\Event;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AbstractAnalyticsEvent;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;

final class TierWasAssignedToMember extends AbstractAnalyticsEvent
{
    public function __construct(
        AnalyticsEventId $analyticsEventId,
        Version $eventVersion,
        private readonly CustomerId $memberId,
        private readonly LevelId $tierId,
        private readonly DateTimeImmutable $assignmentDate,
        StoreId $tenantId
    ) {
        parent::__construct($analyticsEventId, $eventVersion, $tenantId);
    }

    public function getMemberId(): CustomerId
    {
        return $this->memberId;
    }

    public function getTierId(): LevelId
    {
        return $this->tierId;
    }

    public function getAssignmentDate(): DateTimeImmutable
    {
        return $this->assignmentDate;
    }
}
