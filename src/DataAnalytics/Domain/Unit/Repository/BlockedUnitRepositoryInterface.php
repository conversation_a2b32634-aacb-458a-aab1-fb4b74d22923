<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Unit\Repository;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\AggregationType;
use OpenLoyalty\DataAnalytics\Domain\Unit\Entity\BlockedUnit;

interface BlockedUnitRepositoryInterface
{
    public function save(BlockedUnit $blockedUnit): void;

    public function byId(TransferId $transferId): ?BlockedUnit;

    public function getBlockedUnitsSumUntilDate(
        StoreId $tenantId,
        string $walletTypeCode,
        DateTimeImmutable $untilDate
    ): float;

    public function getBlockedUnitsGroupedByDateInterval(
        StoreId $tenantId,
        string $walletTypeCode,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array;
}
