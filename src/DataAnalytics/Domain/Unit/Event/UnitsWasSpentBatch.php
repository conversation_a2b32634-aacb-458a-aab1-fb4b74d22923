<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Unit\Event;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AbstractAnalyticsEvent;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsBatchEventsInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;

final class UnitsWasSpentBatch extends AbstractAnalyticsEvent implements AnalyticsBatchEventsInterface
{
    /**
     * @param AnalyticsEventId $analyticsEventId
     * @param Version          $eventVersion
     * @param StoreId          $tenantId
     * @param UnitsWasSpent[]  $unitsWasSpentEvents
     */
    public function __construct(
        AnalyticsEventId $analyticsEventId,
        Version $eventVersion,
        StoreId $tenantId,
        private readonly array $unitsWasSpentEvents
    ) {
        parent::__construct($analyticsEventId, $eventVersion, $tenantId);
    }

    public function getUnitsWasSpentEvents(): array
    {
        return $this->unitsWasSpentEvents;
    }
}
