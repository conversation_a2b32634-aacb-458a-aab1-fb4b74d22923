<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Ui\Shared\Console\Command;

use Doctrine\DBAL\Exception as DbalException;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\ORM\EntityManagerInterface;
use OpenLoyalty\DataAnalytics\Domain\Member\Entity\Member;
use OpenLoyalty\DataAnalytics\Domain\Member\Entity\MemberTier;
use OpenLoyalty\DataAnalytics\Domain\Member\Entity\MemberTransaction;
use OpenLoyalty\DataAnalytics\Domain\Segment\Entity\Segment;
use OpenLoyalty\DataAnalytics\Domain\Shared\Entity\AnalyticsEvent;
use OpenLoyalty\DataAnalytics\Domain\Tenant\Entity\Tenant;
use OpenLoyalty\DataAnalytics\Domain\Tier\Entity\Tier;
use OpenLoyalty\DataAnalytics\Domain\Unit\Entity\BlockedUnit;
use OpenLoyalty\DataAnalytics\Domain\Unit\Entity\ExpiredUnit;
use OpenLoyalty\DataAnalytics\Domain\Unit\Entity\IssuedUnit;
use OpenLoyalty\DataAnalytics\Domain\Unit\Entity\LockedUnit;
use OpenLoyalty\DataAnalytics\Domain\Unit\Entity\SpentUnit;
use OpenLoyalty\DataAnalytics\Domain\Unit\Entity\UnlockedUnit;
use OpenLoyalty\DataAnalytics\Domain\Wallet\Entity\WalletType;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Throwable;

final class TruncateAnalyticsTablesCommand extends Command
{
    private const ENTITIES_TO_TRUNCATE = [
        AnalyticsEvent::class,
        Tenant::class,
        Segment::class,
        Tier::class,
        Member::class,
        MemberTier::class,
        MemberTransaction::class,
        IssuedUnit::class,
        SpentUnit::class,
        ExpiredUnit::class,
        WalletType::class,
        LockedUnit::class,
        UnlockedUnit::class,
        BlockedUnit::class,
    ];

    public function __construct(
        private readonly EntityManagerInterface $entityManager
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('oloy:analytics:truncate')
            ->setDescription('Truncate all analytics data.');
    }

    /**
     * @throws DbalException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($input->isInteractive()) {
            $helper = $this->getHelper('question');
            $question = new ConfirmationQuestion(
                'Are you sure you want to truncate all analytics data? [Y/n]: ',
                false
            );

            /* @phpstan-ignore-next-line */
            if (!$helper->ask($input, $output, $question)) {
                return self::SUCCESS;
            }
        }

        try {
            $this->truncateData();
        } catch (Throwable $exception) {
            $output->writeln(sprintf('<error>%s</error>', $exception->getMessage()));

            throw $exception;
        }

        return self::SUCCESS;
    }

    /**
     * @throws DbalException
     */
    private function truncateData(): void
    {
        $connection = $this->entityManager->getConnection();
        /** @var ?AbstractPlatform $databasePlatform */
        $databasePlatform = $connection->getDatabasePlatform();
        if (null === $databasePlatform) {
            throw new DbalException('Database platform is null');
        }

        foreach (self::ENTITIES_TO_TRUNCATE as $entityToTruncate) {
            $connection->executeStatement(
                $databasePlatform->getTruncateTableSQL(
                    $this->entityManager->getClassMetadata($entityToTruncate)->getTableName()
                )
            );
        }
    }
}
