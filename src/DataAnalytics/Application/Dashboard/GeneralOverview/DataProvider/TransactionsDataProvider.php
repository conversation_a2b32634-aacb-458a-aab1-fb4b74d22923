<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Application\Dashboard\GeneralOverview\DataProvider;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\DataAnalytics\Domain\Member\Repository\MemberTransactionAggregatedRepositoryInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\AggregationType;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\GeneralOverviewDataType;

final class TransactionsDataProvider extends AbstractGeneralOverviewDataProvider
{
    public function __construct(
        private readonly MemberTransactionAggregatedRepositoryInterface $transactionAggregatedRepository
    ) {
    }

    public function getData(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array {
        return $this->transactionAggregatedRepository->getTransactionsCountGroupedByDateInterval(
            $tenantId,
            $aggregationType,
            $intervalStartDate,
            $intervalEndDate
        );
    }

    protected function getSupportedDataType(): GeneralOverviewDataType
    {
        return new GeneralOverviewDataType(GeneralOverviewDataType::TRANSACTIONS);
    }
}
