<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Application\Dashboard\GeneralOverview\Service;

use DateTimeImmutable;
use OpenLoyalty\DataAnalytics\Domain\Member\Repository\MemberActivityRepositoryInterface;
use OpenLoyalty\DataAnalytics\Domain\Member\Repository\MemberRepositoryInterface;
use OpenLoyalty\DataAnalytics\Domain\Member\Repository\MemberTransactionRepositoryInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\GeneralOverviewDataType;
use OpenLoyalty\DataAnalytics\Domain\Tenant\Entity\Tenant;

final class IntervalStartDateProvider
{
    public function __construct(
        private readonly MemberRepositoryInterface $memberRepository,
        private readonly MemberActivityRepositoryInterface $memberActivityRepository,
        private readonly MemberTransactionRepositoryInterface $memberTransactionRepository
    ) {
    }

    public function getStartDateByDataType(
        GeneralOverviewDataType $dataType,
        Tenant $tenant
    ): DateTimeImmutable {
        return match ($dataType->getType()) {
            GeneralOverviewDataType::REGISTERED_MEMBERS => $this->memberRepository->getOldestEventDate($tenant),
            GeneralOverviewDataType::ACTIVE_MEMBERS => $this->memberActivityRepository->getOldestEventDate($tenant),
            default => $this->memberTransactionRepository->getOldestEventDate($tenant),
        };
    }
}
