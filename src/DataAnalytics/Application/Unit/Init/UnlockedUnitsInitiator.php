<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Application\Unit\Init;

use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\DataAnalytics\Application\Unit\DTO\UnlockedUnits;
use OpenLoyalty\DataAnalytics\Domain\Member\Exception\GenderTypeIsInvalid;
use OpenLoyalty\DataAnalytics\Domain\Shared\Exception\VersionValueIsInvalid;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventBusInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;
use OpenLoyalty\DataAnalytics\Domain\Unit\Event\UnitsWasUnlocked;
use OpenLoyalty\DataAnalytics\Domain\Unit\Event\UnitsWasUnlockedBatch;
use OpenLoyalty\DataAnalytics\Domain\Unit\UnitsFacadeInterface;

final readonly class UnlockedUnitsInitiator
{
    public function __construct(
        private StoreRepository $storeRepository,
        private UnitsFacadeInterface $unitFacade,
        private UuidGeneratorInterface $uuidGenerator,
        private AnalyticsEventBusInterface $analyticsEventBus,
        private int $initMessageBatchSize
    ) {
    }

    /**
     * @throws GenderTypeIsInvalid
     * @throws AssertionFailedException
     * @throws VersionValueIsInvalid
     */
    public function init(): void
    {
        foreach ($this->storeRepository->findAll() as $tenant) {
            $tenantId = $tenant->getStoreId();
            $messages = [];

            /**
             * @var UnlockedUnits $unlockedUnits
             */
            foreach ($this->unitFacade->getUnlockedUnits($tenantId) as $unlockedUnits) {
                $messages[] = new UnitsWasUnlocked(
                    new AnalyticsEventId($this->uuidGenerator->generate()),
                    new Version(1),
                    $tenantId,
                    $unlockedUnits->transferId,
                    $unlockedUnits->ownerId,
                    $unlockedUnits->walletTypeCode,
                    $unlockedUnits->value,
                    $unlockedUnits->unlockedDate,
                    $unlockedUnits->cancellationDate
                );

                if ($this->initMessageBatchSize === count($messages)) {
                    $this->dispatchMessages($messages, $tenantId);
                    $messages = [];
                }
            }

            if (count($messages) > 0) {
                $this->dispatchMessages($messages, $tenantId);
            }
        }
    }

    /**
     * @throws AssertionFailedException
     * @throws VersionValueIsInvalid
     */
    private function dispatchMessages(array $messages, StoreId $tenantId): void
    {
        $this->analyticsEventBus->dispatch(
            new UnitsWasUnlockedBatch(
                new AnalyticsEventId($this->uuidGenerator->generate()),
                new Version(1),
                $tenantId,
                $messages
            ),
            false
        );
    }
}
