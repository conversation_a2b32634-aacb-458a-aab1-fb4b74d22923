<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Application\Unit\DTO;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;

final readonly class ExpiredUnits
{
    public function __construct(
        public StoreId $tenantId,
        public TransferId $transferId,
        public float $value,
        public CustomerId $ownerId,
        public string $walletTypeCode,
        public ?DateTimeImmutable $expiredDate,
        public DateTimeImmutable $createdDate
    ) {
    }
}
