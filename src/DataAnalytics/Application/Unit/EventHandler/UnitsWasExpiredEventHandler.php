<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Application\Unit\EventHandler;

use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventHandlerInterface;
use OpenLoyalty\DataAnalytics\Domain\Unit\Event\UnitsWasExpired;
use OpenLoyalty\DataAnalytics\Domain\Unit\Service\ExpiredUnitsRegistrationServiceInterface;

final readonly class UnitsWasExpiredEventHandler implements AnalyticsEventHandlerInterface
{
    public function __construct(
        private ExpiredUnitsRegistrationServiceInterface $expiredUnitsRegistrationService
    ) {
    }

    public function __invoke(UnitsWasExpired $event): void
    {
        $this->expiredUnitsRegistrationService->saveExpiredUnits($event);
    }
}
