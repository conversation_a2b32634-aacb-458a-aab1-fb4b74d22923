<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Application\Unit\EventHandler;

use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventHandlerInterface;
use OpenLoyalty\DataAnalytics\Domain\Unit\Event\UnitsWasSpentBatch;
use OpenLoyalty\DataAnalytics\Domain\Unit\Service\SpentUnitsRegistrationServiceInterface;

final readonly class UnitsWasSpentBatchEventHandler implements AnalyticsEventHandlerInterface
{
    public function __construct(
        private SpentUnitsRegistrationServiceInterface $spentUnitsRegistrationService
    ) {
    }

    public function __invoke(UnitsWasSpentBatch $event): void
    {
        foreach ($event->getUnitsWasSpentEvents() as $unitsWasSpentEvent) {
            $this->spentUnitsRegistrationService->saveSpentUnits($unitsWasSpentEvent);
        }
    }
}
