<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Application\Wallet\EventHandler;

use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventHandlerInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\Repository\AnalyticsEventRepositoryInterface;
use OpenLoyalty\DataAnalytics\Domain\Wallet\Event\WalletTypeWasUpdated;
use OpenLoyalty\DataAnalytics\Domain\Wallet\Repository\WalletTypeRepositoryInterface;
use Psr\Log\LoggerInterface;

final readonly class WalletTypeWasUpdatedEventHandler implements AnalyticsEventHandlerInterface
{
    public function __construct(
        private AnalyticsEventRepositoryInterface $analyticsEventRepository,
        private WalletTypeRepositoryInterface $walletTypeRepository,
        private LoggerInterface $dataAnalyticsLogger,
    ) {
    }

    public function __invoke(WalletTypeWasUpdated $event): void
    {
        $walletType = $this->walletTypeRepository->byId($event->getWalletTypeId());

        if (null === $walletType) {
            $this->dataAnalyticsLogger->error(
                sprintf(
                    '[DataAnalytics] Attempt to update an non existing wallet type with ID.: %s',
                    $event->getWalletTypeId()
                )
            );

            return;
        }

        if ($walletType->getName() === $event->getWalletTypeName()) {
            return;
        }

        $this->analyticsEventRepository->save($event);
        $walletType->update($event->getWalletTypeName(), $event->getWalletTypeUpdateDate());
    }
}
