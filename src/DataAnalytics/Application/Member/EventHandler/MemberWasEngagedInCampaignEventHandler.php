<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Application\Member\EventHandler;

use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\DataAnalytics\Domain\Member\Entity\MemberCampaignEngagement;
use OpenLoyalty\DataAnalytics\Domain\Member\Event\MemberWasEngagedInCampaign;
use OpenLoyalty\DataAnalytics\Domain\Member\Identifier\MemberCampaignEngagementId;
use OpenLoyalty\DataAnalytics\Domain\Member\Repository\MemberCampaignEngagementRepositoryInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventHandlerInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\Repository\AnalyticsEventRepositoryInterface;

final class MemberWasEngagedInCampaignEventHandler implements AnalyticsEventHandlerInterface
{
    public function __construct(
        private readonly AnalyticsEventRepositoryInterface $analyticsEventRepository,
        private readonly UuidGeneratorInterface $uuidGenerator,
        private readonly MemberCampaignEngagementRepositoryInterface $memberCampaignEngagementRepository
    ) {
    }

    /**
     * @throws AssertionFailedException
     */
    public function __invoke(MemberWasEngagedInCampaign $event): void
    {
        $this->analyticsEventRepository->save($event);

        $this->memberCampaignEngagementRepository->save(
            MemberCampaignEngagement::create(
                new MemberCampaignEngagementId($this->uuidGenerator->generate()),
                $event->getCampaignId(),
                $event->getMemberId(),
                $event->getNumberOfEffects(),
                $event->getEngagementDate(),
                $event->getTenantId()
            )
        );
    }
}
