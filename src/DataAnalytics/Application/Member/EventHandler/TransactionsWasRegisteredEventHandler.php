<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Application\Member\EventHandler;

use OpenLoyalty\DataAnalytics\Domain\Member\Event\TransactionsWasRegistered;
use OpenLoyalty\DataAnalytics\Domain\Member\Service\TransactionRegistrationServiceInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventHandlerInterface;

final class TransactionsWasRegisteredEventHandler implements AnalyticsEventHandlerInterface
{
    public function __construct(
        private readonly TransactionRegistrationServiceInterface $transactionRegistrationService
    ) {
    }

    public function __invoke(TransactionsWasRegistered $event): void
    {
        foreach ($event->getTransactionRegistrationEvents() as $transactionEvent) {
            $this->transactionRegistrationService->saveTransaction($transactionEvent);
        }
    }
}
