<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Application\Member\Init;

use Assert\AssertionFailedException;
use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\DataAnalytics\Domain\Member\Event\MembersWasRegistered;
use OpenLoyalty\DataAnalytics\Domain\Member\Event\MemberWasRegistered;
use OpenLoyalty\DataAnalytics\Domain\Member\Exception\GenderTypeIsInvalid;
use OpenLoyalty\DataAnalytics\Domain\Member\MemberFacadeInterface;
use OpenLoyalty\DataAnalytics\Domain\Member\ValueObject\Gender;
use OpenLoyalty\DataAnalytics\Domain\Shared\Exception\VersionValueIsInvalid;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventBusInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;

final readonly class MemberInitiator
{
    public function __construct(
        private StoreRepository $storeRepository,
        private MemberFacadeInterface $memberFacade,
        private UuidGeneratorInterface $uuidGenerator,
        private AnalyticsEventBusInterface $analyticsEventBus,
        private int $initMessageBatchSize
    ) {
    }

    /**
     * @throws AssertionFailedException
     * @throws VersionValueIsInvalid
     * @throws GenderTypeIsInvalid
     */
    public function init(): void
    {
        foreach ($this->storeRepository->findAll() as $tenant) {
            $tenantId = $tenant->getStoreId();
            $messages = [];
            foreach ($this->memberFacade->getAllActive($tenantId) as $member) {
                $messages[] = new MemberWasRegistered(
                    new AnalyticsEventId($this->uuidGenerator->generate()),
                    new Version(1),
                    $member->getCustomerId(),
                    DateTimeImmutable::createFromMutable($member->getActivatedAt()),
                    new Gender($member->getGender()->getType()),
                    $member->getStoreId()
                );

                if ($this->initMessageBatchSize === count($messages)) {
                    $this->dispatchMessages($messages, $tenantId);
                    $messages = [];
                }
            }

            if (count($messages) > 0) {
                $this->dispatchMessages($messages, $tenantId);
            }
        }
    }

    /**
     * @throws AssertionFailedException
     * @throws VersionValueIsInvalid
     */
    private function dispatchMessages(array $messages, StoreId $tenantId): void
    {
        $this->analyticsEventBus->dispatch(
            new MembersWasRegistered(
                new AnalyticsEventId($this->uuidGenerator->generate()),
                new Version(1),
                $tenantId,
                $messages
            ),
            false
        );
    }
}
