<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Application\Segment\EventHandler;

use OpenLoyalty\DataAnalytics\Domain\Segment\Event\SegmentWasUpdated;
use OpenLoyalty\DataAnalytics\Domain\Segment\Repository\SegmentRepositoryInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventHandlerInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\Repository\AnalyticsEventRepositoryInterface;

final class SegmentWasUpdatedEventHandler implements AnalyticsEventHandlerInterface
{
    public function __construct(
        private readonly AnalyticsEventRepositoryInterface $analyticsEventRepository,
        private readonly SegmentRepositoryInterface $segmentRepository
    ) {
    }

    public function __invoke(SegmentWasUpdated $event): void
    {
        $segment = $this->segmentRepository->byId($event->getSegmentId());

        if ($segment->getName() === $event->getSegmentName()) {
            return;
        }

        $this->analyticsEventRepository->save($event);
        $segment->update($event->getSegmentName(), $event->getSegmentUpdateDate());
    }
}
