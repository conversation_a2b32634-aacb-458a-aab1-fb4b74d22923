<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Application\Shared\Interval;

use OpenLoyalty\DataAnalytics\Domain\Shared\Exception\AggregationTypeNotSupported;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\AggregationType;

interface IntervalFormatterInterface
{
    /**
     * @throws AggregationTypeNotSupported
     */
    public function getSqlDateFormat(AggregationType $aggregationType): string;

    /**
     * @throws AggregationTypeNotSupported
     */
    public function getPhpDateFormat(AggregationType $aggregationType): string;
}
