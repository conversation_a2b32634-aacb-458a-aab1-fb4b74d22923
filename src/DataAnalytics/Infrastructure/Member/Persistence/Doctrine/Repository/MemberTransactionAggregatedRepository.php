<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Infrastructure\Member\Persistence\Doctrine\Repository;

use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Search\ContextQueryBuilderInterface;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\Core\Infrastructure\Persistence\ResultCacheResolver;
use OpenLoyalty\DataAnalytics\Application\Shared\Interval\IntervalFormatterInterface;
use OpenLoyalty\DataAnalytics\Domain\Member\Entity\MemberTransaction;
use OpenLoyalty\DataAnalytics\Domain\Member\Repository\MemberTransactionAggregatedRepositoryInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\Exception\AggregationTypeNotSupported;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\AggregationType;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class MemberTransactionAggregatedRepository extends DoctrineRepository implements MemberTransactionAggregatedRepositoryInterface
{
    public function __construct(
        EntityManagerInterface $entityManager,
        ContextQueryBuilderInterface $queryBuilder,
        ResultCacheResolver $resultCacheResolver,
        LoggerInterface $appLogger,
        private readonly IntervalFormatterInterface $intervalFormatter,
        private readonly int $queryResultCacheTTL,
        ParameterBagInterface $parameterBag
    ) {
        parent::__construct(
            $entityManager,
            $queryBuilder,
            $resultCacheResolver,
            $appLogger,
            $parameterBag
        );
    }

    /**
     * @throws AggregationTypeNotSupported
     */
    public function getRevenueGroupedByDateInterval(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array {
        $dateFormat = $this->intervalFormatter->getSqlDateFormat($aggregationType);
        $builder = $this->repository->createQueryBuilder('t');

        $builder
            ->select('sum(t.grossValue) as grossValue, date_format(t.registrationDate, \''.$dateFormat.'\') as interval')
            ->andWhere('t.tenantId = :tenantId')
            ->andWhere('t.registrationDate >= :intervalStartDate')
            ->andWhere('t.registrationDate < :intervalEndDate')
            ->groupBy('interval')
            ->orderBy('interval', 'ASC')
            ->setParameter('tenantId', $tenantId)
            ->setParameter('intervalStartDate', $intervalStartDate)
            ->setParameter('intervalEndDate', $intervalEndDate);

        $results = $builder->getQuery()->enableResultCache($this->queryResultCacheTTL)->getResult();
        $data = [];
        foreach ($results as $result) {
            $data[$result['interval']] = (float) $result['grossValue'];
        }

        return $data;
    }

    /**
     * @throws AggregationTypeNotSupported
     */
    public function getAverageSpendingGroupedByDateInterval(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array {
        $revenueGrouped = $this->getRevenueGroupedByDateInterval(
            $tenantId,
            $aggregationType,
            $intervalStartDate,
            $intervalEndDate
        );

        $membersGrouped = $this->getMembersGroupedForDateInterval(
            $tenantId,
            $aggregationType,
            $intervalStartDate,
            $intervalEndDate
        );

        $data = [];
        foreach ($revenueGrouped as $date => $revenue) {
            if (isset($membersGrouped[$date])) {
                $data[$date] = $revenue / $membersGrouped[$date];
            } else {
                $data[$date] = (float) $revenue;
            }
        }

        return $data;
    }

    /**
     * @throws AggregationTypeNotSupported
     */
    public function getAverageTransactionValueGroupedByDateInterval(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array {
        $revenueGrouped = $this->getRevenueGroupedByDateInterval(
            $tenantId,
            $aggregationType,
            $intervalStartDate,
            $intervalEndDate
        );

        $transactionsGrouped = $this->getTransactionsCountGroupedByDateInterval(
            $tenantId,
            $aggregationType,
            $intervalStartDate,
            $intervalEndDate
        );

        $data = [];
        foreach ($revenueGrouped as $date => $revenue) {
            if (isset($transactionsGrouped[$date])) {
                $data[$date] = $revenue / $transactionsGrouped[$date];
            } else {
                $data[$date] = (float) $revenue;
            }
        }

        return $data;
    }

    /**
     * @throws AggregationTypeNotSupported
     */
    public function getAverageNumberOfTransactionsGroupedByDateInterval(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array {
        $transactionsGrouped = $this->getTransactionsCountGroupedByDateInterval(
            $tenantId,
            $aggregationType,
            $intervalStartDate,
            $intervalEndDate
        );

        $membersGrouped = $this->getMembersGroupedForDateInterval(
            $tenantId,
            $aggregationType,
            $intervalStartDate,
            $intervalEndDate
        );

        $data = [];
        foreach ($transactionsGrouped as $date => $transactions) {
            if (isset($membersGrouped[$date])) {
                $data[$date] = $transactions / $membersGrouped[$date];
            } else {
                $data[$date] = (float) $transactions;
            }
        }

        return $data;
    }

    /**
     * @throws AggregationTypeNotSupported
     */
    public function getTransactionsCountGroupedByDateInterval(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array {
        $dateFormat = $this->intervalFormatter->getSqlDateFormat($aggregationType);
        $builder = $this->repository->createQueryBuilder('t');

        $builder
            ->select('count(t.transactionId) as transactionsCount, date_format(t.registrationDate, \''.$dateFormat.'\') as interval')
            ->andWhere('t.tenantId = :tenantId')
            ->andWhere('t.registrationDate >= :intervalStartDate')
            ->andWhere('t.registrationDate < :intervalEndDate')
            ->groupBy('interval')
            ->orderBy('interval', 'ASC')
            ->setParameter('tenantId', $tenantId)
            ->setParameter('intervalStartDate', $intervalStartDate)
            ->setParameter('intervalEndDate', $intervalEndDate);

        $results = $builder->getQuery()->enableResultCache($this->queryResultCacheTTL)->getResult();
        $data = [];
        foreach ($results as $result) {
            $data[$result['interval']] = $result['transactionsCount'];
        }

        return $data;
    }

    protected function getClass(): string
    {
        return MemberTransaction::class;
    }

    /**
     * @throws AggregationTypeNotSupported
     */
    private function getMembersGroupedForDateInterval(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array {
        $dateFormat = $this->intervalFormatter->getSqlDateFormat($aggregationType);
        $builder = $this->repository->createQueryBuilder('t');

        $builder
            ->select('count(distinct t.memberId) as membersCount, date_format(t.registrationDate, \''.$dateFormat.'\') as interval')
            ->andWhere('t.tenantId = :tenantId')
            ->andWhere('t.registrationDate >= :intervalStartDate')
            ->andWhere('t.registrationDate < :intervalEndDate')
            ->groupBy('interval')
            ->orderBy('interval', 'ASC')
            ->setParameter('tenantId', $tenantId)
            ->setParameter('intervalStartDate', $intervalStartDate)
            ->setParameter('intervalEndDate', $intervalEndDate);

        $results = $builder->getQuery()->enableResultCache($this->queryResultCacheTTL)->getResult();
        $data = [];
        foreach ($results as $result) {
            $data[$result['interval']] = $result['membersCount'];
        }

        return $data;
    }
}
