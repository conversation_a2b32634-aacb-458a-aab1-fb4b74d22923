<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Infrastructure\Member\Persistence\Doctrine\Repository;

use DateTimeImmutable;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Exception;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Search\ContextQueryBuilderInterface;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\Core\Infrastructure\Persistence\ResultCacheResolver;
use OpenLoyalty\DataAnalytics\Application\Shared\Decorator\IntervalResponseDecorator;
use OpenLoyalty\DataAnalytics\Application\Shared\Interval\IntervalFormatterInterface;
use OpenLoyalty\DataAnalytics\Domain\Member\Entity\MemberActivity;
use OpenLoyalty\DataAnalytics\Domain\Member\Repository\MemberActivityRepositoryInterface;
use OpenLoyalty\DataAnalytics\Domain\Member\ValueObject\ActivityType;
use OpenLoyalty\DataAnalytics\Domain\Shared\Exception\AggregationTypeNotSupported;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\AggregationType;
use OpenLoyalty\DataAnalytics\Domain\Tenant\Entity\Tenant;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

final class MemberActivityRepository extends DoctrineRepository implements MemberActivityRepositoryInterface
{
    public function __construct(
        EntityManagerInterface $entityManager,
        ContextQueryBuilderInterface $queryBuilder,
        ResultCacheResolver $resultCacheResolver,
        LoggerInterface $appLogger,
        private readonly IntervalResponseDecorator $intervalResponseDecorator,
        private readonly IntervalFormatterInterface $intervalFormatter,
        private readonly int $queryResultCacheTTL,
        ParameterBagInterface $parameterBag
    ) {
        parent::__construct(
            $entityManager,
            $queryBuilder,
            $resultCacheResolver,
            $appLogger,
            $parameterBag
        );
    }

    public function save(MemberActivity $memberActivity): void
    {
        $this->entityManager->persist($memberActivity);
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws Exception
     */
    public function getOldestEventDate(Tenant $tenant): DateTimeImmutable
    {
        $builder = $this->repository->createQueryBuilder('ma');

        $builder
            ->select('min(ma.activityChangeDate) as oldestDate')
            ->andWhere('ma.tenantId = :tenantId')
            ->setParameter('tenantId', $tenant->getTenantId());

        $result = $builder->getQuery()->getSingleResult();
        if (null === $result['oldestDate']) {
            return new DateTimeImmutable();
        }

        return new DateTimeImmutable($result['oldestDate']);
    }

    public function getActiveMembersCountForDateInterval(
        StoreId $tenantId,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): int {
        $initialActiveMembers = $this->getInitialActiveMembers($tenantId, $intervalStartDate);

        $initialActiveMembers += $this->getActiveMembersCountForDateIntervalByType($tenantId, $intervalStartDate, $intervalEndDate, ActivityType::ACTIVE);
        $initialActiveMembers -= $this->getActiveMembersCountForDateIntervalByType($tenantId, $intervalStartDate, $intervalEndDate, ActivityType::INACTIVE);

        return max($initialActiveMembers, 0);
    }

    private function getInitialActiveMembers(
        StoreId $tenantId,
        DateTimeImmutable $intervalStartDate
    ): int {
        $initialActiveMembers = $this->getInitialActiveMembersCountBeforeIntervalStartDateByType(
            $tenantId,
            $intervalStartDate,
            ActivityType::ACTIVE
        );

        $initialInactiveMembers = $this->getInitialActiveMembersCountBeforeIntervalStartDateByType(
            $tenantId,
            $intervalStartDate,
            ActivityType::INACTIVE
        );

        $initialActiveMembers -= $initialInactiveMembers;

        return max($initialActiveMembers, 0);
    }

    /**
     * @throws AggregationTypeNotSupported
     */
    public function getActiveMembersGroupedByDateInterval(
        StoreId $tenantId,
        AggregationType $aggregationType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array {
        $initialActiveMembers = $this->getInitialActiveMembers($tenantId, $intervalStartDate);
        $dateFormat = $this->intervalFormatter->getSqlDateFormat($aggregationType);

        $groupedActiveMembersCount = $this->getGroupedMembersCountByActivity(
            $tenantId,
            $dateFormat,
            ActivityType::ACTIVE,
            $intervalStartDate,
            $intervalEndDate
        );

        $groupedInactiveMembersCount = $this->getGroupedMembersCountByActivity(
            $tenantId,
            $dateFormat,
            ActivityType::INACTIVE,
            $intervalStartDate,
            $intervalEndDate
        );

        $dateRange = $this->intervalResponseDecorator->getDateRange(
            $aggregationType,
            $intervalStartDate,
            $intervalEndDate->modify('-1 day')
        );

        $data = [];
        foreach ($dateRange as $date) {
            $key = $date->format($this->intervalFormatter->getPhpDateFormat($aggregationType));
            $activeMembersCountKey = array_search($key, array_column($groupedActiveMembersCount, 'interval'));
            if (false !== $activeMembersCountKey) {
                $initialActiveMembers += $groupedActiveMembersCount[$activeMembersCountKey]['membersCount'];
            }

            $inactiveMembersCountKey = array_search($key, array_column($groupedInactiveMembersCount, 'interval'));
            if (false !== $inactiveMembersCountKey) {
                $initialActiveMembers -= $groupedInactiveMembersCount[$inactiveMembersCountKey]['membersCount'];
            }

            $data[$key] = (int) max($initialActiveMembers, 0);
        }

        return $data;
    }

    protected function getClass(): string
    {
        return MemberActivity::class;
    }

    private function getActiveMembersCountForDateIntervalByType(
        StoreId $tenantId,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate,
        string $activityType,
    ): int {
        $builder = $this->repository->createQueryBuilder('ma');

        $builder
            ->select('count(ma.memberId)')
            ->andWhere('ma.activityType.type = :activityType')
            ->andWhere('ma.tenantId = :tenantId')
            ->andWhere('ma.activityChangeDate >= :intervalStartDate')
            ->andWhere('ma.activityChangeDate < :intervalEndDate')
            ->setParameter('activityType', $activityType)
            ->setParameter('tenantId', $tenantId)
            ->setParameter('intervalStartDate', $intervalStartDate)
            ->setParameter('intervalEndDate', $intervalEndDate);

        return $builder->getQuery()->enableResultCache($this->queryResultCacheTTL)->getResult(AbstractQuery::HYDRATE_SINGLE_SCALAR) ?? 0;
    }

    private function getInitialActiveMembersCountBeforeIntervalStartDateByType(
        StoreId $tenantId,
        DateTimeImmutable $intervalStartDate,
        string $activityType,
    ): int {
        $builder = $this->repository->createQueryBuilder('ma');

        $builder
            ->select('count(ma.memberId)')
            ->andWhere('ma.activityType.type = :activityType')
            ->andWhere('ma.tenantId = :tenantId')
            ->andWhere('ma.activityChangeDate < :intervalStartDate')
            ->setParameter('activityType', $activityType)
            ->setParameter('tenantId', $tenantId)
            ->setParameter('intervalStartDate', $intervalStartDate);

        return $builder->getQuery()->enableResultCache($this->queryResultCacheTTL)->getResult(AbstractQuery::HYDRATE_SINGLE_SCALAR) ?? 0;
    }

    private function getGroupedMembersCountByActivity(
        StoreId $tenantId,
        string $dateFormat,
        string $activityType,
        DateTimeImmutable $intervalStartDate,
        DateTimeImmutable $intervalEndDate
    ): array {
        $builder = $this->repository->createQueryBuilder('ma');

        $builder
            ->select('count(ma.memberId) as membersCount, date_format(ma.activityChangeDate, \''.$dateFormat.'\') as interval')
            ->andWhere('ma.activityType.type = :activityType')
            ->andWhere('ma.tenantId = :tenantId')
            ->andWhere('ma.activityChangeDate >= :intervalStartDate')
            ->andWhere('ma.activityChangeDate < :intervalEndDate')
            ->groupBy('interval')
            ->orderBy('interval', 'ASC')
            ->setParameter('activityType', $activityType)
            ->setParameter('tenantId', $tenantId)
            ->setParameter('intervalStartDate', $intervalStartDate)
            ->setParameter('intervalEndDate', $intervalEndDate);

        return $builder->getQuery()->enableResultCache($this->queryResultCacheTTL)->getResult();
    }
}
