<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\DataAnalytics\Infrastructure\Member\Persistence\Doctrine\Type;

use Assert\AssertionFailedException;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use OpenLoyalty\DataAnalytics\Domain\Member\Identifier\MemberTierId;
use Ramsey\Uuid\Doctrine\UuidType;

final class MemberTierIdDoctrineType extends UuidType
{
    public const NAME = 'member_tier_id';

    /**
     * @throws AssertionFailedException
     */
    public function convertToPHPValue($value, AbstractPlatform $platform): ?MemberTierId
    {
        if (empty($value)) {
            return null;
        }

        if ($value instanceof MemberTierId) {
            return $value;
        }

        return new MemberTierId($value);
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if (null === $value) {
            return null;
        }

        if ($value instanceof MemberTierId) {
            return (string) $value;
        }

        return null;
    }

    public function getName(): string
    {
        return self::NAME;
    }
}
