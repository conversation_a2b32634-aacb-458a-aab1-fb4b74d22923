<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Infrastructure\Unit\Service;

use OpenLoyalty\DataAnalytics\Domain\Shared\Repository\AnalyticsEventRepositoryInterface;
use OpenLoyalty\DataAnalytics\Domain\Unit\Event\UnitsWasSpent;
use OpenLoyalty\DataAnalytics\Domain\Unit\Factory\SpentUnitFactory;
use OpenLoyalty\DataAnalytics\Domain\Unit\Repository\SpentUnitRepositoryInterface;
use OpenLoyalty\DataAnalytics\Domain\Unit\Service\SpentUnitsRegistrationServiceInterface;
use Psr\Log\LoggerInterface;

final readonly class SpentUnitsRegistrationService implements SpentUnitsRegistrationServiceInterface
{
    public function __construct(
        private AnalyticsEventRepositoryInterface $analyticsEventRepository,
        private SpentUnitRepositoryInterface $spentUnitRepository,
        private SpentUnitFactory $spentUnitFactory,
        private LoggerInterface $dataAnalyticsLogger,
    ) {
    }

    public function saveSpentUnits(UnitsWasSpent $event): void
    {
        $spentUnit = $this->spentUnitRepository->byId($event->getTransferId());

        if (null !== $spentUnit) {
            $this->dataAnalyticsLogger->warning(
                sprintf(
                    '[DataAnalytics] Attempt to register an existing spent units with ID.: %s',
                    $event->getTransferId()
                )
            );

            return;
        }

        $this->analyticsEventRepository->save($event);
        $this->spentUnitRepository->save(
            $this->spentUnitFactory->createFromEvent($event)
        );
    }
}
