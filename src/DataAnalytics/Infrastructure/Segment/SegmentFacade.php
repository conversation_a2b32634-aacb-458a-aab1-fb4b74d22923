<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Infrastructure\Segment;

use OpenLoyalty\DataAnalytics\Domain\Segment\SegmentFacadeInterface;
use OpenLoyalty\Segment\Domain\SegmentGateInterface;

final class SegmentFacade implements SegmentFacadeInterface
{
    public function __construct(
        private readonly SegmentGateInterface $segmentGate
    ) {
    }

    public function getAllSegments(): iterable
    {
        return $this->segmentGate->getAllSegments();
    }
}
