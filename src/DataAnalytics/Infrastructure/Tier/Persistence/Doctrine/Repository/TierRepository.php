<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Infrastructure\Tier\Persistence\Doctrine\Repository;

use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\DataAnalytics\Domain\Tier\Entity\Tier;
use OpenLoyalty\DataAnalytics\Domain\Tier\Exception\TierNotFound;
use OpenLoyalty\DataAnalytics\Domain\Tier\Repository\TierRepositoryInterface;
use function sprintf;

final class TierRepository extends DoctrineRepository implements TierRepositoryInterface
{
    public function save(Tier $tier): void
    {
        $this->entityManager->persist($tier);
    }

    /**
     * @throws TierNotFound
     */
    public function byId(LevelId $tierId): Tier
    {
        $tier = $this->find($tierId);
        if (null === $tier) {
            throw new TierNotFound(sprintf('Tier with ID %s not found.', $tierId));
        }

        return $tier;
    }

    public function findOneOrNull(LevelId $tierId): ?Tier
    {
        return $this->find($tierId);
    }

    protected function getClass(): string
    {
        return Tier::class;
    }
}
