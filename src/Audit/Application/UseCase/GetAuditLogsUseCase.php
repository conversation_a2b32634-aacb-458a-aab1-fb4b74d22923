<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Audit\Application\UseCase;

use OpenLoyalty\Audit\Application\Service\AuditManagerInterface;
use OpenLoyalty\Audit\Domain\AuditLogRepository;
use OpenLoyalty\Audit\Domain\SecurityAuditLog;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponse;

class GetAuditLogsUseCase
{
    private AuditLogRepository $auditLogRepository;
    private AuditManagerInterface $auditManager;
    private SearchableResponderInterface $searchableResponder;

    public function __construct(
        AuditLogRepository $auditLogRepository,
        AuditManagerInterface $auditManager,
        SearchableResponderInterface $searchableResponder
    ) {
        $this->auditLogRepository = $auditLogRepository;
        $this->auditManager = $auditManager;
        $this->searchableResponder = $searchableResponder;
    }

    public function execute(CriteriaCollectionInterface $criteria): SearchableResponse
    {
        $response = $this->searchableResponder->fromCriteria($this->auditLogRepository, $criteria, true);

        $this->auditManager->auditAdminEvent(
            SecurityAuditLog::AUDIT_LOG_VIEWED_TYPE,
            SecurityAuditLog::SYSTEM_ENTITY_TYPE,
            null,
            []
        );

        return $response;
    }
}
