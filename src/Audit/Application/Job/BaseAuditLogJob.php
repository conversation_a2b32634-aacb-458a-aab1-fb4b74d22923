<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Audit\Application\Job;

use OpenLoyalty\Core\Application\LoggableEntityInterface;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\JobInterface;

abstract class BaseAuditLogJob implements JobInterface, LoggableEntityInterface
{
    /**
     * @var StoreId|null
     */
    protected $storeId;

    public function __construct(?StoreId $storeId)
    {
        $this->storeId = $storeId;
    }

    public function getStoreId(): ?StoreId
    {
        return $this->storeId;
    }

    public function getLoggableEntity(): ?object
    {
        return null;
    }
}
