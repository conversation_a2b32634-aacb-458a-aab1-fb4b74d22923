OpenLoyalty\Audit\Domain\AuditLog:
    type: entity
    table: audit
    indexes:
        auditCreatedAtIdx:
            columns: [ created_at ]
    inheritanceType: "SINGLE_TABLE"
    id:
        auditLogId:
            type: audit_log_id
            column: audit_log_id
    fields:
        createdAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        updatedAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        createdBy:
            type: string
            nullable: true
        updatedBy:
            type: string
            nullable: true
        eventType:
            type: string
        entityType:
            type: string
        entityId:
            type: string
            nullable: true
        username:
            type: string
            options:
                default: 'system'
        userId:
            type: string
            options:
                default: 'system'
        userType:
            type: string
            nullable: true
        data:
            type: json
        ip:
            type: string
            nullable: true
    manyToOne:
        store:
            targetEntity: OpenLoyalty\Core\Domain\Store
            joinColumn:
                name: store_id
                nullable: true
                referencedColumnName: id
