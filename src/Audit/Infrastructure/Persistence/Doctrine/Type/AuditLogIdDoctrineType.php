<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Audit\Infrastructure\Persistence\Doctrine\Type;

use Assert\AssertionFailedException;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use OpenLoyalty\Core\Domain\Id\AuditLogId;
use Ramsey\Uuid\Doctrine\UuidType;
use Ramsey\Uuid\UuidInterface;

final class AuditLogIdDoctrineType extends UuidType
{
    public const NAME = 'audit_log_id';

    /**
     * @return UuidInterface|AuditLogId|null
     * @throws AssertionFailedException
     */
    public function convertToPHPValue($value, AbstractPlatform $platform): UuidInterface|AuditLogId|null
    {
        if (empty($value)) {
            return null;
        }

        if ($value instanceof AuditLogId) {
            return $value;
        }

        return new AuditLogId($value);
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value instanceof AuditLogId) {
            return (string) $value;
        }

        return null;
    }

    public function getName(): string
    {
        return self::NAME;
    }

    public function requiresSQLCommentHint(AbstractPlatform $platform): bool
    {
        return true;
    }
}
