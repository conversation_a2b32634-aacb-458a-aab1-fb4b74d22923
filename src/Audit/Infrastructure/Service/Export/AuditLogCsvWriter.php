<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Audit\Infrastructure\Service\Export;

use OpenLoyalty\Audit\Application\Service\AuditLogWriterInterface;
use OpenLoyalty\Audit\Domain\AuditLog;

class AuditLogCsvWriter implements AuditLogWriterInterface
{
    /**
     * @var array
     */
    private $lines = [];

    /**
     * @var false|resource
     */
    private $fileHandle;

    public function open(string $filename): void
    {
        if (file_exists($filename)) {
            throw new \InvalidArgumentException('File already exists.');
        }

        $this->fileHandle = fopen($filename, 'xb');

        $this->lines[] = ['Log ID', 'Store code', 'Username', 'User type', 'User ID', 'Event type', 'Entity type', 'Entity ID', 'Created at', 'IP'];
        $this->flush();
    }

    public function write(AuditLog $element): void
    {
        $line = [
            (string) $element->getAuditLogId(),
            $element->getStore() ? $element->getStore()->getCode() : null,
            $element->getUsername(),
            $element->getUserType(),
            $element->getUserId(),
            $element->getEventType(),
            $element->getEntityType(),
            $element->getEntityId(),
            $element->getCreatedAt()->format(DATE_W3C),
            $element->getIp(),
        ];

        $this->lines[] = $line;
    }

    public function close(): void
    {
        $this->flush();
        fclose($this->fileHandle);
    }

    public function flush(): void
    {
        foreach ($this->lines as $line) {
            fputcsv($this->fileHandle, $line);
        }
        $this->lines = [];
    }
}
