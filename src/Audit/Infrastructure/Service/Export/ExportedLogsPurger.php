<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Audit\Infrastructure\Service\Export;

use Exception;
use OpenLoyalty\Audit\Application\Exception\FileStreamException;
use OpenLoyalty\Audit\Application\Exception\RequiredUniqueNodeException;
use OpenLoyalty\Audit\Application\Service\ExportedLogsPurgerInterface;
use OpenLoyalty\Audit\Domain\AuditLogRepository;
use Prewk\XmlStringStreamer;
use Throwable;

class ExportedLogsPurger implements ExportedLogsPurgerInterface
{
    private const FLUSH_FREQUENCY = 1000;

    public function __construct(
        private AuditLogRepository $auditLogRepository
    ) {
    }

    /**
     * @throws Exception
     */
    public function __invoke(string $filename): int
    {
        try {
            $parser = new XmlStringStreamer\Parser\UniqueNode(['uniqueNode' => 'entry']);
        } catch (Throwable $exception) {
            throw new RequiredUniqueNodeException($exception->getMessage());
        }
        try {
            $stream = new XmlStringStreamer\Stream\File($filename);
        } catch (Throwable $exception) {
            throw new FileStreamException($exception->getMessage());
        }
        $streamer = new XmlStringStreamer($parser, $stream);

        $idsRead = 0;
        $toDelete = [];
        while ($node = $streamer->getNode()) {
            $xml = simplexml_load_string($node);
            $toDelete[] = (string) $xml['id'];
            ++$idsRead;
            if (0 === $idsRead % self::FLUSH_FREQUENCY) {
                $this->auditLogRepository->bulkRemove($toDelete);
                $toDelete = [];
            }
        }

        if (!empty($toDelete)) {
            $this->auditLogRepository->bulkRemove($toDelete);
        }

        return $idsRead;
    }
}
