<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Audit\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Audit\Application\UseCase\GetAuditLogsArchivesUseCase;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableTotalResponse;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;

class GetLogArchiveList extends AbstractFOSRestController
{
    private GetAuditLogsArchivesUseCase $useCase;

    public function __construct(
        GetAuditLogsArchivesUseCase $useCase
    ) {
        $this->useCase = $useCase;
    }

    /**
     * @Route(methods={"GET"}, name="oloy.audit.log.archive.list", path="/audit/log/archive")
     * @Security("is_granted('AUDIT_LOG')")
     *
     * @Operation(
     *     tags={"Audit"},
     *     summary="Get audit log archives list",
     *     operationId="auditLogArchiveGetList",
     *     @OA\Response(
     *         response="200",
     *         description="Returned when successful",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="items",
     *                     type="array",
     *                     @OA\Items(type="string")
     *                 ),
     *                 @OA\Property(property="total", ref=@Model(type=SearchableTotalResponse::class))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="401",
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 oneOf={
     *                     @OA\Schema(ref="#/components/schemas/ExpiredToken"),
     *                     @OA\Schema(ref="#/components/schemas/InvalidToken"),
     *                     @OA\Schema(ref="#/components/schemas/Unauthorized")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     * )
     */
    public function __invoke(): View
    {
        return $this->view(
            $this->useCase->execute(),
            Response::HTTP_OK
        );
    }
}
