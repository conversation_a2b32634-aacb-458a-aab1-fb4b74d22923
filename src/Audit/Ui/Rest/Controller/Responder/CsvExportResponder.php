<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Audit\Ui\Rest\Controller\Responder;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;

class CsvExportResponder
{
    public function __invoke($filename): Response
    {
        $response = new StreamedResponse();
        $response->setStatusCode(Response::HTTP_OK);
        $response->headers->set('Content-Type', 'text/csv; charset=utf-8');
        $response->headers->set('Content-Disposition', 'attachment; filename="'.basename($filename).'"');
        $response->setCallback(function () use ($filename): void {
            readfile($filename);
        });

        return $response;
    }
}
