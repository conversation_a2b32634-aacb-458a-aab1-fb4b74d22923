<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Audit\Ui\Console\Command;

use OpenLoyalty\Audit\Application\Exception\MinimumArchiveBeforeDateException;
use OpenLoyalty\Audit\Application\Exception\NotAllLogsWereArchived;
use OpenLoyalty\Audit\Application\Exception\RemovedMoreLogsThanExpected;
use OpenLoyalty\Audit\Application\Service\AuditLogArchiveParametersProvider;
use OpenLoyalty\Audit\Application\UseCase\ArchiveAuditLogsUseCase;
use OpenLoyalty\Audit\Infrastructure\Service\Export\Exception\FileAlreadyExistsException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class ArchiveAuditLogsCommand extends Command
{
    /**
     * @var ArchiveAuditLogsUseCase
     */
    private $useCase;

    /**
     * @var AuditLogArchiveParametersProvider
     */
    private $parametersProvider;

    /**
     * @var TranslatorInterface
     */
    private $translator;

    public function __construct(
        ArchiveAuditLogsUseCase $useCase,
        AuditLogArchiveParametersProvider $parametersProvider,
        TranslatorInterface $translator
    ) {
        parent::__construct();
        $this->useCase = $useCase;
        $this->parametersProvider = $parametersProvider;
        $this->translator = $translator;
    }

    protected function configure(): void
    {
        $this
            ->setName('oloy:audit:archive')
            ->setDescription('Archive old audit logs.')
            ->addArgument(
                'beforeDate',
                InputArgument::REQUIRED,
                'Date before which logs will be archived, in yyyy-mm-dd format'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $beforeDate = \DateTime::createFromFormat('Y-m-d', $input->getArgument('beforeDate'));

        if (!$beforeDate) {
            $output->writeln('<error>Invalid before date</error>');

            return self::FAILURE;
        }

        try {
            $totalLogsArchived = $this->useCase->execute(
                $this->parametersProvider->getFilename($beforeDate),
                $this->parametersProvider->getCriteria($beforeDate),
            );

            $output->writeln(sprintf('<info>%d logs archived</info>', $totalLogsArchived));
        } catch (MinimumArchiveBeforeDateException $e) {
            $output->writeln(sprintf(
                '<error>%s</error>',
                sprintf($this->translator->trans($e->getMessage()), $e->getMinimumKeepDays())
            ));
        } catch (NotAllLogsWereArchived|RemovedMoreLogsThanExpected $e) {
            $output->writeln(sprintf(
                '<error>%s</error>',
                sprintf($this->translator->trans($e->getMessage()), $e->getCount())
            ));
        } catch (FileAlreadyExistsException $e) {
            $output->writeln(sprintf(
                '<comment>%s</comment>',
                $this->translator->trans('audit.export.already_generated')
            ));
        }

        return self::SUCCESS;
    }
}
