<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Application\Exception;

/**
 * Class CustomerNotFoundException.
 */
class CustomerNotFoundException extends TransactionException
{
    /**
     * @var string|null
     */
    private $field;

    /**
     * CustomerNotFoundException constructor.
     */
    public function __construct(?string $field = null)
    {
        parent::__construct();
        $this->field = $field;
    }

    public function getField(): ?string
    {
        return $this->field;
    }
}
