<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Application\UseCase;

use Exception;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Import\Infrastructure\ImportResult;
use OpenLoyalty\Import\Infrastructure\Service\ImportFileManager;
use OpenLoyalty\Transaction\Infrastructure\Import\MatchCustomerXmlImporter;

/**
 * Class ImportMatchCustomerUseCase.
 */
class ImportMatchCustomerUseCase
{
    private const PREFIX_NAME = 'match_customer';

    /**
     * @var MatchCustomerXmlImporter
     */
    private $importer;

    /**
     * @var ImportFileManager
     */
    private $importFileManager;

    /**
     * ImportMatchCustomerUseCase constructor.
     */
    public function __construct(MatchCustomerXmlImporter $importer, ImportFileManager $importFileManager)
    {
        $this->importer = $importer;
        $this->importFileManager = $importFileManager;
    }

    /**
     * @throws Exception
     */
    public function execute(
        string $realPath,
        string $mimeType,
        string $originalName,
        string $extension,
        Store $store
    ): ImportResult {
        $importFile = $this->importFileManager->upload(
            $realPath,
            $mimeType,
            $originalName,
            $extension,
            self::PREFIX_NAME
        );

        return $this->importer->import($this->importFileManager->getAbsolutePath($importFile), $store);
    }
}
