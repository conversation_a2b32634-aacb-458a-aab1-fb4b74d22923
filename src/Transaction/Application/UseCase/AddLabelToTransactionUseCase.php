<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Application\UseCase;

use OpenLoyalty\Core\Domain\Id\TransactionId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Transaction\Application\Command\AddLabelsToTransaction;

class AddLabelToTransactionUseCase
{
    private CommandBusInterface $commandBus;

    public function __construct(
        CommandBusInterface $commandBus
    ) {
        $this->commandBus = $commandBus;
    }

    public function execute(TransactionId $transactionId, array $labels): void
    {
        $command = new AddLabelsToTransaction(
            $transactionId,
            $labels
        );

        $this->commandBus->dispatch($command);
    }
}
