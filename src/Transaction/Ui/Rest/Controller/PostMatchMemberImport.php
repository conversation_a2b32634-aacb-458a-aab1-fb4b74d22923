<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Ui\Rest\Controller;

use Exception;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Import\Infrastructure\Form\Type\ImportFileFormType;
use OpenLoyalty\Transaction\Application\UseCase\ImportMatchCustomerUseCase;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostMatchMemberImport extends AbstractFOSRestController
{
    private FormFactoryInterface $formFactory;
    private ImportMatchCustomerUseCase $useCase;
    private StoreContextProviderInterface $storeContextProvider;

    public function __construct(
        FormFactoryInterface $formFactory,
        ImportMatchCustomerUseCase $useCase,
        StoreContextProviderInterface $storeContextProvider
    ) {
        $this->formFactory = $formFactory;
        $this->useCase = $useCase;
        $this->storeContextProvider = $storeContextProvider;
    }

    /**
     * @Route(methods={"POST"}, name="oloy.transaction.match_member", path="/{storeCode}/transaction/assign/import")
     *
     * @Security("is_granted('CREATE_TRANSACTION')")
     *
     * @throws Exception
     */
    public function __invoke(Request $request): View
    {
        $form = $this->formFactory->createNamed('file', ImportFileFormType::class);
        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        /** @var UploadedFile $file */
        $file = $form->getData()->getFile();
        $result = $this->useCase->execute(
            $file->getRealPath(),
            $file->getClientMimeType(),
            $file->getClientOriginalName(),
            (string) $file->guessExtension(),
            $this->storeContextProvider->getStore()
        );

        return $this->view($result, Response::HTTP_OK);
    }
}
