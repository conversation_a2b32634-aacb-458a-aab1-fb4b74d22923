<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Ui\Rest\Controller\Label;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Transaction\Application\UseCase\RemoveLabelFromTransactionUseCase;
use OpenLoyalty\Transaction\Domain\Transaction;
use OpenLoyalty\Transaction\Infrastructure\Form\Type\RemoveLabelsFormType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Security("is_granted('ROLE_USER')")
 */
class Delete extends AbstractFOSRestController
{
    private FormFactoryInterface $formFactory;
    private RemoveLabelFromTransactionUseCase $useCase;

    public function __construct(FormFactoryInterface $formFactory, RemoveLabelFromTransactionUseCase $useCase)
    {
        $this->formFactory = $formFactory;
        $this->useCase = $useCase;
    }

    /**
     * @Route(methods={"DELETE"}, name="oloy.transaction.label.delete", path="/{storeCode}/transaction/{transaction}/labels", requirements={"transaction"="%routing.uuid%"})
     *
     * @Security("is_granted('DELETE_LABEL', transaction)")
     *
     * @Operation(
     *     tags={"Transactions"},
     *     summary="Delete transaction’s custom attribute",
     *     operationId="lebelDelete",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(ref="#/components/parameters/transaction"),
     *     @OA\RequestBody(
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 ref=@Model(type=RemoveLabelsFormType::class)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="204",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Returned when form contains errors",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Request $request, Transaction $transaction): View
    {
        $form = $this->formFactory->createNamed('', RemoveLabelsFormType::class, null, [
            'method' => 'DELETE',
        ]);
        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $data = $form->getData();

        $this->useCase->execute(
            $transaction->getTransactionId(),
            $data['labels']
        );

        return $this->view();
    }
}
