<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Transaction\Application\UseCase\UpdateTransactionUseCase;
use OpenLoyalty\Transaction\Domain\Exception\DocumentNumberShouldBeUniqueException;
use OpenLoyalty\Transaction\Domain\Exception\TransactionIsNotUpdatableException;
use OpenLoyalty\Transaction\Infrastructure\Form\Type\TransactionFormType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Security("is_granted('ROLE_USER')")
 */
class Put extends AbstractFOSRestController
{
    private FormFactoryInterface $formFactory;
    private UpdateTransactionUseCase $useCase;
    private TranslatorInterface $translator;
    private StoreContextProviderInterface $storeContextProvider;

    public function __construct(
        FormFactoryInterface $formFactory,
        UpdateTransactionUseCase $useCase,
        TranslatorInterface $translator,
        StoreContextProviderInterface $storeContextProvider
    ) {
        $this->formFactory = $formFactory;
        $this->useCase = $useCase;
        $this->translator = $translator;
        $this->storeContextProvider = $storeContextProvider;
    }

    /**
     * @Route(methods={"PUT"}, name="oloy.transaction.update", path="/{storeCode}/transaction/{transaction}", requirements={"transaction"="%routing.uuid%"})
     *
     * @Security("is_granted('UPDATE_TRANSACTION')")
     *
     * @Operation(
     *     tags={"Transactions"},
     *     summary="Update transaction’s details",
     *     operationId="transactionPut",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(ref="#/components/parameters/transaction"),
     *     @OA\RequestBody(
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(property="transaction", ref="#/components/schemas/PostTransaction")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="204",
     *         description="Returned when successful",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Returned when form contains errors",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Request $request, \OpenLoyalty\Transaction\Domain\Transaction $transaction): View
    {
        //todo workaround OLOY-9700
        ini_set('memory_limit', $this->getParameter('increased_memory_limit_for_transactions'));

        $form = $this->formFactory->createNamed('transaction', TransactionFormType::class, null, [
            'method' => 'PUT',
            'current_transaction_id' => $transaction->getTransactionId(),
        ]);
        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $data = $form->getData();

        try {
            $this->useCase->execute(
                $transaction->getTransactionId(),
                $data,
                $this->storeContextProvider->getStore()
            );

            return $this->view();
        } catch (DocumentNumberShouldBeUniqueException $e) {
            $form->get('header')->get('documentNumber')->addError(new FormError(
                $this->translator->trans('transaction.document_number_should_be_unique')
            ));
        } catch (TransactionIsNotUpdatableException $e) {
            $form->addError(new FormError(
                $this->translator->trans('transaction.transaction_is_not_updatable')
            ));
        }

        return $this->view($form, Response::HTTP_BAD_REQUEST);
    }
}
