<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Domain\DTO\Gate\Transaction;

final readonly class CustomerBasicData
{
    public function __construct(
        public ?string $email = null,
        public ?string $name = null,
        public ?string $nip = null,
        public ?string $phone = null,
        public ?string $loyaltyCardNumber = null,
        public ?CustomerAddress $address = null,
        public ?string $customerId = null
    ) {
    }
}
