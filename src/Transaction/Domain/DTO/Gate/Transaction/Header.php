<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Domain\DTO\Gate\Transaction;

use DateTimeImmutable;

final readonly class Header
{
    /**
     * @param Label[] $labels
     */
    public function __construct(
        public string $documentNumber,
        public string $documentType,
        public DateTimeImmutable $purchasedAt,
        public ?string $purchasePlace = null,
        public array $labels = [],
        public ?string $linkedDocumentNumber = null,
    ) {
    }
}
