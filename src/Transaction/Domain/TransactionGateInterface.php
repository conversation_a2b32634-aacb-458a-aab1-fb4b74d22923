<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Domain;

use DateTimeImmutable;
use Generator;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransactionId;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Transaction\Domain\DTO\Gate\Transaction\Transaction as TransactionDTO;

interface TransactionGateInterface
{
    public function getMinTransactionDateInPeriod(
        StoreId $storeId,
        CustomerId $customerId,
        DateTimeImmutable $startPeriodDate,
        DateTimeImmutable $endPeriodDate
    ): ?DateTimeImmutable;

    public function getByDocumentNumber(StoreId $storeId, string $documentNumber): ?Transaction;

    public function getById(StoreId $storeId, TransactionId $transactionId): ?Transaction;

    /* @phpstan-ignore-next-line  */
    public function findAllMatchedTransactionsByStoreIdAndPurchasedAt(StoreId $storeId, ?DateTimeImmutable $startDate, ?DateTimeImmutable $endDate): iterable;

    public function createTransaction(
        StoreId $storeId,
        TransactionDTO $transactionDTO,
        bool $isSync = false,
    ): ?Transaction;

    public function findByCriteriaReadContext(CriteriaCollectionInterface $criteriaCollection): Generator;

    /**
     * @return array<string>
     */
    public function findCustomersWithTransactionAmountEachDayInPeriod(
        StoreId $storeId,
        DateTimeImmutable $purchaseDateFrom,
        DateTimeImmutable $purchaseDateTo,
        float $fromAmount,
        float $toAmount
    ): array;

    /**
     * @return array<string>
     */
    public function findCustomersWithTransactionAmountInAtLeastOneDayExceeding(StoreId $storeId, DateTimeImmutable $purchaseDateFrom, DateTimeImmutable $purchaseDateTo, float $toAmount): array;

    public function findCustomersWithTransactionAmountInPeriod(StoreId $storeId, DateTimeImmutable $purchaseDateFrom, DateTimeImmutable $purchaseDateTo, float $fromAmount, float $toAmount): Generator;

    public function findCustomersWithTransactionAmountInPeriodExceeding(StoreId $storeId, DateTimeImmutable $purchaseDateFrom, DateTimeImmutable $purchaseDateTo, float $toAmount): Generator;

    public function findCustomersWithTransactionCountInPeriod(StoreId $storeId, DateTimeImmutable $purchaseDateFrom, DateTimeImmutable $purchaseDateTo, string $condition): Generator;
}
