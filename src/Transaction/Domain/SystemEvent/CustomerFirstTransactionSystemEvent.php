<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Transaction\Domain\SystemEvent;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\TransactionId;
use OpenLoyalty\Core\Domain\Message\EventInterface;

class CustomerFirstTransactionSystemEvent implements EventInterface
{
    /**
     * @var CustomerId
     */
    protected $customerId;

    /**
     * @var \OpenLoyalty\Core\Domain\Id\TransactionId
     */
    protected $transactionId;

    public function __construct(TransactionId $transactionId, CustomerId $customerId)
    {
        $this->transactionId = $transactionId;
        $this->customerId = $customerId;
    }

    public function getCustomerId()
    {
        return $this->customerId;
    }

    public function getTransactionId(): TransactionId
    {
        return $this->transactionId;
    }
}
