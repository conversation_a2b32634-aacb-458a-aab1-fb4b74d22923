<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Transaction\Domain\SystemEvent;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\ChannelId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransactionId;
use OpenLoyalty\Transaction\Domain\ValueObject\CustomerBasicData;
use OpenLoyalty\Transaction\Domain\ValueObject\TransactionHeader;

interface TransactionMatchingTriggeringEventInterface
{
    public function getTransactionId(): TransactionId;

    /**
     * @return \OpenLoyalty\Transaction\Domain\ValueObject\ItemData[]
     */
    public function getItems(): array;

    public function getChannelId(): ?ChannelId;

    public function getGrossValue(): float;

    public function getGrossValueWithoutDeliveryCosts(): float;

    public function getAmountExcludedForLevel(): float;

    public function getStoreId(): StoreId;

    public function getCustomerData(): CustomerBasicData;

    public function getHeader(): TransactionHeader;

    public function getAssignDate(): DateTimeImmutable;

    public function isSync(): bool;
}
