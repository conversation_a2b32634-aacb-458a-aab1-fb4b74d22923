<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Domain\ReadModel\Projector\Initializer;

use Generator;
use OpenLoyalty\Core\Domain\ReadModel\ProjectionInitializerInterface;
use OpenLoyalty\Core\Domain\ReadModel\ProjectorInterface;
use OpenLoyalty\Core\Domain\ReadModel\Request\InitializeRequestProjection;
use OpenLoyalty\Transaction\Domain\ReadModel\Projector\SingleTransactionRequestProjection;
use OpenLoyalty\Transaction\Domain\ReadModel\Projector\TransactionProjector;
use OpenLoyalty\Transaction\Domain\TransactionRepositoryInterface;

final class TransactionInitializer implements ProjectionInitializerInterface
{
    public function __construct(
        private readonly TransactionRepositoryInterface $transactionRepository
    ) {
    }

    public function initialize(InitializeRequestProjection $requestProjection): Generator
    {
        $transactionIds = $this->transactionRepository->getAllWithoutProjection($requestProjection->storeId);

        foreach ($transactionIds as $transactionId) {
            yield new SingleTransactionRequestProjection(
                $requestProjection->storeId,
                $transactionId,
            );
        }
    }

    public function supports(ProjectorInterface $projector): bool
    {
        return $projector instanceof TransactionProjector;
    }
}
