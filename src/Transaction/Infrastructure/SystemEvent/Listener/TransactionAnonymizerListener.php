<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Infrastructure\SystemEvent\Listener;

use OpenLoyalty\Core\Domain\Message\EventHandlerInterface;
use OpenLoyalty\Transaction\Domain\TransactionRepositoryInterface;
use OpenLoyalty\User\Domain\SystemEvent\CustomerWasAnonymizedSystemEvent;

class TransactionAnonymizerListener implements EventHandlerInterface
{
    public function __construct(private TransactionRepositoryInterface $transactionRepository)
    {
    }

    public function __invoke(CustomerWasAnonymizedSystemEvent $event): void
    {
        $this->transactionRepository->anonymize($event->getStoreId(), $event->getCustomerId());
    }
}
