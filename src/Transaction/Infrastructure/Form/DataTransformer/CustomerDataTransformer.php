<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Infrastructure\Form\DataTransformer;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Transaction\Domain\ValueObject\CustomerBasicData;
use Ramsey\Uuid\Uuid;
use Symfony\Component\Form\DataTransformerInterface;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
class CustomerDataTransformer implements DataTransformerInterface
{
    public function transform($value): ?array
    {
        return $value;
    }

    public function reverseTransform($value): ?CustomerBasicData
    {
        return new CustomerBasicData(
            $value['email'] ? strtolower($value['email']) : null,
            $value['name'] ?? null,
            $value['nip'] ?? null,
            $value['phone'] ? strtolower($value['phone']) : null,
            $value['loyaltyCardNumber'] ?? null,
            $value['address'] ?? null,
            null !== $value['customerId'] && Uuid::isValid($value['customerId']) ? new CustomerId($value['customerId']) : null
        );
    }
}
