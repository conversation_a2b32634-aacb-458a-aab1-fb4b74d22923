<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Transaction\Infrastructure\Persistence\Doctrine\Type;

use Assert\AssertionFailedException;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use OpenLoyalty\Core\Domain\Id\TransactionId;
use Ramsey\Uuid\Doctrine\UuidType;
use Ramsey\Uuid\UuidInterface;

final class TransactionIdDoctrineType extends UuidType
{
    public const NAME = 'transaction_id';

    /**
     * @return UuidInterface|TransactionId|null
     * @throws AssertionFailedException
     */
    public function convertToPHPValue($value, AbstractPlatform $platform): UuidInterface|TransactionId|null
    {
        if (empty($value)) {
            return null;
        }

        if ($value instanceof TransactionId) {
            return $value;
        }

        return new TransactionId($value);
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if (null === $value) {
            return null;
        }

        if ($value instanceof TransactionId) {
            return (string) $value;
        }

        return null;
    }

    public function getName(): string
    {
        return self::NAME;
    }

    public function requiresSQLCommentHint(AbstractPlatform $platform): bool
    {
        return true;
    }
}
