services:
    _defaults:
        autoconfigure: true
        autowire: true
        public: false

    OpenLoyalty\Transaction\Ui\Console\Command\Export\DataMapper\TransactionDataMapper: ~

    OpenLoyalty\Transaction\Ui\Console\Command\Export\TransactionExportCommand:
        arguments:
            $dataProvider: '@OpenLoyalty\Transaction\Application\Export\TransactionDataProvider'

    OpenLoyalty\Transaction\Application\Export\TransactionDataProvider:
        arguments:
           $mapper: '@OpenLoyalty\Transaction\Ui\Console\Command\Export\DataMapper\TransactionDataMapper'
