<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Channel\Domain\SystemEvent;

use OpenLoyalty\Core\Domain\Id\ChannelId;

class ChannelUpdatedSystemEvent extends ChannelSystemEvent
{
    protected string $channelName;

    public function __construct(ChannelId $channelId, string $name)
    {
        parent::__construct($channelId);
        $this->channelName = $name;
    }

    public function getChannelName(): string
    {
        return $this->channelName;
    }
}
