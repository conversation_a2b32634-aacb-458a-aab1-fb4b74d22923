doctrine:
    dbal:
        types:
          channel_id: OpenLoyalty\Channel\Infrastructure\Persistence\Doctrine\Type\ChannelIdDoctrineType
    orm:
        entity_managers:
            readmodel:
                mappings:
                    OpenLoyaltyChannel:
                        type: yml
                        dir: '%kernel.project_dir%/src/Channel/Infrastructure/Persistence/Doctrine/ORM'
                        is_bundle: false
                        prefix: OpenLoyalty\Channel\Domain

jms_serializer:
    metadata:
      directories:
        Channel:
          namespace_prefix: "OpenLoyalty\\Channel\\Domain"
          path: "@OpenLoyaltyChannelBundle/Resources/config/serializer"
