<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Infrastructure\DataAnalytics\Listener;

use Assert\AssertionFailedException;
use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Message\EventHandlerInterface;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\Exception\VersionValueIsInvalid;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventBusInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;
use OpenLoyalty\DataAnalytics\Domain\Unit\Event\UnitsWasUnlocked;
use OpenLoyalty\Points\Domain\AccountFacadeInterface;
use OpenLoyalty\Points\Domain\SystemEvent\TransferHasBeenUnlocked;
use OpenLoyalty\Points\Domain\Transfer;

final class TransferHasBeenUnlockedListener implements EventHandlerInterface
{
    public function __construct(
        private readonly UuidGeneratorInterface $uuidGenerator,
        private readonly AnalyticsEventBusInterface $analyticsEventBus,
        private readonly AccountFacadeInterface $accountFacade
    ) {
    }

    /**
     * @throws AssertionFailedException
     * @throws VersionValueIsInvalid
     */
    public function __invoke(TransferHasBeenUnlocked $event): void
    {
        $transfer = $event->getTransfer();
        $wallet = $this->accountFacade->getWalletById($transfer->getOwner());

        if (null === $wallet) {
            return;
        }

        if (Transfer::ADD_TYPE === $transfer->getType()) {
            $this->analyticsEventBus->dispatch(
                new UnitsWasUnlocked(
                    new AnalyticsEventId($this->uuidGenerator->generate()),
                    new Version(1),
                    $transfer->getStoreId(),
                    $transfer->getTransferId(),
                    $wallet->owner,
                    $wallet->code,
                    $transfer->getValue(),
                    DateTimeImmutable::createFromInterface($transfer->getUnlockedAt()),
                )
            );
        }
    }
}
