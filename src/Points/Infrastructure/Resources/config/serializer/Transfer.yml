OpenLoyalty\Points\Domain\Transfer:
    exclusion_policy: ALL
    discriminator:
        disabled: true
    properties:
        transferId:
            expose: true
            inline: true
        type:
            expose: true
        owner:
            expose: true
            inline: true
        lockedUntil:
            expose: true
            skip_when_empty: false
        expiresAt:
            expose: true
        storeId:
            expose: true
            inline: true
        senderId:
            expose: true
            inline: true
        receiverId:
            expose: true
            inline: true
        comment:
            expose: true
        value:
            expose: true
            type: float<6>
        createdAt:
            expose: true
        cancelled:
            expose: true
        pending:
            expose: true
        actionCause:
            expose: true
        unlockedAt:
            expose: true
        externalTransferId:
            expose: true
    virtual_properties:
        relatedTransferId:
            exp: "object.getRelatedTransferId() ? object.getRelatedTransferId().__toString() : null"
        expiredAt:
            exp: "object.getType() === 'expired' ? object.getCreatedAt() : null"
        walletId:
            exp: "object.getOwner().__toString()"
