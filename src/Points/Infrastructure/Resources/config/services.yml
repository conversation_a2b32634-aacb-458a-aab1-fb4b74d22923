imports:
    - { resource: services/*.yml }

services:

    _defaults:
        autoconfigure: true
        autowire: true
        public: false

    _instanceof:
        OpenLoyalty\Core\Domain\Message\EventHandlerInterface:
            tags: [ { name: messenger.message_handler, bus: event.bus } ]
        OpenLoyalty\Core\Domain\DataRetention\DataRetentionHandlerInterface:
            tags: [ 'oloy.core.dataretention.handler' ]

    OpenLoyalty\Points\Infrastructure\DataFixtures\ORM\:
        resource: '../../DataFixtures/ORM/*'

    OpenLoyalty\Points\Infrastructure\Form\Type\TransferPointsFormType:
        tags:
            - { name: 'form.type' }

    OpenLoyalty\Points\Infrastructure\Form\Type\TransferPointsByCustomerFormType:
        tags:
            - { name: 'form.type' }

    OpenLoyalty\Points\Infrastructure\Form\Type\AddPointsFormType:
        tags:
            - { name: 'form.type' }

    OpenLoyalty\Points\Infrastructure\Form\Type\SpendPointsFormType:
        parent: 'OpenLoyalty\Points\Infrastructure\Form\Type\AddPointsFormType'
        public: true
        autowire: false             # because of parent
        autoconfigure: false        # because of parent
        tags:
            - { name: 'form.type' }

    OpenLoyalty\Points\Infrastructure\ParamConverter\TransferParamConverter:
        tags:
            - { name: 'request.param_converter', priority: '2', converter: 'points_transfer_converter' }

    OpenLoyalty\Points\Infrastructure\Import\PointsTransferNodeStreamer: ~

    OpenLoyalty\Points\Application\Import\PointsTransferXmlImportConverter: ~

    OpenLoyalty\Points\Application\Import\PointsTransferImportProcessor: ~

    OpenLoyalty\Points\Infrastructure\Import\PointsTransferXmlImporter:
        calls:
            - [setProcessor, ['@OpenLoyalty\Points\Application\Import\PointsTransferImportProcessor']]
            - [setXmlStreamer, ['@OpenLoyalty\Points\Infrastructure\Import\PointsTransferNodeStreamer']]
            - [setConverter, ['@OpenLoyalty\Points\Application\Import\PointsTransferXmlImportConverter']]

    OpenLoyalty\Points\Application\Import\CustomerProviderInterface: '@OpenLoyalty\Points\Infrastructure\Import\CustomerProvider'

    OpenLoyalty\Points\Infrastructure\Import\CustomerProvider: ~

    OpenLoyalty\Points\Infrastructure\Persistence\Doctrine\Repository\DoctrineTransferRepository: ~

    OpenLoyalty\Points\Infrastructure\Persistence\Doctrine\Repository\DoctrineExpiringTransferRepository: ~

    OpenLoyalty\Points\Infrastructure\Validator\Constraints\CustomerValidator: ~
    OpenLoyalty\Points\Infrastructure\Validator\Constraints\ExternalTransferIdValidator: ~

    OpenLoyalty\Points\Infrastructure\AccountFacade: ~
    OpenLoyalty\Points\Infrastructure\MemberFacade: ~

    OpenLoyalty\Points\Infrastructure\PointsGate: ~

    OpenLoyalty\Points\Infrastructure\Event\Listener\TransferSerializationSubscriber:
        tags:
            - { name: 'jms_serializer.event_subscriber' }

    OpenLoyalty\Points\Application\DataMapper\:
        resource: '../../../Application/DataMapper'

    OpenLoyalty\Points\Infrastructure\DataAnalytics\Listener\:
        resource: '../../../Infrastructure/DataAnalytics/Listener'

    OpenLoyalty\Points\Application\Listener\PublishTransferWasAdded: ~
    OpenLoyalty\Points\Application\Listener\PublishTransferWasCancelled: ~

    OpenLoyalty\Points\Application\BulkAction\CancelTransferBulkActionManager:
        tags: ['oloy.bulk_actions.manager']
        arguments:
            $bulkActionIdsNumberInMessage: '%bulk_action_cancel_transfer_ids_number_in_message%'
            $numberOfItemsToFlush: '%bulk_action_cancel_transfer_number_of_items_to_flush%'

    OpenLoyalty\Points\Application\BulkAction\ActivateTransferBulkActionManager:
        tags: [ 'oloy.bulk_actions.manager' ]
        arguments:
            $bulkActionIdsNumberInMessage: '%bulk_action_activate_transfer_ids_number_in_message%'
            $numberOfItemsToFlush: '%bulk_action_activate_transfer_number_of_items_to_flush%'

    OpenLoyalty\Points\Application\BulkAction\ExpireTransferBulkActionManager:
        tags: [ 'oloy.bulk_actions.manager' ]
        arguments:
            $bulkActionIdsNumberInMessage: '%bulk_action_expire_transfer_ids_number_in_message%'
            $numberOfItemsToFlush: '%bulk_action_expire_transfer_number_of_items_to_flush%'

    OpenLoyalty\Points\Application\BulkAction\ArrayToCriteriaCollectionMapper: ~
    OpenLoyalty\Points\Application\BulkAction\IdsToProcessProvider: ~

    OpenLoyalty\Points\Infrastructure\DataRetention\Handler\TransferExpiringHandler:
        arguments:
            $maxDeleteCount: '%data_retention_transfer_expiring_max_delete_count%'
            $batchSize: '%data_retention_transfer_expiring_batch_size%'
