<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Infrastructure\DataRetention\Doctrine;

use OpenLoyalty\Core\Infrastructure\DataRetention\DataRetentionProcedureTemplate;

final class TransferExpiringProcedure
{
    public static function generateProcedureSql(): string
    {
        $sql = <<<SQL
                WITH te AS (
                    SELECT te.id
                    FROM transfer_expiring te
                    WHERE te.available_value = 0
                    LIMIT batch_size
                )
                DELETE FROM transfer_expiring
                WHERE id IN (SELECT id FROM te);
        SQL;

        return DataRetentionProcedureTemplate::generateProcedureSql('delete_transfer_expiring', $sql);
    }
}
