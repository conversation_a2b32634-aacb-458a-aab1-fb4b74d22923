<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Infrastructure\Form\Type;

use OpenLoyalty\User\Infrastructure\Validator\Constraint\Customer;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Uuid;

class TransferPointsFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('sender', TextType::class, [
            'constraints' => [new NotBlank(), new Uuid(), new Customer()],
            'documentation' => [
                'example' => '7239e6f7-e52e-466b-8c24-699709b64076',
            ],
        ]);
    }

    public function getParent(): string
    {
        return TransferPointsByCustomerFormType::class;
    }
}
