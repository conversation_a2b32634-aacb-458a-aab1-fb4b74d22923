<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Infrastructure\Persistence\Doctrine\Repository;

use DateTimeImmutable;
use Generator;
use OpenLoyalty\Account\Domain\Wallet;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransactionId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\Search\Context\ContextInterface;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\Core\Infrastructure\Search\Doctrine\Context;
use OpenLoyalty\Points\Domain\Transfer;
use OpenLoyalty\Points\Domain\TransferRepositoryInterface;
use OpenLoyalty\Points\Domain\TransferRepositoryReadContextInterface;
use OpenLoyalty\User\Domain\Customer;

final class DoctrineTransferRepository extends DoctrineRepository implements TransferRepositoryInterface, TransferRepositoryReadContextInterface
{
    protected function getClass(): string
    {
        return Transfer::class;
    }

    public function byId(TransferId $transferId): ?Transfer
    {
        return $this->find($transferId);
    }

    public function getById(StoreId $storeId, TransferId $transferId): ?Transfer
    {
        /** @var ?Transfer $transfer */
        $transfer = $this->findOneBy(['storeId' => $storeId, 'transferId' => $transferId]);

        return $transfer;
    }

    public function save(Transfer $transfer): void
    {
        $this->entityManager->persist($transfer);
        $this->entityManager->flush();
    }

    public function remove(Transfer $transfer): void
    {
        $this->entityManager->remove($transfer);
        $this->entityManager->flush();
    }

    public function findAllToUnlock(StoreId $storeId, DateTimeImmutable $before): Generator
    {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->select('t.transferId')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.pending = true')
            ->andWhere('t.cancelled = false')
            ->andWhere('t.lockedUntil <= :before')
            ->andWhere('t.failedUnlockAt IS NULL')
            ->setParameter('storeId', $storeId)
            ->setParameter('before', $before);

        return $this->toIterableWithClearEntityManagerCache($queryBuilder->getQuery(), ['transferId']);
    }

    public function getTotalByType(
        StoreId $storeId,
        array $transferTypes,
        bool $pending = false,
        ?AccountId $owner = null,
        ?DateTimeImmutable $from = null,
        ?DateTimeImmutable $to = null,
        ?WalletTypeId $walletTypeId = null
    ): float {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->leftJoin(Wallet::class, 'w', 'WITH', 'w.accountId = t.owner')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.pending = :pending')
            ->andWhere('t.cancelled = false')
            ->andWhere('t.type IN (:transferTypes)')
            ->setParameter('storeId', $storeId)
            ->setParameter('pending', $pending)
            ->setParameter('transferTypes', $transferTypes)
            ->select('SUM(t.value)');

        if (null !== $to) {
            $queryBuilder
                ->andWhere('t.createdAt <= :to')
                ->setParameter('to', $to);
        }

        if (null !== $from) {
            $queryBuilder
                ->andWhere('t.createdAt >= :from')
                ->setParameter('from', $from);
        }

        if (null !== $owner) {
            $queryBuilder
                ->andWhere('t.owner = :owner')
                ->setParameter('owner', $owner);
        }

        if (null !== $walletTypeId) {
            $queryBuilder
                ->andWhere('w.type = :walletTypeId')
                ->setParameter('walletTypeId', $walletTypeId);
        }

        return abs((float) $queryBuilder->getQuery()->getSingleScalarResult());
    }

    public function getActiveTotal(StoreId $storeId, ?AccountId $owner = null, ?WalletTypeId $walletTypeId = null): float
    {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->leftJoin(Wallet::class, 'w', 'WITH', 'w.accountId = t.owner')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.pending = false')
            ->andWhere('t.cancelled = false')
            ->setParameter('storeId', $storeId)
            ->select('SUM(t.value)');

        if (null !== $owner) {
            $queryBuilder
                ->andWhere('t.owner = :owner')
                ->setParameter('owner', $owner);
        }

        if (null !== $walletTypeId) {
            $queryBuilder
                ->andWhere('w.type = :walletTypeId')
                ->setParameter('walletTypeId', $walletTypeId);
        }

        return (float) $queryBuilder->getQuery()->getSingleScalarResult();
    }

    public function getPointsHistogramByType(
        StoreId $storeId,
        array $transferTypes,
        ?DateTimeImmutable $from,
        ?DateTimeImmutable $to,
        string $interval,
        ?WalletTypeId $walletTypeId = null
    ): array {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->leftJoin(Wallet::class, 'w', 'WITH', 'w.accountId = t.owner')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.pending = false')
            ->andWhere('t.cancelled = false')
            ->andWhere('t.type IN (:transferTypes)')
            ->setParameter('storeId', $storeId)
            ->setParameter('transferTypes', $transferTypes)
            ->select('SUM(t.value) as sum, COUNT(t.transferId) as count');

        if (null !== $to) {
            $queryBuilder
                ->andWhere('t.createdAt <= :to')
                ->setParameter('to', $to);
        }

        if (null !== $from) {
            $queryBuilder
                ->andWhere('t.createdAt >= :from')
                ->setParameter('from', $from);
        }

        if (null !== $walletTypeId) {
            $queryBuilder
                ->andWhere('w.type = :walletTypeId')
                ->setParameter('walletTypeId', $walletTypeId);
        }

        $this->groupByInterval($queryBuilder, 't.createdAt', $interval);

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function getPendingPointsHistogram(
        StoreId $storeId,
        array $transferTypes,
        ?DateTimeImmutable $from,
        ?DateTimeImmutable $to,
        string $interval,
        ?WalletTypeId $walletTypeId = null
    ): array {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->leftJoin(Wallet::class, 'w', 'WITH', 'w.accountId = t.owner')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.pending = true')
            ->andWhere('t.cancelled = false')
            ->andWhere('t.type IN (:transferTypes)')
            ->setParameter('storeId', $storeId)
            ->setParameter('transferTypes', $transferTypes)
            ->setParameter('from', $from)
            ->setParameter('to', $to)
            ->select('SUM(t.value) as sum, COUNT(t.transferId) as count');

        if (null !== $to) {
            $queryBuilder
                ->andWhere('t.lockedUntil <= :to')
                ->setParameter('to', $to);
        }

        if (null !== $from) {
            $queryBuilder
                ->andWhere('t.lockedUntil >= :from')
                ->setParameter('from', $from);
        }

        if (null !== $walletTypeId) {
            $queryBuilder
                ->andWhere('w.type = :walletTypeId')
                ->setParameter('walletTypeId', $walletTypeId);
        }

        $this->groupByInterval($queryBuilder, 't.lockedUntil', $interval);

        return $queryBuilder->getQuery()->getArrayResult();
    }

    protected function getBaseContext(array $params): ContextInterface
    {
        $context = parent::getBaseContext($params);
        $context->getQueryBuilder()
            ->leftJoin(Wallet::class, 'w', 'WITH', 'w.accountId = '.Context::DEFAULT_ALIAS.'.owner')
            ->leftJoin('w.type', 'wt')
            ->leftJoin(Customer::class, 'c', 'WITH', 'w.owner = c.id')
            ->leftJoin(Campaign::class, 'camp', 'WITH', 'camp.campaignId = '.Context::DEFAULT_ALIAS.'.actionCause.campaignId');

        return $context;
    }

    public function getTotalByTransaction(
        StoreId $storeId,
        TransactionId $transactionId,
        array $transferTypes
    ): float {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.cancelled = false')
            ->andWhere('t.actionCause.transactionId = :transactionId')
            ->andWhere('t.type IN (:transferTypes)')
            ->setParameter('storeId', $storeId)
            ->setParameter('transactionId', $transactionId)
            ->setParameter('transferTypes', $transferTypes)
            ->select('SUM(t.value)');

        return abs((float) $queryBuilder->getQuery()->getSingleScalarResult());
    }

    public function getIssuedUnitsByStoreId(StoreId $storeId): Generator
    {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->leftJoin(Wallet::class, 'w', 'WITH', 'w.accountId = t.owner')
            ->leftJoin('w.type', 'wt')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.type = :transferType')
            ->setParameter('storeId', $storeId)
            ->setParameter('transferType', Transfer::ADD_TYPE)
            ->select(
                't.storeId',
                't.transferId',
                't.value',
                't.createdAt as issuedDate',
                'w.owner',
                'wt.code',
                't.cancelledAt'
            );

        return $this->toIterableWithClearEntityManagerCache($queryBuilder->getQuery());
    }

    public function getSpentUnitsByStoreId(StoreId $storeId): Generator
    {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->leftJoin(Wallet::class, 'w', 'WITH', 'w.accountId = t.owner')
            ->leftJoin('w.type', 'wt')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.type = :transferType')
            ->setParameter('storeId', $storeId)
            ->setParameter('transferType', Transfer::SPEND_TYPE)
            ->select('t.storeId, t.transferId, t.value, t.createdAt as spentDate, w.owner, wt.code');

        return $this->toIterableWithClearEntityManagerCache($queryBuilder->getQuery());
    }

    public function getExpiredUnitsByStoreId(StoreId $storeId): Generator
    {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->leftJoin(Wallet::class, 'w', 'WITH', 'w.accountId = t.owner')
            ->leftJoin('w.type', 'wt')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.type = :transferType')
            ->setParameter('storeId', $storeId)
            ->setParameter('transferType', Transfer::EXPIRE_TYPE)
            ->select('t.storeId, t.transferId, t.value, t.registeredAt as expiredDate, t.createdAt as createdDate, w.owner, wt.code');

        return $this->toIterableWithClearEntityManagerCache($queryBuilder->getQuery());
    }

    public function getLockedUnitsByStoreId(StoreId $storeId): Generator
    {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->leftJoin(Wallet::class, 'w', 'WITH', 'w.accountId = t.owner')
            ->leftJoin('w.type', 'wt')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.type = :transferType')
            ->andWhere('t.lockedUntil IS NOT NULL')
            ->setParameter('storeId', $storeId)
            ->setParameter('transferType', Transfer::ADD_TYPE)
            ->select(
                't.storeId',
                't.transferId',
                't.value',
                't.createdAt',
                'w.owner',
                'wt.code',
                't.cancelledAt'
            );

        return $this->toIterableWithClearEntityManagerCache($queryBuilder->getQuery());
    }

    public function getUnlockedUnitsByStoreId(StoreId $storeId): Generator
    {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->leftJoin(Wallet::class, 'w', 'WITH', 'w.accountId = t.owner')
            ->leftJoin('w.type', 'wt')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.type = :transferType')
            ->andWhere('t.pending = false')
            ->andWhere('t.unlockedAt IS NOT NULL')
            ->setParameter('storeId', $storeId)
            ->setParameter('transferType', Transfer::ADD_TYPE)
            ->select(
                't.storeId',
                't.transferId',
                't.value',
                't.unlockedAt',
                'w.owner',
                'wt.code',
                't.cancelledAt'
            );

        return $this->toIterableWithClearEntityManagerCache($queryBuilder->getQuery());
    }

    public function getBlockedUnitsByStoreId(StoreId $storeId): Generator
    {
        $queryBuilder = $this->repository->createQueryBuilder('t')
            ->leftJoin(Wallet::class, 'w', 'WITH', 'w.accountId = t.owner')
            ->leftJoin('w.type', 'wt')
            ->andWhere('t.storeId = :storeId')
            ->andWhere('t.type = :transferType')
            ->setParameter('storeId', $storeId)
            ->setParameter('transferType', Transfer::BLOCK_TYPE)
            ->select(
                't.storeId',
                't.transferId',
                't.value',
                't.createdAt as blockedAt',
                'w.owner',
                'wt.code',
                't.cancelledAt'
            );

        return $this->toIterableWithClearEntityManagerCache($queryBuilder->getQuery());
    }
}
