<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Application\Export;

use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use OpenLoyalty\Account\Domain\Wallet;
use OpenLoyalty\Core\Application\Export\DateRangeCriteriaBuilder;
use OpenLoyalty\Core\Application\Export\ExportDataMapperInterface;
use OpenLoyalty\Core\Application\Export\IterableDataProviderInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Points\Domain\TransferRepositoryInterface;
use OpenLoyalty\User\Domain\Customer;

final readonly class PointsTransferDataProvider implements IterableDataProviderInterface
{
    public function __construct(
        private DateRangeCriteriaBuilder $criteriaBuilder,
        private SearchableResponderInterface $searchableResponder,
        private ExportDataMapperInterface $mapper,
        private TransferRepositoryInterface $transferRepository,
        private EntityManagerInterface $entityManager
    ) {
    }

    public function getItemsCountByDate(?DateTimeImmutable $date): int
    {
        return $this->searchableResponder->countByCriteria(
            $this->transferRepository,
            $this->criteriaBuilder->getDateRangeCriteria($date)
        );
    }

    public function getItemsByDate(?DateTimeImmutable $date): Generator
    {
        foreach (
            $this->searchableResponder->fromCriteriaIterable(
                $this->transferRepository,
                $this->criteriaBuilder->getDateRangeCriteria($date)
            ) as $item
        ) {
            yield $this->mapper->map($item);

            $this->entityManager->detach($item);
            $this->entityManager->clear(Customer::class); // @phpstan-ignore-line
            $this->entityManager->clear(Wallet::class); // @phpstan-ignore-line
        }
    }
}
