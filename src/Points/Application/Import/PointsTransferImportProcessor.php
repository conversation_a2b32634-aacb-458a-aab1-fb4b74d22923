<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Application\Import;

use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Import\Domain\ImporterProcessor;
use OpenLoyalty\Import\Domain\ProcessImportResult;
use OpenLoyalty\Points\Application\Command\AddPoints;
use OpenLoyalty\Points\Application\Command\SpendPoints;

class PointsTransferImportProcessor implements ImporterProcessor
{
    protected CommandBusInterface $commandBus;

    public function __construct(CommandBusInterface $commandBus)
    {
        $this->commandBus = $commandBus;
    }

    public function processItem($entity, Store $store): ProcessImportResult
    {
        if (!$entity instanceof AddPoints && !$entity instanceof SpendPoints) {
            throw new \InvalidArgumentException('Entity object is not AddPoints|SpendPoints');
        }

        $this->commandBus->dispatch($entity);

        return new ProcessImportResult($entity->getTransferId());
    }
}
