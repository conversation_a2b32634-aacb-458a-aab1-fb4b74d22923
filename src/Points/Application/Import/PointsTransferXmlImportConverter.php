<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Application\Import;

use Assert\AssertionFailedException;
use DateTimeImmutable;
use InvalidArgumentException;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Import\Domain\Exception\ImportConvertException;
use OpenLoyalty\Import\Domain\Validator\XMLNodesValidatorInterface;
use OpenLoyalty\Import\Domain\Validator\XMLNodeValidatorInterface;
use OpenLoyalty\Import\Domain\XMLImportConverter;
use OpenLoyalty\Points\Application\Command\AddPoints;
use OpenLoyalty\Points\Application\Command\SpendPoints;
use OpenLoyalty\Points\Domain\Transfer;
use OpenLoyalty\User\Application\Exception\CustomerNotFoundException;
use SimpleXMLElement;
use Symfony\Contracts\Translation\TranslatorInterface;

class PointsTransferXmlImportConverter implements XMLImportConverter
{
    public function __construct(
        private readonly XMLNodesValidatorInterface $nodesValidator,
        private readonly UuidGeneratorInterface $uuidGenerator,
        private readonly CustomerProviderInterface $accountProvider,
        private readonly TranslatorInterface $translator
    ) {
    }

    /**
     * @throws CustomerNotFoundException
     */
    protected function findCustomer(Store $store, string $email, string $phone, string $loyaltyNumber): CustomerId
    {
        $customerId = $this->accountProvider->provideOne(
            $store->getStoreId(),
            strtolower($email),
            strtolower($phone),
            $loyaltyNumber
        );

        if (!$customerId) {
            throw new CustomerNotFoundException('Customer does not exist for given data');
        }

        return $customerId;
    }

    /**
     * @throws ImportConvertException
     * @throws AssertionFailedException
     * @throws CustomerNotFoundException
     */
    public function convert(SimpleXMLElement $element, Store $store)
    {
        $this->nodesValidator->checkValidNodes(
            $element,
            [
                'points' => ['format' => XMLNodeValidatorInterface::DECIMAL_FORMAT, 'required' => true],
                'type' => ['required' => true],
                'expiresInDays' => ['format' => XMLNodeValidatorInterface::DECIMAL_FORMAT, 'required' => true],
            ]
        );

        if (!$element->{'customerId'}
            && !$element->{'customerEmail'}
            && !$element->{'customerPhoneNumber'}
            && !$element->{'customerLoyaltyCardNumber'}
        ) {
            throw new ImportConvertException($this->translator->trans('At least one node should be defined (customerId, customerEmail, customerLoyaltyCardNumber, customerPhoneNumber)'));
        }

        $transferType = (string) $element->{'type'};
        $customerId = !empty($element->{'customerId'}) ? new CustomerId((string) $element->{'customerId'}) : null;

        if (null === $customerId) {
            $customerId = $this->findCustomer(
                $store,
                (string) $element->{'customerEmail'},
                (string) $element->{'customerPhoneNumber'},
                (string) $element->{'customerLoyaltyCardNumber'}
            );
        }
        $transferId = new TransferId($this->uuidGenerator->generate());

        switch ($transferType) {
            case Transfer::ADD_TYPE:
                return new AddPoints(
                    $transferId,
                    $store->getStoreId(),
                    $customerId,
                    (float) $element->{'points'},
                    new DateTimeImmutable(),
                    null,
                    null,
                    $element->{'expiresInDays'} ? (int) $element->{'expiresInDays'} : null,
                );
            case Transfer::SPEND_TYPE:
                return new SpendPoints(
                    $transferId,
                    $store->getStoreId(),
                    $customerId,
                    (float) $element->{'points'},
                    new DateTimeImmutable(),
                );
        }

        throw new InvalidArgumentException(sprintf('type = %s', $transferType));
    }

    public function getIdentifier(SimpleXMLElement $element, ?Store $store): string
    {
        return sprintf(
            '%s/(%s %s)',
            $element->{'customerId'} ?? $element->{'customerEmail'} ?? $element->{'customerPhoneNumber'} ?? $element->{'customerLoyaltyCardNumber'},
            $element->{'type'},
            $element->{'points'}
        );
    }
}
