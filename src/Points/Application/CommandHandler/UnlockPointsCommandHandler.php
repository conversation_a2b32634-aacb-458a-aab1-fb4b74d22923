<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Application\CommandHandler;

use OpenLoyalty\Core\Application\TimezoneResolverInterface;
use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Points\Application\Command\UnlockTransfer;
use OpenLoyalty\Points\Domain\TransferExecutorInterface;

class UnlockPointsCommandHandler implements CommandHandlerInterface
{
    public function __construct(
        private readonly TransferExecutorInterface $transferExecutor,
        private readonly TimezoneResolverInterface $timezoneResolver
    ) {
    }

    public function __invoke(UnlockTransfer $command): void
    {
        $this->timezoneResolver->setDefaultTimezoneByStoreId($command->getStoreId());
        $this->transferExecutor->unlock(
            $command->getTransferId(),
            $command->getStoreId(),
            $command->getCreatedAt(),
            $command->getCreatedBy(),
            $command->isSilentError()
        );
    }
}
