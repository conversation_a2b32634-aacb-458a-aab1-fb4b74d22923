<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Application\CommandHandler;

use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Points\Application\Command\BlockPoints;
use OpenLoyalty\Points\Domain\AccountFacadeInterface;
use OpenLoyalty\Points\Domain\TransferExecutorInterface;
use OpenLoyalty\Points\Domain\TransferFactoryInterface;

class BlockPointsCommandHandler implements CommandHandlerInterface
{
    public function __construct(
        private TransferExecutorInterface $transferExecutor,
        private TransferFactoryInterface $transferFactory,
        private AccountFacadeInterface $accountFacade
    ) {
    }

    public function __invoke(BlockPoints $command): void
    {
        $wallet = $this->accountFacade->getWallet($command->getStoreId(), $command->getCustomerId(), $command->getWalletCode());

        $transfer = $this->transferFactory->block(
            $command->getStoreId(),
            $wallet->getWalletId(),
            $command->getValue(),
            $command->getCreatedAt(),
            $command->getTransferId(),
            $command->getExternalTransferId()
        )
            ->comment($command->getComment())
            ->setCreatedByUsername($command->getCreatedBy());

        $this->transferExecutor->create($transfer);
    }
}
