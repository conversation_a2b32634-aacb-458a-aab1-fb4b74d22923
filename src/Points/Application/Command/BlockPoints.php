<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Application\Command;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;

final readonly class BlockPoints extends AbstractUnitCommand
{
    public function __construct(
        private TransferId $transferId,
        private StoreId $storeId,
        private CustomerId $customerId,
        private float $value,
        private DateTimeImmutable $createdAt,
        private ?string $comment = null,
        private ?string $createdBy = null,
        private ?string $walletCode = null,
        private ?string $externalTransferId = null
    ) {
        parent::__construct($this->transferId, $this->storeId);
    }

    public function getCustomerId(): CustomerId
    {
        return $this->customerId;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function getCreatedBy(): ?string
    {
        return $this->createdBy;
    }

    public function getWalletCode(): ?string
    {
        return $this->walletCode;
    }

    public function getExternalTransferId(): ?string
    {
        return $this->externalTransferId;
    }
}
