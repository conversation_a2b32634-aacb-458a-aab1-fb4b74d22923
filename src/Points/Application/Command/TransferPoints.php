<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Application\Command;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;

final readonly class TransferPoints extends AbstractUnitCommand
{
    public function __construct(
        private TransferId $senderTransferId,
        private StoreId $storeId,
        private CustomerId $sender,
        private CustomerId $receiver,
        private float $value,
        private DateTimeImmutable $createdAt,
        private ?string $comment = null,
        private ?string $createdBy = null
    ) {
        parent::__construct($this->senderTransferId, $this->storeId);
    }

    public function getSenderTransferId(): TransferId
    {
        return $this->senderTransferId;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getSender(): CustomerId
    {
        return $this->sender;
    }

    public function getReceiver(): CustomerId
    {
        return $this->receiver;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function getCreatedBy(): ?string
    {
        return $this->createdBy;
    }
}
