<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Application\UseCase;

use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Import\Infrastructure\ImportResult;
use OpenLoyalty\Import\Infrastructure\Service\ImportFileManager;
use OpenLoyalty\Points\Infrastructure\Import\PointsTransferXmlImporter;

/**
 * Class ImportTransferPointsUseCase.
 */
class ImportTransferPointsUseCase
{
    /**
     * @var PointsTransferXmlImporter
     */
    private $importer;

    /**
     * @var ImportFileManager
     */
    private $importFileManager;

    /**
     * ImportTransferPointsUseCase constructor.
     */
    public function __construct(
        PointsTransferXmlImporter $importer,
        ImportFileManager $importFileManager
    ) {
        $this->importer = $importer;
        $this->importFileManager = $importFileManager;
    }

    /**
     * @throws \Exception
     */
    public function execute(string $realPath, string $mimeType, string $originalName, string $extension, Store $store): ImportResult
    {
        $importFile = $this->importFileManager->upload($realPath, $mimeType, $originalName, $extension, 'transfers');

        return $this->importer->import($this->importFileManager->getAbsolutePath($importFile), $store);
    }
}
