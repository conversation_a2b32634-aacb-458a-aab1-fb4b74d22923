<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Application\UseCase;

use Carbon\CarbonImmutable;
use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Points\Application\Command\AddPoints;

final readonly class AddPointsTransferUseCase
{
    public function __construct(
        private CommandBusInterface $commandBus,
        private UuidGeneratorInterface $uuidGenerator,
    ) {
    }

    public function execute(
        string $currentUserName,
        StoreId $storeId,
        CustomerId $customerId,
        float $points,
        ?string $comment,
        ?int $lockedUntilDays,
        ?int $expiresInDays,
        ?string $walletCode,
        DateTimeImmutable $createdAt = new CarbonImmutable(),
        ?string $externalTransferId = null
    ): TransferId {
        $transferId = new TransferId($this->uuidGenerator->generate());

        $command = new AddPoints(
            $transferId,
            $storeId,
            $customerId,
            $points,
            $createdAt,
            $comment,
            $lockedUntilDays,
            $expiresInDays,
            $currentUserName,
            $walletCode,
            $externalTransferId
        );
        $this->commandBus->dispatch($command);

        return $transferId;
    }
}
