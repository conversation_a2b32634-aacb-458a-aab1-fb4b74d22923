<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Application\UseCase;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Points\Application\Command\UnlockTransfer;

final readonly class ActivatePointsUseCase
{
    public function __construct(private CommandBusInterface $commandBus)
    {
    }

    public function execute(
        StoreId $storeId,
        TransferId $transferId,
        string $userName,
        DateTimeImmutable $unlockedAt = new DateTimeImmutable()
    ): void {
        $this->commandBus->dispatch(
            new UnlockTransfer($transferId, $storeId, $unlockedAt, $userName, false),
            threadSafeKey: $storeId.$transferId
        );
    }
}
