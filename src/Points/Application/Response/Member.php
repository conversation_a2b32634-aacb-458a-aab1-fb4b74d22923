<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Application\Response;

use OpenLoyalty\Core\Domain\Id\CustomerId;

final class Member
{
    public function __construct(
        private CustomerId $id,
        private ?string $firstName,
        private ?string $lastName,
        private ?string $email,
        private ?string $phoneNumber,
        private ?string $loyaltyCardNumber
    ) {
    }

    public function getId(): CustomerId
    {
        return $this->id;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function getPhoneNumber(): ?string
    {
        return $this->phoneNumber;
    }

    public function getLoyaltyCardNumber(): ?string
    {
        return $this->loyaltyCardNumber;
    }
}
