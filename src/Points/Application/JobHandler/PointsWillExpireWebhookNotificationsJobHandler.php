<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Application\JobHandler;

use Exception;
use OpenLoyalty\Account\Domain\SystemEvent\PointsWillExpire;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Core\Domain\Message\JobHandlerInterface;
use OpenLoyalty\Points\Application\Job\PointsWillExpireWebhookNotificationsJob;
use Psr\Log\LoggerInterface;

final readonly class PointsWillExpireWebhookNotificationsJobHandler implements JobHandlerInterface
{
    public function __construct(
        private EventBusInterface $eventBus,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(PointsWillExpireWebhookNotificationsJob $job): void
    {
        foreach ($job->getEvents() as $event) {
            try {
                $this->eventBus->dispatch($event);
            } catch (Exception) {
                $this->logger->error(sprintf('Cannot dispatch webhook %s', PointsWillExpire::NOTIFICATION_TYPE));
            }
        }
    }
}
