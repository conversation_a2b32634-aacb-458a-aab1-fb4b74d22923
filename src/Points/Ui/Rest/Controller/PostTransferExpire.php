<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Account\Domain\Exception\NotEnoughPointsException;
use OpenLoyalty\Points\Application\UseCase\ExpirePointsUseCase;
use OpenLoyalty\Points\Domain\Exception\TransferCanNotBeExpiredException;
use OpenLoyalty\Points\Domain\Exception\TransferNotFoundException;
use OpenLoyalty\Points\Domain\Transfer;
use OpenLoyalty\Ui\Rest\Responder\ErrorResponderInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

class PostTransferExpire extends AbstractFOSRestController
{
    private TranslatorInterface $translator;
    private ExpirePointsUseCase $useCase;
    private ErrorResponderInterface $formErrorResponder;

    public function __construct(
        TranslatorInterface $translator,
        ExpirePointsUseCase $useCase,
        ErrorResponderInterface $formErrorResponder
    ) {
        $this->translator = $translator;
        $this->useCase = $useCase;
        $this->formErrorResponder = $formErrorResponder;
    }

    /**
     * @Route(methods={"POST"}, name="oloy.points.transfer.expire", path="/{storeCode}/points/{transfer}/expire", requirements={"transfer"="%routing.uuid%"})
     *
     * @Security("is_granted('EXPIRE', transfer)")
     *
     * @Operation(
     *     tags={"Points"},
     *     summary="Expire units transfer",
     *     operationId="pointsPostTransferExpire",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(ref="#/components/parameters/transfer"),
     *     @OA\Response(
     *         response="204",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Returned when points transfer cannot be expired",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="401",
     *         ref="#/components/responses/Unauthorized"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Transfer $transfer): View
    {
        try {
            $this->useCase->execute($transfer->getStoreId(), $transfer->getTransferId());

            return $this->view(null, Response::HTTP_NO_CONTENT);
        } catch (TransferCanNotBeExpiredException|TransferNotFoundException|NotEnoughPointsException $exception) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans('account.points_transfer.cannot_be_expired')
            );
        }
    }
}
