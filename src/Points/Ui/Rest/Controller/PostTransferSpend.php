<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Account\Domain\Exception\NotEnoughPointsException;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Points\Application\UseCase\SpendPointsTransferUseCase;
use OpenLoyalty\Points\Infrastructure\Form\Type\SpendPointsFormType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

class PostTransferSpend extends AbstractFOSRestController
{
    private FormFactoryInterface $formFactory;
    private TranslatorInterface $translator;
    private SpendPointsTransferUseCase $useCase;
    private StoreContextProviderInterface $storeContextProvider;

    public function __construct(
        FormFactoryInterface $formFactory,
        TranslatorInterface $translator,
        SpendPointsTransferUseCase $useCase,
        StoreContextProviderInterface $storeContextProvider
    ) {
        $this->formFactory = $formFactory;
        $this->translator = $translator;
        $this->useCase = $useCase;
        $this->storeContextProvider = $storeContextProvider;
    }

    /**
     * @Route(methods={"POST"}, name="oloy.points.transfer.spend", path="/{storeCode}/points/spend")
     *
     * @Security("is_granted('SPEND_POINTS')")
     *
     * @Operation(
     *     tags={"Points"},
     *     summary="Spend units",
     *     operationId="pointsPostTransferSpend",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\RequestBody(
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(property="transfer", ref=@Model(type=SpendPointsFormType::class))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Return points transfer ID.",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="transferId",
     *                     type="string",
     *                     example="00000000-0000-0000-0000-000000000000"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Returned when form contains errors",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="401",
     *         ref="#/components/responses/Unauthorized"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         description="Returned when there is no account attached to customer",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Request $request): View
    {
        $form = $this->formFactory->createNamed('transfer', SpendPointsFormType::class);

        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $data = $form->getData();

        try {
            $response = $this->useCase->execute(
                $this->getUser()->getUsername(),
                $this->storeContextProvider->getStore()->getStoreId(),
                new CustomerId($data['customer']),
                $data['points'],
                $data['comment'],
                $data['walletCode'],
                /* @phpstan-ignore-next-line */
                externalTransferId: $data['externalTransferId']
            );

            return $this->view($response);
        } catch (NotEnoughPointsException $e) {
            $form->get('points')->addError(
                new FormError($this->translator->trans($e->getMessageKey(), $e->getMessageParams()))
            );
        }

        return $this->view($form, Response::HTTP_BAD_REQUEST);
    }
}
