<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Ui\Console\Command;

use Carbon\CarbonImmutable;
use DateTimeImmutable;
use OpenLoyalty\Account\Domain\Webhook\DataProvider\PointsWillExpireDataProvider;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Ui\Console\Command\AbstractCommand;
use OpenLoyalty\Messaging\Domain\CachedWebhookSubscriptionRepositoryInterface;
use OpenLoyalty\Points\Application\UseCase\SendNotificationsForPointsExpiringForTransfersUseCase;
use OpenLoyalty\Points\Domain\ExpiringTransferRepositoryInterface;
use OpenLoyalty\Settings\Infrastructure\Model\SettingsNames;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

final class SendExpirePointsNotifications extends AbstractCommand
{
    private const COMMAND_NAME = 'oloy:points:notify:expiration';

    public function __construct(
        private readonly CachedWebhookSubscriptionRepositoryInterface $subscriptionRepository,
        private readonly SettingsManager $settingsManager,
        private readonly StoreRepository $storeRepository,
        private readonly TranslatorInterface $translator,
        private readonly SendNotificationsForPointsExpiringForTransfersUseCase $useCase,
    ) {
        parent::__construct(self::COMMAND_NAME);
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        $this
            ->setDescription('Send expire points notification to the users')
            ->addArgument('days-to-expire', InputArgument::OPTIONAL, 'Number of days to expire points')
            ->addArgument(
                'today',
                InputArgument::OPTIONAL,
                'The day for which we run the command. Default is today',
                null,
            )
            ->addArgument(
                'chunk-number',
                InputArgument::OPTIONAL,
                'Transfers are grouped per page.',
                null
            )
            ->addArgument(
                'limit',
                InputArgument::OPTIONAL,
                'Limit transfers per action. Default '.ExpiringTransferRepositoryInterface::DEFAULT_EXPIRED_MAX_RESULTS,
                ExpiringTransferRepositoryInterface::DEFAULT_EXPIRED_MAX_RESULTS
            )
        ;
    }

    protected function executeStore(Store $store, InputInterface $input, OutputInterface $output): void
    {
        $pointsWereExpiredSubscriptions = $this->subscriptionRepository->byEventName(
            PointsWillExpireDataProvider::WEBHOOK_EVENT_NAME,
            $store->getStoreId()
        );

        if (empty($pointsWereExpiredSubscriptions)) {
            $output->writeln(sprintf('<comment>%s</comment>', $this->translator->trans('points.webhook_not_configured.exception')));

            return;
        }

        $daysToExpire = $input->getArgument('days-to-expire');
        $settingDaysToExpire = $this->settingsManager->getSettingByKey(SettingsNames::EXPIRE_POINTS_NOTIFICATION_DAYS->value, $store);

        if (null === $daysToExpire && null !== $settingDaysToExpire) {
            $daysToExpire = $settingDaysToExpire->getValue();
        }

        if (null === $daysToExpire) {
            $output->writeln(sprintf('<comment>%s</comment>', $this->translator->trans('points.no_expiry_date.exception')));

            return;
        }

        $today = $input->getArgument('today') ?? (new CarbonImmutable())->format('Y-m-d');
        /* @phpstan-ignore-next-line  */
        $expireFrom = new DateTimeImmutable(sprintf('%s midnight +%d days', $today, $daysToExpire));
        $to = clone $expireFrom;
        $expireTo = $to->modify('+1 day');
        $output->writeln(sprintf('Processing from: %s to: %s', $expireFrom->format(DATE_ATOM), $expireTo->format(DATE_ATOM)));

        /* @phpstan-ignore-next-line  */
        $offset = null !== $input->getArgument('chunk-number') ? intval($input->getArgument('chunk-number')) : null;
        /* @phpstan-ignore-next-line  */
        $limit = intval($input->getArgument('limit'));
        $this->useCase->sendNotifications($store, $expireFrom, $expireTo, $offset, $limit);
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $storeCode = $this->getRequestedStoreCode($input);
        if ($storeCode) {
            $store = $this->storeRepository->byCode($storeCode);
            if (!$store) {
                $output->writeln(sprintf('<error>Store with code "%s" not found.</error>', $storeCode));

                return self::FAILURE;
            }
            $stores = [$store];
        } else {
            $stores = $this->storeRepository->findAllActive();
        }

        foreach ($stores as $store) {
            $output->writeln(sprintf($this->translator->trans('points.command.processing_store'), $store->getCode()));
            $this->executeStore($store, $input, $output);
        }

        return self::SUCCESS;
    }
}
