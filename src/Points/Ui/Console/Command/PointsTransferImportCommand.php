<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Ui\Console\Command;

use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Import\Infrastructure\FileImporter;
use OpenLoyalty\Import\Infrastructure\Service\ConsoleImportFileManager;
use OpenLoyalty\Import\Ui\Console\Command\AbstractFileImportCommand;
use OpenLoyalty\Points\Infrastructure\Import\PointsTransferXmlImporter;

class PointsTransferImportCommand extends AbstractFileImportCommand
{
    /**
     * @var PointsTransferXmlImporter
     */
    private $importer;

    public function __construct(
        PointsTransferXmlImporter $importer,
        ConsoleImportFileManager $importFileManager,
        StoreContextProviderInterface $storeContextProvider
    ) {
        $this->importer = $importer;
        $this->importFileManager = $importFileManager;
        $this->storeContextProvider = $storeContextProvider;
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        parent::configure();

        $this
            ->setName('oloy:points:import')
            ->setDescription('Import points transfers from XML file');
    }

    /**
     * {@inheritdoc}
     */
    protected function getImporter(): FileImporter
    {
        return $this->importer;
    }
}
