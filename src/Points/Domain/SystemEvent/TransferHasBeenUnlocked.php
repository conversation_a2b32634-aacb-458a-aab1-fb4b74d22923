<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Domain\SystemEvent;

use OpenLoyalty\Core\Domain\Message\EventInterface;
use OpenLoyalty\Points\Domain\Transfer;

class TransferHasBeenUnlocked implements EventInterface
{
    private Transfer $transfer;

    public function __construct(Transfer $transfer)
    {
        $this->transfer = $transfer;
    }

    public function getTransfer(): Transfer
    {
        return $this->transfer;
    }
}
