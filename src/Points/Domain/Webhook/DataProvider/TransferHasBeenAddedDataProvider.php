<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Domain\Webhook\DataProvider;

use OpenLoyalty\Core\Domain\Exception\StoreNotFoundException;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\Webhook\DataProvider\WebhookDataProviderInterface;
use OpenLoyalty\Core\Domain\Webhook\Event\WebhookEventInterface;
use OpenLoyalty\Core\Domain\Webhook\Factory\MemberResponseFactoryInterface;
use OpenLoyalty\Core\Domain\Webhook\Factory\WalletResponseFactoryInterface;
use OpenLoyalty\Core\Domain\Webhook\Response\WebhookData;
use OpenLoyalty\Points\Domain\SystemEvent\TransferHasBeenAdded;
use OpenLoyalty\Points\Domain\Transfer;

final readonly class TransferHasBeenAddedDataProvider implements WebhookDataProviderInterface
{
    public function __construct(
        private StoreRepository $storeRepository,
        private MemberResponseFactoryInterface $memberResponseFactory,
        private WalletResponseFactoryInterface $walletResponseFactory
    ) {
    }

    public function getEventName(): string
    {
        return 'UnitsTransferWasAdded';
    }

    /**
     * @throws StoreNotFoundException
     */
    public function getData(TransferHasBeenAdded|WebhookEventInterface $event): ?WebhookData
    {
        $storeCode = $this->storeRepository->byId($event->getStoreId())?->getCode();
        if (null === $storeCode) {
            throw new StoreNotFoundException($event->getStoreId());
        }

        $transfer = $event->getTransfer();
        $wallet = $this->walletResponseFactory->create($transfer->getOwner());

        $relatedTransferId = null;
        if (Transfer::EXPIRE_TYPE === $transfer->getType()) {
            $relatedTransferId = (string) $transfer->getRelatedTransferId();
        }

        return new WebhookData(
            $this->getEventName(),
            $storeCode,
            [
                'member' => $this->memberResponseFactory->create($wallet->owner),
                'wallet' => $wallet,
                'createdAt' => $transfer->getCreatedAt(),
                'expiresAt' => $transfer->getExpiresAt(),
                'units' => $transfer->getValue(),
                'comment' => $transfer->getComment(),
                'type' => $transfer->getType(),
                'pending' => $transfer->isPending(),
                'lockedUntil' => $transfer->getLockedUntil(),
                'transferId' => (string) $transfer->getTransferId(),
                'relatedTransferId' => $relatedTransferId,
                'actionCause' => $transfer->getActionCause(),
            ]
        );
    }

    public function supports(string $eventClassName): bool
    {
        return TransferHasBeenAdded::class === $eventClassName;
    }
}
