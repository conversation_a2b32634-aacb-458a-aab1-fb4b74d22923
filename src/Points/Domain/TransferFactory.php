<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Domain;

use Assert\AssertionFailedException;
use DateTimeImmutable;
use OpenLoyalty\Account\Domain\Provider\ValidityTransferDecorator;
use OpenLoyalty\Account\Domain\Provider\WalletProviderInterface;
use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Core\Domain\ValueObject\ActionCause;
use OpenLoyalty\Core\Domain\ValueObject\RangePeriodDate;
use OpenLoyalty\Points\Domain\Enum\SubType;

final readonly class TransferFactory implements TransferFactoryInterface
{
    public function __construct(
        private UuidGeneratorInterface $uuidGenerator,
        private ValidityTransferDecorator $validityTransferDecorator,
        private WalletProviderInterface $walletProvider
    ) {
    }

    public function add(
        StoreId $storeId,
        AccountId $owner,
        float $value,
        DateTimeImmutable $createdAt,
        ?TransferId $transferId = null,
        ?string $comment = null,
        ?RangePeriodDate $locked = null,
        ?RangePeriodDate $expired = null,
        ?string $createdByUsername = null,
        ?ActionCause $actionCause = null,
        ?DateTimeImmutable $registeredAt = null,
        ?string $externalTransferId = null
    ): Transfer {
        //In this case, the business date is the date from the SpendPoints command.
        $registeredAt = $registeredAt ?? $createdAt;

        $transfer = new Transfer(
            $transferId ?? new TransferId($this->uuidGenerator->generate()),
            Transfer::ADD_TYPE,
            $owner,
            $storeId,
            $value,
            $createdAt,
            $registeredAt,
            externalTransferId: $externalTransferId
        );

        $transfer->comment($comment);
        $transfer->setCreatedByUsername($createdByUsername);

        if (null !== $actionCause) {
            $transfer->assignActionCause($actionCause);
        }

        $wallet = $this->walletProvider->provideById($owner);

        return $this->validityTransferDecorator->decorate($transfer, $wallet->getWalletType(), $locked, $expired);
    }

    public function spend(
        StoreId $storeId,
        AccountId $owner,
        float $value,
        DateTimeImmutable $createdAt,
        ?TransferId $transferId = null,
        ?DateTimeImmutable $registeredAt = null,
        ?SubType $subType = null,
        ?string $externalTransferId = null
    ): Transfer {
        //In this case, the business date is the date from the SpendPoints command.
        $registeredAt = $registeredAt ?? $createdAt;

        return new Transfer(
            $transferId ?? new TransferId($this->uuidGenerator->generate()),
            Transfer::SPEND_TYPE,
            $owner,
            $storeId,
            $value,
            $createdAt,
            $registeredAt,
            subType: $subType,
            externalTransferId: $externalTransferId
        );
    }

    /**
     * @return Transfer[]
     * @throws AssertionFailedException
     */
    public function transfer(
        StoreId $storeId,
        AccountId $senderId,
        float $value,
        AccountId $receiverId,
        DateTimeImmutable $createdAt,
        ?TransferId $senderTransferId = null
    ): array {
        //In this case, the business date is the date from the TransferPoints command.
        $registeredAt = $createdAt;

        $result[] = (new Transfer(
            $senderTransferId ?? new TransferId($this->uuidGenerator->generate()),
            Transfer::TRANSFER_OUT_TYPE,
            $senderId,
            $storeId,
            $value,
            $createdAt,
            $registeredAt
        ))->assignReceiverId($receiverId);

        $result[] = (new Transfer(
            new TransferId($this->uuidGenerator->generate()),
            Transfer::TRANSFER_IN_TYPE,
            $receiverId,
            $storeId,
            $value,
            $createdAt,
            $registeredAt
        ))->assignSenderId($senderId);

        return $result;
    }

    /**
     * @param  DateTimeImmutable        $registeredAt The date when the transfer should expire
     * @throws AssertionFailedException
     */
    public function expire(
        StoreId $storeId,
        AccountId $owner,
        float $value,
        DateTimeImmutable $createdAt,
        DateTimeImmutable $registeredAt,
        ?TransferId $relatedTransferId = null,
    ): Transfer {
        return new Transfer(
            new TransferId($this->uuidGenerator->generate()),
            Transfer::EXPIRE_TYPE,
            $owner,
            $storeId,
            $value,
            $createdAt,
            $registeredAt,
            $relatedTransferId
        );
    }

    public function block(
        StoreId $storeId,
        AccountId $owner,
        float $value,
        DateTimeImmutable $createdAt,
        ?TransferId $transferId = null,
        ?string $externalTransferId = null
    ): Transfer {
        //In this case, the business date is the date from the BlockPoints command.
        $registeredAt = $createdAt;

        return new Transfer(
            $transferId ?? new TransferId($this->uuidGenerator->generate()),
            Transfer::BLOCK_TYPE,
            $owner,
            $storeId,
            $value,
            $createdAt,
            $registeredAt,
            externalTransferId: $externalTransferId
        );
    }
}
