<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Domain\Expiring;

use DateTimeImmutable;
use OpenLoyalty\Account\Domain\Exception\NotEnoughPointsException;
use OpenLoyalty\Core\Domain\Exception\NotFoundException;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Points\Domain\AccountFacadeInterface;
use OpenLoyalty\Points\Domain\Exception\ModifyTransferExpirationIsNotAllowedException;
use OpenLoyalty\Points\Domain\Exception\TransferNotFoundException;
use OpenLoyalty\Points\Domain\ExpiringTransfer;
use OpenLoyalty\Points\Domain\ExpiringTransferRepositoryInterface;
use OpenLoyalty\Points\Domain\Transfer;
use OpenLoyalty\Points\Domain\TransferExecutorInterface;
use OpenLoyalty\Points\Domain\TransferFactoryInterface;
use OpenLoyalty\Points\Domain\TransferRepositoryInterface;
use Psr\Log\LoggerInterface;

final class TransferEvaluator implements TransferEvaluatorInterface
{
    public const TRANSFERS_LIMIT = 250;

    public function __construct(
        private readonly TransferExecutorInterface $transferExecutor,
        private readonly TransferFactoryInterface $transferFactory,
        private readonly ExpiringTransferRepositoryInterface $expiringTransferRepository,
        private readonly TransferRepositoryInterface $transferRepository,
        private readonly LoggerInterface $logger,
        private readonly AccountFacadeInterface $accountFacade,
    ) {
    }

    /**
     * @throws TransferNotFoundException
     * @throws NotFoundException
     * @throws NotEnoughPointsException
     */
    public function expire(
        TransferId $transferId,
        StoreId $storeId,
        DateTimeImmutable $createdAt,
        DateTimeImmutable $expiredAt = null,
        ?string $createdBy = null,
        bool $silentError = false
    ): void {
        /** @var ExpiringTransfer|null $transfer */
        $transfer = $this->expiringTransferRepository->findOneBy(['transferId' => $transferId, 'storeId' => $storeId]);

        if (null === $transfer) {
            if (false === $silentError) {
                throw new TransferNotFoundException(sprintf('The unit transfer with ID "%s" does not exist.', (string) $transferId));
            }

            $this->logger->warning('Transfer cannot be expired twice. ', ['transferId' => $transferId]);

            return;
        }

        if ($transfer->getAvailableValue() > 0) {
            $expireTransfer = $this->transferFactory->expire(
                $transfer->getStoreId(),
                $transfer->getOwner(),
                $transfer->getAvailableValue(),
                $createdAt,
                $expiredAt ?? $transfer->getExpiresAt(), //if expired date is not passed or its null, the business date is expiresAt
                $transferId,
            )->setCreatedByUsername($createdBy);

            try {
                $this->transferExecutor->create($expireTransfer);
            } catch (NotFoundException|NotEnoughPointsException $exception) {
                if (false === $silentError) {
                    throw $exception;
                }
                $this->transferRepository->remove($expireTransfer);
                $transfer->markAsFailedExpireAt(new DateTimeImmutable());
                $this->expiringTransferRepository->save($transfer);
                $this->logger->error(
                    sprintf(
                        'Transfer %s can not be expired',
                        (string) $transferId
                    ),
                    [
                        'exception' => (string) $exception,
                    ]
                );

                return;
            }
        }

        $this->expiringTransferRepository->remove($transfer);
    }

    public function cancel(
        TransferId $transferId,
        StoreId $storeId
    ): void {
        /** @var ExpiringTransfer|null $transfer */
        $transfer = $this->expiringTransferRepository->findOneBy(['transferId' => $transferId, 'storeId' => $storeId]);

        // skip if expiring does not exist.
        if (null === $transfer) {
            return;
        }

        $this->expiringTransferRepository->remove($transfer);
    }

    public function addExpiring(Transfer $transfer): ExpiringTransfer
    {
        $walletType = $this->accountFacade->getWalletType($transfer->getOwner());

        $this->logger->info('Attempt to add expiring transfer.',
            [
                'transferId' => $transfer->getTransferId(),
                'storeId' => $transfer->getStoreId(),
                'value' => $transfer->getValue(),
                'allowNegative' => $walletType->allowNegativeBalance(),
                'transferType' => $transfer->getType(),
            ]
        );

        $expiring = new ExpiringTransfer(
            $transfer->getTransferId(),
            $transfer->getOwner(),
            $transfer->getValue(),
            $transfer->getExpiresAt(),
            $transfer->getStoreId(),
            $transfer->getCreatedAt()
        );

        if ($walletType->allowNegativeBalance()) {
            $this->logger->info('Consume units for adding transfer for negative wallet balance.',
                [
                    'transferId' => $transfer->getTransferId(),
                    'storeId' => $transfer->getStoreId(),
                    'value' => $transfer->getValue(),
                    'allowNegative' => $walletType->allowNegativeBalance(),
                    'transferType' => $transfer->getType(),
                ]
            );

            $pointsToConsume = $transfer->getValue();

            $expiringTransfers = $this->expiringTransferRepository->findAllNegativeAvailableByOwner($transfer->getOwner());
            $modifiedTransfers = [];

            foreach ($expiringTransfers as $expiringTransfer) {
                if (abs($pointsToConsume) >= abs($expiringTransfer->getAvailableValue())) {
                    $pointsToConsume -= abs($expiringTransfer->getAvailableValue());
                    $this->expiringTransferRepository->remove($expiringTransfer);
                } else {
                    $modifiedTransfers[] = $expiringTransfer;
                    $expiringTransfer->setAvailableValue($expiringTransfer->getAvailableValue() + $pointsToConsume);
                    $pointsToConsume = 0;
                    break;
                }
            }

            if (!empty($modifiedTransfers)) {
                $transferArrays = array_chunk($modifiedTransfers, self::TRANSFERS_LIMIT);
                foreach ($transferArrays as $transfers) {
                    $this->expiringTransferRepository->save(...$transfers);
                }
            }
                $expiring->setAvailableValue($pointsToConsume);
        }

        $this->logger->info('Save expiring transfer with allowed available value to expire.',
            [
                'transferId' => $expiring->getTransferId(),
                'storeId' => $expiring->getStoreId(),
                'availableValue' => $expiring->getAvailableValue(),
                'value' => $expiring->getValue(),
                'allowNegative' => $walletType->allowNegativeBalance(),
            ]
        );
        $this->expiringTransferRepository->save($expiring);

        return $expiring;
    }

    public function consumeExpiring(Transfer $transfer): void
    {
        $walletType = $this->accountFacade->getWalletType($transfer->getOwner());

        $this->logger->info('Attempt to consume units from transfer.',
            [
                'transferId' => $transfer->getTransferId(),
                'storeId' => $transfer->getStoreId(),
                'pointsToConsume' => $transfer->getValue(),
                'allowNegative' => $walletType->allowNegativeBalance(),
                'transferType' => $transfer->getType(),
            ]
        );

        $pointsToConsume = $transfer->getValue();

        if ($pointsToConsume <= 0) {
            return;
        }

        $expiringTransfers = $this->expiringTransferRepository->findAllAvailableByOwner($transfer->getOwner());
        $modifiedTransfers = [];

        foreach ($expiringTransfers as $expiringTransfer) {
            $modifiedTransfers[] = $expiringTransfer;
            if ($pointsToConsume >= $expiringTransfer->getAvailableValue()) {
                $pointsToConsume -= $expiringTransfer->getAvailableValue();
                $expiringTransfer->setAvailableValue(0);
            } else {
                $expiringTransfer->setAvailableValue($expiringTransfer->getAvailableValue() - $pointsToConsume);
                $pointsToConsume = 0;
                break;
            }
        }

        if (!empty($modifiedTransfers)) {
            $transferArrays = array_chunk($modifiedTransfers, self::TRANSFERS_LIMIT);
            foreach ($transferArrays as $transfers) {
                $this->expiringTransferRepository->save(...$transfers);
            }
        }

        if ($pointsToConsume > 0 && $walletType->allowNegativeBalance()) {
            $expiring = new ExpiringTransfer(
                $transfer->getTransferId(),
                $transfer->getOwner(),
                $transfer->getValue(),
                $transfer->getExpiresAt(),
                $transfer->getStoreId(),
                $transfer->getCreatedAt()
            );

            $expiring->setAvailableValue($pointsToConsume * -1);

            $this->expiringTransferRepository->save($expiring);

            $this->logger->info('Save expiring transfer with negative available value for units that was not consumed.',
                [
                    'transferId' => $expiring->getTransferId(),
                    'storeId' => $expiring->getStoreId(),
                    'availableValue' => $expiring->getAvailableValue(),
                    'value' => $expiring->getValue(),
                    'allowNegative' => $walletType->allowNegativeBalance(),
                ]
            );
        }
    }

    public function changeExpiration(StoreId $storeId, TransferId $transferId, ?DateTimeImmutable $expirationDate): void
    {
        $transfer = $this->transferRepository->getById($storeId, $transferId);

        if (null === $transfer) {
            throw new TransferNotFoundException(sprintf('Transfer "%s" not found in store %s.', $transferId, $storeId));
        }

        if (!in_array($transfer->getType(), [Transfer::ADD_TYPE, Transfer::TRANSFER_IN_TYPE])) {
            throw new ModifyTransferExpirationIsNotAllowedException(sprintf('Changing expiration for transfer "%s" is not allowed because of type.', $transferId));
        }

        if ($transfer->isCancelled()) {
            throw new ModifyTransferExpirationIsNotAllowedException(sprintf('Changing expiration for transfer "%s" is not allowed because is cancelled.', $transferId));
        }

        if (null !== $expirationDate && $expirationDate < new DateTimeImmutable()) {
            throw new ModifyTransferExpirationIsNotAllowedException(sprintf('Changing expiration for transfer "%s" is not allowed because expiration date is in the past.', $transferId));
        }

        // CASE 1: remove expiration
        if (null === $expirationDate) {
            $this->cancel($transferId, $storeId);
            $transfer->expiresAt(null);
            $this->transferRepository->save($transfer);

            return;
        }

        $prevExpiresAt = $transfer->getExpiresAt();

        // CASE 2: change expiration (it was defined earlier or not)
        /** @var ExpiringTransfer|null $transferExpiring */
        $transferExpiring = $this->expiringTransferRepository->findOneBy(['transferId' => $transferId, 'storeId' => $storeId]);
        if (null !== $transferExpiring) {
            $transferExpiring->changeExpiresAt($expirationDate);
            $this->expiringTransferRepository->save($transferExpiring);
        }

        $transfer->expiresAt($expirationDate);
        $this->transferRepository->save($transfer);

        // EDGE CASE: it was NOT defined earlier, we need to recreate a record in expiration table
        if (null === $prevExpiresAt) {
            $available =
                $this->transferRepository->getActiveTotal($storeId, $transfer->getOwner()) - $this->expiringTransferRepository->getTotalToExpire($storeId, $transfer->getOwner());

            // not available units, so skip adding expiration record
            if ($available <= 0) {
                return;
            }

            $transferExpiring = $this->addExpiring($transfer);

            // reduce transfer available value to max. available for wallet
            if ($transferExpiring->getAvailableValue() > $available) {
                $transferExpiring->setAvailableValue($available);
                $this->expiringTransferRepository->save($transferExpiring);
            }
        }
    }
}
