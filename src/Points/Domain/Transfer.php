<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Points\Domain;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;
use OpenLoyalty\Core\Domain\ValueObject\ActionCause;
use OpenLoyalty\Points\Domain\Enum\SubType;
use OpenLoyalty\Points\Domain\Exception\TransferCanNotBeCancelledException;
use OpenLoyalty\Points\Domain\Exception\TransferIsNotPendingException;

class Transfer implements TimestampableInterface, BlameableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    public const ADD_TYPE = 'adding';
    public const SPEND_TYPE = 'spending';
    public const TRANSFER_OUT_TYPE = 'p2p_spending';
    public const TRANSFER_IN_TYPE = 'p2p_adding';
    public const BLOCK_TYPE = 'blocked';
    public const EXPIRE_TYPE = 'expired';

    protected TransferId $transferId;
    protected string $type;
    protected ?SubType $subType;
    protected AccountId $owner;
    /**
     * @description The business date on which the transfer was created
     */
    protected DateTimeImmutable $registeredAt;
    protected ?DateTimeImmutable $lockedUntil = null;
    protected ?DateTimeImmutable $expiresAt = null;
    protected StoreId $storeId;
    protected ?AccountId $senderId = null;
    protected ?AccountId $receiverId = null;
    protected ?string $comment = null;
    protected bool $cancelled = false;
    protected ?DateTimeImmutable $cancelledAt = null;
    protected bool $pending = false;
    protected ?DateTimeImmutable $unlockedAt = null;
    protected ?ActionCause $actionCause = null;
    protected ?DateTimeImmutable $failedUnlockAt = null;
    protected string|int|float $value = 0.0;
    protected ?string $createdByUsername = null;
    protected ?TransferId $relatedTransferId;
    private ?string $externalTransferId = null;

    public function __construct(
        TransferId $transferId,
        string $type,
        AccountId $owner,
        StoreId $storeId,
        float $value,
        DateTimeImmutable $createdAt,
        DateTimeImmutable $registeredAt,
        ?TransferId $relatedTransferId = null,
        ?SubType $subType = null,
        ?string $externalTransferId = null
    ) {
        $this->transferId = $transferId;
        $this->type = $type;
        $this->owner = $owner;
        $this->storeId = $storeId;
        $this->value = $value;
        $this->createdAt = $createdAt;
        $this->registeredAt = $registeredAt;
        $this->relatedTransferId = $relatedTransferId;
        $this->subType = $subType;
        $this->externalTransferId = $externalTransferId;

        // make sure that value for negative types is always negative
        if ($this->isNegative()) {
            $this->value = -abs($this->value);
        }
    }

    public function getTransferId(): TransferId
    {
        return $this->transferId;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getOwner(): AccountId
    {
        return $this->owner;
    }

    public function getLockedUntil(): ?DateTimeImmutable
    {
        return $this->lockedUntil;
    }

    public function getExpiresAt(): ?DateTimeImmutable
    {
        return $this->expiresAt;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getSenderId(): ?AccountId
    {
        return $this->senderId;
    }

    public function getReceiverId(): ?AccountId
    {
        return $this->receiverId;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function getValue(): float
    {
        return abs((float) $this->value);
    }

    public function getRawValue(): float
    {
        return $this->value;
    }

    public function getCreatedByUsername(): ?string
    {
        return $this->createdByUsername;
    }

    public function getCancelledAt(): ?DateTimeImmutable
    {
        return $this->cancelledAt;
    }

    public function isCancelled(): bool
    {
        return $this->cancelled;
    }

    /**
     * @throws TransferCanNotBeCancelledException
     */
    public function cancel(DateTimeImmutable $cancelledAt): void
    {
        if ($this->isCancelled()) {
            throw new TransferCanNotBeCancelledException(sprintf('The unit transfer with ID "%s" has already been canceled and cannot be canceled again.', (string) $this->transferId));
        }

        if (!in_array($this->getType(), [Transfer::ADD_TYPE, Transfer::BLOCK_TYPE])) {
            throw new TransferCanNotBeCancelledException(sprintf('The unit transfer with ID "%s" is not a valid type (only "adding" or "pending" transfers can be canceled).', (string) $this->transferId));
        }

        $this->cancelledAt = $cancelledAt;
        $this->cancelled = true;
    }

    public function lockedUntil(?DateTimeImmutable $lockedUntil): self
    {
        $this->lockedUntil = $lockedUntil;
        $this->pending = null !== $lockedUntil;

        return $this;
    }

    public function expiresAt(?DateTimeImmutable $expiresAt): self
    {
        if (null === $expiresAt) {
            $this->expiresAt = null;
        } else {
            $this->expiresAt = $expiresAt;
        }

        return $this;
    }

    public function comment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function setCreatedByUsername(?string $createdByUsername): self
    {
        $this->createdByUsername = $createdByUsername;

        return $this;
    }

    public function assignSenderId(?AccountId $senderId): self
    {
        $this->senderId = $senderId;

        return $this;
    }

    public function assignReceiverId(?AccountId $receiverId): self
    {
        $this->receiverId = $receiverId;

        return $this;
    }

    /**
     * @throws TransferIsNotPendingException
     */
    public function activate(DateTimeImmutable $unlockedAt): void
    {
        if (!$this->isPending()) {
            throw new TransferIsNotPendingException(sprintf('The unit transfer with ID "%s" is not a valid type (only "pending" transfers can be activated).', (string) $this->transferId));
        }

        if ($this->isCancelled()) {
            throw new TransferIsNotPendingException(sprintf('The unit transfer with ID "%s" cannot be activated because it has been canceled.', (string) $this->transferId));
        }

        $this->unlockedAt = $unlockedAt;
        $this->pending = false;
    }

    public function getUnlockedAt(): ?DateTimeImmutable
    {
        return $this->unlockedAt;
    }

    public function isPending(): bool
    {
        return $this->pending;
    }

    public function isNegative(): bool
    {
        return in_array($this->type, [self::SPEND_TYPE, self::TRANSFER_OUT_TYPE, self::BLOCK_TYPE, self::EXPIRE_TYPE]);
    }

    public function allowNegativeBalance(): bool
    {
        return $this->isNegative() && !($this->subType?->value === SubType::BUY_REWARD->value || self::TRANSFER_OUT_TYPE === $this->type || self::BLOCK_TYPE === $this->type);
    }

    public function getSubType(): ?SubType
    {
        return $this->subType;
    }

    public function getActionCause(): ?ActionCause
    {
        return $this->actionCause;
    }

    public function assignActionCause(ActionCause $actionCause): self
    {
        $this->actionCause = $actionCause;

        return $this;
    }

    public function getRelatedTransferId(): ?TransferId
    {
        return $this->relatedTransferId;
    }

    public function getFailedUnlockAt(): ?DateTimeImmutable
    {
        return $this->failedUnlockAt;
    }

    public function markAsFailedUnlock(DateTimeImmutable $failedUnlockAt): void
    {
        $this->failedUnlockAt = $failedUnlockAt;
        $this->unlockedAt = null;
        $this->pending = true;
    }

    public function getRegisteredAt(): DateTimeImmutable
    {
        return $this->registeredAt;
    }

    public function getExternalTransferId(): ?string
    {
        return $this->externalTransferId;
    }
}
