<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\GroupOfValues\Application\UseCase;

use OpenLoyalty\GroupOfValues\Application\DataMapper\ValueDataMapper;
use OpenLoyalty\GroupOfValues\Application\Response\Value as ValueResponse;
use OpenLoyalty\GroupOfValues\Domain\Entity\Value;

final readonly class GetValueUseCase
{
    public function __construct(
        private ValueDataMapper $dataMapper,
    ) {
    }

    public function execute(Value $value): ValueResponse
    {
        return $this->dataMapper->map($value);
    }
}
