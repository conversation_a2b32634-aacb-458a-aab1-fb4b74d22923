<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\GroupOfValues\Application\UseCase;

use OpenLoyalty\Core\Domain\Id\GroupOfValuesId;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\UuidCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponse;
use OpenLoyalty\GroupOfValues\Application\DataMapper\ValueDataMapper;
use OpenLoyalty\GroupOfValues\Domain\ValueRepositoryInterface;
use Ramsey\Uuid\Uuid;

final class GetValuesListUseCase
{
    public function __construct(
        private readonly ValueRepositoryInterface $valueRepository,
        private readonly SearchableResponderInterface $searchableResponder,
        private readonly ValueDataMapper $dataMapper
    ) {
    }

    public function execute(
        CriteriaCollectionInterface $criteriaCollection,
        GroupOfValuesId $groupOfValuesId
    ): SearchableResponse {
        $criteriaCollection->add(new UuidCriteria(
            'groupOfValues',
            CriteriaInterface::EQUAL,
            Uuid::fromString((string) $groupOfValuesId)
        ));

        $searchableResponse = $this->searchableResponder->fromCriteria($this->valueRepository, $criteriaCollection);
        $values = $this->dataMapper->mapList($searchableResponse->getItems());
        $total = $searchableResponse->getTotal();

        return new SearchableResponse($values, $total->getAll(), $total->getFiltered());
    }
}
