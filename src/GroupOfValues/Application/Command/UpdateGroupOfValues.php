<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\GroupOfValues\Application\Command;

use OpenLoyalty\Core\Domain\Id\GroupOfValuesId;
use OpenLoyalty\Core\Domain\Message\CommandInterface;
use OpenLoyalty\GroupOfValues\Domain\Entity\GroupOfValuesTranslation;

final readonly class UpdateGroupOfValues implements CommandInterface
{
    public function __construct(
        private GroupOfValuesId $groupOfValuesId,
        private bool $active,
        /**
         * @var array<string, ?GroupOfValuesTranslation>
         */
        private array $translations
    ) {
    }

    public function getGroupOfValuesId(): GroupOfValuesId
    {
        return $this->groupOfValuesId;
    }

    /**
     * @return array<string, ?GroupOfValuesTranslation>
     */
    public function getTranslations(): array
    {
        return $this->translations;
    }

    public function isActive(): bool
    {
        return $this->active;
    }
}
