<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\GroupOfValues\Application\CommandHandler;

use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\GroupOfValues\Application\Command\UpdateValue;
use OpenLoyalty\GroupOfValues\Domain\Exception\ValueNotFoundException;
use OpenLoyalty\GroupOfValues\Domain\ValueRepositoryInterface;

final readonly class UpdateValueCommandHandler implements CommandHandlerInterface
{
    public function __construct(
        private ValueRepositoryInterface $valueRepository,
    ) {
    }

    /**
     * @throws ValueNotFoundException
     */
    public function __invoke(UpdateValue $command): void
    {
        $value = $this->valueRepository->byId($command->getValueId());

        if (null === $value) {
            throw new ValueNotFoundException();
        }

        $value->changeValue($command->getValue());
        $value->changeDescription($command->getDescription());
    }
}
