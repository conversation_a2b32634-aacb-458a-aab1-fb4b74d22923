OpenLoyalty\GroupOfValues\Domain\Entity\GroupOfValues:
    type: entity
    table: group_of_values
    indexes:
        groupOfValuesCreatedAtIdx:
            columns: [ created_at ]
    inheritanceType: SINGLE_TABLE
    id:
        groupOfValuesId:
            type: group_of_values_id
    fields:
        active:
            type: boolean
            options:
                default: 0
        createdAt:
            type: datetime_immutable_microseconds
        updatedAt:
            type: datetime_immutable_microseconds
        createdBy:
            type: string
            nullable: true
        updatedBy:
            type: string
            nullable: true
    manyToOne:
        store:
            targetEntity: OpenLoyalty\Core\Domain\Store
            joinColumn:
                nullable: false
                name: store_id
                referencedColumnName: id
