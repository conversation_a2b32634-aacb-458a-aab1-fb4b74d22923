OpenLoyalty\GroupOfValues\Domain\Entity\Value:
    type: entity
    table: value
    inheritanceType: SINGLE_TABLE
    indexes:
        valueIdx:
            columns: [ value ]
        groupOfValuesIdx:
            columns: [ group_of_values_id ]
        valueCreatedAtIdx:
            columns: [ created_at ]
    id:
        valueId:
            type: value_id
    uniqueConstraints:
        group_value_idx:
            columns: [ group_of_values_id, value ]
    fields:
        value:
            type: text
        description:
            type: text
            length: 64
            nullable: true
        createdAt:
            type: datetime_immutable_microseconds
        updatedAt:
            type: datetime_immutable_microseconds
        createdBy:
            type: string
            nullable: true
        updatedBy:
            type: string
            nullable: true
    manyToOne:
        groupOfValues:
            targetEntity: OpenLoyalty\GroupOfValues\Domain\Entity\GroupOfValues
            joinColumn:
                nullable: false
                name: group_of_values_id
                referencedColumnName: group_of_values_id
                onDelete: CASCADE
        store:
            targetEntity: OpenLoyalty\Core\Domain\Store
            joinColumn:
                nullable: false
                name: store_id
                referencedColumnName: id
