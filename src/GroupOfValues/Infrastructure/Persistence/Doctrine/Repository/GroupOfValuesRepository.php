<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\GroupOfValues\Infrastructure\Persistence\Doctrine\Repository;

use OpenLoyalty\Core\Domain\Id\GroupOfValuesId;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\GroupOfValues\Domain\Entity\GroupOfValues;
use OpenLoyalty\GroupOfValues\Domain\GroupOfValuesRepositoryInterface;

class GroupOfValuesRepository extends DoctrineRepository implements GroupOfValuesRepositoryInterface
{
    protected function getClass(): string
    {
        return GroupOfValues::class;
    }

    public function save(GroupOfValues $groupOfValues): void
    {
        $this->entityManager->persist($groupOfValues);
        $groupOfValues->mergeNewTranslations();
        $this->entityManager->flush();
    }

    public function byId(GroupOfValuesId $groupOfValuesId): ?GroupOfValues
    {
        return $this->find($groupOfValuesId);
    }
}
