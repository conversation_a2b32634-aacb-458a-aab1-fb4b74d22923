<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\GroupOfValues\Infrastructure\Persistence\Doctrine\Repository;

use OpenLoyalty\Core\Domain\Id\GroupOfValuesId;
use OpenLoyalty\Core\Domain\Id\ValueId;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\GroupOfValues\Domain\Entity\Value;
use OpenLoyalty\GroupOfValues\Domain\ValueRepositoryInterface;

class ValueRepository extends DoctrineRepository implements ValueRepositoryInterface
{
    protected function getClass(): string
    {
        return Value::class;
    }

    public function save(Value $value): void
    {
        $this->entityManager->persist($value);
        $this->entityManager->flush();
    }

    public function byId(ValueId $valueId): ?Value
    {
        return $this->find($valueId);
    }

    public function byValueAndGroupOfValuesId(GroupOfValuesId $groupOfValuesId, string $value): ?Value
    {
        $query = $this->repository->createQueryBuilder('v')
            ->leftJoin('v.groupOfValues', 'gov')
            ->where('v.groupOfValues = :groupOfValuesId')
            ->andWhere('LOWER(v.value) = :value')
            ->andWhere('gov.active = :active')
            ->setParameter('groupOfValuesId', $groupOfValuesId)
            ->setParameter('value', strtolower($value))
            ->setParameter('active', true)
            ->getQuery();

        return $query->getOneOrNullResult();
    }

    public function remove(Value $value): void
    {
        $this->entityManager->remove($value);
        $this->entityManager->flush();
    }
}
