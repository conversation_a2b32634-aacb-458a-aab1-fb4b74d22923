<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\GroupOfValues\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchFormFactoryInterface;
use OpenLoyalty\GroupOfValues\Application\UseCase\GetValuesListUseCase;
use OpenLoyalty\GroupOfValues\Domain\Entity\GroupOfValues;
use OpenLoyalty\GroupOfValues\Infrastructure\Form\Type\ValuesSearchType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class GetValues extends AbstractFOSRestController
{
    public function __construct(
        private readonly SearchFormFactoryInterface $searchFormFactory,
        private readonly GetValuesListUseCase $useCase
    ) {
    }

    /**
     * @Route(methods={"GET"}, name="oloy.group_value.list", path="/{storeCode}/groupOfValues/{groupOfValues}/values")
     * @Security("is_granted('LIST_GROUP_OF_VALUES')")
     */
    public function __invoke(Request $request, GroupOfValues $groupOfValues): View
    {
        $form = $this->searchFormFactory->createAndHandle(
            ValuesSearchType::class,
            $request->query->all(),
            $request->getLocale()
        );

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $result = $this->useCase->execute($form->getData(), $groupOfValues->getGroupOfValuesId());

        return $this->view($result, Response::HTTP_OK);
    }
}
