<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Application\UseCase;

use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\UuidCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponse;
use OpenLoyalty\Import\Application\DataMapper\ImportItemDataMapper;
use OpenLoyalty\Import\Domain\ImportItemRepositoryInterface;
use OpenLoyalty\Import\Domain\ValueObject\ImportId;
use Ramsey\Uuid\Uuid;

class GetImportItemsListUseCase
{
    public function __construct(
        private ImportItemRepositoryInterface $importItemRepository,
        private SearchableResponderInterface $searchableResponder,
        private ImportItemDataMapper $dataMapper
    ) {
    }

    public function execute(ImportId $importId, CriteriaCollectionInterface $criteriaCollection): SearchableResponse
    {
        $criteriaCollection->add((new UuidCriteria(
            'import',
            CriteriaInterface::EQUAL,
            Uuid::fromString((string) $importId)
        ))->setAsInternal());

        $searchableResponder = $this->searchableResponder->fromCriteria(
            $this->importItemRepository,
            $criteriaCollection
        );

        $importItems = $this->dataMapper->mapList($searchableResponder->getItems());
        $total = $searchableResponder->getTotal();

        return new SearchableResponse($importItems, $total->getAll(), $total->getFiltered());
    }
}
