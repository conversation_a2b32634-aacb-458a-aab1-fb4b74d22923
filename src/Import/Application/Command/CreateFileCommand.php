<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Application\Command;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\CommandInterface;
use OpenLoyalty\Import\Domain\ValueObject\ImportId;
use OpenLoyalty\Import\Domain\ValueObject\ImportType;
use OpenLoyalty\Import\Domain\ValueObject\UploadedFile;

final class CreateFileCommand implements CommandInterface
{
    public function __construct(
        private StoreId $storeId,
        private ImportId $importId,
        private ImportType $importType,
        private UploadedFile $uploadedFile,
        private ?array $additionalData = null
    ) {
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getImportId(): ImportId
    {
        return $this->importId;
    }

    public function getImportType(): ImportType
    {
        return $this->importType;
    }

    public function getUploadedFile(): UploadedFile
    {
        return $this->uploadedFile;
    }

    public function getAdditionalData(): ?array
    {
        return $this->additionalData;
    }
}
