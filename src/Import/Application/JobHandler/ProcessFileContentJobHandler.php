<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Application\JobHandler;

use Carbon\CarbonImmutable;
use Doctrine\ORM\Exception\ORMException;
use OpenLoyalty\Core\Application\TimezoneResolverInterface;
use OpenLoyalty\Core\Domain\Exception\NotFoundException;
use OpenLoyalty\Core\Domain\Message\JobBusInterface;
use OpenLoyalty\Core\Domain\Message\JobHandlerInterface;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Import\Application\Job\ProcessFileContentJob;
use OpenLoyalty\Import\Application\Job\StartProcessItemsJob;
use OpenLoyalty\Import\Domain\Entity\ImportItem;
use OpenLoyalty\Import\Domain\Exception\NotSupportedFileExtension;
use OpenLoyalty\Import\Domain\Exception\NotSupportedImportTypeException;
use OpenLoyalty\Import\Domain\FileReader;
use OpenLoyalty\Import\Domain\ImportItemRepositoryInterface;
use OpenLoyalty\Import\Domain\ImportRepositoryInterface;
use OpenLoyalty\Import\Domain\ImportTypeSettings;
use OpenLoyalty\Import\Domain\ValueObject\ImportItemId;
use OpenLoyalty\Import\Domain\ValueObject\ItemStatus;
use Psr\Log\LoggerInterface;
use Throwable;

final readonly class ProcessFileContentJobHandler implements JobHandlerInterface
{
    public function __construct(
        private ImportRepositoryInterface $importRepository,
        private ImportItemRepositoryInterface $importItemRepository,
        private JobBusInterface $jobBus,
        private UuidGeneratorInterface $uuidGenerator,
        private FileReader $fileReader,
        private ImportTypeSettings $importTypeSettings,
        private LoggerInterface $importLogger,
        private TimezoneResolverInterface $timezoneResolver,
    ) {
    }

    /**
     * @throws NotFoundException
     * @throws ORMException
     */
    public function __invoke(ProcessFileContentJob $job): void
    {
        $import = $this->importRepository->byId($job->getImportId());

        if (!$import) {
            throw new NotFoundException('Import with id '.$job->getImportId().' doesn\'t exist');
        }

        $this->timezoneResolver->setDefaultTimezoneByStoreId($import->getStore()->getStoreId());

        try {
            $bulkSize = $this->importTypeSettings->getBulkSize($import->getType());
            $itemsToImport = 0;
            $packageNumber = 1;
            // variable needed only for checking if entire package is in database
            $previousPackageNumber = 0;
            $importItemsPackageExists = false;
            $importItems = [];

            foreach ($this->fileReader->getNodes($import->getFilename()) as $node) {
                ++$itemsToImport;

                // checking single import item is enough for entire package is in database,
                // because we added whole package in one bulk insert
                if ($previousPackageNumber !== $packageNumber) {
                    $previousPackageNumber = $packageNumber;

                    if ($this->importItemRepository->importItemExists($packageNumber, $import->getImportId())) {
                        $importItemsPackageExists = true;
                        continue;
                    }
                }

                if (true === $importItemsPackageExists) {
                    if (($itemsToImport % $bulkSize) === 0) {
                        ++$packageNumber;
                        $importItemsPackageExists = false;
                    }
                    continue;
                }

                $now = new CarbonImmutable();
                $importItem = new ImportItem(
                    new ImportItemId($this->uuidGenerator->generate()),
                    $import->getStore(),
                    $import,
                    $node->getValue(),
                    new ItemStatus(ItemStatus::QUEUED),
                    packageNumber: $packageNumber,
                );
                $importItem->setCreatedAt($now);
                $importItem->setUpdatedAt($now);

                $importItems[] = $importItem;

                if (($itemsToImport % $bulkSize) === 0) {
                    $this->importItemRepository->saveBulk($importItems);
                    $this->jobBus->dispatch(new StartProcessItemsJob($import->getImportId(), $packageNumber), false);

                    ++$packageNumber;
                    $importItems = [];
                }
            }

            // Persist items that did not make up an entire batch
            if (!empty($importItems)) {
                $this->importItemRepository->saveBulk($importItems);
                $this->jobBus->dispatch(new StartProcessItemsJob($import->getImportId(), $packageNumber), false);
            }

            $import->changeToQueued($itemsToImport);
            $this->importRepository->save($import);
        } catch (NotSupportedFileExtension|NotSupportedImportTypeException $exception) {
            $import->processFailed($exception->getMessage());
        } catch (ORMException $exception) {
            //for entity managed closed issue
            throw $exception;
        } catch (Throwable $exception) {
            $this->importLogger->error(
                $exception->getMessage(),
                [
                    'importId' => (string) $import->getImportId(),
                    'exception' => (string) $exception,
                ]
            );

            $import->processError(
                'Import failed.',
                (string) $exception
            );
        }
    }
}
