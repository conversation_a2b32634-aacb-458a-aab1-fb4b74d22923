<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View as FosView;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchFormFactoryInterface;
use OpenLoyalty\Import\Application\UseCase\GetImportsListUseCase;
use OpenLoyalty\Import\Infrastructure\Form\Type\ImportSearchType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetImportList extends AbstractFOSRestController
{
    public function __construct(
        private readonly GetImportsListUseCase $useCase,
        private readonly SearchFormFactoryInterface $searchFormFactory
    ) {
    }

    /**
     * @Route(methods={"GET"}, name="oloy.import.list", path="/{storeCode}/import")
     * @Security("is_granted('LIST_IMPORT')")
     */
    public function __invoke(Request $request): FosView
    {
        $form = $this->searchFormFactory->createAndHandle(
            ImportSearchType::class,
            $request->query->all(),
            $request->getLocale()
        );

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $result = $this->useCase->execute($form->getData());

        return $this->view($result, Response::HTTP_OK);
    }
}
