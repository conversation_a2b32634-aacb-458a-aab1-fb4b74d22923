<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Infrastructure;

use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Import\Domain\SegmentFacadeInterface;
use OpenLoyalty\Segment\Domain\SegmentGateInterface;

final readonly class SegmentFacade implements SegmentFacadeInterface
{
    public function __construct(private SegmentGateInterface $segmentGate)
    {
    }

    public function isSegmentExistsInTenant(SegmentId $segmentId, StoreId $storeId): bool
    {
        return $this->segmentGate->isSegmentValid($segmentId, $storeId);
    }
}
