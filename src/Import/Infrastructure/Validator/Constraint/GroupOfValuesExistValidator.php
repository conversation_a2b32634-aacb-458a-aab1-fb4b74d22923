<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Infrastructure\Validator\Constraint;

use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Id\GroupOfValuesId;
use OpenLoyalty\GroupOfValues\Domain\GroupOfValuesRepositoryInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class GroupOfValuesExistValidator extends ConstraintValidator
{
    public function __construct(
        private readonly GroupOfValuesRepositoryInterface $groupOfValuesRepository,
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (null === $value || '' === $value) {
            return;
        }

        try {
            $groupOfValuesId = !$value instanceof GroupOfValuesId ? new GroupOfValuesId($value) : $value;
        } catch (AssertionFailedException) {
            return;
        }

        $entity = $this->groupOfValuesRepository->byId($groupOfValuesId);

        if (null !== $entity) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->setParameter('{{ id }}', $this->formatValue($value))
            ->setCode('groupOfValues.not_found')
            ->addViolation();
    }
}
