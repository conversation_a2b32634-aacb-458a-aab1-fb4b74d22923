<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Infrastructure\Validator\Constraint;

use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Exception\NotFoundException;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Import\Domain\RewardFacadeInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

class RewardExistValidator extends ConstraintValidator
{
    public function __construct(
        private readonly RewardFacadeInterface $rewardFacade,
        private readonly TranslatorInterface $translator,
    ) {
    }

    /**
     * @param ?string $value
     */
    public function validate(mixed $value, Constraint $constraint): void
    {
        if (null === $value || '' === $value) {
            return;
        }

        try {
            $rewardId = !$value instanceof RewardId ? new RewardId($value) : $value;
        } catch (AssertionFailedException) {
            return;
        }

        try {
            $this->rewardFacade->getReward($rewardId);
        } catch (NotFoundException $e) {
            $this->context->buildViolation($this->translator->trans('reward.not_found'))
                ->setParameter('{{ id }}', $value)
                ->addViolation();
        }
    }
}
