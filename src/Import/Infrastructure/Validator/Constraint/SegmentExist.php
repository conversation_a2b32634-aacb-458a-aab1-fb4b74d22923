<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Infrastructure\Validator\Constraint;

use OpenLoyalty\Core\Domain\Id\StoreId;
use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 */
class SegmentExist extends Constraint
{
    public const SEGMENT_NOT_FOUND_ERROR_CODE = 'ea242f3a-110a-4f40-84d4-5bbe8d17b182';

    public string $message = 'Segment not exists.';

    public ?StoreId $storeId = null;
}
