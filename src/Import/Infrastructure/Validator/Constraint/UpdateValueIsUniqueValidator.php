<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Infrastructure\Validator\Constraint;

use OpenLoyalty\Core\Domain\Id\GroupOfValuesId;
use OpenLoyalty\GroupOfValues\Domain\Entity\Value;
use OpenLoyalty\GroupOfValues\Domain\ValueRepositoryInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;
use Throwable;

final class UpdateValueIsUniqueValidator extends ConstraintValidator
{
    public function __construct(
        private readonly ValueRepositoryInterface $valueRepository,
        private readonly TranslatorInterface $translator,
    ) {
    }

    /**
     * @param string $value
     */
    public function validate(mixed $value, Constraint $constraint): void
    {
        if (null === $value || '' === $value) {
            return;
        }

        if (!is_string($constraint->groupOfValuesId)) {
            return;
        }

        try {
            $groupOfValuesId = new GroupOfValuesId($constraint->groupOfValuesId);
        } catch (Throwable) {
            return;
        }

        /**
         * @var Value|null $groupValue
         */
        $groupValue = $this->valueRepository->findOneBy([
            'groupOfValues' => $groupOfValuesId,
            'value' => $value,
        ]);

        if (null === $groupValue) {
            return;
        }

        if (
            is_string($constraint->valueId)
            && (string) $groupValue->getValueId() === $constraint->valueId
        ) {
            return;
        }

        $this->context->buildViolation($this->translator->trans('groupOfValues.value.not_unique'))
            ->setParameter('{{ id }}', $value)
            ->addViolation();
    }
}
