<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Infrastructure\Validator\Constraint;

use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Exception\NotFoundException;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Import\Domain\RewardFacadeInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

final class RewardCouponSourceValidator extends ConstraintValidator
{
    public function __construct(
        private readonly RewardFacadeInterface $rewardFacade,
        private readonly TranslatorInterface $translator,
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (null === $value || '' === $value) {
            return;
        }

        try {
            /* @phpstan-ignore-next-line */
            $rewardId = !$value instanceof RewardId ? new RewardId($value) : $value;
        } catch (AssertionFailedException) {
            return;
        }

        try {
            $reward = $this->rewardFacade->getReward($rewardId);
            if (!$reward->isSourceOfCouponExternal()) {
                $this->context->buildViolation($this->translator->trans('import.rewardCoupon.externalSource'))
                    ->addViolation();
            }
        } catch (NotFoundException $e) {
            $this->context->buildViolation($this->translator->trans('import.rewardCoupon.externalSource'))
                ->addViolation();
        }
    }
}
