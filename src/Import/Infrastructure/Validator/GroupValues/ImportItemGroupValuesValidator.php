<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Infrastructure\Validator\GroupValues;

use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Import\Domain\ValueObject\ImportType;
use OpenLoyalty\Import\Domain\ValueObject\ValidationResult;
use OpenLoyalty\Import\Infrastructure\Form\GroupValues\GroupValueItemDataFormType;
use OpenLoyalty\Import\Infrastructure\Validator\AbstractItemDataValidator;
use Symfony\Component\Form\FormFactoryInterface;

class ImportItemGroupValuesValidator extends AbstractItemDataValidator
{
    public function __construct(
        private FormFactoryInterface $formFactory,
    ) {
    }

    public function validate(Store $store, array $data): ValidationResult
    {
        $form = $this->formFactory->createNamed(
            'groupValue',
            GroupValueItemDataFormType::class,
            null,
            [
                'csrf_protection' => false,
                'groupOfValuesId' => $data['groupOfValuesId'],
            ]
        );

        $form->submit($data);

        $errors = $this->getErrorsFromForm($form);

        return new ValidationResult($errors, 0 === count($errors));
    }

    public function supports(ImportType $importType): bool
    {
        return ImportType::GROUP_VALUE === $importType->getValue();
    }
}
