<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Infrastructure\Validator\UnitTransfer\Adding;

use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Import\Domain\ValueObject\ImportType;
use OpenLoyalty\Import\Domain\ValueObject\ValidationResult;
use OpenLoyalty\Import\Infrastructure\Form\UnitTransfer\Adding\UnitTransferFormType;
use OpenLoyalty\Import\Infrastructure\Validator\AbstractItemDataValidator;
use Symfony\Component\Form\FormFactoryInterface;

class ImportItemUnitTransferValidator extends AbstractItemDataValidator
{
    public function __construct(
        private readonly FormFactoryInterface $formFactory,
    ) {
    }

    public function validate(Store $store, array $data): ValidationResult
    {
        $form = $this->formFactory->createNamed(
            'unitTransferAdding',
            UnitTransferFormType::class,
            null,
            [
                'csrf_protection' => false,
                'storeId' => $store->getStoreId(),
            ]
        );

        $form->submit($data);

        $errors = $this->getErrorsFromForm($form);

        return new ValidationResult($errors, 0 === count($errors));
    }

    public function supports(ImportType $importType): bool
    {
        return ImportType::UNIT_TRANSFER_ADDING === $importType->getValue();
    }
}
