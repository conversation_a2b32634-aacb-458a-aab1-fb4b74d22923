OpenLoyalty\Import\Domain\Entity\ImportItem:
    type: entity
    table: import_item
    indexes:
        importItemCreatedAtIdx:
            columns: [ created_at ]
        importItemImportIdStatusValuePackageNumberIdx:
            columns: [ import_id, status_value, package_number ]
        importItemImportIdStatusValueIdx:
            columns: [ import_id, status_value ]
    inheritanceType: SINGLE_TABLE
    id:
        importItemId:
            type: import_item_id
    fields:
        itemBody:
            type: text
        errorMessage:
            type: text
            nullable: true
        createdAt:
            type: datetime_immutable_microseconds
        updatedAt:
            type: datetime_immutable_microseconds
        createdBy:
            type: string
            nullable: true
        updatedBy:
            type: string
            nullable: true
        packageNumber:
            type: integer
            nullable: true
            options:
                default: null
        errorDetails:
            type: text
            nullable: true
            options:
                default: null
    manyToOne:
        import:
            targetEntity: OpenLoyalty\Import\Domain\Entity\Import
            joinColumn:
                nullable: false
                name: import_id
                referencedColumnName: import_id
                onDelete: CASCADE
        store:
            targetEntity: OpenLoyalty\Core\Domain\Store
            joinColumn:
                nullable: false
                name: store_id
                referencedColumnName: id
    embedded:
        status:
            class: OpenLoyalty\Import\Domain\ValueObject\ItemStatus
        entity:
            class: OpenLoyalty\Import\Domain\ValueObject\Entity
