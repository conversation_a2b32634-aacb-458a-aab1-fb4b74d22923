<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Infrastructure;

use DateTimeImmutable;
use DateTimeInterface;
use OpenLoyalty\Account\Domain\AccountGateInterface;
use OpenLoyalty\Account\Domain\Exception\NotEnoughPointsException;
use OpenLoyalty\Core\Domain\Exception\DomainException;
use OpenLoyalty\Core\Domain\Exception\NotFoundException;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\ValueObject\ActionCause;
use OpenLoyalty\Core\Domain\ValueObject\RangePeriodDate;
use OpenLoyalty\Import\Domain\PointsFacadeInterface;
use OpenLoyalty\Import\Domain\ValueObject\Entity;
use OpenLoyalty\Points\Domain\PointsGateInterface;
use OpenLoyalty\User\Domain\CustomerGateInterface;

final readonly class PointsFacade implements PointsFacadeInterface
{
    public function __construct(
        private AccountGateInterface $accountGate,
        private PointsGateInterface $pointsGate,
        private CustomerGateInterface $customerGate
    ) {
    }

    /**
     * @throws DomainException
     */
    public function addUnits(
        StoreId $storeId,
        CustomerId $customerId,
        float $points,
        string $comment = '',
        ?ActionCause $actionCause = null,
        ?string $walletCode = null,
        ?RangePeriodDate $locked = null,
        ?RangePeriodDate $expired = null,
        ?DateTimeImmutable $registeredAt = null,
        ?string $externalTransferId = null
    ): Entity {
        try {
            $wallet = $this->accountGate->getWallet($storeId, $customerId, $walletCode);

            $transferId = $this->pointsGate->addUnits(
                $storeId,
                $wallet->getWalletId(),
                $points,
                $comment,
                $registeredAt,
                $locked,
                $expired,
                $actionCause,
                $externalTransferId
            );

            $member = $this->customerGate->getCustomer($customerId);
            $transfer = $this->pointsGate->findUnitsByTransferId($storeId, $transferId);

            if (null === $transfer) {
                throw new DomainException('Unit transfer not found (transferId: '.$transferId.')');
            }

            return new Entity(
                (string) $transferId,
                [
                    'walletName' => $wallet->getWalletType()->getName(),
                    'walletCode' => $wallet->getWalletType()->getCode(),
                    'unitSingularName' => $wallet->getWalletType()->getUnitSingularName(),
                    'unitPluralName' => $wallet->getWalletType()->getUnitPluralName(),
                    'value' => $transfer->getValue(),
                    'comment' => $transfer->getComment(),
                    'registeredOn' => $transfer->getRegisteredAt()->format(DateTimeInterface::ATOM),
                    'createdOn' => $transfer->getCreatedAt()->format(DateTimeInterface::ATOM),
                    'isPending' => $transfer->isPending(),
                    'expireAt' => $transfer->getExpiresAt()?->format(DateTimeInterface::ATOM),
                    'memberId' => (string) $customerId,
                    'memberFullName' => $member->getFullName(),
                    'memberEmail' => $member->getEmail(),
                    'memberLoyaltyCardNumber' => $member->getLoyaltyCardNumber(),
                    'memberPhone' => $member->getPhone(),
                    'externalTransferId' => $transfer->getExternalTransferId(),
                ]
            );
        } catch (NotFoundException) {
            throw new DomainException('Member not found (customerId: '.$customerId.')');
        }
    }

    /**
     * @throws DomainException
     */
    public function spendUnit(
        StoreId $storeId,
        CustomerId $customerId,
        float $points,
        ?string $walletCode = null,
        string $comment = '',
        ?ActionCause $actionCause = null,
        ?DateTimeImmutable $registeredAt = null,
        ?string $externalTransferId = null
    ): Entity {
        try {
            $wallet = $this->accountGate->getWallet($storeId, $customerId, $walletCode);

            $transferId = $this->pointsGate->spendUnit(
                $storeId,
                $wallet->getWalletId(),
                $points,
                $comment,
                $actionCause,
                $registeredAt,
                $externalTransferId
            );

            $member = $this->customerGate->getCustomer($customerId);
            $transfer = $this->pointsGate->findUnitsByTransferId($storeId, $transferId);

            if (null === $transfer) {
                throw new DomainException('Unit transfer not found (transferId: '.$transferId.')');
            }

            return new Entity(
                (string) $transferId,
                [
                    'walletName' => $wallet->getWalletType()->getName(),
                    'walletCode' => $wallet->getWalletType()->getCode(),
                    'unitSingularName' => $wallet->getWalletType()->getUnitSingularName(),
                    'unitPluralName' => $wallet->getWalletType()->getUnitPluralName(),
                    'value' => $transfer->getValue(),
                    'comment' => $transfer->getComment(),
                    'registeredOn' => $transfer->getRegisteredAt()->format(DateTimeInterface::ATOM),
                    'createdOn' => $transfer->getCreatedAt()->format(DateTimeInterface::ATOM),
                    'memberId' => (string) $customerId,
                    'memberFullName' => $member->getFullName(),
                    'memberEmail' => $member->getEmail(),
                    'memberLoyaltyCardNumber' => $member->getLoyaltyCardNumber(),
                    'memberPhone' => $member->getPhone(),
                    'externalTransferId' => $transfer->getExternalTransferId(),
                ]
            );
        } catch (NotEnoughPointsException $exception) {
            throw new DomainException($exception->getMessage());
        } catch (NotFoundException) {
            throw new DomainException('Member not found (customerId: '.$customerId.')');
        }
    }
}
