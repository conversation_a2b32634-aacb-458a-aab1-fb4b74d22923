<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Infrastructure\Converter\UnitTransfer\Json\Adding;

use OpenLoyalty\Import\Domain\Converter\ConverterInterface;
use OpenLoyalty\Import\Domain\Enum\SupportedFileExtensions;
use OpenLoyalty\Import\Domain\ValueObject\ImportType;
use OpenLoyalty\Import\Domain\ValueObject\ItemData;

class UnitTransferItemBodyConverter implements ConverterInterface
{
    public function supports(ImportType $importType, string $fileExtension): bool
    {
        return ImportType::UNIT_TRANSFER_ADDING === $importType->getValue() && SupportedFileExtensions::JSON->value === $fileExtension;
    }

    public function convert(string $itemBody, ?array $additionalData = null): ItemData
    {
        $data = $this->convertBody($itemBody);

        return new ItemData($data);
    }

    private function convertBody(string $body): array
    {
        return $this->arrayFilterRecursive(json_decode($body, true, 512, JSON_THROW_ON_ERROR));
    }

    private function arrayFilterRecursive($input): array
    {
        foreach ($input as &$value) {
            if (is_array($value)) {
                $value = $this->arrayFilterRecursive($value);
            }
        }

        return array_filter($input);
    }
}
