<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Domain;

use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Import\Domain\Exception\ImportConvertException;
use SimpleXMLElement;

interface XMLImportConverter
{
    /**
     * @return mixed
     *
     * @throws ImportConvertException
     */
    public function convert(SimpleXMLElement $element, Store $store);

    public function getIdentifier(SimpleXMLElement $element, ?Store $store): string;
}
