<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Domain\EntityConverter;

use OpenLoyalty\Import\Domain\ValueObject\Entity;
use OpenLoyalty\Import\Domain\ValueObject\ImportType;
use OpenLoyalty\Import\Domain\ValueObject\ItemData;

interface EntityConverterInterface
{
    public function supports(ImportType $importType): bool;

    public function convertToEntity(ItemData $itemData): Entity;
}
