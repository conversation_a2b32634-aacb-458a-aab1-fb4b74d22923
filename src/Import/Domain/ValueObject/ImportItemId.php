<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Domain\ValueObject;

use OpenLoyalty\Core\Domain\Model\Identifier;

final class ImportItemId implements Identifier
{
    public function __construct(public readonly string $importId)
    {
    }

    public function __toString()
    {
        return $this->importId;
    }
}
