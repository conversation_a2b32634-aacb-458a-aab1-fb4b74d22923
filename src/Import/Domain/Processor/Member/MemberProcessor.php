<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Domain\Processor\Member;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Import\Domain\CustomerFacadeInterface;
use OpenLoyalty\Import\Domain\Processor\ProcessorInterface;
use OpenLoyalty\Import\Domain\ValueObject\Entity;
use OpenLoyalty\Import\Domain\ValueObject\ImportType;
use OpenLoyalty\Import\Domain\ValueObject\ItemData;

class MemberProcessor implements ProcessorInterface
{
    public function __construct(
        private readonly CustomerFacadeInterface $customerFacade
    ) {
    }

    public function supports(ImportType $importType): bool
    {
        return ImportType::MEMBER === $importType->getValue();
    }

    public function process(StoreId $storeId, ItemData $itemData): Entity
    {
        return $this->customerFacade->createMember(
            $itemData->getValue(),
            $storeId
        );
    }
}
