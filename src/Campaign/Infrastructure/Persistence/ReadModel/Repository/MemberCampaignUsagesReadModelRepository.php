<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Persistence\ReadModel\Repository;

use OpenLoyalty\Campaign\Domain\ReadModel\Entity\MemberCampaignUsages;
use OpenLoyalty\Campaign\Domain\ReadModel\Projector\MassMemberCampaignUsagesRequestProjection;
use OpenLoyalty\Campaign\Domain\ReadModel\Projector\QueryMemberCampaignUsagesRequestProjection;
use OpenLoyalty\Campaign\Domain\ReadModel\Projector\SingleMemberCampaignUsagesRequestProjection;
use OpenLoyalty\Campaign\Domain\ReadModel\Repository\MemberCampaignUsagesReadModelRepositoryInterface;
use OpenLoyalty\Campaign\Domain\ReadModel\Repository\MemberCampaignUsagesReadModelRepositoryReadContextInterface;
use OpenLoyalty\Core\Domain\ReadModel\Request\RequestProjectionInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\UuidCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\UuidListCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\Core\Infrastructure\Persistence\ReadModel\Repository\ReadModelRepository;

class MemberCampaignUsagesReadModelRepository extends ReadModelRepository implements MemberCampaignUsagesReadModelRepositoryInterface, MemberCampaignUsagesReadModelRepositoryReadContextInterface
{
    protected function getClass(): string
    {
        return MemberCampaignUsages::class;
    }

    public function getProjections(RequestProjectionInterface $requestProjection, bool $useQuickResultCache = false): \Generator
    {
        yield from parent::getProjections($requestProjection, $useQuickResultCache);

        if ($requestProjection instanceof SingleMemberCampaignUsagesRequestProjection) {
            $projection = $this->repository->findOneBy([
                'storeId' => $requestProjection->storeId,
                'customerId' => $requestProjection->customerId,
                'campaignId' => $requestProjection->campaignId,
            ]);
            if (null != $projection) {
                yield $projection;
            }
        } elseif ($requestProjection instanceof QueryMemberCampaignUsagesRequestProjection) {
            $criteriaCollection = new CriteriaCollection();
            $criteriaCollection->add(
                UuidCriteria::fromIdentifier(
                    'storeId',
                    CriteriaInterface::EQUAL,
                    $requestProjection->storeId
                )
            );
            $criteriaCollection->add(
                UuidCriteria::fromIdentifier(
                    'campaignId',
                    CriteriaInterface::EQUAL,
                    $requestProjection->campaignId
                )
            );
            yield from $this->findByCriteriaIterable($criteriaCollection);
        } elseif ($requestProjection instanceof MassMemberCampaignUsagesRequestProjection) {
            $criteriaCollection = new CriteriaCollection();
            $criteriaCollection->add(
                UuidListCriteria::fromIdentifiers(
                    'campaignId',
                    CriteriaInterface::IN,
                    $requestProjection->campaignIds,
                    true,
                )
            );
            $criteriaCollection->add(
                UuidCriteria::fromIdentifier(
                    'storeId',
                    CriteriaInterface::EQUAL,
                    $requestProjection->getStoreId()
                )
            );
            $criteriaCollection->add(
                UuidCriteria::fromIdentifier(
                    'customerId',
                    CriteriaInterface::EQUAL,
                    $requestProjection->customerId,
                )
            );

            yield from $this->findByCriteria($criteriaCollection);
        }
    }
}
