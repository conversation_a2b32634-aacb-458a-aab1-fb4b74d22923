<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Persistence\Doctrine\Repository;

use DateTimeImmutable;
use Doctrine\ORM\Query\ResultSetMapping;
use OpenLoyalty\Campaign\Domain\CampaignWalletTypeUsedLimitRepositoryInterface;
use OpenLoyalty\Campaign\Domain\Entity\Effect\PointsEffect;
use OpenLoyalty\Campaign\Domain\Entity\UsedLimit\CampaignWalletTypeUsedLimit;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectStatus;
use OpenLoyalty\Campaign\Domain\ValueObject\ExecutionStatus;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;

final class CampaignWalletTypeUsedLimitRepository extends DoctrineRepository implements CampaignWalletTypeUsedLimitRepositoryInterface
{
    protected function getClass(): string
    {
        return CampaignWalletTypeUsedLimit::class;
    }

    public function findByWalletType(StoreId $storeId, WalletTypeId $walletTypeId): ?CampaignWalletTypeUsedLimit
    {
        /** @var ?CampaignWalletTypeUsedLimit $usedLimit */
        $usedLimit = $this->findOneBy([
            'storeId' => $storeId,
            'walletTypeId' => $walletTypeId,
        ]);

        return $usedLimit;
    }

    public function updateAggregations(DateTimeImmutable $aggregatedAt, StoreId $storeId): void
    {
        $query = <<<SQL
            WITH agg AS (
                SELECT r.wallet_type_id, SUM(r.points) as aggregated_limit_value
                FROM campaign_execution ce
                    INNER JOIN campaign_calculated_effect_result r ON (r.campaign_execution_id = ce.campaign_execution_id)
                    INNER JOIN campaign c ON (c.campaign_id = ce.campaign_id)
                WHERE 
                    ce.status_status = :executionStatus
                    AND r.status_status = :effectStatus
                    AND c.store_id = :storeId
                    AND r.type = :effectType
                    AND ce.executed_at < :aggregatedAt
                GROUP BY r.wallet_type_id
            )
            INSERT INTO campaign_wallet_type_usage_limit (store_id, wallet_type_id, aggregated_limit_value, aggregated_at, updated_at, created_at)
            SELECT :storeId, wt.wallet_type_id, COALESCE(agg.aggregated_limit_value, 0), :aggregatedAt, NOW(), NOW()
            FROM wallet_type wt
                LEFT JOIN agg ON (agg.wallet_type_id = wt.wallet_type_id)
            WHERE wt.store_id = :storeId
            ON CONFLICT ON CONSTRAINT campaign_wallet_type_usage_limit_pkey DO UPDATE
            SET 
                aggregated_limit_value = excluded.aggregated_limit_value,
                aggregated_at = excluded.aggregated_at,
                updated_at = excluded.updated_at
        SQL;

        $nativeQuery = $this->entityManager->createNativeQuery($query, new ResultSetMapping());
        $nativeQuery->execute([
            'storeId' => $storeId,
            'executionStatus' => ExecutionStatus::SUCCESS,
            'effectStatus' => EffectStatus::SUCCESS,
            'effectType' => PointsEffect::EFFECT,
            'aggregatedAt' => $aggregatedAt,
        ]);
    }
}
