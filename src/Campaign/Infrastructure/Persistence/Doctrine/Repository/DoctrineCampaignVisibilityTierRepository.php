<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Persistence\Doctrine\Repository;

use Doctrine\ORM\AbstractQuery;
use OpenLoyalty\Campaign\Domain\CampaignVisibilityTierRepositoryInterface;
use OpenLoyalty\Campaign\Domain\Entity\Visibility\CampaignVisibilityTier;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;

final class DoctrineCampaignVisibilityTierRepository extends DoctrineRepository implements CampaignVisibilityTierRepositoryInterface
{
    protected function getClass(): string
    {
        return CampaignVisibilityTier::class;
    }

    public function countVisibleCampaignsByTierId(LevelId $tierId): int
    {
        $builder = $this->repository->createQueryBuilder('cvt');

        $builder
            ->select('count(distinct cvt.campaignId)')
            ->andWhere('cvt.tierId = :tierId')
            ->setParameter('tierId', $tierId);

        return $builder->getQuery()->getResult(AbstractQuery::HYDRATE_SINGLE_SCALAR) ?? 0;
    }
}
