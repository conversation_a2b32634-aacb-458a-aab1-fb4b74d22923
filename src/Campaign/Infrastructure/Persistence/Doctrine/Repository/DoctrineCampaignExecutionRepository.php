<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Persistence\Doctrine\Repository;

use DateTimeImmutable;
use Doctrine\DBAL\Driver\Exception;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Generator;
use OpenLoyalty\Campaign\Domain\CampaignExecutionRepositoryInterface;
use OpenLoyalty\Campaign\Domain\CampaignExecutionRepositoryReadContextInterface;
use OpenLoyalty\Campaign\Domain\CampaignUsedLimitRepositoryInterface;
use OpenLoyalty\Campaign\Domain\CampaignWalletTypeUsedLimitRepositoryInterface;
use OpenLoyalty\Campaign\Domain\Entity\CampaignExecution\CampaignExecution;
use OpenLoyalty\Campaign\Domain\Entity\Effect\PointsEffect;
use OpenLoyalty\Campaign\Domain\Entity\UsedLimit\CampaignUsedLimit;
use OpenLoyalty\Campaign\Domain\Entity\UsedLimit\UsedLimitType;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectStatus;
use OpenLoyalty\Campaign\Domain\ValueObject\ExecutionStatus;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\Search\Context\ContextInterface;
use OpenLoyalty\Core\Domain\Search\ContextQueryBuilderInterface;
use OpenLoyalty\Core\Domain\ValueObject\LimitPeriod;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\Core\Infrastructure\Persistence\ResultCacheResolver;
use OpenLoyalty\Core\Infrastructure\Search\Doctrine\Context;
use OpenLoyalty\User\Domain\Customer;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

final class DoctrineCampaignExecutionRepository extends DoctrineRepository implements CampaignExecutionRepositoryInterface, CampaignExecutionRepositoryReadContextInterface
{
    public function __construct(
        EntityManagerInterface $entityManager,
        ContextQueryBuilderInterface $queryBuilder,
        ResultCacheResolver $resultCacheResolver,
        LoggerInterface $appLogger,
        ParameterBagInterface $parameterBag,
        private readonly CampaignUsedLimitRepositoryInterface $campaignUsedLimitRepository,
        private readonly CampaignWalletTypeUsedLimitRepositoryInterface $campaignWalletTypeUsedLimitRepository
    ) {
        parent::__construct(
            $entityManager,
            $queryBuilder,
            $resultCacheResolver,
            $appLogger,
            $parameterBag
        );
    }

    protected function getClass(): string
    {
        return CampaignExecution::class;
    }

    public function save(CampaignExecution $campaignExecution): void
    {
        $this->entityManager->persist($campaignExecution);
        $this->entityManager->flush();
    }

    protected function getBaseContext(array $params): ContextInterface
    {
        $context = parent::getBaseContext($params);
        $context->getQueryBuilder()
            ->leftJoin(Context::DEFAULT_ALIAS.'.campaign', 'c')
            ->andWhere('c IS NOT NULL');

        return $context;
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function getUsedPointsByLimitPeriodAndWalletCode(
        StoreId $storeId,
        WalletTypeId $walletTypeId,
        ?LimitPeriod $limitPeriod,
        ?CustomerId $customerId = null,
        bool $withQuickCache = false
    ): float {
        // try to use aggregated value (performance)
        if (null === $limitPeriod && null === $customerId) {
            /** @var CampaignUsedLimit $aggregation */
            $aggregation = $this->campaignWalletTypeUsedLimitRepository->findByWalletType($storeId, $walletTypeId);

            // if there is aggregation then use it and plus rest of values
            if (null !== $aggregation) {
                $aggValue = $this->getUsedPointsByLimitPeriodAndWalletCodeQuery($walletTypeId, $aggregation->getAggregatedAt(), null, null, $withQuickCache);

                return $aggValue + $aggregation->getAggregatedLimitValue();
            }
        }

        return $this->getUsedPointsByLimitPeriodAndWalletCodeQuery(
            $walletTypeId,
            $limitPeriod?->getStart(),
            $limitPeriod?->getEnd(),
            $customerId,
            $withQuickCache
        );
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function getUsedPointsByLimitPeriod(
        StoreId $storeId,
        CampaignId $campaignId,
        ?LimitPeriod $limitPeriod,
        ?CustomerId $customerId = null,
        bool $withQuickCache = false
    ): float {
        // try to use aggregated value (performance)
        if (null === $limitPeriod && null === $customerId) {
            /** @var CampaignUsedLimit $aggregation */
            $aggregation = $this->campaignUsedLimitRepository->findByType($storeId, $campaignId, UsedLimitType::UNITS);

            // if there is aggregation then use it and plus rest of values
            if (null !== $aggregation) {
                $aggValue = $this->getUsedPointsByLimitPeriodQuery($campaignId, $aggregation->getAggregatedAt(), null, null, $withQuickCache);

                return $aggValue + $aggregation->getAggregatedLimitValue();
            }
        }

        return $this->getUsedPointsByLimitPeriodQuery(
            $campaignId,
            $limitPeriod?->getStart(),
            $limitPeriod?->getEnd(),
            $customerId,
            $withQuickCache
        );
    }

    public function getExecutionsByLimitPeriod(
        StoreId $storeId,
        CampaignId $campaignId,
        ?LimitPeriod $limitPeriod,
        ?CustomerId $customerId = null,
        bool $withQuickCache = false
    ): int {
        // try to use aggregated value (performance)
        if (null === $limitPeriod && null === $customerId) {
            /** @var CampaignUsedLimit $aggregation */
            $aggregation = $this->campaignUsedLimitRepository->findByType($storeId, $campaignId, UsedLimitType::COMPLETIONS);

            // if there is aggregation then use it and plus rest of values
            if (null !== $aggregation) {
                $aggValue = $this->getExecutionsByLimitPeriodQuery($campaignId, $aggregation->getAggregatedAt(), null, null, $withQuickCache);

                return $aggValue + (int) $aggregation->getAggregatedLimitValue();
            }
        }

        return $this->getExecutionsByLimitPeriodQuery(
            $campaignId,
            $limitPeriod?->getStart(),
            $limitPeriod?->getEnd(),
            $customerId,
            $withQuickCache
        );
    }

    /**
     * @return int                      the number of deleted rows
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function deleteByStatus(string $status, int $limit): int
    {
        $connection = $this->entityManager->getConnection();
        $query = 'WITH rows AS (SELECT ce.campaign_execution_id FROM campaign_execution ce WHERE ce.status_status = :status LIMIT :limit)
        DELETE FROM campaign_execution ce WHERE ce.campaign_execution_id IN (SELECT * FROM rows);';

        return $connection->prepare($query)->executeStatement(
            [
                'status' => $status,
                'limit' => $limit,
            ]
        );
    }

    public function findCustomersByCampaignCompletion(
        StoreId $storeId,
        CampaignId $campaignId,
        int $completionNumber,
        DateTimeImmutable $fromDate,
        DateTimeImmutable $toDate,
    ): Generator {
        if (0 === $completionNumber) {
            $subQuery = $this->repository->createQueryBuilder('e')
                ->distinct()
                ->select('r.target')
                ->join('e.calculatedEffectResults', 'r')
                ->join('e.campaign', 'c')
                ->andWhere('c.store = :storeId')
                ->andWhere('c.campaignId = :campaignId')
                ->andWhere('e.status.code = :executionStatus')
                ->andWhere('r.status.code = :status')
                ->andWhere('e.executedAt >= :fromDate')
                ->andWhere('e.executedAt < :toDate')
                ->groupBy('r.target')
                ->having('COUNT(DISTINCT e.campaignExecutionId) > :completionNumber');

            $queryBuilder = $this->entityManager->createQueryBuilder();
            $queryBuilder
                ->distinct()
                ->select('cus.id as customerId')
                ->from(Customer::class, 'cus')
                ->where('cus.storeId = :storeId')
                ->andWhere('cus.active = true')
                ->andWhere('cus.deletedAt IS NULL')
                ->andWhere($queryBuilder->expr()->notIn('cus.id', $subQuery->getDQL()))
                ->setParameter('storeId', $storeId)
                ->setParameter('campaignId', $campaignId)
                ->setParameter('executionStatus', ExecutionStatus::SUCCESS)
                ->setParameter('status', EffectStatus::SUCCESS)
                ->setParameter('completionNumber', $completionNumber)
                ->setParameter('fromDate', $fromDate)
                ->setParameter('toDate', $toDate);
        } else {
            $queryBuilder = $this->repository->createQueryBuilder('e')
                ->distinct()
                ->select('r.target as customerId')
                ->join('e.calculatedEffectResults', 'r')
                ->join('e.campaign', 'c')
                ->andWhere('c.store = :storeId')
                ->andWhere('c.campaignId = :campaignId')
                ->andWhere('e.status.code = :executionStatus')
                ->andWhere('r.status.code = :status')
                ->andWhere('e.executedAt >= :fromDate')
                ->andWhere('e.executedAt < :toDate')
                ->groupBy('r.target')
                ->having('COUNT(DISTINCT e.campaignExecutionId) = :completionNumber')
                ->setParameter('storeId', $storeId)
                ->setParameter('campaignId', $campaignId)
                ->setParameter('executionStatus', ExecutionStatus::SUCCESS)
                ->setParameter('status', EffectStatus::SUCCESS)
                ->setParameter('completionNumber', $completionNumber)
                ->setParameter('fromDate', $fromDate)
                ->setParameter('toDate', $toDate);
        }

        foreach ($this->toIterableWithClearEntityManagerCache($queryBuilder->getQuery()) as $row) {
            yield $row['customerId'];
        }
    }

    protected function getExecutionsByLimitPeriodQuery(
        CampaignId $campaignId,
        ?DateTimeImmutable $executedAtFrom = null,
        ?DateTimeImmutable $executedAtTo = null,
        ?CustomerId $customerId = null,
        bool $withQuickCache = false
    ): int {
        $queryBuilder = $this->repository->createQueryBuilder('e')
            ->select('COUNT(e.campaignExecutionId)')
            ->join('e.campaign', 'c')
            ->andWhere('e.status.code = :executionStatus')
            ->andWhere('c.campaignId = :campaignId')
            ->setParameter('executionStatus', ExecutionStatus::SUCCESS)
            ->setParameter('campaignId', $campaignId);

        if (null !== $customerId) {
            $queryBuilder
                ->andWhere('e.customerId = :customerId')
                ->setParameter('customerId', $customerId);
        }

        if (null !== $executedAtFrom) {
            $queryBuilder
                ->andWhere('e.executedAt >= :fromDate')
                ->setParameter('fromDate', $executedAtFrom);
        }

        if (null !== $executedAtTo) {
            $queryBuilder
                ->andWhere('e.executedAt <= :toDate')
                ->setParameter('toDate', $executedAtTo);
        }

        $query = $withQuickCache && null === $customerId ? $this->resultCacheResolver->resolveQuick($queryBuilder->getQuery()) : $queryBuilder->getQuery();

        return (int) $query->getSingleScalarResult();
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    protected function getUsedPointsByLimitPeriodQuery(
        CampaignId $campaignId,
        ?DateTimeImmutable $executedAtFrom = null,
        ?DateTimeImmutable $executedAtTo = null,
        ?CustomerId $customerId = null,
        bool $withQuickCache = false
    ): float {
        $queryBuilder = $this->repository->createQueryBuilder('e')
            ->select('SUM(r.points)')
            ->join('e.calculatedEffectResults', 'r')
            ->join('e.campaign', 'c')
            ->andWhere('e.status.code = :executionStatus')
            ->andWhere('c.campaignId = :campaignId')
            ->andWhere('r.status.code = :status')
            ->andWhere('r.type = :type')
            ->setParameter('executionStatus', ExecutionStatus::SUCCESS)
            ->setParameter('campaignId', $campaignId)
            ->setParameter('status', EffectStatus::SUCCESS)
            ->setParameter('type', PointsEffect::EFFECT);

        if (null !== $customerId) {
            $queryBuilder
                ->andWhere('r.target = :customerId')
                ->setParameter('customerId', $customerId);
        }

        if (null !== $executedAtFrom) {
            $queryBuilder
                ->andWhere('e.executedAt >= :fromDate')
                ->setParameter('fromDate', $executedAtFrom);
        }

        if (null !== $executedAtTo) {
            $queryBuilder
                ->andWhere('e.executedAt <= :toDate')
                ->setParameter('toDate', $executedAtTo);
        }

        $query = $withQuickCache && null === $customerId ? $this->resultCacheResolver->resolveQuick($queryBuilder->getQuery()) : $queryBuilder->getQuery();

        return abs((float) $query->getSingleScalarResult());
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    protected function getUsedPointsByLimitPeriodAndWalletCodeQuery(
        WalletTypeId $walletTypeId,
        ?DateTimeImmutable $executedAtFrom = null,
        ?DateTimeImmutable $executedAtTo = null,
        ?CustomerId $customerId = null,
        bool $withQuickCache = false
    ): float {
        $queryBuilder = $this->repository->createQueryBuilder('e')
            ->select('SUM(r.points)')
            ->join('e.calculatedEffectResults', 'r')
            ->andWhere('e.status.code = :executionStatus')
            ->andWhere('r.walletTypeId = :walletTypeId')
            ->andWhere('r.status.code = :status')
            ->andWhere('r.type = :type')
            ->setParameter('executionStatus', ExecutionStatus::SUCCESS)
            ->setParameter('walletTypeId', $walletTypeId)
            ->setParameter('status', EffectStatus::SUCCESS)
            ->setParameter('type', PointsEffect::EFFECT);

        if (null !== $customerId) {
            $queryBuilder
                ->andWhere('r.target = :customerId')
                ->setParameter('customerId', $customerId);
        }

        if (null !== $executedAtFrom) {
            $queryBuilder
                ->andWhere('e.executedAt >= :fromDate')
                ->setParameter('fromDate', $executedAtFrom);
        }

        if (null !== $executedAtTo) {
            $queryBuilder
                ->andWhere('e.executedAt <= :toDate')
                ->setParameter('toDate', $executedAtTo);
        }

        $query = $withQuickCache && null === $customerId ? $this->resultCacheResolver->resolveQuick($queryBuilder->getQuery()) : $queryBuilder->getQuery();

        return abs((float) $query->getSingleScalarResult());
    }
}
