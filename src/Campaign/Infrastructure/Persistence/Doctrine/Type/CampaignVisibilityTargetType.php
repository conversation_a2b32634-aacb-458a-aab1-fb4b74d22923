<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Persistence\Doctrine\Type;

use OpenLoyalty\Campaign\Domain\Enum\VisibilityTarget;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\AbstractEnumType;

final class CampaignVisibilityTargetType extends AbstractEnumType
{
    public const NAME = 'campaign_visibility_target_type';

    public function getName(): string
    {
        return self::NAME;
    }

    public static function getEnumsClass(): string
    {
        return VisibilityTarget::class;
    }
}
