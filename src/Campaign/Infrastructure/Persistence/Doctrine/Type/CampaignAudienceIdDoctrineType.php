<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Persistence\Doctrine\Type;

use Assert\AssertionFailedException;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use OpenLoyalty\Core\Domain\Id\CampaignAudienceId;
use Ramsey\Uuid\Doctrine\UuidType;

final class CampaignAudienceIdDoctrineType extends UuidType
{
    public const NAME = 'campaign_audience_id';

    /**
     * @throws AssertionFailedException
     */
    public function convertToPHPValue($value, AbstractPlatform $platform): ?CampaignAudienceId
    {
        if (empty($value)) {
            return null;
        }

        if ($value instanceof CampaignAudienceId) {
            return $value;
        }

        return new CampaignAudienceId($value);
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value instanceof CampaignAudienceId) {
            return (string) $value;
        }

        if (!empty($value)) {
            return $value;
        }

        return null;
    }

    public function getName(): string
    {
        return self::NAME;
    }

    public function requiresSQLCommentHint(AbstractPlatform $platform): bool
    {
        return true;
    }
}
