OpenLoyalty\Campaign\Domain\Entity\Visibility\CampaignVisibility:
    type: entity
    table: campaign_visibility
    cache:
        usage: NONSTRICT_READ_WRITE
        region: campaign
    id:
        campaignVisibilityId:
            type: campaign_visibility_id
    fields:
        storeId:
            type: store_id
            nullable: false
        target:
            type: campaign_visibility_target_type
            nullable: true
            options:
                default: null
        createdAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        updatedAt:
            type: datetime_immutable_microseconds
            options:
                default: CURRENT_TIMESTAMP
        createdBy:
            type: string
            nullable: true
        updatedBy:
            type: string
            nullable: true
    oneToOne:
        campaign:
            targetEntity: OpenLoyalty\Campaign\Domain\Campaign
            mappedBy: visibility
            cache:
                usage: NONSTRICT_READ_WRITE
                region: campaign
    oneToMany:
        segments:
            targetEntity: OpenLoyalty\Campaign\Domain\Entity\Visibility\CampaignVisibilitySegment
            cascade: [ "ALL" ]
            mappedBy: campaignVisibility
            orphanRemoval: true
            onDelete: CASCADE
        tiers:
            targetEntity: OpenLoyalty\Campaign\Domain\Entity\Visibility\CampaignVisibilityTier
            cascade: [ "ALL" ]
            mappedBy: campaignVisibility
            orphanRemoval: true
            onDelete: CASCADE
