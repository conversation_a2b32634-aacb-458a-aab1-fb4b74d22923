OpenLoyalty\Campaign\Domain\Entity\Code\CampaignCode:
    type: entity
    table: campaign_code
    indexes:
        campaignCodeIdx:
            columns: [ code ]
        campaignCodeCreatedAtIdx:
            columns: [ created_at ]
    id:
        codeId:
            type: campaign_code_id
    fields:
        storeId:
            type: store_id
        code:
            type: text
        usedByMemberId:
            type: customer_id
            nullable: true
        usedAt:
            type: datetime_immutable
            nullable: true
        createdAt:
            type: datetime_immutable_microseconds
        updatedAt:
            type: datetime_immutable_microseconds
        createdBy:
            type: string
            nullable: true
        updatedBy:
            type: string
            nullable: true
    manyToOne:
        campaign:
            targetEntity: OpenLoyalty\Campaign\Domain\Campaign
            joinColumn:
                nullable: false
                name: campaign_id
                referencedColumnName: campaign_id
                onDelete: CASCADE
    embedded:
        status:
            class: OpenLoyalty\Campaign\Domain\ValueObject\CodeStatus
    uniqueConstraints:
        unique_campaign_code_idx:
            columns: [ store_id, code ]
