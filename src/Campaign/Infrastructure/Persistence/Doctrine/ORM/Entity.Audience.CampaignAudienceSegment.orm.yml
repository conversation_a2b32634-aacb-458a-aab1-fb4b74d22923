OpenLoyalty\Campaign\Domain\Entity\Audience\CampaignAudienceSegment:
    type: entity
    table: campaign_audience_segment
    id:
        id:
            type: integer
            generator:
                strategy: AUTO
    fields:
        campaignId:
            type: campaign_id
            nullable: false
        segmentId:
            type: segment_id
            nullable: false
    manyToOne:
        campaignAudience:
            targetEntity: OpenLoyalty\Campaign\Domain\Entity\Audience\CampaignAudience
            inversedBy: segments
            joinColumn:
                name: campaign_audience_id
                referencedColumnName: campaign_audience_id
