OpenLoyalty\Campaign\Domain\Entity\Visibility\CampaignVisibilityTier:
    type: entity
    table: campaign_visibility_tier
    id:
        id:
            type: integer
            generator:
                strategy: AUTO
    fields:
        campaignId:
            type: campaign_id
            nullable: false
        tierId:
            type: level_id
            nullable: false
    manyToOne:
        campaignVisibility:
            targetEntity: OpenLoyalty\Campaign\Domain\Entity\Visibility\CampaignVisibility
            inversedBy: tiers
            joinColumn:
                name: campaign_visibility_id
                referencedColumnName: campaign_visibility_id
    indexes:
        campaignVisibilityTierIdx:
            columns: [ tier_id ]