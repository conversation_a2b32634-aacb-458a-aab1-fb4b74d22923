<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Security\Voter;

use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\PermissionAccess;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * @todo extract another Voter for Achievement context (OLOY-11989)
 *
 * @extends Voter<string, mixed>
 */
class CampaignVoter extends Voter
{
    public const PERMISSION_RESOURCE = 'CAMPAIGN';

    public const CREATE = 'CREATE_CAMPAIGN';
    public const UPDATE = 'UPDATE_CAMPAIGN';
    public const DELETE = 'DELETE_CAMPAIGN';
    public const LIST = 'LIST_CAMPAIGNS';
    public const GET = 'GET_CAMPAIGNS';
    public const SIMULATE = 'SIMULATE';
    public const VIEW_AVAILABLE = 'VIEW_AVAILABLE';
    public const LIST_CODES = 'LIST_CODES';
    public const GENERATE_CODES = 'GENERATE_CODES';

    public const ACHIEVEMENT_TRIGGERS = 'ACHIEVEMENT_TRIGGERS';

    public function __construct(protected UserPermissionCheckerInterface $permissionChecker)
    {
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return in_array($attribute, [self::GET, self::UPDATE, self::DELETE]) ||
               (null === $subject && in_array($attribute, [self::CREATE, self::LIST, self::SIMULATE, self::GENERATE_CODES, self::LIST_CODES, self::ACHIEVEMENT_TRIGGERS])) ||
               ($subject instanceof Customer && self::VIEW_AVAILABLE == $attribute);
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        /** @var User $user */
        $user = $token->getUser();

        if (!$user instanceof User) {
            return false;
        }
        $viewAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionInCurrentStore(
                $user,
                self::PERMISSION_RESOURCE,
                [PermissionAccess::VIEW]
            );

        $fullAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionInCurrentStore(
            $user,
            self::PERMISSION_RESOURCE,
            [PermissionAccess::VIEW, PermissionAccess::MODIFY]
        );

        return match ($attribute) {
            self::CREATE, self::UPDATE, self::DELETE, self::GENERATE_CODES => $fullAdmin,
            self::LIST, self::ACHIEVEMENT_TRIGGERS, self::GET, self::SIMULATE, self::LIST_CODES => $viewAdmin,
            self::VIEW_AVAILABLE => $viewAdmin || $this->canCustomerView($user, $subject),
            default => false,
        };
    }

    private function canCustomerView(User $user, Customer $customer): bool
    {
        return $user->hasRole('ROLE_PARTICIPANT')
            && $customer->getCustomerId()
            && (string) $customer->getCustomerId() === $user->getId();
    }
}
