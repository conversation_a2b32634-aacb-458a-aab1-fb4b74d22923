<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Validator\Constraint;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

class MemberExistsValidator extends ConstraintValidator
{
    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly TranslatorInterface $translator,
        private readonly StoreContextProviderInterface $storeContextProvider,
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        /**
         * @var $value \OpenLoyalty\Campaign\Domain\Context\Customer
         */
        if (null === $value || null === $value->id) {
            return;
        }
        $storeId = $this->storeContextProvider->getStore()->getStoreId();
        $member = $this->customerRepository->exist($storeId, new CustomerId($value->id));

        if (null === $member) {
            $this->context
                ->buildViolation($this->translator->trans('campaign.member_not_exist'))
                ->setParameter('{{ memberId }}', $value->id)
                ->addViolation();
        }
    }
}
