<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Validator\Constraint;

use ArgumentCountError;
use OpenLoyalty\Campaign\Domain\AccountFacadeInterface;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\CustomEvent\Domain\CustomEventSchemaRepositoryInterface;
use OpenLoyalty\CustomEvent\Infrastructure\Service\SchemaMocker;
use OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepositoryInterface;
use OpenLoyalty\Tools\ExpressionLanguage\ValueExpressionLanguageInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Contracts\Translation\TranslatorInterface;

class EffectExpressionValidValidator extends ExpressionConstraintValidator
{
    public function __construct(
        private readonly ValueExpressionLanguageInterface $expressionLanguage,
        private readonly TranslatorInterface $translator,
        CustomEventSchemaRepositoryInterface $customEventSchemaRepository,
        InternalEventSchemaRepositoryInterface $internalEventSchemaRepository,
        StoreContextProviderInterface $storeContextProvider,
        SchemaMocker $schemaMocker,
        AccountFacadeInterface $accountFacade
    ) {
        parent::__construct(
            $customEventSchemaRepository,
            $internalEventSchemaRepository,
            $storeContextProvider,
            $schemaMocker,
            $accountFacade
        );
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (null === $value) {
            return;
        }

        if (!$constraint instanceof EffectExpressionValid && (null === $constraint->type || null === $constraint->trigger)) {
            $this->context->buildViolation($this->translator->trans('campaign.invalid_effect'))->addViolation();
        }

        try {
            $this->expressionLanguage->evaluate(
                $value, //@phpstan-ignore-line
                $this->getExpressionContext($constraint)->getContextDSL());
        } catch (ArgumentCountError) {
            $this->context->buildViolation(
                sprintf(
                    '%s Function: %s',
                    $this->translator->trans('core.invalid_expression_function'),
                    $value
                ),
            )->addViolation();
        } catch (\Throwable $ex) {
            $this->context->buildViolation($ex->getMessage(), ['expression' => $value])->addViolation();

            return;
        }
    }
}
