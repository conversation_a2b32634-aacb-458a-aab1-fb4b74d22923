<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Validator\Constraint;

use Symfony\Component\Validator\Constraint;

class EffectExpressionValid extends Constraint
{
    public ?string $type;

    public ?string $trigger;

    public ?string $event;

    public function getDefaultOption(): string
    {
        return 'trigger';
    }

    public function getRequiredOptions(): array
    {
        return ['type', 'trigger'];
    }
}
