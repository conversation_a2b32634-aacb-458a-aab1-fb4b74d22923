<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Validator\Constraint;

use OpenLoyalty\Campaign\Domain\AccountFacadeInterface;
use OpenLoyalty\Campaign\Domain\Entity\Effect\ExpirationStrategy;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\CustomEvent\Domain\CustomEventSchemaRepositoryInterface;
use OpenLoyalty\CustomEvent\Infrastructure\Service\SchemaMocker;
use OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepositoryInterface;
use OpenLoyalty\Tools\ExpressionLanguage\InvalidExpressionException;
use OpenLoyalty\Tools\ExpressionLanguage\ValueExpressionLanguageInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Contracts\Translation\TranslatorInterface;
use Throwable;

final class CustomExpirationExpressionValidValidator extends ExpressionConstraintValidator
{
    public function __construct(
        private readonly ValueExpressionLanguageInterface $expressionLanguage,
        private readonly TranslatorInterface $translator,
        CustomEventSchemaRepositoryInterface $customEventSchemaRepository,
        InternalEventSchemaRepositoryInterface $internalEventSchemaRepository,
        StoreContextProviderInterface $storeContextProvider,
        SchemaMocker $schemaMocker,
        AccountFacadeInterface $accountFacade
    ) {
        parent::__construct(
            $customEventSchemaRepository,
            $internalEventSchemaRepository,
            $storeContextProvider,
            $schemaMocker,
            $accountFacade
        );
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (ExpirationStrategy::Expression->value === $constraint->strategy && null === $value) {
            $this->context->buildViolation($this->translator->trans('campaign.expiration_points_strategy.invalid_expiration_strategy'))->addViolation();
        }

        if (null === $value) {
            return;
        }

        if (!$constraint instanceof EffectExpressionValid && (null === $constraint->trigger)) {
            $this->context->buildViolation($this->translator->trans('campaign.expiration_points_strategy.invalid_expression'))->addViolation();
        }

        try {
            $this->expressionLanguage->evaluate(
                $value, //@phpstan-ignore-line
                $this->getExpressionContext($constraint)->getContextDSL()
            );
        } catch (InvalidExpressionException $exception) {
            $this->context->buildViolation($exception->getMessage(), ['expression' => $value])->addViolation();
        } catch (Throwable) {
            $this->context->buildViolation($this->translator->trans('campaign.expiration_points_strategy.invalid_expression'))->addViolation();
        }
    }
}
