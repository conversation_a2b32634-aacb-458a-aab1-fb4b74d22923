<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Validator\Constraint;

use OpenLoyalty\Core\Domain\Id\StoreId;
use Symfony\Component\Validator\Constraint;

final class CanBeDeletedValid extends Constraint
{
    public ?StoreId $storeId = null;

    public function getDefaultOption(): string
    {
        return 'storeId';
    }

    public function getRequiredOptions(): array
    {
        return ['storeId'];
    }
}
