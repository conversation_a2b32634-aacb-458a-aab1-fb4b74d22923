<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Validator\Constraint;

use OpenLoyalty\Core\Domain\AccountFacadeInterface;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

class WalletTypeValidValidator extends ConstraintValidator
{
    public function __construct(
        private readonly AccountFacadeInterface $accountFacade,
        private readonly StoreContextProviderInterface $storeContextProvider,
        private readonly TranslatorInterface $translator
    ) {
    }

    /**
     * @param string $value
     */
    public function validate(mixed $value, Constraint $constraint): void
    {
        if (empty($value) && !is_string($value)) {
            return;
        }

        $storeId = $this->storeContextProvider->getStore()->getStoreId();
        $walletType = $this->accountFacade->getWalletType($storeId, $value);

        if (null === $walletType) {
            $this->context->buildViolation($this->translator->trans($constraint->message))->addViolation();
        }
    }
}
