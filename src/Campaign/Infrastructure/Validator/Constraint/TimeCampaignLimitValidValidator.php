<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Validator\Constraint;

use OpenLoyalty\Campaign\Domain\CampaignRepositoryInterface;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

final class TimeCampaignLimitValidValidator extends ConstraintValidator
{
    public function __construct(
        private readonly CampaignRepositoryInterface $campaignRepository,
        private readonly TranslatorInterface $translator,
        private readonly StoreContextProviderInterface $storeContextProvider,
        private readonly int $limit
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (Trigger::TIME !== $value) {
            return;
        }

        $storeId = $this->storeContextProvider->getStore()->getStoreId();
        $count = $this->campaignRepository->getCountByTrigger($storeId, Trigger::create(Trigger::TIME));

        if ($this->limit <= $count) {
            $this->context->buildViolation(
                $this->translator->trans('campaign.time_campaign.limit_reached'))
                ->setParameter('{{ limit }}', (string) $this->limit)
                ->addViolation();
        }
    }
}
