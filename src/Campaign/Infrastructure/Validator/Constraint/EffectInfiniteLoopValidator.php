<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Validator\Constraint;

use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepository;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

class EffectInfiniteLoopValidator extends ConstraintValidator
{
    public function __construct(
        private readonly TranslatorInterface $translator
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (null === $value) {
            return;
        }

        if (!$constraint instanceof EffectInfiniteLoop
            && (null === $constraint->trigger || null === $constraint->event)) {
            $this->context
                ->buildViolation($this->translator->trans('campaign.invalid_effect'))
                ->addViolation();
        }

        if (Trigger::INTERNAL_EVENT === $constraint->trigger
            && InternalEventSchemaRepository::MEMBER_DETAILS_CHANGED === $constraint->event) {
            $this->context
                ->buildViolation($this->translator->trans('campaign.potential_infinite_campaign_loop'))
                ->addViolation();
        }
    }
}
