<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Validator\Constraint;

use OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepositoryInterface;
use <PERSON>ymfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

class InternalEventExistsValidator extends ConstraintValidator
{
    public function __construct(
        private readonly TranslatorInterface $translator,
        private readonly InternalEventSchemaRepositoryInterface $internalEventSchemaRepository
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (null === $value) {
            return;
        }

        if (in_array($value, $this->getInternalEvents(), true)) {
            return;
        }

        $this->context
            ->buildViolation($this->translator->trans('campaign.internal_event_not_exist'))
            ->setParameter('{{ event }}', $value)
            ->addViolation();
    }

    /**
     * @return array<string>
     */
    private function getInternalEvents(): array
    {
        return array_keys($this->internalEventSchemaRepository->findAll());
    }
}
