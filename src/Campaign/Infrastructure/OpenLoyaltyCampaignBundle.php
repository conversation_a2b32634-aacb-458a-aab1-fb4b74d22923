<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure;

use Doctrine\Bundle\DoctrineBundle\DependencyInjection\Compiler\DoctrineOrmMappingsPass;
use OpenLoyalty\Campaign\Infrastructure\Persistence\Doctrine\Type\TransactionItemsFiltersArrayDoctrineType;
use OpenLoyalty\Core\Infrastructure\AbstractOpenLoyaltyBundle;
use Symfony\Component\DependencyInjection\ContainerBuilder;

class OpenLoyaltyCampaignBundle extends AbstractOpenLoyaltyBundle
{
    public function __construct()
    {
        parent::__construct([
            'transaction_items_filter_array' => TransactionItemsFiltersArrayDoctrineType::class,
        ]);
    }

    public function build(ContainerBuilder $container): void
    {
        parent::build($container);
        $container->addCompilerPass($this->buildMappingCompilerPass());
    }

    public function buildMappingCompilerPass(): DoctrineOrmMappingsPass
    {
        return DoctrineOrmMappingsPass::createYamlMappingDriver(
            [
                __DIR__.'/Persistence/Doctrine/ORM' => 'OpenLoyalty\Campaign\Domain',
                __DIR__.'/Persistence/ReadModel/ORM' => 'OpenLoyalty\Campaign\Domain\ReadModel\Entity',
            ],
        );
    }
}
