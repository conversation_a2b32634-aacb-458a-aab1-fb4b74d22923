OpenLoyalty\Campaign\Domain\ValueObject\Limits:
    exclusion_policy: ALL
    virtual_properties:
        getPoints:
            expose: true
            skip_when_empty: true
            serialized_name: points
            exp: object.getUnitsLimit()
        getPointsPerMember:
            expose: true
            skip_when_empty: true
            serialized_name: pointsPerMember
            exp: object.getUnitsPerMemberLimit()
        getExecutionsPerMemberLimit:
            expose: true
            skip_when_empty: true
            serialized_name: executionsPerMember
            exp: object.getExecutionsPerMemberLimit()
