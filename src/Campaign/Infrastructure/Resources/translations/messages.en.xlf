<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="en" target-language="en" datatype="plaintext" original="file.ext">
        <body>
            <trans-unit id="campaign.invalid_type">
                <source>campaign.invalid_type</source>
                <target>Invalid campaign type.</target>
            </trans-unit>
            <trans-unit id="campaign.invalid_trigger">
                <source>campaign.invalid_trigger</source>
                <target>Invalid campaign trigger.</target>
            </trans-unit>
            <trans-unit id="campaign.invalid_simulate_trigger">
                <source>campaign.invalid_simulate_trigger</source>
                <target>Invalid campaign simulation trigger.</target>
            </trans-unit>
            <trans-unit id="campaign.invalid_rule">
                <source>campaign.invalid_rule</source>
                <target>Invalid campaign rule.</target>
            </trans-unit>
            <trans-unit id="campaign.required_type_and_trigger">
                <source>campaign.required_type_and_trigger</source>
                <target>Campaign type and trigger are required.</target>
            </trans-unit>
            <trans-unit id="campaign.cannot_change_type">
                <source>campaign.cannot_change_type</source>
                <target>Cannot change the campaign's type.</target>
            </trans-unit>
            <trans-unit id="campaign.invalid_condition">
                <source>campaign.invalid_condition</source>
                <target>Invalid campaign rule condition (operator or data).</target>
            </trans-unit>
            <trans-unit id="campaign_type_is_invalid">
                <source>campaign_type_is_invalid</source>
                <target>Campaign type is not valid.</target>
             </trans-unit>
            <trans-unit id="campaign.invalid_effect">
                <source>campaign.invalid_effect</source>
                <target>Invalid campaign rule effect data.</target>
            </trans-unit>
            <trans-unit id="campaign.syntax_error">
                <source>campaign.syntax_error</source>
                <target>Syntax error (invalid attribute, function or datatype)</target>
            </trans-unit>
            <trans-unit id="campaign.custom_event_schema_exists">
                <source>campaign.custom_event_schema_exists</source>
                <target>Custom event schema {{ eventType }} exists</target>
            </trans-unit>
            <trans-unit id="campaign.custom_event_schema_not_exist">
                <source>campaign.custom_event_schema_not_exist</source>
                <target>Custom event schema {{ eventType }} does not exist</target>
            </trans-unit>
            <trans-unit id="campaign.custom_event_schema.non_unique_field_name">
                <source>campaign.custom_event_schema.non_unique_field_name</source>
                <target>Custom event schema is not valid. All fields' names should be unique.</target>
            </trans-unit>
            <trans-unit id="campaign.internal_event_not_exist">
                <source>campaign.internal_event_not_exist</source>
                <target>Internal event: {{ event }} does not exist</target>
            </trans-unit>
            <trans-unit id="campaign.badge_type_not_exist">
                <source>campaign.badge_type_not_exist</source>
                <target>Badge type: {{ badgeTypeId }} does not exist</target>
            </trans-unit>
            <trans-unit id="campaign.custom_event_attribute_not_exist">
                <source>campaign.custom_event_attribute_not_exist</source>
                <target>Custom event: {{ event }} attribute: {{ attribute }} does not exist</target>
            </trans-unit>
            <trans-unit id="campaign.member_filter_strategy_not_exist">
                <source>campaign.member_filter_strategy_not_exist</source>
                <target>Member filter: {{ strategy }} does not exist</target>
            </trans-unit>
            <trans-unit id="campaign.achievement_not_exist">
                <source>campaign.achievement_not_exist</source>
                <target>Achievement {{ achievementId }} does not exist</target>
            </trans-unit>
            <trans-unit id="campaign.member_not_exist">
                <source>campaign.member_not_exist</source>
                <target>Member {{ memberId }} does not exist</target>
            </trans-unit>
            <trans-unit id="campaign.potential_infinite_campaign_loop">
                <source>campaign.potential_infinite_campaign_loop</source>
                <target>You’re using the Member custom attributes as a trigger and campaign effect. Please change the campaign trigger or effect to avoid an infinite campaign loop.</target>
            </trans-unit>
            <trans-unit id="campaign.expiration_points_strategy.invalid_expression">
                <source>campaign.expiration_points_strategy.invalid_expression</source>
                <target>Provided expression is invalid, please check used functions and parameters.</target>
            </trans-unit>
            <trans-unit id="campaign.expiration_points_strategy.invalid_expiration_strategy">
                <source>campaign.expiration_points_strategy.invalid_expiration_strategy</source>
                <target>Provided expiration strategy is invalid for empty value, please check available strategies.</target>
            </trans-unit>
            <trans-unit id="campaign.audience_target.limit_reached">
                <source>campaign.audience_target.limit_reached</source>
                <target>The campaign target reached limit of {{ limit }} members</target>
            </trans-unit>
            <trans-unit id="campaign.time_campaign.limit_reached">
                <source>campaign.time_campaign.limit_reached</source>
                <target>The limit of {{ limit }} time campaigns has been reached</target>
            </trans-unit>
            <trans-unit id="campaign.execution_schedule_is_require">
                <source>campaign.execution_schedule_is_require</source>
                <target>The field is require for campaign for trigger strategy {{ triggerStrategy }}</target>
            </trans-unit>
            <trans-unit id="campaign.cannot_delete_used_campaign">
                <source>campaign.cannot_delete_used_campaign</source>
                <target>To delete the campaign, it must first be removed from all associated segments conditions.</target>
            </trans-unit>
            <trans-unit id="campaign.global_active_campaign_limit_reached">
                <source>campaign.global_active_campaign_limit_reached</source>
                <target>You have reached the global limit of {{campaign_limit}} active campaigns. To activate this campaign, please deactivate or delete existing ones, or contact your Customer Success Manager or the support <NAME_EMAIL>.</target>
            </trans-unit>
            <trans-unit id="campaign.global_active_redemption_code_limit_reached">
                <source>campaign.global_active_redemption_code_limit_reached</source>
                <target>You have reached the global limit of {{redemption_code_limit}} active redemption codes. To add more coupons to this campaign, please deactivate or delete other campaigns with redemption codes, delete existing codes, or contact your Customer Success Manager or the support <NAME_EMAIL>.</target>
            </trans-unit>
            <trans-unit id="campaign.lock_points_strategy.invalid_lock_strategy">
                <source>campaign.lock_points_strategy.invalid_lock_strategy</source>
                <target>Provided lock strategy is invalid for empty value, please check available strategies.</target>
            </trans-unit>
            <trans-unit id="campaign.lock_points_strategy.invalid_expression">
                <source>campaign.lock_points_strategy.invalid_expression</source>
                <target>Provided expression is invalid, please check used functions and parameters.</target>
            </trans-unit>
            <trans-unit id="campaign.transaction_item_filter_code_unique">
                <source>campaign.transaction_item_filter_code_unique</source>
                <target>The code {{code}} is not unique.</target>
            </trans-unit>
            <trans-unit id="campaign.invalidPayload">
                <source>campaign.invalidPayload</source>
                <target>Invalid request payload.</target>
            </trans-unit>
        </body>
    </file>
</xliff>
