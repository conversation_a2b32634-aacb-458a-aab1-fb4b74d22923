usa:
  campaign1:
    uuid: '7239e6f7-e52e-466b-8c24-699709b64076'
    name: 'Earn 1 point for every $1 spent'
    description: 'Earn 1 point for every $1 spent (without "for adult" items)'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect: 'give_points'
            pointsRule: "round_up(transaction.grossValue - transaction.category('for_adult').grossValue)"
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign2:
    uuid: 'a3373706-fcdf-4845-867b-659f55632956'
    name: 'Get 100 points for Bluetooth JBL GO 2 Pink'
    description: 'Get 100 points for Bluetooth JBL GO 2 Pink'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect: 'give_points'
            pointsRule: round_up(transaction.sku('JBLG103').qty * 100)
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign3:
    uuid: 'b8762d14-8ce2-4370-b57d-cd47e59f6954'
    name: 'Double Your Points. Buy on Wednesday'
    description: 'Double Your Points. Buy on Wednesday!'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - attribute: 'transaction.purchasedAt'
            operator: 'is_day_of_week'
            data:
              - 'Wednesday'
        effects:
          - effect: 'give_points'
            pointsRule: round_up((transaction.grossValue - transaction.category('for_adult').grossValue) * 2)
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign4:
    uuid: '59eb9382-2591-4cf7-a562-377eb18d711c'
    name: 'Spend 200 USD and receive 20 USD for the following order'
    description: 'Spend 200 USD and receive coupon for 20 USD'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - attribute: 'transaction.grossValue'
            operator: 'is_greater_or_equal'
            data: '200'
          - attribute: 'transaction.purchasedAt'
            operator: 'is_between'
            data:
              from: '2020-01-01T00:00:00+01:00'
              to: '2024-01-01T00:00:00+01:00'
        effects:
          - effect: 'give_reward'
            rewardId: '3bbd86bc-1c76-41b2-97cb-4f82c9aee61e'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign5:
    uuid: '7364f6a7-03c7-4255-b75e-23887fb1b2f4'
    name: 'Spend X USD (more than 200 USD) and receive coupon for X/2'
    description: 'Spend X USD (more than 200 USD) and receive coupon for X/2. For example: if spend 250 USD then will receive coupon for 25 USD'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - attribute: 'transaction.grossValue'
            operator: 'is_greater_or_equal'
            data: '200'
        effects:
          - effect: 'give_reward'
            rewardId: '8b0cda16-a9a3-4b5d-8d77-5981ef42da93'
            couponValueRule: (transaction.grossValue - 200)/2
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign6:
    uuid: '5b9bc21a-46e8-4620-8535-98a654f2f875'
    name: 'Happy birthday! Get 50 points and a free delivery code.'
    description: 'Happy birthday! Get 50 points and a free delivery code.'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - operator: 'expression'
            data: transaction.purchasedAt == customer.birthDate
        effects:
          - effect: 'give_reward'
            rewardId: 'a4837176-d492-45b8-bc3d-49f772fa258c'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign7:
    uuid: '6aaace08-d500-406a-8355-68c9183614e4'
    name: 'It’s getting cold - get a new jacket in November and double your points'
    description: 'It’s getting cold - get a new jacket in November and double your points'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - attribute: 'transaction.purchasedAt'
            operator: 'is_month_of_year'
            data:
              - 'November'
          - operator: 'expression'
            data: transaction.category('winter').qty > 0
        effects:
          - effect: 'give_points'
            pointsRule: round_up(transaction.category('winter').grossValue * 2)
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign8:
    uuid: '6c439502-4a64-4b43-9de8-1b97db67b75e'
    name: '5 Bonus points if you share the post'
    description: '5 Bonus points if you share the post'
    trigger: 'custom_event'
    type: 'direct'
    event: 'facebook_share'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect: 'give_points'
            pointsRule: '5'
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign9:
    uuid: '6adaf78d-308a-452f-91bd-d00173b11783'
    name: 'Earn 15 points by every review of our product'
    description: 'Earn 15 points by every review of our product'
    trigger: 'custom_event'
    type: 'direct'
    event: 'product_review'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect: 'give_points'
            pointsRule: '15'
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign10:
    uuid: '96a7dd63-faae-44a4-bb26-3e122578a404'
    name: 'Earn X points for miles into flight awards'
    description: 'Earn X points for miles into flight awards (not more than 1000)'
    trigger: 'custom_event'
    type: 'direct'
    event: 'mile'
    rules:
      - target: self
        conditions:
          - attribute: 'event.body.number_of_miles'
            operator: 'is_greater'
            data: '10000'
        effects:
          - effect: 'give_points'
            pointsRule: "round_up((event.body.number_of_miles / 10000) > 1000 ? 1000 : event.body.number_of_miles / 10000)"
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign11:
    uuid: '801e7a68-6908-43ac-94fa-532546058f9b'
    name: 'Use the redemption code and get 100 points'
    description: 'Use the redemption code and get 100 points.'
    trigger: 'custom_event_unique_code'
    type: 'direct'
    event: 'coupon_use'
    codeGenerator:
      characterSet: 'alpha'
      length: 12
    generateCodes: 100
    eventCodeAttribute: 'coupon'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect: 'give_points'
            pointsRule: '100'
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
    limits:
      executionsPerMember:
        value: 4
        interval:
          type: 'calendarWeeks'
          value: 2,
      unitsPerMember:
        value: 200
        interval:
          type: 'calendarDays'
          value: 14
  campaign12:
    uuid: '19682a9d-df3a-42d7-aa04-eb0fd946bc94'
    name: 'Log in 20 times in the last 30 days and get 150 points and a voucher'
    description: 'Log in 20 times (once a day) in the last 30 days and get 150 points and a voucher.'
    trigger: 'achievement'
    achievementId: '2626c762-47ee-4b35-8fe7-2ebb77b4a9b4'
    type: 'direct'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect: 'give_points'
            pointsRule: '150'
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
          - effect: 'give_reward'
            rewardId: '3bbd86bc-1c76-41b2-97cb-4f82c9aee61e'
    limits:
      executionsPerMember:
        value: 4
        interval:
          type: 'calendarWeeks'
          value: 2,
      unitsPerMember:
        value: 200
        interval:
          type: 'calendarDays'
          value: 2
  campaign13:
    uuid: '8748b90e-49af-4128-84b2-b3521c6549cd'
    name: 'Spend $50 for certain categories to complete the achievement to get a reward'
    description: 'Member needs to spend $50 for each category (Shoes, Hats, Drinks, Food) to complete the challenge and get reward.'
    trigger: 'achievement'
    achievementId: '6d9579fb-c324-4d9c-b41c-8661fad70696'
    type: 'direct'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect: 'give_reward'
            rewardId: 'b989b3ad-dc12-4355-9bcc-7e3e960dc8bd'
            unitsLockRule:
              lockStrategy: 'from_wallet'
    limits:
      executionsPerMember:
        value: 4
        interval:
          type: 'calendarWeeks'
          value: 2,
      unitsPerMember:
        value: 200
        interval:
          type: 'calendarDays'
          value: 2
  campaign14:
    uuid: '89c84a5f-f7c2-432b-8a0e-3829f4358510'
    name: 'Member needs make a purchase and living in one of the European cities to get 100 points'
    description: 'Members living in one of the selected European cities who make a purchase get 100 points.'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - attribute: 'customer.address.city'
            operator: 'is_one_of_group_values'
            data:
              groupOfValues:
                - 'ccf93bde-2f50-4fb0-bd3a-21af2f23d6ac'
        effects:
          - effect: 'give_points'
            pointsRule: '100'
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign15:
    uuid: '2b96aecb-3bd6-4f9a-835a-bd9fbe3ac3e4'
    name: 'For a monthly spend of up to $10K members get: $5K x 1% Cashback + next $2.5K x 1.2% + next $2.5K x 1.5%'
    description: "A promotion that can influence customer behavior and check the customer's possible spending potential. Customer is rewarded with 1% cashback when spent $5K and 1,2% when spent additional $2,5K and 1,5% when spent additional $2,5K, so in total must spent $10K in calendar month. If a customer spends more money in a given period, they can earn more cash back. The mechanism is based on tiers and gives customers the chance to receive a chashback depending on the amount of money spent in the current month. This is a very useful promotion for those who want to increase sales in a short time. Cashback is an obvious benefit that usually customers like and create a desire use it for future expenses, so next purchase."
    trigger: 'internal_event'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - data: "event.body.achievementId == '8c70269a-2579-47ad-a990-7cbd8199caf7'"
            operator: 'expression'
        effects:
          - effect: 'give_points'
            pointsRule: 'percent_value_distribution(event.body.progressChanges[0], [5000, 7500, 10000], [0.01, 0.012, 0.015, 0], event.body.progressStatuses[0].currentPeriodValue - event.body.progressChanges[0])'
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign16:
      uuid: 'd9d1537e-2bce-4e0f-93a1-a3511eb8bdc0'
      name: 'Member birthday = get 100 points'
      description: 'In this case member need to have a birthday to get points.'
      trigger: 'time'
      triggerStrategy:
         type: 'birthday'
         executionSchedule: '1 0 * * *'
      type: 'direct'
      memberFilter: 'birthday'
      rules:
        - target: self
          conditions: ~
          effects:
            - effect: 'give_points'
              pointsRule: '100'
              unitsExpirationRule:
                expirationStrategy: 'from_wallet'
              unitsLockRule:
                lockStrategy: 'from_wallet'
  campaign17:
    uuid: '84f81fb9-8833-41c4-a661-72bd214b6bd7'
    name: 'Make a $200 transaction two months in a row and get 100 points and 10% discount'
    description: 'Member needs to make a $200 transaction two month in a row to get points and discount.'
    trigger: 'achievement'
    achievementId: '17645ec5-68d8-4aa8-b5a9-bba20102b59e'
    type: 'direct'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect: 'give_points'
            pointsRule: '100'
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
          - effect:  'give_reward'
            rewardId: 'c6e6654f-a5f7-41d3-a209-537c8a8e8edb'
  campaign18:
    uuid: 'c9db7763-2daa-4a00-ac41-4963abbd6f5b'
    name: 'Make 5 Transactions each of at least $30 and get 10% discount'
    description: 'Member needs to make 5 transactions each of which needs to be at least $30 = get 10% discount.'
    trigger: 'achievement'
    achievementId: '650a41e4-6cd4-466f-929e-20e33199ab85'
    type: 'direct'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect:  'give_reward'
            rewardId: 'c6e6654f-a5f7-41d3-a209-537c8a8e8edb'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign19:
    uuid: '565df789-9756-4e65-939c-8ff8ac93d86d'
    name: 'Get extra 200 points for any purchase above 250 EUR'
    description: 'Get extra 200 points for any purchase above 250 EUR, extra points valid for 2 weeks.'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - attribute: 'transaction.grossValue'
            operator: 'is_greater'
            data: '250'
        effects:
          - effect: 'give_points'
            pointsRule: '200'
            unitsExpirationRule:
              expirationStrategy: 'expression'
              expression: 'add_days_to_date(transaction.purchasedAt, 15)'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign20:
    uuid: 'ba01b8cc-8ded-491e-b31d-9a2f667f7dd8'
    name: "If you've completed a campaign within the last 30 days, there's another campaign offering extra points or vouchers."
    description: 'If you completed campaign in the last 30 days, there is another campaign for extra points/voucher. Valid for 30 days.'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - attribute: 'customer.segments'
            operator: 'contains'
            data: '38cefc36-6e8f-47d3-b1c6-0a31bd8e6dfc'
        effects:
          - effect: 'give_points'
            pointsRule: '100'
            unitsExpirationRule:
              expirationStrategy: 'expression'
              expression: 'add_days_to_date(transaction.purchasedAt, 30)'
            unitsLockRule:
              lockStrategy: 'from_wallet'
    visibility:
      target: 'segment'
      segments:
        - '38cefc36-6e8f-47d3-b1c6-0a31bd8e6dfc'
canada:
  campaign1:
    uuid: '73bcab7c-e634-4a2a-bd7a-717049f6a362'
    name: 'Earn 1 point for every $1 spent'
    description: 'Earn 1 point for every $1 spent (without "for adult" items)'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect: 'give_points'
            pointsRule: round_up(transaction.grossValue - transaction.category('for_adult').grossValue)
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign2:
    uuid: 'fdc65ea0-eb26-4730-ab08-7cedeab39249'
    name: 'Get 100 points for Bluetooth JBL GO 2 Pink'
    description: 'Get 100 points for Bluetooth JBL GO 2 Pink'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect: 'give_points'
            pointsRule: round_up(transaction.sku('JBLG103').qty * 100)
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign3:
    uuid: '9e4a33ee-f2c6-48b6-b5f4-323bbbf4d1ea'
    name: 'Double Your Points. Buy on Wednesday'
    description: 'Double Your Points. Buy on Wednesday!'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - attribute: 'transaction.purchasedAt'
            operator: 'is_day_of_week'
            data:
              - 'Wednesday'
        effects:
          - effect: 'give_points'
            pointsRule: round_up((transaction.grossValue - transaction.category('for_adult').grossValue) * 2)
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign4:
    uuid: 'ebf84d52-0cf4-4350-90fe-59206544d43c'
    name: 'Spend 200 USD and receive 20 USD for the following order'
    description: 'Spend 200 USD and receive coupon for 20 USD'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - attribute: 'transaction.grossValue'
            operator: 'is_greater_or_equal'
            data: '200'
          - attribute: 'transaction.purchasedAt'
            operator: 'is_between'
            data:
              from: '2020-01-01T00:00:00+01:00'
              to: '2024-01-01T00:00:00+01:00'
        effects:
          - effect: 'give_reward'
            rewardId: '5938821f-57ab-444e-95ed-a73e59131108'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign5:
    uuid: '0c4d8582-a0f7-4615-b9f5-96a41bdfb550'
    name: 'Spend X USD (more than 200 USD) and receive coupon for X/2'
    description: 'Spend X USD (more than 200 USD) and receive coupon for X/2. For example: if spend 250 USD then will receive coupon for 25 USD'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - attribute: 'transaction.grossValue'
            operator: 'is_greater_or_equal'
            data: '200'
        effects:
          - effect: 'give_reward'
            rewardId: 'd9acdf74-bf4d-4ff2-bc34-300b327bf820'
            couponValueRule: (transaction.grossValue - 200)/2
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign6:
    uuid: 'c0147d89-4e05-4375-b754-ed0205898744'
    name: 'Happy birthday! Get 50 points and a free delivery code.'
    description: 'Happy birthday! Get 50 points and a free delivery code.'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - operator: 'expression'
            data: transaction.purchasedAt == customer.birthDate
        effects:
          - effect: 'give_reward'
            rewardId: '47957e40-aef7-4adf-baf6-77d084b1040f'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign7:
    uuid: '7a309c30-48f0-4ff7-97fe-78b58fb4c031'
    name: 'It’s getting cold - get a new jacket in November and double your points'
    description: 'It’s getting cold - get a new jacket in November and double your points'
    trigger: 'transaction'
    type: 'direct'
    rules:
      - target: self
        conditions:
          - attribute: 'transaction.purchasedAt'
            operator: 'is_month_of_year'
            data:
              - 'November'
          - operator: 'expression'
            data: transaction.category('winter').qty > 0
        effects:
          - effect: 'give_points'
            pointsRule: round_up(transaction.category('winter').grossValue * 2)
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign8:
    uuid: '717bfcd5-3b90-4337-b82c-93f243ee62c4'
    name: '5 Bonus points if you share the post'
    description: '5 Bonus points if you share the post'
    trigger: 'custom_event'
    type: 'direct'
    event: 'facebook_share'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect: 'give_points'
            pointsRule: '5'
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign9:
    uuid: 'd4d61e3e-df76-4cb5-a403-4c55f1afbeb2'
    name: 'Earn 15 points by every review of our product'
    description: 'Earn 15 points by every review of our product'
    trigger: 'custom_event'
    type: 'direct'
    event: 'product_review'
    rules:
      - target: self
        conditions: ~
        effects:
          - effect: 'give_points'
            pointsRule: '15'
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
  campaign10:
    uuid: '0ee87a83-acf2-44e2-917a-4bbf80b920d7'
    name: 'Earn X points for miles into flight awards'
    description: 'Earn X points for miles into flight awards (not more than 1000)'
    trigger: 'custom_event'
    type: 'direct'
    event: 'mile'
    rules:
      - target: self
        conditions:
          - attribute: 'event.body.number_of_miles'
            operator: 'is_greater'
            data: '10000'
        effects:
          - effect: 'give_points'
            pointsRule: "round_up((event.body.number_of_miles / 10000) > 1000 ? 1000 : event.body.number_of_miles / 10000)"
            unitsExpirationRule:
              expirationStrategy: 'from_wallet'
            unitsLockRule:
              lockStrategy: 'from_wallet'
