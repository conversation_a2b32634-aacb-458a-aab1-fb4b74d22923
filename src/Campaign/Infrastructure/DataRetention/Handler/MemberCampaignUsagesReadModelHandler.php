<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\DataRetention\Handler;

use Doctrine\DBAL\Connection;
use OpenLoyalty\Core\Infrastructure\DataRetention\AbstractHandler;
use Psr\Log\LoggerInterface;

class MemberCampaignUsagesReadModelHandler extends AbstractHandler
{
    public function __construct(
        Connection $connection,
        LoggerInterface $dataRetentionLogger,
        private readonly int $maxDeleteCount,
        private readonly int $batchSize
    ) {
        parent::__construct($connection, $dataRetentionLogger);
    }

    protected function getProcedureName(): string
    {
        return 'delete_member_campaign_usages_read_model';
    }

    protected function getMaxDeleteCount(): int
    {
        return $this->maxDeleteCount;
    }

    protected function getBatchSize(): int
    {
        return $this->batchSize;
    }
}
