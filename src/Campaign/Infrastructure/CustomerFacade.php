<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure;

use DateTimeImmutable;
use Generator;
use OpenLoyalty\Campaign\Domain\CustomerFacadeInterface;
use OpenLoyalty\Campaign\Domain\Model\BadgeMember as ModelBadgeMember;
use OpenLoyalty\Core\Domain\Id\BadgeTypeId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Search\Criteria\BooleanCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\Criteria;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\PaginationCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\UuidCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Domain\CustomerGateInterface;

class CustomerFacade implements CustomerFacadeInterface
{
    public function __construct(private readonly CustomerGateInterface $customerGate)
    {
    }

    public function getCustomer(CustomerId $customerId): Customer
    {
        return $this->customerGate->getCustomer($customerId);
    }

    public function addOrUpdateMemberCustomAttribute(
        StoreId $storeId,
        CustomerId $customerId,
        string $key,
        string $value
    ): void {
        $this->customerGate->addOrUpdateMemberCustomAttribute($storeId, $customerId, $key, $value);
    }

    public function removeMemberCustomAttribute(StoreId $storeId, CustomerId $customerId, string $key): void
    {
        $this->customerGate->removeMemberCustomAttribute($storeId, $customerId, $key);
    }

    public function getMembers(StoreId $storeId, int $maxResults): Generator
    {
        $criteriaCollection = new CriteriaCollection();
        $criteriaCollection->add(UuidCriteria::fromIdentifier('storeId', CriteriaInterface::EQUAL, $storeId));

        $criteriaCollection->add(new PaginationCriteria(1, 100));

        return $this->customerGate->getMembersByCriteria($criteriaCollection);
    }

    public function getAllActiveMembers(?StoreId $storeId = null): Generator
    {
        $criteriaCollection = new CriteriaCollection();
        if (null !== $storeId) {
            $criteriaCollection->add(UuidCriteria::fromIdentifier('storeId', Criteria::EQUAL, $storeId));
        }
        $criteriaCollection->add(new BooleanCriteria('active', Criteria::EQUAL, true));
        yield from $this->customerGate->getMembersByCriteria($criteriaCollection);
    }

    public function getAllActiveCount(StoreId $storeId): int
    {
        return $this->customerGate->getAllActiveCount($storeId);
    }

    public function getAllActiveIdsIterable(StoreId $storeId): Generator
    {
        return $this->customerGate->getAllActiveIdsIterable($storeId);
    }

    public function getByBirthdayAnniversary(
        StoreId $storeId,
        DateTimeImmutable $from,
        DateTimeImmutable $to,
        bool $onlyActive = true
    ): iterable {
        return $this->customerGate->getByBirthdayAnniversary($storeId, $from, $to, $onlyActive);
    }

    public function getByRegistrationAnniversary(
        StoreId $storeId,
        DateTimeImmutable $from,
        DateTimeImmutable $to,
        bool $onlyActive = true
    ): iterable {
        return $this->customerGate->getByRegistrationAnniversary($storeId, $from, $to, $onlyActive);
    }

    /**
     * @return ModelBadgeMember[]
     */
    public function getMemberBadges(StoreId $storeId, CustomerId $memberId): array
    {
        $badges = $this->customerGate->getMemberBadges($storeId, $memberId);
        $memberBadges = [];
        foreach ($badges as $badge) {
            $memberBadges[] = new ModelBadgeMember(
                $badge['memberId'],
                $badge['badgeTypeId'],
                $badge['completedCount']
            );
        }

        return $memberBadges;
    }

    public function badgeTypeExists(StoreId $storeId, BadgeTypeId $badgeTypeId): bool
    {
        return $this->customerGate->badgeTypeExists($storeId, $badgeTypeId);
    }
}
