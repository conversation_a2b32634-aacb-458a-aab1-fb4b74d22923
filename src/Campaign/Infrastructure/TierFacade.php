<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure;

use OpenLoyalty\Campaign\Domain\TierFacadeInterface;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Level\Domain\TierGateInterface;

final class TierFacade implements TierFacadeInterface
{
    public function __construct(private readonly TierGateInterface $tierGate)
    {
    }

    public function isTierValid(LevelId $tierId, StoreId $storeId): bool
    {
        return $this->tierGate->isTierValid($tierId, $storeId);
    }

    /**
     * @return string[]
     */
    public function getUserTiersIds(CustomerId $customerId, StoreId $storeId): array
    {
        return $this->tierGate->getUserTiersIds($customerId, $storeId);
    }

    public function getNumberOfCustomerInTier(LevelId $levelId): int
    {
        return $this->tierGate->getNumberOfCustomerInTier($levelId);
    }

    public function getCustomerIdsInTier(LevelId $levelId, StoreId $storeId): \Generator
    {
        return $this->tierGate->getCustomerIdsInTier($levelId, $storeId);
    }
}
