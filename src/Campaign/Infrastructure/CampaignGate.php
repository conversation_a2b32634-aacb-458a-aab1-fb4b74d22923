<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure;

use DateTimeImmutable;
use Generator;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\CampaignCodesRepositoryInterface;
use OpenLoyalty\Campaign\Domain\CampaignExecutionRepositoryInterface;
use OpenLoyalty\Campaign\Domain\CampaignExecutionRepositoryReadContextInterface;
use OpenLoyalty\Campaign\Domain\CampaignGateInterface;
use OpenLoyalty\Campaign\Domain\CampaignRepositoryInterface;
use OpenLoyalty\Campaign\Domain\CampaignRepositoryReadContextInterface;
use OpenLoyalty\Campaign\Domain\CampaignVisibilitySegmentRepositoryInterface;
use OpenLoyalty\Campaign\Domain\CampaignVisibilityTierRepositoryInterface;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Core\Domain\ValueObject\LimitPeriod;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;

final class CampaignGate implements CampaignGateInterface
{
    public function __construct(
        private readonly CampaignCodesRepositoryInterface $campaignCodesRepository,
        private readonly SearchableResponderInterface $searchableResponder,
        private readonly CampaignExecutionRepositoryInterface $campaignExecutionRepository,
        private readonly CampaignVisibilitySegmentRepositoryInterface $campaignVisibilitySegmentRepository,
        private readonly CampaignVisibilityTierRepositoryInterface $campaignVisibilityTierRepository,
        private readonly CampaignRepositoryReadContextInterface $campaignRepositoryReadContext,
        private readonly CampaignExecutionRepositoryReadContextInterface $campaignExecutionRepositoryReadContext,
        private readonly CampaignRepositoryInterface $campaignRepository,
    ) {
    }

    public function getCampaignCodes(CriteriaCollectionInterface $criteriaCollection): Generator
    {
        return $this->searchableResponder->fromCriteriaIterable(
            $this->campaignCodesRepository,
            $criteriaCollection
        );
    }

    public function getWalletUnitUsages(StoreId $storeId, WalletTypeId $walletTypeId, CustomerId $customerId, ?LimitPeriod $limitPeriod): float
    {
        return $this->campaignExecutionRepository->getUsedPointsByLimitPeriodAndWalletCode(
            $storeId,
            $walletTypeId,
            $limitPeriod,
            $customerId
        );
    }

    public function isSegmentUsedInCampaigns(SegmentId $segmentId): bool
    {
        return (bool) $this->campaignVisibilitySegmentRepository->countVisibleCampaignsBySegmentId($segmentId);
    }

    public function isTierUsedInCampaigns(LevelId $tierId): bool
    {
        return (bool) $this->campaignVisibilityTierRepository->countVisibleCampaignsByTierId($tierId);
    }

    public function findCustomersByCampaignCompletion(
        StoreId $storeId,
        CampaignId $campaignId,
        int $campaignCompletionNumber,
        DateTimeImmutable $fromDate,
        DateTimeImmutable $toDate,
    ): Generator {
        yield from $this->campaignExecutionRepositoryReadContext->findCustomersByCampaignCompletion(
            $storeId,
            $campaignId,
            $campaignCompletionNumber,
            $fromDate,
            $toDate,
        );
    }

    public function getCampaign(CampaignId $campaignId, StoreId $storeId): ?Campaign
    {
        return $this->campaignRepositoryReadContext->findOneBy([
            'campaignId' => $campaignId,
            'store' => $storeId,
        ]);
    }

    /**
     * @return array<array<string, string|bool>>
     */
    public function findCampaignsAssociatedToSegment(SegmentId $segmentId, StoreId $storeId): array
    {
        return $this->campaignRepositoryReadContext->findAllWithAssociationsToSegment($segmentId, $storeId);
    }

    public function isCampaignTriggeredBy(CampaignId $campaignId, StoreId $storeId, Trigger $triggerType): bool
    {
        return null !== $this->campaignRepository->findOneBy([
            'campaignId' => $campaignId,
            'store' => $storeId,
            'trigger.code' => $triggerType->getCode(),
        ]);
    }
}
