<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\Type;

use OpenLoyalty\Campaign\Domain\Enum\AudienceTarget;
use OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\LevelValid;
use OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\SegmentValid;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Event\PreSubmitEvent;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Validator\Constraints\Count;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Uuid;

final class CampaignAudienceFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('target', CampaignAudienceTargetFormType::class);
        $builder->addEventListener(FormEvents::PRE_SUBMIT, [$this, 'onPreSubmit']);
    }

    public function onPreSubmit(PreSubmitEvent $event): void
    {
        $data = $event->getData();
        $form = $event->getForm();

        if (false === isset($data['target'])) {
            return;
        }

        if (AudienceTarget::Segment->value === $data['target']) {
            $form->add(
                'segments',
                CollectionType::class,
                [
                    'allow_add' => true,
                    'allow_delete' => true,
                    'entry_type' => TextType::class,
                    'entry_options' => [
                        'constraints' => [
                            new NotBlank(),
                            new Uuid(),
                            new SegmentValid(),
                        ],
                    ],
                    'constraints' => [
                        new NotBlank(),
                        new Count(['max' => 1]),
                    ],
                ]
            );
        }

        if (AudienceTarget::Tier->value === $data['target']) {
            $form->add(
                'tiers', CollectionType::class,
                [
                    'allow_add' => true,
                    'allow_delete' => true,
                    'entry_type' => TextType::class,
                    'entry_options' => [
                        'constraints' => [
                            new NotBlank(),
                            new Uuid(),
                            new LevelValid(),
                        ],
                    ],
                    'constraints' => [
                        new NotBlank(),
                        new Count(['max' => 1]),
                    ],
                ]
            );
        }
    }
}
