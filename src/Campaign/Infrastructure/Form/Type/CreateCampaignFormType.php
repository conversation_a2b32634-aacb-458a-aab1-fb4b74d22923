<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\Type;

use DateTime;
use OpenLoyalty\Campaign\Application\Command\CreateCampaign;
use OpenLoyalty\Campaign\Infrastructure\Form\EventSubscriber\CampaignCodesSubscriber;
use OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\GlobalActiveCampaignLimit;
use OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\RedemptionCodeLimit;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\DataMapperInterface;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Traversable;

final class CreateCampaignFormType extends AbstractType implements DataMapperInterface
{
    private ?CampaignId $campaignId;
    private ?Store $store;

    public function __construct(
        private readonly CampaignCodesSubscriber $campaignCodesSubscriber
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $this->campaignId = $options['campaignId'];
        $this->store = $options['store'];

        $builder->add('active', CheckboxType::class, [
            'required' => false,
            'constraints' => [
                new GlobalActiveCampaignLimit(),
                new RedemptionCodeLimit(),
            ],
        ]);
        $builder->add('leaderboard', LeaderboardConfigFormType::class, [
            'required' => false,
        ]);
        $builder->addEventSubscriber($this->campaignCodesSubscriber);
        $builder->setDataMapper($this);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefault('store', null);
    }

    /**
     * @param Traversable<FormInterface> $forms
     * @param CreateCampaign             $viewData
     */
    public function mapFormsToData(Traversable $forms, &$viewData): void
    {
        if (null === $this->campaignId || null === $this->store) {
            return;
        }

        $formArray = iterator_to_array($forms);

        $viewData = new CreateCampaign(
            $this->campaignId,
            $this->store->getStoreId(),
            $formArray['active']->getData() ?? false,
            $formArray['translations']->getData() ?? [],
            $formArray['type']->getData() ?? '',
            $formArray['trigger']->getData() ?? '',
            $formArray['activity']->get('startsAt')->getData() ?? new DateTime(),
            $formArray['activity']->get('endsAt')->getData(),
            /* @phpstan-ignore-next-line */
            $formArray['rules']?->getData() ?? [],
            isset($formArray['limits']) ? $formArray['limits']->getData() : null,
            /* @phpstan-ignore-next-line */
            $formArray['labels']?->getData() ?? [],
            isset($formArray['visibility']) ? $formArray['visibility']->getData() : null,
            isset($formArray['audience']) ? $formArray['audience']->getData() : null,
            $formArray['triggerStrategy']->get('type')->getData(),
            /* @phpstan-ignore-next-line */
            isset($formArray['transactionItemsFilters']) ? $formArray['transactionItemsFilters']->getData() : null,
            $formArray['leaderboard'],
        );

        $viewData->setEvent(isset($formArray['event']) ? $formArray['event']->getData() : null);
        $viewData->setMemberFilter(isset($formArray['memberFilter']) ? $formArray['memberFilter']->getData() : null);
        $viewData->setExecutionSchedule(isset($formArray['triggerStrategy']['executionSchedule']) ? $formArray['triggerStrategy']['executionSchedule']->getData() : null);
        $viewData->setAchievementId(isset($formArray['achievementId']) ? $formArray['achievementId']->getData() : null);
        $viewData->setDisplayOrder(null !== $formArray['displayOrder']->getData() ? (int) $formArray['displayOrder']->getData() : null);
        if (isset($formArray['multiLevel']) && null !== $formArray['multiLevel']->getData()) {
            $viewData->enableMultiLevel($formArray['multiLevel']->getData());
        } else {
            $viewData->disableMultiLevel();
        }
        $this->mapCodeGenerator($viewData, $formArray);
    }

    public function mapDataToForms($viewData, $forms): void
    {
        // Unused
    }

    public function getParent(): string
    {
        return CampaignFormType::class;
    }

    private function mapCodeGenerator(CreateCampaign $viewData, array $formArray): void
    {
        if (Trigger::CUSTOM_EVENT_UNIQUE_CODE === $viewData->getTrigger()) {
            $codeGenerator = $formArray['codeGenerator']?->getData();
            $generateCodes = $formArray['generateCodes']?->getData();
            $eventCodeAttribute = isset($formArray['eventCodeAttribute']) ? $formArray['eventCodeAttribute']->getData() : null;

            if (null !== $codeGenerator && null !== $generateCodes && null !== $eventCodeAttribute) {
                $viewData->enableCodeGeneration(
                    $codeGenerator,
                    $generateCodes,
                    $eventCodeAttribute
                );
            }
        }
    }
}
