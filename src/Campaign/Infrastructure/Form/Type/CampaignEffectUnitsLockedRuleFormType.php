<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\Type;

use OpenLoyalty\Campaign\Domain\Entity\Effect\ExpirationStrategy;
use OpenLoyalty\Campaign\Domain\Entity\Effect\LockStrategy;
use OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\CustomLockExpressionValid;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Validator\Constraints\NotBlank;

final class CampaignEffectUnitsLockedRuleFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event): void {
            $form = $event->getForm();
            $campaignForm = $form->getRoot();

            $form->add('lockStrategy', ChoiceType::class, [
                'required' => false,
                'empty_data' => LockStrategy::FromWallet->value,
                'constraints' => [new NotBlank()],
                'choices' => [
                    'expression' => LockStrategy::Expression->value,
                    'from_wallet' => LockStrategy::FromWallet->value,
                    'never_expires' => LockStrategy::NoPending->value,
                ],
            ]);

            /** @var array<string, mixed> $eventData */
            $eventData = $event->getData() ?? [];
            $strategy = $eventData['lockStrategy'] ?? null;

            if (ExpirationStrategy::Expression->value === $strategy) {
                $form->add('expression', TextType::class, [
                    'required' => false,
                    'empty_data' => null,
                    'constraints' => [new CustomLockExpressionValid([
                        'type' => $campaignForm->has('type') ? $campaignForm->get('type')->getData() : null,
                        'trigger' => $campaignForm->has('trigger') ? $campaignForm->get('trigger')->getData() : null,
                        'event' => $campaignForm->has('event') ? $campaignForm->get('event')->getData() : null,
                        /* @phpstan-ignore-next-line */
                        'strategy' => $strategy,
                    ]),
                    ]]);
            }
        });
    }
}
