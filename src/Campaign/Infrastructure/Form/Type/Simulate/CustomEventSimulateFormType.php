<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\Type\Simulate;

use OpenLoyalty\Core\Domain\Context\CustomEvent;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\DateRangeValid;
use OpenLoyalty\CustomEvent\Infrastructure\Form\Type\CustomEventBodyFormType;
use OpenLoyalty\CustomEvent\Infrastructure\Validator\Constraint\CustomEventSchemaExists;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Event\PreSubmitEvent;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Uuid;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
class CustomEventSimulateFormType extends AbstractType implements DataTransformerInterface
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('id', TextType::class, [
            'required' => false,
            'constraints' => [new Uuid()],
            'documentation' => [
                'example' => '7239e6f7-e52e-466b-8c24-699709b64076',
            ],
        ]);
        $builder->add('type', TextType::class, [
            'required' => true,
            'constraints' => [
                new CustomEventSchemaExists(),
                new NotBlank(),
            ],
        ]);
        $builder->add('eventDate', DateTimeType::class, [
            'required' => true,
            'widget' => 'single_text',
            'format' => DateTimeType::HTML5_FORMAT,
            'constraints' => [
                new NotBlank(),
                new DateRangeValid(),
            ],
        ]);

        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (PreSubmitEvent $event): void {
            $form = $event->getForm();
            $data = $event->getData();

            if (!isset($data['type'])) {
                return;
            }

            $form->add('body', CustomEventBodyFormType::class, [
                'required' => false,
                'event_type' => $data['type'],
            ]);
        });

        $builder->addModelTransformer($this);
    }

    public function transform(mixed $value): ?array
    {
        return $value;
    }

    public function reverseTransform(mixed $value): ?CustomEvent
    {
        if (null === $value) {
            return null;
        }

        $customEvent = new CustomEvent($value['body'] ?? []);
        $customEvent->id = $value['id'] ?? null;
        $customEvent->type = $value['type'] ?? null;
        $customEvent->eventDate = $value['eventDate'] ?? null;

        return $customEvent;
    }
}
