<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\Type\Simulate;

use OpenLoyalty\Campaign\Domain\Context\Wallet;
use OpenLoyalty\Core\Infrastructure\Form\Type\PointsType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
class WalletSimulateFormType extends AbstractType implements DataTransformerInterface
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('walletType', TextType::class, [
        ]);

        $builder->add('activeUnits', PointsType::class, [
            'required' => false,
        ]);
        $builder->add('spentUnits', PointsType::class, [
            'required' => false,
        ]);
        $builder->add('earnedUnits', PointsType::class, [
            'required' => false,
        ]);
        $builder->add('lockedUnits', PointsType::class, [
            'required' => false,
        ]);
        $builder->add('blockedUnits', PointsType::class, [
            'required' => false,
        ]);
        $builder->add('expiredUnits', PointsType::class, [
            'required' => false,
        ]);

        $builder->addModelTransformer($this);
    }

    /**
     * @param  ?array<mixed> $value
     * @return ?array<mixed>
     */
    public function transform(mixed $value): ?array
    {
        return $value;
    }

    /**
     * @param array{walletType: string, activeUnits: float, spentUnits: float,
     *      earnedUnits: float, lockedUnits: float, blockedUnits: float, expiredUnits: float} $value
     */
    public function reverseTransform(mixed $value): ?Wallet
    {
        if (null === $value) {
            return null;
        }

        return Wallet::createFromArray($value);
    }
}
