<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\Type\Simulate;

use OpenLoyalty\Campaign\Domain\Context\Customer;
use OpenLoyalty\Core\Infrastructure\Form\DataTransformer\LabelsDataTransformer;
use OpenLoyalty\Core\Infrastructure\Form\Type\PointsType;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\DateRangeValid;
use OpenLoyalty\User\Infrastructure\Form\Type\LabelFormType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\Count;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Range;
use Symfony\Component\Validator\Constraints\Type as Numeric;
use Symfony\Component\Validator\Constraints\Uuid;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
class CustomerSimulateFormType extends AbstractType implements DataTransformerInterface
{
    public function __construct(
        private readonly int $stringFieldMaxLength,
        private readonly int $numberMinValue,
        private readonly int $numberMaxValue,
        private readonly int $maxCustomAttributesInMember
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('id', TextType::class, [
            'required' => false,
            'constraints' => [new Uuid()],
            'documentation' => [
                'example' => '6a71961d-4038-4a8d-94c3-c8e1b00017a5',
            ],
        ]);
        $builder->add('firstName', TextType::class, [
            'required' => false,
            'constraints' => [
                new Length(['max' => $this->stringFieldMaxLength]),
            ],
        ]);
        $builder->add('lastName', TextType::class, [
            'required' => false,
            'constraints' => [
                new Length(['max' => $this->stringFieldMaxLength]),
            ],
        ]);
        $builder->add('email', TextType::class, [
            'required' => false,
            'constraints' => [
                new Length(['max' => $this->stringFieldMaxLength]),
            ],
        ]);
        $builder->add('phone', TextType::class, [
            'required' => false,
            'constraints' => [
                new Numeric(['type' => 'numeric', 'message' => 'Incorrect phone number format, use +00000000000']),
                new Length(['max' => $this->stringFieldMaxLength]),
            ],
        ]);
        $builder->add('birthDate', DateType::class, [
            'required' => false,
            'widget' => 'single_text',
            'format' => DateType::HTML5_FORMAT,
        ]);
        $builder->add('address', CustomerAddressSimulateFormType::class, [
            'required' => false,
        ]);
        $builder->add('loyaltyCardNumber', TextType::class, [
            'required' => false,
            'constraints' => [
                new Length(['max' => $this->stringFieldMaxLength]),
            ],
        ]);
        $builder->add('legalConsent', CheckboxType::class, [
            'required' => false,
        ]);
        $builder->add('marketingConsent', CheckboxType::class, [
            'required' => false,
        ]);
        $builder->add('dataProcessingConsent', CheckboxType::class, [
            'required' => false,
        ]);
        $builder->add('gender', TextType::class, [
            'required' => false,
            'constraints' => [
                new Length(['max' => $this->stringFieldMaxLength]),
            ],
        ]);
        $builder->add('registeredDate', DateTimeType::class, [
            'required' => false,
            'widget' => 'single_text',
            'format' => DateTimeType::HTML5_FORMAT,
            'constraints' => [new DateRangeValid()],
        ]);
        $builder->add('firstTransactionDate', DateTimeType::class, [
            'required' => false,
            'widget' => 'single_text',
            'format' => DateTimeType::HTML5_FORMAT,
            'constraints' => [new DateRangeValid()],
        ]);
        $builder->add('lastTransactionDate', DateTimeType::class, [
            'required' => false,
            'widget' => 'single_text',
            'format' => DateTimeType::HTML5_FORMAT,
            'constraints' => [new DateRangeValid()],
        ]);
        $builder->add('levelAchievementDate', DateTimeType::class, [
            'required' => false,
            'widget' => 'single_text',
            'format' => DateTimeType::HTML5_FORMAT,
            'constraints' => [new DateRangeValid()],
        ]);
        $builder->add('levelId', TextType::class, [
            'required' => false,
            'constraints' => [new Uuid()],
            'documentation' => [
                'example' => '6e68b8ad-b774-4863-ab2c-16d05874f533',
            ],
        ]);
        $builder->add('numberOfPurchases', IntegerType::class, [
            'required' => false,
        ]);
        $builder->add('purchaseAmount', NumberType::class, [
            'required' => false,
            'constraints' => [
                new Range(['min' => $this->numberMinValue, 'max' => $this->numberMaxValue]),
            ],
        ]);
        $builder->add('averagePurchaseAmount', NumberType::class, [
            'required' => false,
            'constraints' => [
                new Range(['min' => $this->numberMinValue, 'max' => $this->numberMaxValue]),
            ],
        ]);
        $builder->add('wallets', CollectionType::class, [
            'required' => false,
            'allow_add' => true,
            'allow_delete' => true,
            'entry_type' => WalletSimulateFormType::class,
        ]);

        $builder->add('activePoints', PointsType::class, [
            'required' => false,
        ]);
        $builder->add('spentPoints', PointsType::class, [
            'required' => false,
        ]);
        $builder->add('earnedPoints', PointsType::class, [
            'required' => false,
        ]);
        $builder->add('lockedPoints', PointsType::class, [
            'required' => false,
        ]);
        $builder->add('blockedPoints', PointsType::class, [
            'required' => false,
        ]);
        $builder->add('expiredPoints', PointsType::class, [
            'required' => false,
        ]);
        $builder->add($builder->create('labels', CollectionType::class, [
            'required' => false,
            'allow_add' => true,
            'allow_delete' => true,
            'entry_type' => LabelFormType::class,
            'constraints' => [
                new Count(['max' => $this->maxCustomAttributesInMember]),
            ],
        ])->addModelTransformer(new LabelsDataTransformer()));

        $builder->add('segments', CollectionType::class, [
            'required' => false,
            'allow_add' => true,
            'allow_delete' => true,
            'entry_type' => TextType::class,
            'entry_options' => [
                'constraints' => [new NotBlank(), new Uuid(['strict' => false])],
            ],
            'documentation' => [
                'example' => ['6e68b8ad-b774-4863-ab2c-16d05874f533', 'cb9e7370-34ad-4390-ab1a-8c6a8addf622'],
            ],
        ]);

        $builder->add('tiers', CollectionType::class, [
            'required' => false,
            'allow_add' => true,
            'allow_delete' => true,
            'entry_type' => TextType::class,
            'entry_options' => [
                'constraints' => [new NotBlank(), new Uuid(['strict' => false])],
            ],
            'documentation' => [
                'example' => ['6e68b8ad-b774-4863-ab2c-16d05874f533', 'cb9e7370-34ad-4390-ab1a-8c6a8addf622'],
            ],
        ]);

        $builder->addModelTransformer($this);
    }

    public function transform(mixed $value): ?array
    {
        return $value;
    }

    public function reverseTransform(mixed $value): ?Customer
    {
        if (null === $value) {
            return null;
        }

        return Customer::createFromArray($value);
    }
}
