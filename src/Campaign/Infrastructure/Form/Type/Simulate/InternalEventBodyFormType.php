<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\Type\Simulate;

use OpenLoyalty\CustomEvent\Infrastructure\Form\EventSubscriber\DynamicSchemaFieldsSubscriber;
use OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepositoryInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\OptionsResolver\OptionsResolver;

class InternalEventBodyFormType extends AbstractType
{
    public function __construct(
        private readonly InternalEventSchemaRepositoryInterface $internalEventSchemaRepository,
        private readonly int $maxCustomEventSchemaFields,
        private readonly int $stringFieldMaxLength,
        private readonly int $numberMinValue,
        private readonly int $numberMaxValue
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $schema = $this->internalEventSchemaRepository->findByType($options['event_type']);

        if (null === $schema) {
            $builder->getForm()->addError(new FormError('Schema is not defined'));

            return;
        }

        $builder->addEventSubscriber(
            new DynamicSchemaFieldsSubscriber(
                $schema->getSchema()->getFields(),
                $this->maxCustomEventSchemaFields,
                $this->stringFieldMaxLength,
                $this->numberMinValue,
                $this->numberMaxValue
            )
        );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setRequired(['event_type']);
    }
}
