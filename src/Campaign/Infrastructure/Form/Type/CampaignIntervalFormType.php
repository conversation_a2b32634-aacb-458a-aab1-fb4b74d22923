<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\Type;

use OpenLoyalty\Core\Domain\Exception\InvalidLimitIntervalTypeException;
use OpenLoyalty\Core\Domain\Exception\InvalidLimitIntervalValueException;
use OpenLoyalty\Core\Domain\ValueObject\LimitInterval;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Event\PreSubmitEvent;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Range;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
class CampaignIntervalFormType extends AbstractType implements DataTransformerInterface
{
    private const MIN_VALUE = 1;
    private const MAX_VALUE = **********;
    private const DEFAULT_VALUE = '1';

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('type', ChoiceType::class, [
            'required' => true,
            'choices' => LimitInterval::ALLOWED_TYPES,
            'constraints' => [new NotBlank()],
        ]);

        $builder->addModelTransformer($this);
        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (PreSubmitEvent $event): void {
            $event->getForm()->add('value', IntegerType::class, [
                'required' => true,
                'constraints' => [
                    new NotBlank(),
                    new Range(['min' => self::MIN_VALUE, 'max' => self::MAX_VALUE]),
                ],
                'empty_data' => self::DEFAULT_VALUE,
            ]);
        });
    }

    public function transform(mixed $value): ?array
    {
        return $value;
    }

    /**
     * @throws InvalidLimitIntervalValueException|InvalidLimitIntervalTypeException
     */
    public function reverseTransform(mixed $value): ?LimitInterval
    {
        if (!isset($value['type'], $value['value']) || $value['value'] < self::MIN_VALUE) {
            return null;
        }

        return LimitInterval::create($value['type'], $value['value']);
    }
}
