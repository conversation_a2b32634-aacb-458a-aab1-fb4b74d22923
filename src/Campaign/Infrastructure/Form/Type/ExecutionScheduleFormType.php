<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\Type;

use OpenLoyalty\Campaign\Infrastructure\Form\DataTransformer\ExecutionScheduleDataTransformer;
use OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\NotBlankMonthlyExecutionSchedule;
use OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\NotBlankWeeklyExecutionSchedule;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;

final class ExecutionScheduleFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('dayOfWeek', ChoiceType::class, [
            'required' => false,
            'multiple' => true,
            'expanded' => false,
            'choices' => array_flip(range(0, 6)),
            'constraints' => [
                new NotBlankWeeklyExecutionSchedule(),
            ],
        ]);

        $builder->add('dayOfMonth', ChoiceType::class, [
            'required' => false,
            'multiple' => true,
            'expanded' => false,
            'choices' => array_merge(
                array_combine(range(1, 31), range(1, 31)),
                ['L' => 'L']
            ),
            'constraints' => [
                new NotBlankMonthlyExecutionSchedule(),
            ],
        ]);

        $builder->addModelTransformer(new ExecutionScheduleDataTransformer());
    }
}
