<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\Type;

use OpenLoyalty\Campaign\Domain\ValueObject\LeaderboardConfig;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\NotBlank;

final class MetricFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('type', ChoiceType::class, [
            'required' => true,
            'choices' => [
                'Earned Units Cumulative' => LeaderboardConfig::METRIC_TYPE_EARNED_UNITS_CUMULATIVE,
            ],
            'constraints' => [new NotBlank()],
        ]);

        $builder->add('walletTypeCode', TextType::class, [
            'required' => true,
            'constraints' => [new NotBlank()],
        ]);
    }
}
