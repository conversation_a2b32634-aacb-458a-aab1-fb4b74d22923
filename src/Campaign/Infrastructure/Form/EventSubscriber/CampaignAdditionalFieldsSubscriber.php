<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\EventSubscriber;

use OpenLoyalty\Campaign\Infrastructure\Form\Type\AchievementFormType;
use OpenLoyalty\Campaign\Infrastructure\Form\Type\CampaignLimitFormType;
use OpenLoyalty\Campaign\Infrastructure\Form\Type\CampaignRuleFormType;
use OpenLoyalty\Campaign\Infrastructure\Form\Type\LeaderboardFormType;
use OpenLoyalty\Campaign\Infrastructure\Form\Type\MemberFilterFormType;
use OpenLoyalty\Campaign\Infrastructure\Form\Type\TransactionItemsFilterType;
use OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\CustomEventAttributeExistValid;
use OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\InternalEventExists;
use OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\UniqueTransactionItemFilterCode;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use OpenLoyalty\CustomEvent\Infrastructure\Validator\Constraint\CustomEventSchemaExists;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Form\Event\PreSubmitEvent;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Validator\Constraints\Count;
use Symfony\Component\Validator\Constraints\NotBlank;

class CampaignAdditionalFieldsSubscriber implements EventSubscriberInterface
{
    private const RULES_MIN = 1;
    private const RULES_MAX = 6;

    public function __construct(
        private readonly int $transactionItemsFiltersMax
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [FormEvents::PRE_SUBMIT => 'onPreSubmit'];
    }

    public function onPreSubmit(PreSubmitEvent $event): void
    {
        $form = $event->getForm();
        $data = $event->getData();

        if (!isset($data['trigger'])) {
            return;
        }

        switch ($data['trigger']) {
            case Trigger::RETURN_TRANSACTION:
            case Trigger::TRANSACTION:
                $form->add('transactionItemsFilters', CollectionType::class, [
                    'allow_add' => true,
                    'allow_delete' => true,
                    'entry_type' => TransactionItemsFilterType::class,
                    'error_bubbling' => false,
                    'constraints' => [
                        new Count(min: 0, max: $this->transactionItemsFiltersMax),
                        new UniqueTransactionItemFilterCode(),
                    ],
                ]);
                break;
            case Trigger::CUSTOM_EVENT:
                $form->add('event', TextType::class, [
                    'required' => false,
                    'constraints' => [new NotBlank(), new CustomEventSchemaExists()],
                ]);
                break;
            case Trigger::INTERNAL_EVENT:
                $form->add('event', TextType::class, [
                    'constraints' => [new NotBlank(), new InternalEventExists()],
                ]);
                break;
            case Trigger::CUSTOM_EVENT_UNIQUE_CODE:
                $form->add('event', TextType::class, [
                    'constraints' => [
                        new NotBlank(),
                        new CustomEventSchemaExists(),
                    ],
                ]);
                if (isset($data['event'])) {
                    $form->add('eventCodeAttribute', TextType::class, [
                        'constraints' => [
                            new NotBlank(),
                            new CustomEventAttributeExistValid(['eventType' => $data['event']]),
                        ],
                    ]);
                }
                break;
            case Trigger::TIME:
                $form->add('memberFilter', MemberFilterFormType::class);
                break;
            case Trigger::ACHIEVEMENT:
                $form->add('achievementId', AchievementFormType::class);
                break;
            case Trigger::LEADERBOARD:
                $form->add('leaderboard', LeaderboardFormType::class, [
                    'required' => false,
                ]);
                break;
        }

        $minRulesCount = self::RULES_MIN;
        if (Trigger::LEADERBOARD === $data['trigger']) {
            $minRulesCount = 0;
        }

        $form->add('rules', CollectionType::class, [
            'allow_add' => true,
            'allow_delete' => true,
            'entry_type' => CampaignRuleFormType::class,
            'error_bubbling' => false,
            'constraints' => [new Count(['min' => $minRulesCount, 'max' => self::RULES_MAX])],
        ]);

        if (isset($data['limits'])) {
            $form->add('limits', CampaignLimitFormType::class, ['required' => false]);
        }
    }
}
