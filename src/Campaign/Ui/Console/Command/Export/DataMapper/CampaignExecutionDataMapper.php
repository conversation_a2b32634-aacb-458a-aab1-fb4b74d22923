<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Ui\Console\Command\Export\DataMapper;

use Assert\AssertionFailedException;
use OpenLoyalty\Campaign\Domain\Entity\CampaignExecution\CampaignExecution as CampaignExecutionEntity;
use OpenLoyalty\Campaign\Ui\Console\Command\Export\Response\CampaignExecution;
use OpenLoyalty\Core\Application\Export\ExportDataMapperInterface;

class CampaignExecutionDataMapper implements ExportDataMapperInterface
{
    public function mapList(array $items): array
    {
        $executions = [];
        foreach ($items as $item) {
            $executions[] = $this->map($item);
        }

        return $executions;
    }

    /**
     * @param CampaignExecutionEntity $item
     *
     * @return CampaignExecution
     *
     * @throws AssertionFailedException
     */
    public function map(object $item, array $context = []): object
    {
        return new CampaignExecution(
            $item->getCampaignExecutionId(),
            $item->getCampaign()->getStoreId(),
            $item->getCampaign()->getCampaignId(),
            null,
            $item->getCreatedAt(),
            $item->getUpdatedAt(),
            $item->getExecutedAt(),
            $item->getStatus(),
            $item->getCustomerId(),
            $item->getTransactionId(),
            $item->getMessage(),
            $item->getWarningMessage()
        );
    }
}
