<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Ui\Console\Command\Export\Response;

use DateTimeImmutable;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectStatus;
use OpenLoyalty\Core\Domain\Id\CalculatedEffectResultId;
use OpenLoyalty\Core\Domain\Id\CampaignExecutionId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Core\Domain\Id\StoreId;

final class CalculatedEffectResult
{
    public function __construct(
       private CalculatedEffectResultId $calculatedEffectResultId,
       private StoreId $tenantId,
       private CampaignExecutionId $campaignExecutionId,
       private EffectStatus $status,
       private CustomerId $targetId,
       private string $type,
       private DateTimeImmutable $createdAt,
       private DateTimeImmutable $updatedAt,
       private ?float $points,
       private ?float $couponValue,
       private ?RewardId $rewardId,
       private ?string $walletCode,
       private ?string $error
    ) {
    }

    public function getCalculatedEffectResultId(): CalculatedEffectResultId
    {
        return $this->calculatedEffectResultId;
    }

    public function getTenantId(): StoreId
    {
        return $this->tenantId;
    }

    public function getCampaignExecutionId(): CampaignExecutionId
    {
        return $this->campaignExecutionId;
    }

    public function getStatus(): string
    {
        return (string) $this->status;
    }

    public function getTargetId(): CustomerId
    {
        return $this->targetId;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function getPoints(): ?float
    {
        return $this->points;
    }

    public function getCouponValue(): ?float
    {
        return $this->couponValue;
    }

    public function getRewardId(): ?RewardId
    {
        return $this->rewardId;
    }

    public function getWalletCode(): ?string
    {
        return $this->walletCode;
    }

    public function getError(): ?string
    {
        return $this->error;
    }
}
