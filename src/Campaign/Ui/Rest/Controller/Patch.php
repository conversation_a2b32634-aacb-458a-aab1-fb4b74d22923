<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Campaign\Application\Command\PartialUpdateCampaign;
use OpenLoyalty\Campaign\Application\UseCase\PartialUpdateCampaignUseCase;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Infrastructure\Form\Type\PartialUpdateCampaignFormType;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class Patch extends AbstractFOSRestController
{
    public function __construct(
        private readonly PartialUpdateCampaignUseCase $useCase,
        private readonly FormFactoryInterface $formFactory,
        private readonly StoreContextProviderInterface $storeContextProvider
    ) {
    }

    /**
     * @Route(methods={"PATCH"}, name="oloy.campaign.patch", path="/{storeCode}/campaign/{campaign}")
     *
     * @Security("is_granted('UPDATE_CAMPAIGN')")
     */
    public function __invoke(Campaign $campaign, Request $request): View
    {
        $form = $this->formFactory->createNamed(
            'campaign',
            PartialUpdateCampaignFormType::class,
            [],
            [
                'campaignId' => $campaign->getCampaignId(),
                'method' => 'PATCH',
                'store' => $this->storeContextProvider->getStore(),
            ]
        );

        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        /** @var PartialUpdateCampaign $data */
        $data = $form->getData();
        $this->useCase->execute($data);

        return $this->view();
    }
}
