<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\DataMapper;

use OpenLoyalty\Campaign\Application\Response\CampaignCode as CampaignCodeResponse;
use OpenLoyalty\Campaign\Domain\Entity\Code\CampaignCode;
use OpenLoyalty\Core\Application\DataMapper\DataMapperInterface;

class CampaignCodesDataMapper implements DataMapperInterface
{
    public function map(CampaignCode $campaignCode): CampaignCodeResponse
    {
        return new CampaignCodeResponse(
            $campaignCode->getCodeId(),
            $campaignCode->getCode(),
            $campaignCode->getStatus()->getValue(),
            $campaignCode->getUsedByMemberId(),
            $campaignCode->getUsedAt(),
            $campaignCode->getCreatedAt(),
            $campaignCode->getCreatedBy()
        );
    }

    /**
     * @param array<CampaignCode> $entities
     *
     * @return array<CampaignCodeResponse>
     */
    public function mapList(array $entities): array
    {
        $campaignCodes = [];
        foreach ($entities as $entity) {
            $campaignCodes[] = $this->map($entity);
        }

        return $campaignCodes;
    }
}
