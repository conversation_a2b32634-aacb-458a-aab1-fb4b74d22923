<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\DataMapper;

use Doctrine\Common\Collections\Collection;
use OpenLoyalty\Core\Application\DataMapper\DataMapperInterface;

class TranslationsDataMapper implements DataMapperInterface
{
    public function map(Collection $translations): array
    {
        $translationsArray = [];

        foreach ($translations as $translation) {
            $translationsArray[] = [
                'id' => $translation->getId(),
                'name' => $translation->getName(),
                'description' => $translation->getDescription(),
                'locale' => $translation->getLocale(),
            ];
        }

        return $translationsArray;
    }
}
