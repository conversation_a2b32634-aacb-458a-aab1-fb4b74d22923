<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\UseCase;

use OpenLoyalty\Campaign\Application\Command\CreateCampaign;
use OpenLoyalty\Campaign\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Campaign\Domain\Exception\InvalidCampaignTypeException;
use OpenLoyalty\Core\Domain\Exception\InvalidTriggerException;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;

class CreateCampaignUseCase
{
    private CommandBusInterface $commandBus;

    public function __construct(CommandBusInterface $commandBus)
    {
        $this->commandBus = $commandBus;
    }

    /**
     * @throws InvalidConditionException
     * @throws \OpenLoyalty\Campaign\Domain\Effect\InvalidEffectException
     * @throws InvalidCampaignTypeException
     * @throws InvalidTriggerException
     * @throws \OpenLoyalty\Campaign\Domain\Rule\InvalidCampaignRuleException
     */
    public function execute(CreateCampaign $createCampaign): CampaignId
    {
        $this->commandBus->dispatch($createCampaign);

        return $createCampaign->getCampaignId();
    }
}
