<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\Response;

use OpenLoyalty\Campaign\Domain\Entity\Effect\DeductEffect as DeductEffectEntity;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;

class DeductEffectResult extends EffectResult
{
    public function __construct(
        private ?CampaignResponse $campaign,
        private float $points,
        private ?string $wallet,
        private string $expression,
        private ?EffectTarget $target
    ) {
        parent::__construct($campaign, DeductEffectEntity::EFFECT, $target);
    }

    public function getPoints(): float
    {
        return $this->points;
    }

    public function getExpression(): string
    {
        return $this->expression;
    }

    public function getWallet(): ?string
    {
        return $this->wallet;
    }
}
