<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\Response;

use OpenLoyalty\Core\Domain\ValueObject\LimitInterval;

class Usages
{
    public function __construct(
        private readonly float $currentValue,
        private readonly ?float $limitValue,
        private readonly float $remaining,
        private readonly ?LimitInterval $interval
    ) {
    }

    public function getCurrentValue(): float
    {
        return $this->currentValue;
    }

    public function getInterval(): ?LimitInterval
    {
        return $this->interval;
    }

    public function getLimitValue(): ?float
    {
        return $this->limitValue;
    }

    public function getRemaining(): float
    {
        return $this->remaining;
    }
}
