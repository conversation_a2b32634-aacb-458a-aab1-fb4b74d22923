<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\Response;

use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;
use OpenLoyalty\Core\Domain\Id\CampaignId;

class EffectResult
{
    private ?CampaignResponse $campaign;
    private string $effect;
    private ?EffectTarget $target;

    public function __construct(?CampaignResponse $campaign, string $effect, ?EffectTarget $target)
    {
        $this->campaign = $campaign;
        $this->effect = $effect;
        $this->target = $target;
    }

    public function getCampaign(): ?CampaignResponse
    {
        return $this->campaign;
    }

    public function getCampaignId(): ?CampaignId
    {
        return $this->campaign?->campaignId;
    }

    public function getEffect(): string
    {
        return $this->effect;
    }

    public function getTarget(): ?EffectTarget
    {
        return $this->target;
    }
}
