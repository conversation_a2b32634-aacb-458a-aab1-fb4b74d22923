<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\Command;

use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Messaging\Domain\NotifiableCommandInterface;

final class PartialUpdateCampaign extends CampaignCommand implements NotifiableCommandInterface
{
    public function __construct(
        private CampaignId $campaignId,
        private readonly ?int $displayOrder,
        private readonly ?bool $active,
        private readonly StoreId $storeId
    ) {
        parent::__construct($campaignId);
    }

    public function getDisplayOrder(): ?int
    {
        return $this->displayOrder;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }
}
