<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\Command;

use DateTimeInterface;
use OpenLoyalty\Campaign\Domain\CampaignTranslation;
use OpenLoyalty\Campaign\Domain\ValueObject\CodeGenerator;
use OpenLoyalty\Campaign\Domain\ValueObject\Limits;
use OpenLoyalty\Campaign\Domain\ValueObject\MemberFilter;
use OpenLoyalty\Campaign\Domain\ValueObject\TransactionItemsFilter;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Model\Label;
use OpenLoyalty\Messaging\Domain\NotifiableCommandInterface;

final class CreateCampaign extends CampaignCommand implements NotifiableCommandInterface
{
    private StoreId $storeId;

    /**
     * @var array<?CampaignTranslation>
     */
    private array $translations;
    private string $trigger;
    private string $type;
    private DateTimeInterface $startsAt;
    private ?DateTimeInterface $endsAt;
    private array $rules;
    private bool $active;
    private ?Limits $limits;
    private ?string $event = null;
    private ?MemberFilter $memberFilter = null;
    private ?AchievementId $achievementId = null;
    private array $labels;
    private ?int $displayOrder = null;
    private ?int $multiLevel = null;
    private ?CodeGenerator $codeGenerator = null;
    private ?int $generateCodes = null;
    private ?string $eventCodeAttribute = null;
    private ?array $visibility;
    private ?array $audience;
    private ?string $triggerStrategyType;
    private ?string $executionSchedule = null;

    public function __construct(
        CampaignId $campaignId,
        StoreId $storeId,
        bool $active,
        array $translations,
        string $type,
        string $trigger,
        DateTimeInterface $startsAt,
        ?DateTimeInterface $endsAt,
        array $rules,
        ?Limits $limits = null,
        array $labels = [],
        ?array $campaignVisibility = null,
        ?array $campaignAudience = null,
        ?string $triggerStrategyType = null,
        /** @var ?array<TransactionItemsFilter> $transactionItemsFilters */
        private ?array $transactionItemsFilters = null
    ) {
        parent::__construct($campaignId);

        $this->active = $active;
        $this->translations = $translations;
        $this->type = $type;
        $this->trigger = $trigger;
        $this->startsAt = $startsAt;
        $this->endsAt = $endsAt;
        $this->storeId = $storeId;
        $this->rules = $rules;
        $this->limits = $limits;
        $this->labels = $labels;
        $this->visibility = $campaignVisibility;
        $this->audience = $campaignAudience;
        $this->triggerStrategyType = $triggerStrategyType;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getTranslations(): array
    {
        return $this->translations;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getTrigger(): string
    {
        return $this->trigger;
    }

    public function getStartsAt(): DateTimeInterface
    {
        return $this->startsAt;
    }

    public function getEndsAt(): ?DateTimeInterface
    {
        return $this->endsAt;
    }

    public function getRules(): array
    {
        return $this->rules;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function getLimits(): ?Limits
    {
        return $this->limits;
    }

    public function getEvent(): ?string
    {
        return $this->event;
    }

    public function setEvent(?string $event): void
    {
        $this->event = $event;
    }

    public function getMemberFilter(): ?MemberFilter
    {
        return $this->memberFilter;
    }

    public function setMemberFilter(?MemberFilter $memberFilter): void
    {
        $this->memberFilter = $memberFilter;
    }

    public function setAchievementId(?AchievementId $achievementId): void
    {
        $this->achievementId = $achievementId;
    }

    public function getAchievementId(): ?AchievementId
    {
        return $this->achievementId;
    }

    /**
     * @return Label[]
     */
    public function getLabels(): array
    {
        return $this->labels;
    }

    public function setDisplayOrder(?int $displayOrder): void
    {
        $this->displayOrder = $displayOrder;
    }

    public function getDisplayOrder(): ?int
    {
        return $this->displayOrder;
    }

    public function enableMultiLevel(?int $multiLevel): void
    {
        $this->multiLevel = $multiLevel;
    }

    public function disableMultiLevel(): void
    {
        $this->multiLevel = null;
    }

    public function getMultiLevel(): ?int
    {
        return $this->multiLevel;
    }

    public function enableCodeGeneration(
        CodeGenerator $codeGenerator,
        int $generateCodes,
        string $eventCodeAttribute
    ): void {
        $this->codeGenerator = $codeGenerator;
        $this->generateCodes = $generateCodes;
        $this->eventCodeAttribute = $eventCodeAttribute;
    }

    public function getCodeGenerator(): ?CodeGenerator
    {
        return $this->codeGenerator;
    }

    public function getGenerateCodes(): ?int
    {
        return $this->generateCodes;
    }

    public function getEventCodeAttribute(): ?string
    {
        return $this->eventCodeAttribute;
    }

    public function getVisibility(): ?array
    {
        return $this->visibility;
    }

    public function getAudience(): ?array
    {
        return $this->audience;
    }

    public function getTriggerStrategyType(): ?string
    {
        return $this->triggerStrategyType;
    }

    public function getExecutionSchedule(): ?string
    {
        return $this->executionSchedule;
    }

    public function setExecutionSchedule(?string $executionSchedule): void
    {
        $this->executionSchedule = $executionSchedule;
    }

    /**
     * @return ?array<TransactionItemsFilter>
     */
    public function getTransactionItemsFilters(): ?array
    {
        return $this->transactionItemsFilters;
    }
}
