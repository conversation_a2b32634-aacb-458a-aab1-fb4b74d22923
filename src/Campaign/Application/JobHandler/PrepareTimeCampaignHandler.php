<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\JobHandler;

use Assert\AssertionFailedException;
use DateTimeImmutable;
use Generator;
use OpenLoyalty\Campaign\Application\Job\PrepareTimeCampaign;
use OpenLoyalty\Campaign\Application\Job\ProcessTimeCampaign;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\CampaignRepositoryInterface;
use OpenLoyalty\Campaign\Domain\CampaignTimeRequestRepositoryInterface;
use OpenLoyalty\Campaign\Domain\Entity\CampaignExecution\CampaignTimeRequest;
use OpenLoyalty\Campaign\Domain\Exception\CampaignNotFoundException;
use OpenLoyalty\Campaign\Domain\TriggerStrategy\TriggerStrategyExecutorInterface;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\CampaignTimeRequestId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\JobBusInterface;
use OpenLoyalty\Core\Domain\Message\JobHandlerInterface;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;

final readonly class PrepareTimeCampaignHandler implements JobHandlerInterface
{
    public function __construct(
        private CampaignRepositoryInterface $campaignRepository,
        private TriggerStrategyExecutorInterface $triggerStrategyExecutor,
        private CampaignTimeRequestRepositoryInterface $campaignTimeRequestRepository,
        private UuidGeneratorInterface $uuidGenerator,
        private JobBusInterface $jobBus,
    ) {
    }

    /**
     * @throws AssertionFailedException
     * @throws CampaignNotFoundException
     */
    public function __invoke(PrepareTimeCampaign $job): void
    {
        $storeId = $job->getStoreId();
        $campaignId = $job->getCampaignId();
        $requestedAt = $job->getRequestedAt();

        $campaign = $this->campaignRepository->findOneBy([
            'campaignId' => $campaignId,
            'store' => $storeId,
        ]);

        if (!$campaign instanceof Campaign) {
            throw new CampaignNotFoundException();
        }

        $filteredMembers = $this->triggerStrategyExecutor->getFilteredMembers(
            $campaign,
            $requestedAt
        );

        $this->campaignTimeRequestRepository->saveBulk(
            $this->getMessagesToSave(
                $filteredMembers,
                $storeId,
                $campaignId,
                $requestedAt
            )
        );

        $this->jobBus->dispatch(new ProcessTimeCampaign($storeId, $campaignId, $requestedAt));
    }

    /**
     * @throws AssertionFailedException
     */
    private function getMessagesToSave(
        iterable $filteredMembers,
        StoreId $storeId,
        CampaignId $campaign,
        DateTimeImmutable $requestedAt
    ): Generator {
        foreach ($filteredMembers as $memberId) {
            yield CampaignTimeRequest::create(
                new CampaignTimeRequestId($this->uuidGenerator->generate()),
                $memberId,
                $storeId,
                $campaign,
                $requestedAt,
            );
        }
    }
}
