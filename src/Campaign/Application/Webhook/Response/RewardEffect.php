<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\Webhook\Response;

use OpenLoyalty\Core\Domain\Id\RewardId;

final class RewardEffect extends CrudEffect
{
    public function __construct(
        string $effect,
        public readonly RewardId $rewardId,
        public readonly ?string $couponValueRule = null
    ) {
        parent::__construct($effect);
    }
}
