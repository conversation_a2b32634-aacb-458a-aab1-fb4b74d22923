<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\Webhook\Factory;

use OpenLoyalty\Campaign\Application\Webhook\Response\Interval;
use OpenLoyalty\Campaign\Application\Webhook\Response\Limit;
use OpenLoyalty\Campaign\Application\Webhook\Response\Limits as LimitsResponse;
use OpenLoyalty\Campaign\Domain\ValueObject\Limits;
use OpenLoyalty\Core\Domain\ValueObject\UnitsLimit\LimitInterface;

final class LimitsResponseFactory
{
    public function create(?Limits $limits): ?LimitsResponse
    {
        if (null === $limits) {
            return null;
        }

        $unitsLimit = $this->getLimitResponseFromLimitVO($limits->getUnitsLimit());
        $unitsPerMemberLimit = $this->getLimitResponseFromLimitVO($limits->getUnitsPerMemberLimit());
        $executionPerMemberLimit = $this->getLimitResponseFromLimitVO($limits->getExecutionsPerMemberLimit());

        return new LimitsResponse($unitsLimit, $unitsPerMemberLimit, $executionPerMemberLimit);
    }

    private function getLimitResponseFromLimitVO(?LimitInterface $limitVO): ?Limit
    {
        if (null === $limitVO) {
            return null;
        }

        return new Limit(
            $limitVO->getValue(),
            new Interval(
                $limitVO->getInterval()?->getType(),
                $limitVO->getInterval()?->getValue(),
            )
        );
    }
}
