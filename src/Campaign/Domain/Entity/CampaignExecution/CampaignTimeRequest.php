<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Entity\CampaignExecution;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\CampaignTimeRequestId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;

class CampaignTimeRequest implements TimestampableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    private CampaignTimeRequestId $requestId;
    private CustomerId $customerId;

    private StoreId $storeId;

    private CampaignId $campaignId;

    private DateTimeImmutable $requestedAt;

    private ?DateTimeImmutable $executedAt;

    private function __construct(
        CampaignTimeRequestId $requestId,
        CustomerId $customerId,
        StoreId $storeId,
        CampaignId $campaignId,
        DateTimeImmutable $requestedAt,
    ) {
        $this->requestId = $requestId;
        $this->customerId = $customerId;
        $this->storeId = $storeId;
        $this->campaignId = $campaignId;
        $this->requestedAt = $requestedAt;
        $this->createdAt = new DateTimeImmutable();
    }

    public static function create(
        CampaignTimeRequestId $requestId,
        CustomerId $customerId,
        StoreId $storeId,
        CampaignId $campaignId,
        DateTimeImmutable $requestedAt,
    ): self {
        return new self(
            $requestId,
            $customerId,
            $storeId,
            $campaignId,
            $requestedAt,
        );
    }

    public function getCustomerId(): CustomerId
    {
        return $this->customerId;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getCampaignId(): CampaignId
    {
        return $this->campaignId;
    }

    public function getRequestedAt(): DateTimeImmutable
    {
        return $this->requestedAt;
    }

    public function markAsExecuted(): void
    {
        $this->executedAt = new DateTimeImmutable();
    }

    public function isExecuted(): bool
    {
        return null !== $this->executedAt;
    }
}
