<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Entity\Visibility;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Enum\VisibilityTarget;
use OpenLoyalty\Core\Domain\Id\CampaignVisibilityId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;

class CampaignVisibility implements TimestampableInterface, BlameableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    /**
     * @var Collection<CampaignVisibilitySegment>
     */
    private Collection $segments;
    /**
     * @var Collection<CampaignVisibilityTier>
     */
    private Collection $tiers;

    /**
     * @param CampaignVisibilityTier[]    $tiers
     * @param CampaignVisibilitySegment[] $segments
     */
    public function __construct(
        private CampaignVisibilityId $campaignVisibilityId,
        private Campaign $campaign,
        private StoreId $storeId,
        private VisibilityTarget $target,
        array $tiers,
        array $segments
    ) {
        $this->tiers = new ArrayCollection();
        $this->segments = new ArrayCollection();

        $this->updateTiers($tiers);
        $this->updateSegments($segments);
    }

    public function updateTiers(array $tiers): void
    {
        $this->tiers->clear();
        foreach ($tiers as $tier) {
            $tier->setCampaignVisibility($this);
            $this->tiers->add($tier);
        }
    }

    public function updateSegments(array $segments): void
    {
        $this->segments->clear();
        foreach ($segments as $segment) {
            $segment->setCampaignVisibility($this);
            $this->segments->add($segment);
        }
    }

    public function getCampaignVisibilityId(): CampaignVisibilityId
    {
        return $this->campaignVisibilityId;
    }

    public function getCampaign(): Campaign
    {
        return $this->campaign;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getTarget(): VisibilityTarget
    {
        return $this->target;
    }

    public function updateTarget(VisibilityTarget $target): void
    {
        $this->target = $target;
    }

    /**
     * @return CampaignVisibilitySegment[]
     */
    public function getSegments(): array
    {
        return $this->segments->toArray();
    }

    /**
     * @return CampaignVisibilityTier[]
     */
    public function getTiers(): array
    {
        return $this->tiers->toArray();
    }
}
