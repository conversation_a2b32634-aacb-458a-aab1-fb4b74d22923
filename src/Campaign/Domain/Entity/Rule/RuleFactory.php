<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Entity\Rule;

use OpenLoyalty\Campaign\Domain\Entity\Condition\ConditionBuilder;
use OpenLoyalty\Campaign\Domain\Entity\Effect\EffectBuilder;
use OpenLoyalty\Campaign\Domain\ValueObject\Target;

class RuleFactory implements RuleFactoryInterface
{
    private ConditionBuilder $conditionFactory;
    private EffectBuilder $effectFactory;

    public function __construct(
        ConditionBuilder $conditionFactory,
        EffectBuilder $effectFactory
    ) {
        $this->conditionFactory = $conditionFactory;
        $this->effectFactory = $effectFactory;
    }

    /**
     * @throws \OpenLoyalty\Campaign\Domain\Rule\InvalidCampaignRuleException
     * @throws \OpenLoyalty\Campaign\Domain\Condition\InvalidConditionException
     * @throws \OpenLoyalty\Campaign\Domain\Effect\InvalidEffectException
     */
    public function fromArray(array $ruleData): Rule
    {
        if (!array_key_exists('conditions', $ruleData) || !array_key_exists('effects', $ruleData)) {
            throw new \OpenLoyalty\Campaign\Domain\Rule\InvalidCampaignRuleException();
        }

        $name = $ruleData['name'] ?? null;
        $description = $ruleData['description'] ?? null;
        $target = $ruleData['target'] ?? Target::SELF;

        $conditionsArray = [];
        foreach ($ruleData['conditions'] as $condition) {
            $conditionsArray[] = $this->conditionFactory->create($condition);
        }

        $effectsArray = [];
        foreach ($ruleData['effects'] as $effect) {
            $effectsArray[] = $this->effectFactory->create($effect);
        }

        return Rule::create($conditionsArray, $effectsArray, Target::create($target), $name, $description);
    }
}
