<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Entity\Audience;

use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\LevelId;

class CampaignAudienceTier
{
    /**
     * @@set
     */
    private int $id;
    private CampaignAudience $campaignAudience;
    private CampaignId $campaignId;

    public function __construct(
        private readonly LevelId $tierId
    ) {
    }

    public function getCampaignAudience(): CampaignAudience
    {
        return $this->campaignAudience;
    }

    public function setCampaignAudience(CampaignAudience $campaignAudience): void
    {
        $this->campaignAudience = $campaignAudience;
        $this->campaignId = $campaignAudience->getCampaign()->getCampaignId();
    }

    public function getCampaignId(): CampaignId
    {
        return $this->campaignId;
    }

    public function getTierId(): LevelId
    {
        return $this->tierId;
    }
}
