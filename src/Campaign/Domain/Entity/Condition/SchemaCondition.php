<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Entity\Condition;

use OpenLoyalty\Core\Domain\Condition\Schema\SchemaConditionItemInterface;
use OpenLoyalty\Core\Domain\ValueObject\Condition\Condition;
use OpenLoyalty\Core\Domain\ValueObject\Condition\Value;

class SchemaCondition extends AbstractCondition implements SchemaConditionItemInterface
{
    private Value $value;
    private Condition $condition;

    public function __construct(Value $value, Condition $condition)
    {
        parent::__construct('');

        $this->value = $value;
        $this->condition = $condition;
    }

    /** @phpstan-ignore-next-line */
    public function getData()
    {
        return null;
    }

    public function getExpression(): string
    {
        throw new \LogicException('Not used');
    }

    public function getOperator(): ?string
    {
        return null;
    }

    /** @phpstan-ignore-next-line */
    public static function fromArray(?string $attribute, $data): ConditionInterface
    {
        throw new \LogicException('Not used');
    }

    public function getValue(): Value
    {
        return $this->value;
    }

    public function getCondition(): Condition
    {
        return $this->condition;
    }
}
