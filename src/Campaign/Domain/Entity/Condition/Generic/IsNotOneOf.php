<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Entity\Condition\Generic;

use OpenLoyalty\Campaign\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Campaign\Domain\Entity\Condition\AbstractCondition;
use OpenLoyalty\Campaign\Domain\Entity\Condition\ConditionInterface;

class IsNotOneOf extends AbstractCondition
{
    public const OPERATOR = 'is_not_one_of';

    /**
     * @var array
     */
    private array $values;

    public function __construct(string $attributeName, array $values)
    {
        parent::__construct($attributeName);
        $this->values = $values;
    }

    public function getExpression(): string
    {
        return sprintf(
            'lower(%s) not in %s',
            $this->getAttributeName(),
            $this->getValuesAsString()
        );
    }

    private function getValuesAsString(): string
    {
        $string = '[';
        foreach ($this->values as $value) {
            if (is_numeric($value)) {
                $string .= $value.',';
            } else {
                $string .= "lower('".$value."'),";
            }
        }
        $string = substr($string, 0, -1);
        $string .= ']';

        return $string;
    }

    public function getOperator(): string
    {
        return self::OPERATOR;
    }

    public static function fromArray(?string $attribute, $data): ConditionInterface
    {
        if (!is_array($data)) {
            throw new InvalidConditionException('Value is not an array');
        }

        return new self($attribute, $data);
    }

    public function getData()
    {
        return $this->values;
    }
}
