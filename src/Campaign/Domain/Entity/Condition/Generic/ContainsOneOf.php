<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Entity\Condition\Generic;

use OpenLoyalty\Campaign\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Campaign\Domain\Entity\Condition\AbstractCondition;
use OpenLoyalty\Campaign\Domain\Entity\Condition\ConditionInterface;

class ContainsOneOf extends AbstractCondition
{
    public const OPERATOR = 'contains_one_of';

    public function __construct(
        readonly string $attributeName,
        /**
         * @var string[] $values
         */
        private readonly array $values,
    ) {
        parent::__construct($attributeName);
    }

    /**
     * @return string[]
     */
    public function getData(): array
    {
        return $this->values;
    }

    public function getExpression(): string
    {
        $criteria = [];
        foreach ($this->values as $value) {
            $criteria[] = sprintf(
                '\'%s\' in %s',
                $value,
                $this->getAttributeName(),
            );
        }

        return sprintf('(%s)', implode(' or ', $criteria));
    }

    public function getOperator(): string
    {
        return self::OPERATOR;
    }

    /**
     * @throws InvalidConditionException
     */
    public static function fromArray(?string $attribute, mixed $data): ConditionInterface
    {
        if (!is_array($data)) {
            throw new InvalidConditionException('Value is not an array');
        }
        if (null === $attribute) {
            throw new InvalidConditionException('Attribute is not an string');
        }

        return new self($attribute, $data);
    }
}
