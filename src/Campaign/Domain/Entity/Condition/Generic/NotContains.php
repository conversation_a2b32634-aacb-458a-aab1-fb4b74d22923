<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Entity\Condition\Generic;

use OpenLoyalty\Campaign\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Campaign\Domain\Entity\Condition\AbstractCondition;
use OpenLoyalty\Campaign\Domain\Entity\Condition\ConditionInterface;

class NotContains extends AbstractCondition
{
    public const OPERATOR = 'not_contains';

    /**
     * @var scalar
     */
    private $value;

    public function __construct(string $attributeName, $value)
    {
        parent::__construct($attributeName);
        $this->value = $value;
    }

    public function getExpression(): string
    {
        return sprintf(
            '\'%s\' not in %s',
            $this->value,
            $this->getAttributeName(),
        );
    }

    public function getOperator(): string
    {
        return self::OPERATOR;
    }

    public static function fromArray(?string $attribute, $data): ConditionInterface
    {
        if (!is_scalar($data)) {
            throw new InvalidConditionException('Value is not a scalar value');
        }

        return new self($attribute, $data);
    }

    /**
     * @return scalar
     */
    public function getData()
    {
        return $this->value;
    }
}
