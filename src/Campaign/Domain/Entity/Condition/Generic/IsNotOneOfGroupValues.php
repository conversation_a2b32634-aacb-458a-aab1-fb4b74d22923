<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Entity\Condition\Generic;

use OpenLoyalty\Campaign\Domain\Entity\Condition\AbstractCondition;
use OpenLoyalty\Campaign\Domain\Entity\Condition\ConditionInterface;

class IsNotOneOfGroupValues extends AbstractCondition
{
    public const OPERATOR = 'is_not_one_of_group_values';

    public function __construct(
        string $attributeName,
        private array $value
    ) {
        parent::__construct($attributeName);
    }

    public function getExpression(): string
    {
        $criteria = [];
        foreach ($this->value['groupOfValues'] as $groupOfValuesId) {
            $criteria[] = sprintf(
                "groupValues(%s).isNotInGroup('%s', '%s')",
                $this->getAttributeName(),
                $groupOfValuesId,
                $this->value['attributeKey'] ?? null
            );
        }

        return sprintf('(%s)', implode(' or ', $criteria));
    }

    public function getOperator(): string
    {
        return self::OPERATOR;
    }

    public static function fromArray(?string $attribute, $data): ConditionInterface
    {
        return new self($attribute, $data);
    }

    public function getData(): array
    {
        return $this->value;
    }
}
