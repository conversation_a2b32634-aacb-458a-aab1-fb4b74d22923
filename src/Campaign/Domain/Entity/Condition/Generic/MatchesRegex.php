<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Entity\Condition\Generic;

use OpenLoyalty\Campaign\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Campaign\Domain\Entity\Condition\AbstractCondition;
use OpenLoyalty\Campaign\Domain\Entity\Condition\ConditionInterface;

class MatchesRegex extends AbstractCondition
{
    public const OPERATOR = 'matches_regex';

    /**
     * @var numeric-string
     */
    private $value;

    public function __construct(string $attributeName, string $value)
    {
        parent::__construct($attributeName);
        $this->value = $value;
    }

    public function getExpression(): string
    {
        return sprintf(
            '%s matches "%s" == 1',
            $this->getAttributeName(),
            $this->value
        );
    }

    public function getOperator(): string
    {
        return self::OPERATOR;
    }

    public static function fromArray(?string $attribute, $data): ConditionInterface
    {
        if (!is_string($data)) {
            throw new InvalidConditionException('Value is not a string');
        }

        return new self($attribute, $data);
    }

    public function getData()
    {
        return $this->value;
    }
}
