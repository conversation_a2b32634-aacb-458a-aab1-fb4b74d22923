<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\TriggerStrategy\Strategy;

use DateInterval;
use DateTimeImmutable;
use Exception;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\CustomerFacadeInterface;
use OpenLoyalty\Core\Domain\ValueObject\TriggerStrategy;

final readonly class BirthdayStrategy implements TriggerStrategyInterface
{
    public function __construct(
        private CustomerFacadeInterface $customerFacade
    ) {
    }

    /**
     * @throws Exception
     */
    public function getFilteredMembers(Campaign $campaign, DateTimeImmutable $currentDate): iterable
    {
        return $this->customerFacade->getByBirthdayAnniversary(
            $campaign->getStoreId(),
            $currentDate,
            (clone $currentDate)->add(new DateInterval('P1D'))
        );
    }

    public function supports(TriggerStrategy $triggerStrategy): bool
    {
        return null === $triggerStrategy->getType() || TriggerStrategy::BIRTHDAY === $triggerStrategy->getType();
    }

    public function getFilteredMembersCount(Campaign $campaign): int
    {
        return 1;
    }
}
