<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\TriggerStrategy;

use DateTimeImmutable;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\TriggerStrategy\Strategy\InvalidTriggerStrategyException;
use OpenLoyalty\Campaign\Domain\TriggerStrategy\Strategy\TriggerStrategyInterface;
use OpenLoyalty\Campaign\Domain\TriggerStrategy\Validator\DailyCampaignTargetLimitValidatorInterface;
use Psr\Log\LoggerInterface;

final class TriggerStrategyExecutor implements TriggerStrategyExecutorInterface
{
    /**
     * @var array<TriggerStrategyInterface>
     */
    private array $strategies;

    /**
     * @throws InvalidTriggerStrategyException
     */
    public function __construct(
        iterable $strategies,
        private readonly DailyCampaignTargetLimitValidatorInterface $validator,
        private readonly LoggerInterface $logger,
    ) {
        foreach ($strategies as $strategy) {
            if (!$strategy instanceof TriggerStrategyInterface) {
                throw new InvalidTriggerStrategyException();
            }

            $this->strategies[] = $strategy;
        }
    }

    public function getFilteredMembers(
        Campaign $campaign,
        DateTimeImmutable $currentDate
    ): iterable {
        foreach ($this->strategies as $strategy) {
            if (!$strategy->supports($campaign->getTriggerStrategy())) {
                continue;
            }

            $targetCount = $strategy->getFilteredMembersCount($campaign);
            if (!$this->validator->isValid($targetCount)) {
                $this->logCampaignLimitReached($campaign, $targetCount);

                return [];
            }

            return $strategy->getFilteredMembers($campaign, $currentDate);
        }

        return [];
    }

    private function logCampaignLimitReached(Campaign $campaign, $targetCount): void
    {
        $this->logger->info(
            'Campaign limit is reached for this effect',
            [
                'storeId' => (string) $campaign->getStoreId(),
                'campaignId' => (string) $campaign->getCampaignId(),
                'targetCount' => $targetCount,
            ]
        );
    }
}
