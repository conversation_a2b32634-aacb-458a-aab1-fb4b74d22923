<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\TriggerStrategy\Validator;

final readonly class DailyCampaignTargetLimitValidator implements DailyCampaignTargetLimitValidatorInterface
{
    public function __construct(
        private int $targetLimit
    ) {
    }

    public function isValid(int $targetCount): bool
    {
        return $this->targetLimit >= $targetCount;
    }
}
