<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\TriggerStrategy\Validator;

use OpenLoyalty\Core\Domain\ValueObject\TriggerStrategy;

final class MemberFilterStrategyValidator implements MemberFilterStrategyValidatorInterface
{
    public function isValid(string $strategy): bool
    {
        return TriggerStrategy::BIRTHDAY === $strategy;
    }
}
