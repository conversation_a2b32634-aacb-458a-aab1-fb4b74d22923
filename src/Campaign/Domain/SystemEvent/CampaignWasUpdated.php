<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\SystemEvent;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Core\Domain\Message\EventInterface;

final class CampaignWasUpdated implements EventInterface
{
    public function __construct(
        private readonly Campaign $campaign,
        private readonly bool $memberLimitChanged = false,
        private readonly bool $globalLimitChanged = false,
    ) {
    }

    public function getCampaign(): Campaign
    {
        return $this->campaign;
    }

    public function isMemberLimitChanged(): bool
    {
        return $this->memberLimitChanged;
    }

    public function isGlobalLimitChanged(): bool
    {
        return $this->globalLimitChanged;
    }
}
