<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\SystemEvent;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Core\Domain\Message\EventInterface;

final readonly class CampaignWasCreated implements EventInterface
{
    public function __construct(
        private Campaign $campaign
    ) {
    }

    public function getCampaign(): Campaign
    {
        return $this->campaign;
    }
}
