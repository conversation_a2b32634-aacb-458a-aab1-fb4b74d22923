<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain;

use DateTimeImmutable;
use OpenLoyalty\Campaign\Domain\Entity\UsedLimit\CampaignUsedLimit;
use OpenLoyalty\Campaign\Domain\Entity\UsedLimit\UsedLimitType;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Repository;

interface CampaignUsedLimitRepositoryInterface extends Repository
{
    public function findByType(StoreId $storeId, CampaignId $campaignId, UsedLimitType $type): ?CampaignUsedLimit;

    public function updateUnitsAggregations(DateTimeImmutable $aggregatedAt, StoreId $storeId): void;

    public function updateCompletionsAggregations(DateTimeImmutable $aggregatedAt, StoreId $storeId): void;
}
