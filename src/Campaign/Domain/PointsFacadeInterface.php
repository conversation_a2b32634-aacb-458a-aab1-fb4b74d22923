<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\Model\Wallet;
use OpenLoyalty\Core\Domain\ValueObject\ActionCause;
use OpenLoyalty\Core\Domain\ValueObject\RangePeriodDate;

interface PointsFacadeInterface
{
    public function addUnits(
        StoreId $storeId,
        CustomerId $customerId,
        float $points,
        string $comment = '',
        ?ActionCause $actionCause = null,
        ?string $walletCode = null,
        ?RangePeriodDate $locked = null,
        ?RangePeriodDate $expired = null,
    ): TransferId;

    public function spendUnit(
        StoreId $storeId,
        CustomerId $customerId,
        float $points,
        Wallet $wallet,
        string $comment = '',
        ?ActionCause $actionCause = null,
    ): void;
}
