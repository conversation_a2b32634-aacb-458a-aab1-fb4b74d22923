<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Time;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\StoreId;

interface CampaignProcessorInterface
{
    public function process(
        StoreId $storeId,
        CampaignId $campaignId,
        DateTimeImmutable $requestedAt
    ): void;
}
