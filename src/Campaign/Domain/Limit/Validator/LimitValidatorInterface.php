<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Limit\Validator;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\ValueObject\Limits;
use OpenLoyalty\Campaign\Domain\ValueObject\UsedUnits;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\ValueObject\UnitsLimit\LimitInterface;

interface LimitValidatorInterface
{
    public function isValid(
        StoreId $storeId,
        Context $context,
        Campaign $campaign,
        array $calculatedEffects,
        ?UsedUnits $usedUnits = null,
    ): bool;

    public function getLimit(?Limits $limits): ?LimitInterface;
}
