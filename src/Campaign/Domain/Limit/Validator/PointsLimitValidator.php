<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Limit\Validator;

use OpenLoyalty\Campaign\Domain\ValueObject\Limits;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\ValueObject\LimitPeriod;
use OpenLoyalty\Core\Domain\ValueObject\UnitsLimit\LimitInterface;

class PointsLimitValidator extends AbstractPointsLimitValidator
{
    public function getLimit(?Limits $limits): ?LimitInterface
    {
        return $limits?->getUnitsLimit();
    }

    protected function getUsedPoints(StoreId $storeId, CampaignId $campaignId, ?LimitPeriod $limitPeriod, ?CustomerId $customerId): float
    {
        return $this->campaignExecutionRepository->getUsedPointsByLimitPeriod($storeId, $campaignId, $limitPeriod, null, true);
    }
}
