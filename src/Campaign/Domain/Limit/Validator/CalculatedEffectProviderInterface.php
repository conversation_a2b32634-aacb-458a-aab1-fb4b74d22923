<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Limit\Validator;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\CalculatedEffect;
use OpenLoyalty\Campaign\Domain\ValueObject\UsedUnits;
use OpenLoyalty\Core\Domain\Id\StoreId;

interface CalculatedEffectProviderInterface
{
    /**
     * @param array<CalculatedEffect> $calculatedEffects
     */
    public function getValidEffects(
        StoreId $storeId,
        Context $context,
        Campaign $campaign,
        array $calculatedEffects,
        bool $logAdditionalInfo = false,
        ?UsedUnits $usedUnits = null,
    ): ?array;
}
