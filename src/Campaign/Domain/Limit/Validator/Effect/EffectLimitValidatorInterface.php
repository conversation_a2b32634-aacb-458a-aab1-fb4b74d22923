<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Limit\Validator\Effect;

use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\CalculatedEffect;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Model\WalletType;

interface EffectLimitValidatorInterface
{
    public function isValid(
        StoreId $storeId,
        Context $context,
        WalletType $walletType,
        ?CalculatedEffect $calculatedEffect
    ): bool;
}
