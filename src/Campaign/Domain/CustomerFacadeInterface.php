<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Campaign\Domain;

use DateTimeImmutable;
use Generator;
use OpenLoyalty\Campaign\Domain\Model\BadgeMember as ModelBadgeMember;
use OpenLoyalty\Core\Domain\Id\BadgeTypeId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\User\Domain\Customer;

interface CustomerFacadeInterface
{
    public function getCustomer(CustomerId $customerId): Customer;

    public function addOrUpdateMemberCustomAttribute(
        StoreId $storeId,
        CustomerId $customerId,
        string $key,
        string $value
    ): void;

    public function removeMemberCustomAttribute(
        StoreId $storeId,
        CustomerId $customerId,
        string $key
    ): void;

    public function getAllActiveMembers(?StoreId $storeId = null): Generator;

    public function getMembers(StoreId $storeId, int $maxResults): Generator;

    public function getAllActiveCount(StoreId $storeId): int;

    public function getAllActiveIdsIterable(StoreId $storeId): Generator;

    public function getByBirthdayAnniversary(
        StoreId $storeId,
        DateTimeImmutable $from,
        DateTimeImmutable $to,
        bool $onlyActive = true
    ): iterable;

    public function getByRegistrationAnniversary(
        StoreId $storeId,
        DateTimeImmutable $from,
        DateTimeImmutable $to,
        bool $onlyActive = true
    ): iterable;

    /**
     * @return ModelBadgeMember[]
     */
    public function getMemberBadges(StoreId $storeId, CustomerId $memberId): array;

    public function badgeTypeExists(StoreId $storeId, BadgeTypeId $badgeTypeId): bool;
}
