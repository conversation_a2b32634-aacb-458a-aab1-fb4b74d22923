<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Webhook\Factory;

use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\Webhook\Response\Trigger as TriggerResponse;
use OpenLoyalty\Core\Domain\Context\ContextItemInterface;
use OpenLoyalty\Core\Domain\Context\InternalEvent;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use OpenLoyalty\Tools\ExpressionLanguage\ContextItem\DynamicContent;

final class TriggerResponseFactory
{
    public function create(Trigger $trigger, array $contextItems): ?TriggerResponse
    {
        $id = $this->getTriggerId($trigger, $contextItems);

        if (null === $id) {
            return null;
        }

        $triggerCode = $trigger->getCode();

        $responseData = $this->getTriggerResponseData($triggerCode, $contextItems);

        return TriggerResponse::create(
            $id,
            $triggerCode,
            ...$responseData
        );
    }

    /**
     * @param  ContextItemInterface[] $contextItems
     * @return array<string, mixed>
     */
    private function getTriggerResponseData(string $triggerCode, array $contextItems): array
    {
        $data = [];

        switch ($triggerCode) {
            case Trigger::INTERNAL_EVENT:
                if ($contextItems[Context::EVENT_ITEM] instanceof InternalEvent) {
                    $bodyData = $contextItems[Context::EVENT_ITEM]->body instanceof DynamicContent
                        ? $contextItems[Context::EVENT_ITEM]->body->data
                        : $contextItems[Context::EVENT_ITEM]->body;
                    $data['payload'] = $bodyData;
                }
                break;

            case Trigger::CUSTOM_EVENT:
                $data = [
                    'createdAt' => $contextItems[Context::EVENT_ITEM]->createdAt, // @phpstan-ignore-line
                    'eventDate' => $contextItems[Context::EVENT_ITEM]->eventDate, // @phpstan-ignore-line
                    'eventId' => $contextItems[Context::EVENT_ITEM]->eventId, // @phpstan-ignore-line
                ];
                break;

            case Trigger::TRANSACTION:
                $data = [
                    'createdAt' => $contextItems[Context::TRANSACTION_ITEM]->createdAt, // @phpstan-ignore-line
                    'purchasedAt' => $contextItems[Context::TRANSACTION_ITEM]->purchasedAt, // @phpstan-ignore-line
                    'documentNumber' => $contextItems[Context::TRANSACTION_ITEM]->documentNumber, // @phpstan-ignore-line
                ];
                break;

            default:
                break;
        }

        return $data;
    }

    /**
     * @param ContextItemInterface[] $contextItems
     */
    private function getTriggerId(Trigger $trigger, array $contextItems): ?string
    {
        return match ($trigger->getCode()) {
            Trigger::ACHIEVEMENT => (string) $contextItems[Context::ACHIEVEMENT_ITEM]->achievementId, // @phpstan-ignore-line
            Trigger::CUSTOM_EVENT, Trigger::CUSTOM_EVENT_UNIQUE_CODE => (string) $contextItems[Context::EVENT_ITEM]->customEventId, // @phpstan-ignore-line
            Trigger::TRANSACTION => (string) $contextItems[Context::TRANSACTION_ITEM]->id, // @phpstan-ignore-line
            Trigger::INTERNAL_EVENT => $contextItems[Context::EVENT_ITEM]->name, // @phpstan-ignore-line
            default => null,
        };
    }
}
