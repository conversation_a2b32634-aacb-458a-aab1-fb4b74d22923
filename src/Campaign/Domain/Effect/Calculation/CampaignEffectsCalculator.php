<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Effect\Calculation;

use Assert\AssertionFailedException;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Condition\CampaignConditionContext;
use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\Engine\EvaluationWarning;
use OpenLoyalty\Campaign\Domain\Entity\Rule\Rule;
use OpenLoyalty\Campaign\Domain\SimulateContext;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;
use OpenLoyalty\Core\Domain\Condition\Schema\ConditionExpressionBuilder;
use OpenLoyalty\Core\Domain\Exception\InvalidTriggerException;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Tools\ExpressionLanguage\ConditionExpressionLanguageInterface;
use OpenLoyalty\Tools\ExpressionLanguage\InvalidExpressionException;
use Psr\Log\LoggerInterface;

class CampaignEffectsCalculator
{
    public function __construct(
        private readonly ConditionExpressionLanguageInterface $conditionExpressionLanguage,
        private readonly LoggerInterface $campaignLogger,
        private readonly EffectCalculator $effectCalculator,
        private readonly ConditionExpressionBuilder $conditionExpressionBuilder
    ) {
    }

    /**
     * @return CalculatedEffect[]
     *
     * @throws AssertionFailedException
     * @throws InvalidTriggerException
     */
    public function calculateEffects(StoreId $storeId, Context $context, Campaign $campaign): array
    {
        $calculatedEffects = [];

        foreach ($campaign->getRules() as $rule) {
            $ruleDSL = $this->buildRuleDSL($campaign, $rule);
            try {
                $conditionResult = $this->conditionExpressionLanguage->evaluate(
                    $ruleDSL,
                    $context->getContextDSL()
                );
            } catch (InvalidExpressionException $exception) {
                $context->addWarning(
                    new EvaluationWarning($campaign->getCampaignId(), $exception->getMessage())
                );
                $this->campaignLogger->warning(
                    \sprintf(
                        'Expression "%s" parsed failed: %s',
                        $ruleDSL,
                        $exception->getMessage()
                    ),
                    [
                        'storeId' => (string) $storeId,
                        'campaignId' => (string) $campaign->getCampaignId(),
                        'condition' => $ruleDSL,
                        'context' => $context->getContextDSL(),
                    ]
                );

                $conditionResult = false;
            }

            if (!$context instanceof SimulateContext) {
                $this->campaignLogger->info(
                    \sprintf('Campaign condition result is: %s', $conditionResult), //@phpstan-ignore-line
                    [
                        'storeId' => (string) $storeId,
                        'campaignId' => (string) $campaign->getCampaignId(),
                        'condition' => $ruleDSL,
                    ]
                );
            }

            if (true !== $conditionResult) {
                continue;
            }

            foreach ($rule->getEffects() as $effect) {
                $effectTarget = $this->getEffectTarget($context, $rule);

                if (!$context instanceof SimulateContext && null === $effectTarget->getCustomerId()) {
                    $this->campaignLogger->warning(
                        'Target customerId can not be null for effect.',
                        [
                            'storeId' => (string) $storeId,
                            'campaignId' => (string) $campaign->getCampaignId(),
                            'condition' => $ruleDSL,
                            'context' => $context->getContextDSL(),
                            'effect' => $effect,
                        ]
                    );
                    continue;
                }

                try {
                    $calculatedEffect = $this->effectCalculator->calculateEffect($context, $effect, $effectTarget);

                    if (null !== $calculatedEffect) {
                        $calculatedEffects[] = $calculatedEffect;
                    }
                } catch (\Throwable $exception) { //Throwable because of DivisionByZero and other Runtime exceptions
                    $context->addWarning(new EvaluationWarning($campaign->getCampaignId(), $exception->getMessage()));
                    $this->campaignLogger->warning(
                        \sprintf(
                            'Expression "%s" parsed failed: %s',
                            $ruleDSL,
                            $exception->getMessage()
                        ),
                        [
                            'storeId' => (string) $storeId,
                            'campaignId' => (string) $campaign->getCampaignId(),
                            'condition' => $ruleDSL,
                            'context' => $context->getContextDSL(),
                            'effect' => $effect,
                        ]
                    );
                }
            }
        }

        if (!$context instanceof SimulateContext) {
            $this->campaignLogger->info(
                \sprintf('Calculated effects for campaign: %s', $campaign->getCampaignId()),
                [
                    'storeId' => (string) $storeId,
                    'campaignId' => (string) $campaign->getCampaignId(),
                    'calculatedEffects' => $calculatedEffects,
                ]
            );
        }

        return $calculatedEffects;
    }

    /**
     * @throws AssertionFailedException
     */
    private function getEffectTarget(Context $context, Rule $rule): EffectTarget
    {
        return $rule->getTarget()->isReferrer() ?
            new EffectTarget($context->getReferrerId(), $rule->getTarget()) :
            new EffectTarget($context->getCustomerId(), $rule->getTarget());
    }

    private function buildRuleDSL(Campaign $campaign, Rule $rule): string
    {
        return sprintf('%s and %s',
            $rule->getConditionDSL(),
            $this->conditionExpressionBuilder->build(
                new CampaignConditionContext(
                    $campaign->getType()->getCode(),
                    $campaign->getTrigger()->getCode(),
                    $campaign->getEvent()
                ),
                $rule->getSchemaConditions()
            )
        );
    }
}
