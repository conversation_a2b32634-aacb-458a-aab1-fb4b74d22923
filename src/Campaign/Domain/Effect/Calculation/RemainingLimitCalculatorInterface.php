<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Effect\Calculation;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\ValueObject\UsedUnits;

interface RemainingLimitCalculatorInterface
{
    /**
     * @param  CalculatedEffect[] $calculatedEffects
     * @return CalculatedEffect[]
     */
    public function calculateRemaining(
        Campaign $campaign,
        Context $context,
        array $calculatedEffects,
        UsedUnits $usedLimits
    ): array;
}
