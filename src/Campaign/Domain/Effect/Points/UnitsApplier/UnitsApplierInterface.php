<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Effect\Points\UnitsApplier;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\CalculatedEffect;
use OpenLoyalty\Core\Domain\Id\TransferId;

interface UnitsApplierInterface
{
    public function addUnits(CalculatedEffect $calculatedEffect, Campaign $campaign, Context $context): ?TransferId;
}
