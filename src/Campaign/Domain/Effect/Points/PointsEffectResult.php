<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Effect\Points;

use OpenLoyalty\Account\Domain\Provider\DefaultWalletTypeProvider;
use OpenLoyalty\Campaign\Domain\Effect\EffectResultInterface;
use OpenLoyalty\Campaign\Domain\Entity\Effect\PointsEffect as PointsEffectEntity;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;
use OpenLoyalty\Core\Domain\Id\CampaignId;

class PointsEffectResult implements EffectResultInterface
{
    public function __construct(
        private CampaignId $campaignId,
        private float $points,
        private ?string $walletCode,
        private string $expression,
        private ?EffectTarget $target
    ) {
        $this->walletCode = $this->walletCode ?? DefaultWalletTypeProvider::DEFAULT_UNIT_CODE;
    }

    public function getPoints(): float
    {
        return $this->points;
    }

    public function getExpression(): string
    {
        return $this->expression;
    }

    public function getEffect(): string
    {
        return PointsEffectEntity::EFFECT;
    }

    public function getCampaignId(): CampaignId
    {
        return $this->campaignId;
    }

    public function getTarget(): ?EffectTarget
    {
        return $this->target;
    }

    public function getWalletCode(): ?string
    {
        return $this->walletCode;
    }
}
