<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Effect\Reward;

use OpenLoyalty\Campaign\Domain\Effect\EffectResultInterface;
use OpenLoyalty\Campaign\Domain\Entity\Effect\RewardEffect as RewardEffectEntity;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\RewardId;

class RewardEffectResult implements EffectResultInterface
{
    private CampaignId $campaignId;
    private RewardId $rewardId;
    private ?float $couponValue;
    private ?EffectTarget $target;

    public function __construct(
        CampaignId $campaignId,
        RewardId $rewardId,
        ?float $couponValue = null,
        ?EffectTarget $target = null
    ) {
        $this->campaignId = $campaignId;
        $this->rewardId = $rewardId;
        $this->couponValue = $couponValue;
        $this->target = $target;
    }

    public function getRewardId(): RewardId
    {
        return $this->rewardId;
    }

    public function getCouponValue(): ?float
    {
        return $this->couponValue;
    }

    public function getEffect(): string
    {
        return RewardEffectEntity::EFFECT;
    }

    public function getCampaignId(): CampaignId
    {
        return $this->campaignId;
    }

    public function getTarget(): ?EffectTarget
    {
        return $this->target;
    }
}
