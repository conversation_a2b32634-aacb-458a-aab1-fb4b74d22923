<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Effect\CustomAttribute;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\CustomerFacadeInterface;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\CalculatedEffect;
use OpenLoyalty\Campaign\Domain\Effect\EffectAlgorithmInterface;
use OpenLoyalty\Campaign\Domain\Entity\CampaignExecution\CalculatedEffectResult;
use OpenLoyalty\Campaign\Domain\Entity\Effect\AssignMemberCustomAttributeEffect as EntityAssignMemberCustomAttributeEffect;
use OpenLoyalty\Campaign\Domain\Factory\CalculatedEffectResultFactoryInterface;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectStatus;
use OpenLoyalty\Core\Domain\Model\Label;
use Psr\Log\LoggerInterface;

class AssignMemberCustomAttributeEffect implements EffectAlgorithmInterface
{
    public function __construct(
        private CustomerFacadeInterface $customerFacade,
        private CalculatedEffectResultFactoryInterface $calculatedEffectResultFactory,
        private LoggerInterface $campaignLogger,
    ) {
    }

    public function supports(CalculatedEffect $calculatedEffect): bool
    {
        return EntityAssignMemberCustomAttributeEffect::EFFECT === $calculatedEffect->getEffect()->getType();
    }

    public function apply(
        Context $context,
        Campaign $campaign,
        CalculatedEffect $calculatedEffect
    ): CalculatedEffectResult {
        if (!$calculatedEffect->getEffect() instanceof EntityAssignMemberCustomAttributeEffect) {
            throw new \InvalidArgumentException(get_class($calculatedEffect->getEffect()));
        }

        try {
            $this->customerFacade->addOrUpdateMemberCustomAttribute(
                $campaign->getStoreId(),
                $calculatedEffect->getTarget()->getCustomerId(),
                $calculatedEffect->getEffect()->getCustomAttributeKey(),
                $calculatedEffect->getCustomAttributeValue()
            );

            return $this->calculatedEffectResultFactory->createCustomAttribute(
                EffectStatus::createSuccess(),
                $calculatedEffect->getTarget()->getCustomerId(),
                new Label(
                    $calculatedEffect->getEffect()->getCustomAttributeKey(),
                    $calculatedEffect->getCustomAttributeValue()
                )
            );
        } catch (\Throwable $exception) {
            $this->campaignLogger->error(
                $exception->getMessage(),
                [
                    'campaignId' => (string) $campaign->getCampaignId(),
                    'context' => $context->getContextDSL(),
                    'customAttribute' => $calculatedEffect->getCustomAttributeValue(),
                    'customerId' => (string) $context->getCustomerId(),
                    'targetCustomerId' => (string) $calculatedEffect->getTarget()->getCustomerId(),
                    'innerException' => (string) $exception,
                ]
            );

            return $this->calculatedEffectResultFactory->createCustomAttribute(
                EffectStatus::createFailed(),
                $calculatedEffect->getTarget()->getCustomerId(),
                new Label(
                    $calculatedEffect->getEffect()->getCustomAttributeKey(),
                    $calculatedEffect->getCustomAttributeValue()
                )
            );
        }
    }
}
