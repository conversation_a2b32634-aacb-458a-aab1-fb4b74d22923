<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Factory;

use DateTimeInterface;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Exception\InvalidCampaignEventTypeException;
use OpenLoyalty\Campaign\Domain\ValueObject\CodeGenerator;
use OpenLoyalty\Campaign\Domain\ValueObject\Limits;
use OpenLoyalty\Core\Domain\Exception\InvalidTriggerException;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Store;

interface CampaignFactoryInterface
{
    /**
     * @throws \OpenLoyalty\Campaign\Domain\Condition\InvalidConditionException
     * @throws \OpenLoyalty\Campaign\Domain\Effect\InvalidEffectException
     * @throws \OpenLoyalty\Campaign\Domain\Exception\InvalidCampaignTypeException
     * @throws InvalidTriggerException
     * @throws InvalidCampaignEventTypeException
     * @throws \OpenLoyalty\Campaign\Domain\Rule\InvalidCampaignRuleException
     */
    public function create(
        CampaignId $campaignId,
        Store $store,
        string $type,
        string $trigger,
        DateTimeInterface $startsAt,
        ?DateTimeInterface $endsAt,
        array $rules,
        ?Limits $limits,
        ?string $triggerStrategyType,
        array $labels,
        ?CodeGenerator $codeGenerator,
        ?string $eventCodeAttribute,
        ?string $executionSchedule,
    ): Campaign;
}
