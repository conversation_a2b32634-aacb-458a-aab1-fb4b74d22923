<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain;

use DateTimeImmutable;
use Generator;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\ValueObject\LimitPeriod;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;

interface CampaignGateInterface
{
    public function getCampaignCodes(CriteriaCollectionInterface $criteriaCollection): Generator;

    public function getWalletUnitUsages(StoreId $storeId, WalletTypeId $walletTypeId, CustomerId $customerId, ?LimitPeriod $limitPeriod): float;

    public function isSegmentUsedInCampaigns(SegmentId $segmentId): bool;

    public function isTierUsedInCampaigns(LevelId $tierId): bool;

    /**
     * @return Generator<CustomerId>
     */
    public function findCustomersByCampaignCompletion(
        StoreId $storeId,
        CampaignId $campaignId,
        int $campaignCompletionNumber,
        DateTimeImmutable $fromDate,
        DateTimeImmutable $toDate,
    ): Generator;

    public function getCampaign(CampaignId $campaignId, StoreId $storeId): ?Campaign;

    /**
     * @return array<array<string, string|bool>>
     */
    public function findCampaignsAssociatedToSegment(SegmentId $segmentId, StoreId $storeId): array;

    public function isCampaignTriggeredBy(CampaignId $campaignId, StoreId $storeId, Trigger $triggerType): bool;
}
