<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Checker;

use OpenLoyalty\Campaign\Domain\CampaignCodesRepositoryInterface;
use OpenLoyalty\Core\Domain\Id\CampaignId;

final readonly class ActiveRedemptionCode<PERSON><PERSON>it<PERSON>hecker implements ActiveRedemptionCodeLimitCheckerInterface
{
    public function __construct(
        private CampaignCodesRepositoryInterface $campaignCodesRepository,
        private int $globalRedemptionCodeLimit
    ) {
    }

    public function isGlobalLimitReached(int $newCodesNumber = 0, ?CampaignId $campaignId = null): bool
    {
        $activeCodesCount = $this->getActiveCodesCount($campaignId);

        return $this->hasReachedLimit($activeCodesCount, $newCodesNumber);
    }

    public function getGlobalActiveRedemptionCodeLimit(): int
    {
        return $this->globalRedemptionCodeLimit;
    }

    private function getActiveCodesCount(?CampaignId $campaignId = null): int
    {
        return $this->campaignCodesRepository->getCountAllActiveRedemptionCodes($campaignId);
    }

    private function hasReachedLimit(int $currentCount, int $newCodesNumber): bool
    {
        return ($currentCount + $newCodesNumber) > $this->globalRedemptionCodeLimit;
    }
}
