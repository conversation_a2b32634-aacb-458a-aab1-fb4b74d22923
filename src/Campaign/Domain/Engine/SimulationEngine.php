<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Engine;

use Assert\AssertionFailedException;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Code\CampaignCodeValidatorInterface;
use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\CalculatedEffect;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\CampaignEffectsCalculator;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\RemainingLimitCalculatorInterface;
use OpenLoyalty\Campaign\Domain\Effect\Factory\EffectResultFactory;
use OpenLoyalty\Campaign\Domain\Entity\CampaignExecution\CalculatedEffectResult;
use OpenLoyalty\Campaign\Domain\Limit\Provider\UsedUnitsProviderInterface;
use OpenLoyalty\Campaign\Domain\Limit\Validator\CalculatedEffectProviderInterface;
use OpenLoyalty\Campaign\Domain\Validator\CampaignValidatorInterface;
use OpenLoyalty\Campaign\Domain\ValueObject\ExecutionStatus;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Tools\FeatureFlag\FeatureFlagInterface;
use Psr\Log\LoggerInterface;

class SimulationEngine extends AbstractEngine
{
    public function __construct(
        protected CampaignValidatorInterface $campaignValidator,
        protected CampaignEffectsCalculator $campaignEffectsCalculator,
        protected CalculatedEffectProviderInterface $calculatedEffectProvider,
        private EffectResultFactory $effectResultFactory,
        protected EventBusInterface $eventBus,
        protected CampaignCodeValidatorInterface $campaignCodeValidator,
        LoggerInterface $campaignLogger,
        protected RemainingLimitCalculatorInterface $remainingLimitCalculator,
        protected FeatureFlagInterface $featureFlag,
        protected UsedUnitsProviderInterface $limitProvider
    ) {
        parent::__construct(
            $campaignValidator,
            $campaignEffectsCalculator,
            $calculatedEffectProvider,
            $eventBus,
            $this->campaignCodeValidator,
            $campaignLogger,
            $this->remainingLimitCalculator,
            $this->featureFlag,
            $this->limitProvider
        );
    }

    /**
     * @param CalculatedEffect[] $calculatedEffects
     */
    protected function execute(
        StoreId $storeId,
        Context $context,
        Campaign $campaign,
        array $calculatedEffects
    ): array {
        foreach ($calculatedEffects as $calculatedEffect) {
            $points = $calculatedEffect->getPoints();
            if ((null === $points || $points > 0.0) && !$calculatedEffect->isLimitReached()) {
                $context->addEffect(
                    $this->effectResultFactory->create($campaign, $calculatedEffect)
                );
            }
        }

        return $calculatedEffects; //@phpstan-ignore-line
    }

    /**
     * @param CalculatedEffectResult[] $calculatedEffectResults
     */
    protected function finishCampaignExecution(
        Context $context,
        Campaign $campaign,
        ExecutionStatus $executionStatus,
        array $calculatedEffectResults = [],
        ?string $message = null
    ): void {
        // do nothing
    }

    /**
     * @param array<CalculatedEffect> $calculatedEffects
     *
     * @throws AssertionFailedException
     */
    protected function isCampaignLimitValid(
        StoreId $storeId,
        Context $context,
        Campaign $campaign,
        array $calculatedEffects
    ): ?array {
        if (null === $context->getCustomerId()) {
            return $calculatedEffects;
        }

        return $this->validateCampaignEffects($storeId, $context, $campaign, $calculatedEffects);
    }
}
