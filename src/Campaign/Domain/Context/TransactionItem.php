<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Context;

use OpenLoyalty\Core\Domain\Context\ContextItemInterface;
use OpenLoyalty\Transaction\Domain\Item as DomainTransactionItem;

class TransactionItem implements ContextItemInterface
{
    public ?string $sku = null;
    public ?string $name = null;
    public ?float $qty = 0.0;
    public ?float $grossValue = null;
    public ?string $category = null;
    public ?string $maker = null;
    public array $labels = [];

    public static function create(DomainTransactionItem $item): self
    {
        $instance = new self();
        $instance->sku = $item->getSku()?->getCode();
        $instance->name = $item->getName();
        $instance->qty = $item->getHighPrecisionQuantity() ?? (float) $item->getQuantity();
        $instance->grossValue = $item->getGrossValue();
        $instance->category = $item->getCategory();
        $instance->maker = $item->getMaker();
        $instance->labels = $item->getLabels();

        return $instance;
    }

    public static function createFromArray(array $data): self
    {
        $instance = new self();
        $instance->sku = $data['sku'] ?? null;
        $instance->name = $data['name'] ?? null;
        $instance->qty = $data['highPrecisionQuantity'] ?? (float) $data['qty'];
        $instance->grossValue = $data['grossValue'] ?? null;
        $instance->category = $data['category'] ?? null;
        $instance->maker = $data['maker'] ?? null;
        $instance->labels = $data['labels'] ?? [];

        return $instance;
    }
}
