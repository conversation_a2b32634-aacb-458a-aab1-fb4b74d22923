<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Context\Strategy;

use DateTimeInterface;
use OpenLoyalty\Core\Domain\Context\Strategy\ContextStrategyInterface;
use OpenLoyalty\Core\Domain\Context\Transaction;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;

class SimulateTransactionStrategy implements ContextStrategyInterface
{
    protected Transaction $transaction;

    public function __construct(Transaction $transaction)
    {
        $this->transaction = $transaction;
    }

    /**
     * @throws \OpenLoyalty\Core\Domain\Exception\InvalidTriggerException
     */
    public function getLastTrigger(): Trigger
    {
        $documentType = $this->transaction->documentType;
        $triggerType = (Transaction::TYPE_SELL === $documentType) ? Trigger::TRANSACTION : Trigger::RETURN_TRANSACTION;

        return Trigger::create($triggerType);
    }

    public function getCurrentTime(): DateTimeInterface
    {
        return $this->transaction->purchasedAt;
    }

    public function getContextItems(): array
    {
        return [
            'transaction' => $this->transaction,
        ];
    }
}
