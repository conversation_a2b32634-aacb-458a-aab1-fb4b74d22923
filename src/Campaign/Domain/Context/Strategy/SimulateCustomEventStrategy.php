<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Context\Strategy;

use DateTimeInterface;
use OpenLoyalty\Core\Domain\Context\CustomEvent;
use OpenLoyalty\Core\Domain\Context\Strategy\ContextStrategyInterface;
use OpenLoyalty\Core\Domain\Exception\InvalidTriggerException;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;

class SimulateCustomEventStrategy implements ContextStrategyInterface
{
    public function __construct(protected CustomEvent $customEvent)
    {
    }

    /**
     * @throws InvalidTriggerException
     */
    public function getLastTrigger(): Trigger
    {
        return Trigger::create(Trigger::CUSTOM_EVENT);
    }

    public function getCurrentTime(): DateTimeInterface
    {
        return $this->customEvent->eventDate;
    }

    public function getContextItems(): array
    {
        return [
            'event' => $this->customEvent,
        ];
    }
}
