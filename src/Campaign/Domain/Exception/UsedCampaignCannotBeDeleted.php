<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Exception;

use OpenLoyalty\Core\Domain\Exception\DomainException;

class UsedCampaignCannotBeDeleted extends DomainException
{
    public function __construct()
    {
        parent::__construct('campaign.cannot_delete_used_campaign');
    }
}
