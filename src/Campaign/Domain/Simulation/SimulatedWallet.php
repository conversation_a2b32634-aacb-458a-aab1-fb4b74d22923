<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Simulation;

use OpenLoyalty\Account\Domain\WalletType;
use OpenLoyalty\Core\Domain\Id\AccountId;

class SimulatedWallet
{
    public function __construct(
        private ?AccountId $walletId,
        private WalletType $walletType,
        private bool $rewardSpending,
        private float $activeUnits = 0
    ) {
    }

    public function getWalletId(): ?AccountId
    {
        return $this->walletId;
    }

    public function getWalletType(): WalletType
    {
        return $this->walletType;
    }

    public function getActiveUnits(): float
    {
        return $this->activeUnits;
    }

    public function getRewardSpending(): bool
    {
        return $this->rewardSpending;
    }

    public function addUnits(float $units): void
    {
        $this->activeUnits += $units;
        if ($this->activeUnits < 0) {
            $this->activeUnits = 0;
        }
    }
}
