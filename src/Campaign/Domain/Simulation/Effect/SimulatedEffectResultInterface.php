<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Simulation\Effect;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;

interface SimulatedEffectResultInterface
{
    public function getCampaign(): Campaign;

    public function getEffect(): string;

    public function getTarget(): ?EffectTarget;
}
