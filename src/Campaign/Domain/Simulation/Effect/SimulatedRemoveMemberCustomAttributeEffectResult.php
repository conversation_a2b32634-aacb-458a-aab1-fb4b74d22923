<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Simulation\Effect;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Entity\Effect\RemoveMemberCustomAttributeEffect;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;

class SimulatedRemoveMemberCustomAttributeEffectResult implements SimulatedEffectResultInterface
{
    public function __construct(
        private Campaign $campaign,
        private ?EffectTarget $target,
        private string $customAttributeKey,
    ) {
    }

    public function getEffect(): string
    {
        return RemoveMemberCustomAttributeEffect::EFFECT;
    }

    public function getCampaign(): Campaign
    {
        return $this->campaign;
    }

    public function getTarget(): ?EffectTarget
    {
        return $this->target;
    }

    public function getCustomAttributeKey(): string
    {
        return $this->customAttributeKey;
    }
}
