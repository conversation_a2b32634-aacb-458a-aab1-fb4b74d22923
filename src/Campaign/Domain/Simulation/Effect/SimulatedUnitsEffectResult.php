<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Simulation\Effect;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Entity\Effect\PointsEffect as PointsEffectEntity;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;

class SimulatedUnitsEffectResult implements SimulatedEffectResultInterface
{
    public function __construct(
        private Campaign $campaign,
        private float $points,
        private string $walletCode,
        private string $expression,
        private ?EffectTarget $target
    ) {
    }

    public function getPoints(): float
    {
        return $this->points;
    }

    public function getExpression(): string
    {
        return $this->expression;
    }

    public function getEffect(): string
    {
        return PointsEffectEntity::EFFECT;
    }

    public function getCampaign(): Campaign
    {
        return $this->campaign;
    }

    public function getTarget(): ?EffectTarget
    {
        return $this->target;
    }

    public function getWalletCode(): ?string
    {
        return $this->walletCode;
    }
}
