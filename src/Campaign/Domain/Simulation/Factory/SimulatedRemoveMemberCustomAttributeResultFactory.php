<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Simulation\Factory;

use OpenLoyalty\Campaign\Domain\CampaignRepositoryInterface;
use OpenLoyalty\Campaign\Domain\Effect\EffectResultInterface;
use OpenLoyalty\Campaign\Domain\Entity\Effect\RemoveMemberCustomAttributeEffect;
use OpenLoyalty\Campaign\Domain\Simulation\Effect\SimulatedRemoveMemberCustomAttributeEffectResult;
use OpenLoyalty\Core\Domain\Id\CampaignId;

class SimulatedRemoveMemberCustomAttributeResultFactory implements SimulatedEffectResultFactoryInterface
{
    public function __construct(
        private CampaignRepositoryInterface $campaignRepository
    ) {
    }

    public function supports(EffectResultInterface $effectResult): bool
    {
        return RemoveMemberCustomAttributeEffect::EFFECT === $effectResult->getEffect();
    }

    public function create(
        CampaignId $campaignId,
        RemoveMemberCustomAttributeEffect|EffectResultInterface $effectResult
    ): SimulatedRemoveMemberCustomAttributeEffectResult {
        return new SimulatedRemoveMemberCustomAttributeEffectResult(
            $this->campaignRepository->byId($campaignId),
            $effectResult->getTarget(),
            $effectResult->getCustomAttributeKey()
        );
    }
}
