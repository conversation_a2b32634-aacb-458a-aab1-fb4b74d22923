<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Simulation\Factory;

use OpenLoyalty\Campaign\Domain\CampaignRepositoryInterface;
use OpenLoyalty\Campaign\Domain\Effect\EffectResultInterface;
use OpenLoyalty\Campaign\Domain\Effect\Reward\RewardEffectResult;
use OpenLoyalty\Campaign\Domain\Entity\Effect\RewardEffect;
use OpenLoyalty\Campaign\Domain\Simulation\Effect\SimulatedEffectResultInterface;
use OpenLoyalty\Campaign\Domain\Simulation\Effect\SimulatedRewardEffectResult;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Reward\Domain\Repository\RewardRepository;

class SimulatedRewardEffectResultFactory implements SimulatedEffectResultFactoryInterface
{
    public function __construct(
        private CampaignRepositoryInterface $campaignRepository,
        private RewardRepository $rewardRepository
    ) {
    }

    public function supports(EffectResultInterface $effectResult): bool
    {
        return RewardEffect::EFFECT === $effectResult->getEffect();
    }

    public function create(
        CampaignId $campaignId,
        RewardEffectResult|EffectResultInterface $effectResult
    ): SimulatedEffectResultInterface {
        if (!$effectResult instanceof RewardEffectResult) {
            throw new \InvalidArgumentException(get_class($effectResult));
        }

        return new SimulatedRewardEffectResult(
            $this->campaignRepository->byId($campaignId),
            $this->rewardRepository->byId($effectResult->getRewardId()),
            $effectResult->getTarget(),
            $effectResult->getCouponValue()
        );
    }
}
