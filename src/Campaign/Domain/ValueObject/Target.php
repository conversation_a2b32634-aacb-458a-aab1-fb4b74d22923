<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\ValueObject;

use InvalidArgumentException;

class Target
{
    public const SELF = 'self';
    public const REFERRER = 'referrer';

    public const ALLOWED_CODES = [
        self::SELF => self::SELF,
        self::REFERRER => self::REFERRER,
    ];

    private string $code;

    private function __construct(string $code)
    {
        if (!in_array($code, self::ALLOWED_CODES)) {
            throw new InvalidArgumentException();
        }

        $this->code = $code;
    }

    /**
     * @throws InvalidArgumentException
     */
    public static function create(string $code): self
    {
        return new self($code);
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function equals(self $type): bool
    {
        return $type->getCode() === $this->code;
    }

    public function __toString(): string
    {
        return $this->code;
    }

    public function isReferrer(): bool
    {
        return self::REFERRER === $this->code;
    }
}
