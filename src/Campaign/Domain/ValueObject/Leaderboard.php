<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\ValueObject;

use OpenLoyalty\Core\Domain\ValueObject\Condition\ConditionItem;

final readonly class TransactionItemsFilter
{
    public function __construct(
        private string $code,
        private ?string $name,
        /** @var ConditionItem[] $filters */
        private array $filters = []
    ) {
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @return ConditionItem[]
     */
    public function getFilters(): array
    {
        return $this->filters;
    }
}
