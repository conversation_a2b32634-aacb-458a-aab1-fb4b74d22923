<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\ValueObject;

use DateTimeInterface;

class Activity
{
    private DateTimeInterface $startsAt;
    private ?DateTimeInterface $endsAt;

    private function __construct(DateTimeInterface $startsAt, ?DateTimeInterface $endsAt)
    {
        $this->startsAt = $startsAt;
        $this->endsAt = $endsAt;
    }

    public static function create(DateTimeInterface $startsAt, ?DateTimeInterface $endsAt = null): self
    {
        return new self($startsAt, $endsAt);
    }

    public function getStartsAt(): DateTimeInterface
    {
        return $this->startsAt;
    }

    public function getEndsAt(): ?DateTimeInterface
    {
        return $this->endsAt;
    }
}
