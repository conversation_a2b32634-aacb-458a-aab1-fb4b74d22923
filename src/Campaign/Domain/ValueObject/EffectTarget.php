<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\ValueObject;

use OpenLoyalty\Core\Domain\Id\CustomerId;

class EffectTarget
{
    private ?CustomerId $customerId;
    private Target $target;

    public function __construct(?CustomerId $customerId, Target $target)
    {
        $this->customerId = $customerId;
        $this->target = $target;
    }

    public function getCustomerId(): ?CustomerId
    {
        return $this->customerId;
    }

    public function getTarget(): Target
    {
        return $this->target;
    }
}
