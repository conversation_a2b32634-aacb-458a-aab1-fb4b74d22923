<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain;

use Assert\AssertionFailedException;
use DateTimeImmutable;
use DateTimeInterface;
use OpenLoyalty\Campaign\Domain\Context\Customer;
use OpenLoyalty\Campaign\Domain\Context\ExecutionContext;
use OpenLoyalty\Campaign\Domain\Effect\EffectResultInterface;
use OpenLoyalty\Campaign\Domain\Engine\EvaluationWarning;
use OpenLoyalty\Core\Domain\Context\ContextInterface;
use OpenLoyalty\Core\Domain\Context\ContextItemInterface;
use OpenLoyalty\Core\Domain\Context\CustomEvent;
use OpenLoyalty\Core\Domain\Context\InternalEvent;
use OpenLoyalty\Core\Domain\Context\LazyCustomerItem;
use OpenLoyalty\Core\Domain\Context\Transaction;
use OpenLoyalty\Core\Domain\Exception\InvalidTriggerException;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\CustomEventId;
use OpenLoyalty\Core\Domain\Id\TransactionId;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;

class Context implements ContextInterface
{
    public const CURRENT_TIME_ITEM = 'currentTime';
    public const CUSTOMER_ITEM = 'customer';
    public const REFERRER_ITEM = 'referrer';
    public const TRANSACTION_ITEM = 'transaction';
    public const ACHIEVEMENT_ITEM = 'achievement';
    public const EVENT_ITEM = 'event';
    public const EXECUTION_CONTEXT_ITEM = 'executionContext';

    private DateTimeInterface $currentTime;

    /**
     * @var array<ContextItemInterface>
     */
    private array $contextItems = [];

    /**
     * @var array<EffectResultInterface>
     */
    private array $effects = [];

    /**
     * @var array<EvaluationWarning>
     */
    private array $warnings = [];

    private Trigger $trigger;

    public function __construct(Trigger $trigger, ?DateTimeInterface $currentTime = null)
    {
        $this->trigger = $trigger;
        $this->currentTime = $currentTime ?? new DateTimeImmutable();
    }

    public function addItem(string $name, ?ContextItemInterface $value): self
    {
        $this->contextItems[$name] = $value;

        return $this;
    }

    public function getItem(string $name): ?ContextItemInterface
    {
        return $this->contextItems[$name] ?? null;
    }

    public function updateItem(string $name, ?ContextItemInterface $value): self
    {
        $this->contextItems[$name] = $value;

        return $this;
    }

    public function removeItem(string $name): void
    {
        if (array_key_exists($name, $this->contextItems)) {
            unset($this->contextItems[$name]);
        }
    }

    /**
     * @return ContextItemInterface[]
     */
    public function getItems(): array
    {
        return $this->contextItems;
    }

    public function getExecutionContext(): ?ExecutionContext
    {
        if (!array_key_exists(self::EXECUTION_CONTEXT_ITEM, $this->contextItems)) {
            return null;
        }

        if (!$this->contextItems[self::EXECUTION_CONTEXT_ITEM] instanceof ExecutionContext) {
            return null;
        }

        return $this->contextItems[self::EXECUTION_CONTEXT_ITEM];
    }

    public function getCustomerId(): ?CustomerId
    {
        return $this->getContextCustomerId(self::CUSTOMER_ITEM);
    }

    public function getReferrerId(): ?CustomerId
    {
        return $this->getContextCustomerId(self::REFERRER_ITEM);
    }

    public function getContextDSL(): array
    {
        return array_merge(
            $this->contextItems,
            [
                self::CURRENT_TIME_ITEM => $this->currentTime,
            ]
        );
    }

    public function addEffect(EffectResultInterface $effectResult): void
    {
        $this->effects[] = $effectResult;
    }

    /**
     * @return EffectResultInterface[]
     */
    public function getEffects(): array
    {
        return $this->effects;
    }

    public function addWarning(EvaluationWarning $warning): void
    {
        $this->warnings[] = $warning;
    }

    /**
     * @return EvaluationWarning[]
     */
    public function getWarnings(): array
    {
        return $this->warnings;
    }

    public function getCurrentTime(): DateTimeInterface
    {
        return $this->currentTime;
    }

    public function getTrigger(): Trigger
    {
        return $this->trigger;
    }

    /**
     * @throws InvalidTriggerException
     * @throws AssertionFailedException
     */
    public function getTriggerId(): ?string
    {
        if ($this->trigger->equals(Trigger::create(Trigger::CUSTOM_EVENT))) {
            return (string) $this->getCustomEventId();
        }

        if ($this->trigger->equals(Trigger::create(Trigger::TRANSACTION))) {
            return (string) $this->getTransactionId();
        }

        if ($this->trigger->equals(Trigger::create(Trigger::INTERNAL_EVENT))) {
            return $this->getInternalEventName();
        }

        if ($this->trigger->equals(Trigger::create(Trigger::CUSTOM_EVENT_UNIQUE_CODE))) {
            return (string) $this->getCustomEventId();
        }

        if ($this->trigger->equals(Trigger::create(Trigger::ACHIEVEMENT))) {
            /* @phpstan-ignore-next-line */
            return (string) $this->contextItems[self::ACHIEVEMENT_ITEM]->achievementId;
        }

        return null;
    }

    public function isSimulated(): bool
    {
        return false;
    }

    public function isSync(): bool
    {
        return false;
    }

    public function getCodeFromEvent(string $eventCodeAttribute): ?string
    {
        if (!array_key_exists(self::EVENT_ITEM, $this->contextItems) || !$this->contextItems[self::EVENT_ITEM] instanceof CustomEvent) {
            return null;
        }

        $eventBody = $this->contextItems[self::EVENT_ITEM]->body;

        /* @phpstan-ignore-next-line */
        if (empty($eventBody) || !is_string($eventBody->__get($eventCodeAttribute))) {
            return null;
        }

        return $eventBody->__get($eventCodeAttribute); //@phpstan-ignore-line
    }

    /**
     * @throws AssertionFailedException
     */
    public function getTransactionId(): ?TransactionId
    {
        /** @var Transaction $transaction */
        $transaction = $this->getContextDSL()[self::TRANSACTION_ITEM] ?? null;

        return $transaction ? new TransactionId($transaction->id) : null;
    }

    public function getCustomEventId(): ?CustomEventId
    {
        /** @var CustomEvent|InternalEvent $event */
        $event = $this->getContextDSL()[self::EVENT_ITEM] ?? null;

        return $event instanceof CustomEvent ? $event->customEventId : null;
    }

    public function getInternalEventName(): ?string
    {
        /** @var CustomEvent|InternalEvent $event */
        $event = $this->getContextDSL()[self::EVENT_ITEM] ?? null;

        return $event instanceof InternalEvent ? $event->name : null;
    }

    protected function getContextCustomerId(string $contextName): ?CustomerId
    {
        if (!array_key_exists($contextName, $this->contextItems)) {
            return null;
        }

        $contextItem = $this->contextItems[$contextName];

        if ($contextItem instanceof LazyCustomerItem) {
            return $contextItem->getCustomerId();
        }

        if (!$contextItem instanceof Customer) {
            return null;
        }

        return null !== $contextItem->id ? new CustomerId($contextItem->id) : null;
    }
}
