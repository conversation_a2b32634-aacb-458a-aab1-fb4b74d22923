<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Validator\Constraint;

use OpenLoyalty\Core\Domain\Id\BadgeTypeId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use Symfony\Component\Validator\Constraint;

final class CompletionCountChangeValueValid extends Constraint
{
    public ?BadgeTypeId $badgeTypeId;
    public ?CustomerId $memberId;
    public ?StoreId $storeId;

    public function getRequiredOptions(): array
    {
        return ['badgeTypeId', 'memberId', 'storeId'];
    }
}
