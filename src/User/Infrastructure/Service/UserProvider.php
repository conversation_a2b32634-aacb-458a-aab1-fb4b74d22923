<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Service;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\User\Domain\UserRepositoryReadContextInterface;
use OpenLoyalty\User\Infrastructure\Entity\User;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;

/**
 * @implements UserProviderInterface<UserInterface>
 */
class UserProvider implements UserProviderInterface
{
    public function __construct(
        protected readonly UserRepositoryReadContextInterface $userRepository,
        protected readonly StoreContextProviderInterface $storeContextProvider,
    ) {
    }

    public function loadUserByUsername(string $username): UserInterface
    {
        $requestedStore = $this->storeContextProvider->getRequestedStore();

        return $this->loadUserByUsernameOrEmail(
            $username,
            $requestedStore?->getStoreId(),
            User::class
        );
    }

    public function loadUserByIdentifier(string $identifier): UserInterface
    {
        return $this->loadUserByUsername($identifier);
    }

    public function loadUserByUsernameOrEmail(
        string $username,
        ?StoreId $storeId,
        string $class,
        bool $onlyActive = true
    ): User {
        $user = $this->userRepository->getUserByUsernameOrEmail($username, $storeId, $class, $onlyActive);

        if ($user instanceof $class) {
            return $user;
        }

        throw new UserNotFoundException();
    }

    public function refreshUser(UserInterface $user): UserInterface
    {
        if (!$user instanceof User) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', get_class($user)));
        }

        return $this->loadUserByUsername($user->getUsername());
    }

    public function supportsClass(string $class): bool
    {
        return User::class == $class;
    }
}
