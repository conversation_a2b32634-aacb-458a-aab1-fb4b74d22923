<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Service;

use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Settings\Infrastructure\Model\SettingsNames;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;
use OpenLoyalty\User\Domain\Provider\ExcludeDeliveryCostsProvider;

class SettingsBasedExcludeDeliveryCostsProvider implements ExcludeDeliveryCostsProvider
{
    protected SettingsManager $settingsManager;

    public function __construct(SettingsManager $settingsManager)
    {
        $this->settingsManager = $settingsManager;
    }

    public function areExcluded(Store $store): bool
    {
        $ex = $this->settingsManager->getSettingByKey(SettingsNames::EXCLUDE_DELIVERY_COSTS_FROM_TIER_ASSIGMENT->value, $store);
        if (null === $ex || false === $ex->getValue() || null === $ex->getValue()) {
            return false;
        }

        return true;
    }
}
