<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TierSetId;
use OpenLoyalty\Level\Domain\TierGateInterface;
use OpenLoyalty\User\Domain\TierFacadeInterface;

final class TierFacade implements TierFacadeInterface
{
    public function __construct(private readonly TierGateInterface $tierGate)
    {
    }

    public function isTiersModuleEnabled(StoreId $storeId): bool
    {
        return $this->tierGate->isTiersEnabled($storeId);
    }

    public function getTierName(LevelId $levelId): ?string
    {
        return $this->tierGate->getTierName($levelId);
    }

    public function isTierFromDefaultTierSet(LevelId $levelId): bool
    {
        return $this->tierGate->isTierFromDefaultTierSet($levelId);
    }

    public function isDefaultTierSet(TierSetId $tierSetId): bool
    {
        return $this->tierGate->isDefaultTierSet($tierSetId);
    }

    public function isTierAssignedManually(TierSetId $tierSetId, CustomerId $customerId): bool
    {
        return $this->tierGate->isTierAssignedManually($tierSetId, $customerId);
    }
}
