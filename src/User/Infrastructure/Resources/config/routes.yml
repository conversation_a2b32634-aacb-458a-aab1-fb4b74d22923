acl:
    resource: '../../../Ui/Rest/Controller/Acl/'
    type: annotation

admin:
    resource: '../../../Ui/Rest/Controller/Admin/'
    type: annotation

change_password_admin:
    resource: '../../../Ui/Rest/Controller/Admin/PutPassword.php'
    type: annotation

change_password_customer:
    resource: '../../../Ui/Rest/Controller/Member/PutPassword.php'
    type: annotation

referral:
    resource: '../../../Ui/Rest/Controller/Referral/'
    type: annotation

member:
    resource: '../../../Ui/Rest/Controller/Member/'
    type: annotation

apiKey:
    resource: '../../../Ui/Rest/Controller/ApiKey/'
    type: annotation

request_change_password_admin:
    resource: '../../../Ui/Rest/Controller/Admin/PostPasswordResetRequest.php'
    type: annotation

request_change_password_customer:
    resource: '../../../Ui/Rest/Controller/Member/PostPasswordResetRequest.php'
    type: annotation

reset_password_admin:
    resource: '../../../Ui/Rest/Controller/Admin/PostPasswordReset.php'
    type: annotation

reset_password_customer:
    resource: '../../../Ui/Rest/Controller/Member/PostPasswordReset.php'
    type: annotation

security:
    resource: '../../../Ui/Rest/Controller/GetTokenRevoke.php'
    type: annotation
