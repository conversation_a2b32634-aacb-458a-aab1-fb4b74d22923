services:

    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    _instanceof:
        OpenLoyalty\Settings\Infrastructure\Provider\ChoiceProvider:
            tags:
                - { name: 'ol.settings.choices' }

    OpenLoyalty\User\Infrastructure\Service\UserProvider: ~
    OpenLoyalty\User\Infrastructure\Service\AdminProvider: ~
    OpenLoyalty\User\Infrastructure\Service\CustomerProvider: ~
    OpenLoyalty\User\Infrastructure\Service\InternalAdminProvider: ~
    OpenLoyalty\User\Infrastructure\Service\InternalAdminFactory: ~
    OpenLoyalty\User\Infrastructure\Service\OidcUserProvider:
        arguments:
            $userAuthenticationManager: '@OpenLoyalty\User\Infrastructure\Authentication\Manager\OidcUserAuthenticationManager'

    OpenLoyalty\User\Infrastructure\Service\ChainUserProvider:
        arguments:
            $providers:
                - '@OpenLoyalty\User\Infrastructure\Service\AdminProvider'
                - '@OpenLoyalty\User\Infrastructure\Service\CustomerProvider'
                - '@OpenLoyalty\User\Infrastructure\Service\InternalAdminProvider'

    OpenLoyalty\User\Infrastructure\Service\LevelIdProvider: ~

    OpenLoyalty\User\Domain\LevelIdProviderInterface: '@OpenLoyalty\User\Infrastructure\Service\LevelIdProvider'

    OpenLoyalty\User\Domain\Provider\ExcludeDeliveryCostsProvider: '@OpenLoyalty\User\Infrastructure\Service\SettingsBasedExcludeDeliveryCostsProvider'

    OpenLoyalty\User\Infrastructure\Service\SettingsBasedExcludeDeliveryCostsProvider: ~

    OpenLoyalty\User\Domain\Provider\TierAssignTypeProvider: '@OpenLoyalty\User\Infrastructure\Service\SettingsBasedTierAssignTypeProvider'

    OpenLoyalty\User\Infrastructure\Service\SettingsBasedTierAssignTypeProvider: ~

    OpenLoyalty\User\Infrastructure\TierWalletCodeProviderInterface: '@OpenLoyalty\User\Infrastructure\Service\SettingsTierWalletCodeProvider'

    OpenLoyalty\User\Infrastructure\Service\SettingsTierWalletCodeProvider: ~

    OpenLoyalty\User\Infrastructure\Status\CustomerStatusProvider: ~
    OpenLoyalty\User\Application\Status\CustomerStatusProviderInterface: '@OpenLoyalty\User\Infrastructure\Status\CustomerStatusProvider'

    OpenLoyalty\User\Application\Status\CustomerNextLevelProvider: ~
