OpenLoyalty\User\Application\Response\Customer:
  exclusion_policy: ALL
  properties:
    customerId:
      inline: true
    gender:
      inline: true
    channelId:
      inline: true
    active:
      expose: true
    firstName:
      expose: true
    lastName:
      expose: true
    email:
      expose: true
    phone:
      expose: true
    birthDate:
      expose: true
    createdAt:
      expose: true
    updatedAt:
      expose: true
    registeredAt:
      expose: true
    agreement1:
      expose: true
    agreement2:
      expose: true
    agreement3:
      expose: true
    rewardPurchases:
      expose: true
    transactionsCount:
      expose: true
    returnTransactionsCount:
      expose: true
    transactionsAmount:
      expose: true
    returnTransactionsAmount:
      expose: true
    transactionsAmountWithoutDeliveryCosts:
      expose: true
    firstTransactionDate:
      expose: true
    amountExcludedForLevel:
      expose: true
    averageTransactionAmount:
      expose: true
    averageReturnTransactionAmount:
      expose: true
    levelAchievementDate:
      expose: true
    labels:
      expose: true
    anonymized:
      expose: true
    referralToken:
      expose: true
    currency:
      expose: true
    levelPercent:
      expose: true
    loyaltyCardNumber:
      expose: true
    lastLevelRecalculation:
      expose: true
    address:
      expose: true
    lastTransactionDate:
      expose: true
    defaultAccount:
      expose: true
    company:
      expose: true
  virtual_properties:
    getReferrerCustomerId:
      exp: "object.getReferrerCustomerId() ? object.getReferrerCustomerId().__toString() : null"
      serialized_name: referrerCustomerId
    getManuallyAssignedLevelId:
      exp: "object.getManuallyAssignedLevelId() ? object.getManuallyAssignedLevelId().__toString() : null"
      serialized_name: manuallyAssignedLevelId
    getAvatarPath:
      exp: "object.getAvatar() ? object.getAvatar().getPath() : null"
      serialized_name: avatarPath
    getAvatarMime:
      exp: "object.getAvatar() ? object.getAvatar().getMime() : null"
      serialized_name: avatarMime
    getAvatarOriginalName:
      exp: "object.getAvatar() ? object.getAvatar().getOriginalName() : null"
      serialized_name: avatarOriginalName
