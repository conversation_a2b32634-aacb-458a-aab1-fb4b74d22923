<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="en" target-language="en" datatype="plaintext" original="file.ext">
        <body>
            <trans-unit id="customer.level.downgrade_mode.not_supported">
                <source>customer.level.downgrade_mode.not_supported</source>
                <target>Processing skipped. Selected level downgrade mode is not supported.</target>
            </trans-unit>
            <trans-unit id="customer.level.downgrade_mode.not_available_when_tier_assignment_type_is_not_points">
                <source>customer.level.downgrade_mode.not_available_when_tier_assignment_type_is_not_points</source>
                <target>Processing skipped. Recalculation customer level is not possible when tier assignment type is not points.</target>
            </trans-unit>
            <trans-unit id="customer.registration.invalid_phone_number">
                <source>customer.registration.invalid_phone_number</source>
                <target>Incorrect phone number format, use 00000000000.</target>
            </trans-unit>
            <trans-unit id="customer.registration.invalid_loyalty_card_number">
                <source>customer.registration.invalid_loyalty_card_number</source>
                <target>Incorrect loyalty card number.</target>
            </trans-unit>
            <trans-unit id="customer.registration.email_exists">
                <source>customer.registration.email_exists</source>
                <target>Customer with such email already exists.</target>
            </trans-unit>
            <trans-unit id="customer.registration.invalid_email">
                <source>customer.registration.invalid_email</source>
                <target>Incorrect e-mail address.</target>
            </trans-unit>
            <trans-unit id="customer.registration.loyalty_card_number_exists">
                <source>customer.registration.loyalty_card_number_exists</source>
                <target>Customer with such loyalty card number already exists.</target>
            </trans-unit>
            <trans-unit id="customer.registration.phone_number_exists">
                <source>customer.registration.phone_number_exists</source>
                <target>Customer with such phone already exists.</target>
            </trans-unit>
            <trans-unit id="customer.registration.start_level_not_found">
                <source>customer.registration.start_level_not_found</source>
                <target>Neither level is not available as start level for this customer.</target>
            </trans-unit>
            <trans-unit id="customer.profile_edit.invalid_value_type">
                <source>customer.profile_edit.invalid_value_type</source>
                <target>Type of the %field% field value is incorrect. The correct type is %type%.</target>
            </trans-unit>
            <trans-unit id="customer.profile_edit.field_edit_not_allowed">
                <source>customer.profile_edit.field_edit_not_allowed</source>
                <target>You don't have permission to edit this data.</target>
            </trans-unit>
            <trans-unit id="customer.assign_to_level.level_not_found">
                <source>customer.assign_to_level.level_not_found</source>
                <target>Level not found.</target>
            </trans-unit>
            <trans-unit id="customer.bad_confirmation_token">
                <source>customer.bad_confirmation_token</source>
                <target>Bad confirmation token.</target>
            </trans-unit>
            <trans-unit id="user.acl.access.view">
                <source>user.acl.access.view</source>
                <target>View</target>
            </trans-unit>
            <trans-unit id="user.acl.access.modify">
                <source>user.acl.access.modify</source>
                <target>Modify</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.level">
                <source>user.acl.resource.level</source>
                <target>Levels</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.transaction">
                <source>user.acl.resource.transaction</source>
                <target>Transactions</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.reward">
                <source>user.acl.resource.reward</source>
                <target>Rewards</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.reward_category">
                <source>user.acl.resource.reward_category</source>
                <target>Rewards categories</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.issued_rewards">
                <source>user.acl.resource.issued_rewards</source>
                <target>Issued rewards</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.custom_events">
                <source>user.acl.resource.custom_events</source>
                <target>Custom events</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.campaigns">
                <source>user.acl.resource.campaigns</source>
                <target>Campaigns</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.admin">
                <source>user.acl.resource.admin</source>
                <target>Admins</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.acl">
                <source>user.acl.resource.acl</source>
                <target>ACL</target>
            </trans-unit>
            <trans-unit id="user.acl.can_not_delete_master_role">
                <source>user.acl.can_not_delete_master_role</source>
                <target>Can not remove master role.</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.customer">
                <source>user.acl.resource.customer</source>
                <target>Members</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.user_settings">
                <source>user.acl.resource.user_settings</source>
                <target>Optional User Settings</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.segment">
                <source>user.acl.resource.segment</source>
                <target>Segments</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.settings">
                <source>user.acl.resource.settings</source>
                <target>Settings</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.channel">
                <source>user.acl.resource.channel</source>
                <target>Sales channel</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.pointsTransfer">
                <source>user.acl.resource.pointsTransfer</source>
                <target>Points transfers</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.analytics">
                <source>user.acl.resource.analytics</source>
                <target>Home</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.audit">
                <source>user.acl.resource.audit</source>
                <target>Audit log</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.message_template">
                <source>user.acl.resource.message_template</source>
                <target>Message template</target>
            </trans-unit>
            <trans-unit id="user.anonymization_failed">
                <source>user.anonymization_failed</source>
                <target>Customer anonymization failed</target>
            </trans-unit>
            <trans-unit id="customer.no_store">
                <source>customer.no_store</source>
                <target>Customer is not assigned to any store</target>
            </trans-unit>
            <trans-unit id="customer.coupon.not_found">
                <source>customer.coupon.not_found</source>
                <target>Coupon does not exist</target>
            </trans-unit>
            <trans-unit id="customer.coupon.delivery_status.too_many_results">
                <source>customer.coupon.delivery_status.too_many_results</source>
                <target>Many coupons with given coupon code matched. Try specifying currentStatus.</target>
            </trans-unit>
            <trans-unit id="user.cutting_branch_you_are_sitting_on_is_not allowed">
                <source>user.cutting_branch_you_are_sitting_on_is_not allowed</source>
                <target>You can not deactivate yourself. Log in to system would be impossible.</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.technical_settings">
                <source>user.acl.resource.technical_settings</source>
                <target>Technical settings</target>
            </trans-unit>
            <trans-unit id="user.account.not_found">
                <source>user.account.not_found</source>
                <target>Account not found</target>
            </trans-unit>
            <trans-unit id="user.customer.not_found">
                <source>user.customer.not_found</source>
                <target>Customer not found</target>
            </trans-unit>
            <trans-unit id="user.referrer.not_found">
                <source>user.referrer.not_found</source>
                <target>Referrer not found</target>
            </trans-unit>
            <trans-unit id="customer.registration.invalid_referrer_token">
                <source>customer.registration.invalid_referrer_token</source>
                <target>Invalid referrer token</target>
            </trans-unit>
            <trans-unit id="user.password_reset.already_requested">
                <source>user.password_reset.already_requested</source>
                <target>Password reset already requested. Please wait 2 minutes for the message before retrying.</target>
            </trans-unit>
            <trans-unit id="user.password_reset.failed">
                <source>user.password_reset.failed</source>
                <target>Password reset request failed. Please retry. If problem persists, please contact system support.</target>
            </trans-unit>
            <trans-unit id="customer.level.command.processing_store">
                <source>customer.level.command.processing_store</source>
                <target>Processing %s store...</target>
            </trans-unit>
            <trans-unit id="user.password_change.not_allowed">
                <source>user.password_change.not_allowed</source>
                <target>Password reset is not allowed for this user. Please contact system support.</target>
            </trans-unit>

            <trans-unit id="user.acl.resource.events">
                <source>user.acl.resource.events</source>
                <target>Events</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.language">
                <source>user.acl.resource.language</source>
                <target>Language</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.translations">
                <source>user.acl.resource.translations</source>
                <target>Translations</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.stores">
                <source>user.acl.resource.stores</source>
                <target>Stores</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.wallets">
                <source>user.acl.resource.wallets</source>
                <target>Wallets</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.webhook_subscriptions">
                <source>user.acl.resource.webhook_subscriptions</source>
                <target>Webhook subscriptions</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.imports">
                <source>import.acl.resource.imports</source>
                <target>Imports</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.exports">
                <source>user.acl.resource.exports</source>
                <target>Exports</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.group_of_values">
                <source>user.acl.resource.group_of_values</source>
                <target>Group of values</target>
            </trans-unit>
            <trans-unit id="user.anonymization.already_anonymized">
                <source>user.anonymization.already_anonymized</source>
                <target>Member has been already anonymized.</target>
            </trans-unit>
            <trans-unit id="user.too_many_results">
                <source>user.too_many_results</source>
                <target>Too many members requested.</target>
            </trans-unit>
            <trans-unit id="member.custom_attribute.already.exists">
                <source>member.custom_attribute.already.exists</source>
                <target>Custom attribute already exists</target>
            </trans-unit>
            <trans-unit id="user.login.too_many_attempts">
                <source>user.login.too_many_attempts</source>
                <target>Too many failed login attempts, please try again in number_of_minutes minutes.</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.data_analytics.dashboard.general_overview">
                <source>user.acl.resource.data_analytics.dashboard.general_overview</source>
                <target>Data analytics - General overview dashboard</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.data_analytics.campaign.single_view">
                <source>user.acl.resource.data_analytics.campaign.single_view</source>
                <target>Data analytics - Single campaign view</target>
            </trans-unit>
            <trans-unit id="member.limit.token.exceeded">
                <source>member.limit.token.exceeded</source>
                <target>Limit token generation exceeded. Try again in %recreate_token_limit_in_minutes% minutes.</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.billable_report">
                <source>user.acl.resource.billable_report</source>
                <target>Billable reports</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.global_managment">
                <source>user.acl.resource.global_managment</source>
                <target>Global management</target>
            </trans-unit>
            <trans-unit id="user.acl.editing_role.inactive_store">
                 <source>user.acl.editing_role.inactive_store</source>
                 <target>Store "%storeCode%" is not active.</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.bulk_actions">
                <source>user.acl.resource.bulk_actions</source>
                <target>Bulk actions</target>
            </trans-unit>
            <trans-unit id="user.api_key.limit_per_admin">
                <source>user.api_key.limit_per_admin</source>
                <target>You can add a maximum of {{ limit }} api keys</target>
            </trans-unit>
            <trans-unit id="user.acl.resource.badges">
                <source>user.acl.resource.badges</source>
                <target>Badges</target>
            </trans-unit>
            <trans-unit id="badge.editing_badge_progress_completion_count.single_change_limit_reached">
                <source>badge.editing_badge_progress_completion_count.single_change_limit_reached</source>
                <target>Completion status changes are limited to a max of {{ limit }} units per operation. For larger adjustments, please perform multiple steps.</target>
            </trans-unit>
        </body>
    </file>
</xliff>
