<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Form\Search;

use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\BaseCriteriaType;
use OpenLoyalty\User\Domain\Search\Criteria\RoleCriteria;
use Symfony\Component\Form\Extension\Core\Type\TextType;

final class RoleCriteriaType extends BaseCriteriaType
{
    /**
     * @param array<string, mixed> $formOptions
     * @param string               $value
     */
    protected function transformToCriteria(string $name, string $operator, $value, array $formOptions): CriteriaInterface
    {
        return new RoleCriteria($name, $operator, $value);
    }

    protected function getFieldType(): string
    {
        return TextType::class;
    }
}
