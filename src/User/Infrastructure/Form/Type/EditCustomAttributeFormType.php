<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Form\Type;

use OpenLoyalty\Core\Infrastructure\Form\DataTransformer\LabelsDataTransformer;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\Count;

class EditCustomAttributeFormType extends AbstractType
{
    public function __construct(
        private readonly int $maxCustomAttributesInMember
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add($builder->create('customAttributes', CollectionType::class, [
            'allow_add' => true,
            'allow_delete' => true,
            'entry_type' => LabelFormType::class,
            'constraints' => [new Count(['max' => $this->maxCustomAttributesInMember])],
        ])->addModelTransformer(new LabelsDataTransformer()));
    }
}
