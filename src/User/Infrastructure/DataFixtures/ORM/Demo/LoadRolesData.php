<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\DataFixtures\ORM\Demo;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\User\Infrastructure\Entity\Permission;
use OpenLoyalty\User\Infrastructure\Entity\Role;
use OpenLoyalty\User\Infrastructure\Model\AclAvailableObject;
use OpenLoyalty\User\Infrastructure\Security\PermissionAccess;
use OpenLoyalty\User\Infrastructure\Service\AclManagerInterface;
use Symfony\Component\Yaml\Yaml;

class LoadRolesData extends Fixture implements OrderedFixtureInterface, FixtureGroupInterface
{
    public function __construct(
        private readonly AclManagerInterface $aclManager
    ) {
    }

    public function load(ObjectManager $manager): void
    {
        $data = Yaml::parseFile(__DIR__.'/roles.yaml');

        foreach ($data as $roleCode => $roleData) {
            /** @var Store $store */
            $store = isset($roleData['store']) ? $this->getReference($roleData['store']) : null;
            $role = new Role($roleData['role'], $roleData['name'] ?? null, $roleData['isMaster'] ?? false);

            if (null !== $store) {
                $role->addStore($store);
            }

            /** @var AclAvailableObject $resource */
            foreach ($this->aclManager->getAvailableResources() as $resource) {
                if (isset($roleData['fullView']) && true === $roleData['fullView']) {
                    $role->addPermission(new Permission($resource->getCode(), PermissionAccess::VIEW));
                }
                if (isset($roleData['fullModify']) && true === $roleData['fullModify']) {
                    $role->addPermission(new Permission($resource->getCode(), PermissionAccess::MODIFY));
                }
            }

            $manager->persist($role);
            $this->addReference($roleCode, $role);
        }

        $manager->flush();
    }

    public static function getGroups(): array
    {
        return ['demo'];
    }

    public function getOrder(): int
    {
        return 5;
    }
}
