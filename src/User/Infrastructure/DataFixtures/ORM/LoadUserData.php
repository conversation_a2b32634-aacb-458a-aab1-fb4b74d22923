<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\DataFixtures\ORM;

use Carbon\CarbonImmutable;
use DateTime;
use DateTimeInterface;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use OpenLoyalty\Channel\Infrastructure\DataFixtures\ORM\LoadChannelData;
use OpenLoyalty\Core\Domain\Id\ChannelId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Level\Infrastructure\DataFixtures\ORM\LoadLevelData;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\User\Application\Command\RegisterCustomer;
use OpenLoyalty\User\Domain\ValueObject\ActivationMethod;
use OpenLoyalty\User\Domain\ValueObject\Address;
use OpenLoyalty\User\Domain\ValueObject\Company;
use OpenLoyalty\User\Domain\ValueObject\CustomerData;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class LoadUserData extends Fixture implements ContainerAwareInterface, OrderedFixtureInterface, FixtureGroupInterface
{
    public const USER_USER_ID = '00000000-0000-474c-b092-b0dd880c07e1';
    public const USER_USERNAME = '<EMAIL>';
    public const USER_PASSWORD = 'loyalty';
    public const USER_PHONE_NUMBER = '+48234234000';
    public const USER_LOYALTY_CARD_NUMBER = '47834433524';

    public const USER1_USER_ID = '11111111-0000-474c-b092-b0dd880c07e1';
    public const USER1_USERNAME = '<EMAIL>';
    public const USER1_PHONE_NUMBER = '+48456456000';
    public const USER1_LOYALTY_CARD_NUMBER = '*********';

    public const USER2_USER_ID = '22222222-0000-474c-b092-b0dd880c07e1';
    public const USER2_USERNAME = '<EMAIL>';
    public const USER2_PASSWORD = 'loyalty';
    public const USER2_PHONE_NUMBER = '+48456457000';

    public const USER3_USER_ID = '22222222-0000-474c-b092-b0dd880c07e2';
    public const USER3_USERNAME = '<EMAIL>';
    public const USER3_PHONE_NUMBER = '+48123456789';

    public const USER4_USER_ID = '22222222-0000-474c-b092-b0dd880c07e3';
    public const USER4_USERNAME = '<EMAIL>';
    public const USER4_PHONE_NUMBER = '+48123454789';

    public const TEST_USER_ID = '00000000-0000-474c-b092-b0dd880c07e2';
    public const TEST_USERNAME = '<EMAIL>';
    public const TEST_PASSWORD = 'loyalty';
    public const TEST_USER_PHONE_NUMBER = '+48345345000';

    public const TEST_RETURN_USER_ID = '11000000-0000-474c-b092-b0dd880c07e2';
    public const TEST_RETURN_USERNAME = '<EMAIL>';
    public const TEST_RETURN_USER_PHONE_NUMBER = '+48123123787';

    public const USER_COUPON_RETURN_ID = '00000000-0000-474c-b092-b0dd880c07aa';
    public const USER_COUPON_RETURN_USERNAME = '<EMAIL>';
    public const USER_COUPON_RETURN_PASSWORD = 'loyalty';

    public const USER_TRANSFER_1_USER_ID = '00000000-0000-474c-b092-b0dd880c07f3';
    public const USER_TRANSFER_1_USERNAME = '<EMAIL>';

    public const USER_TRANSFER_2_USER_ID = '00000000-0000-474c-b092-b0dd880c07f4';
    public const USER_TRANSFER_2_USERNAME = '<EMAIL>';

    public const USER_TRANSFER_3_USER_ID = '00000000-0000-474c-b092-b0dd880c07f5';
    public const USER_TRANSFER_3_USERNAME = '<EMAIL>';
    public const USER_TRANSFER_3_PASSWORD = 'loyalty';

    public const USER_IN_STORE_USER_ID = '00000000-0000-224c-b092-8cdd880c07a1';
    public const USER_IN_STORE_USERNAME = '<EMAIL>';

    public const DATA = [
        LoadSettingsData::DEFAULT_STORE_CODE => [
            'userUserId' => self::USER_USER_ID,
            'user1UserId' => self::USER1_USER_ID,
            'user2UserId' => self::USER2_USER_ID,
            'user3UserId' => self::USER3_USER_ID,
            'user4UserId' => self::USER4_USER_ID,
            'testUserId' => self::TEST_USER_ID,
            'testReturnUserId' => self::TEST_RETURN_USER_ID,
            'userCouponReturnId' => self::USER_COUPON_RETURN_ID,
            'userTransfer1UserId' => self::USER_TRANSFER_1_USER_ID,
            'userTransfer2UserId' => self::USER_TRANSFER_2_USER_ID,
            'userTransfer3UserId' => self::USER_TRANSFER_3_USER_ID,
            'userInStoreUserId' => self::USER_IN_STORE_USER_ID,
        ],
        LoadSettingsData::STORE_A_CODE => [
            'userUserId' => '22000000-0000-474c-b092-b0dd880c07e1',
            'user1UserId' => '22111111-0000-474c-b092-b0dd880c07e1',
            'user2UserId' => '33222222-0000-474c-b092-b0dd880c07e1',
            'user3UserId' => '33222222-0000-474c-b092-b0dd880c07e2',
            'user4UserId' => '33222222-0000-474c-b092-b0dd880c07e3',
            'user5UserId' => '33222222-0000-474c-b092-b0dd880c07e5',
            'testUserId' => '22000000-0000-474c-b092-b0dd880c07e2',
            'testReturnUserId' => '33000000-0000-474c-b092-b0dd880c07e2',
            'userCouponReturnId' => '22000000-0000-474c-b092-b0dd880c07aa',
            'userTransfer1UserId' => '22000000-0000-474c-b092-b0dd880c07f3',
            'userTransfer2UserId' => '22000000-0000-474c-b092-b0dd880c07f4',
            'userTransfer3UserId' => '22000000-0000-474c-b092-b0dd880c07f5',
            'userInStoreUserId' => '22000000-0000-224c-b092-8cdd880c07a1',
        ],
        LoadSettingsData::STORE_B_CODE => [
            'userUserId' => '60d2a6c0-619c-435a-b208-52ba15fd8e47',
            'user1UserId' => 'a5fccfae-ba8f-4ea2-82eb-e06c392ec501',
            'user2UserId' => '8e4a4564-c346-4bae-9e24-cc5f26d486d3',
            'user3UserId' => '14a1f4e9-c201-442f-9c35-61a4049ca48d',
            'user4UserId' => '14a1f4e9-c201-442f-9c35-61a4049ca45d',
            'user5UserId' => '14a1f4e9-c201-442f-9c35-61a4049ca44d',
            'testUserId' => 'cd906c86-f5f4-4abd-b5ed-a7d428156ff1',
            'testReturnUserId' => 'f0495526-9f4c-4d44-a21b-f26adc8cac7e',
            'userCouponReturnId' => '51108fcd-81a8-42fe-89fd-9585cf82b2a6',
            'userTransfer1UserId' => 'e3fa31cd-b863-4050-a24a-0e4f323da128',
            'userTransfer2UserId' => '6eef31ee-05ce-4f42-8229-256670217f99',
            'userTransfer3UserId' => '8e773dec-e0b2-4606-9fb1-86723a19aabb',
            'userInStoreUserId' => 'c9bb3ed0-4ba6-44d2-b60d-1bdb57fb265b',
        ],
    ];

    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * {@inheritdoc}
     */
    public function setContainer(ContainerInterface $container = null): void
    {
        $this->container = $container;
    }

    /**
     * {@inheritdoc}
     */
    public function load(ObjectManager $manager): void
    {
        $this->loadData($manager, LoadSettingsData::DEFAULT_STORE_CODE);
        $this->loadData($manager, LoadSettingsData::STORE_A_CODE);
        $this->loadData($manager, LoadSettingsData::STORE_B_CODE);
    }

    protected function loadData(ObjectManager $manager, string $storeCode): void
    {
        /** @var Store $store */
        $store = $this->getReference($storeCode);

        $this->loadCustomersData($manager, $store);
        $this->loadForTransferData($manager, $store);
        $this->loadForCouponReturnData($manager, $store);
    }

    protected function loadCustomersData(ObjectManager $manager, Store $store): void
    {
        /**
         * @var CommandBusInterface $bus
         */
        $bus = $this->container->get(CommandBusInterface::class);

        // USER
        $customerId = new CustomerId(self::DATA[$store->getCode()]['userUserId']);
        $command = new RegisterCustomer(
            $customerId,
            $store->getStoreId(),
            $this->getDefaultCustomerData(
                'John',
                'Doe',
                $this::USER_USERNAME,
                $this::USER_PHONE_NUMBER,
                self::USER_LOYALTY_CARD_NUMBER
            ),
            self::USER_PASSWORD,
            new ActivationMethod(ActivationMethod::ACTIVATE_DIRECT)
        );

        $bus->dispatch($command, true);
        $manager->clear();

        // USER1
        $customerId = new CustomerId(self::DATA[$store->getCode()]['user1UserId']);
        $command = new RegisterCustomer(
            $customerId,
            $store->getStoreId(),
            $this->getDefaultCustomerData(
                'John1',
                'Doe1',
                $this::USER1_USERNAME,
                $this::USER1_PHONE_NUMBER,
                self::USER1_LOYALTY_CARD_NUMBER,
                new CustomerId(self::DATA[$store->getCode()]['userUserId'])
            ),
            null,
            new ActivationMethod(ActivationMethod::ACTIVATE_DIRECT)
        );

        $bus->dispatch($command, true);
        $manager->clear();

        // USER2
        $customerId = new CustomerId(self::DATA[$store->getCode()]['user2UserId']);
        $command = new RegisterCustomer(
            $customerId,
            $store->getStoreId(),
            $this->getDefaultCustomerData('Alice', 'Smith', $this::USER2_USERNAME, $this::USER2_PHONE_NUMBER, 'C1234566'),
            self::USER2_PASSWORD,
            new ActivationMethod(ActivationMethod::ACTIVATE_DIRECT),
            new ChannelId(LoadChannelData::DATA[$store->getCode()]['channelId'])
        );

        $bus->dispatch($command, true);
        $manager->clear();

        // USER3
        $customerId = new CustomerId(self::DATA[$store->getCode()]['user3UserId']);
        $command = new RegisterCustomer(
            $customerId,
            $store->getStoreId(),
            $this->getDefaultCustomerData('Jane', 'Fonda', self::USER3_USERNAME, self::USER3_PHONE_NUMBER, 'D1234566'),
            null,
            new ActivationMethod(ActivationMethod::ACTIVATE_DIRECT),
            new ChannelId(LoadChannelData::DATA[$store->getCode()]['channelId'])
        );

        $bus->dispatch($command, true);
        $manager->clear();

        // USER4
        $customerId = new CustomerId(self::DATA[$store->getCode()]['user4UserId']);
        $command = new RegisterCustomer(
            $customerId,
            $store->getStoreId(),
            $this->getDefaultCustomerData('Jane', 'Fonda', self::USER4_USERNAME, self::USER4_PHONE_NUMBER, 'E1234566'),
            null,
            new ActivationMethod(ActivationMethod::ACTIVATE_DIRECT),
            new ChannelId(LoadChannelData::DATA[$store->getCode()]['channelId'])
        );

        $bus->dispatch($command, true);
        $manager->clear();

        // USER_TEST
        $customerId = new CustomerId(self::DATA[$store->getCode()]['testUserId']);
        $command = new RegisterCustomer(
            $customerId,
            $store->getStoreId(),
            $this->getDefaultCustomerData('Jane', 'Doe', self::TEST_USERNAME, self::TEST_USER_PHONE_NUMBER, '********'),
            self::TEST_PASSWORD,
            new ActivationMethod(ActivationMethod::ACTIVATE_DIRECT),
            null,
            new LevelId(LoadLevelData::DATA[$store->getCode()]['level1Id'])
        );
        $bus->dispatch($command, true);
        $manager->clear();

        // Return test user

        $customerId = new CustomerId(self::DATA[$store->getCode()]['testReturnUserId']);
        $command = new RegisterCustomer(
            $customerId,
            $store->getStoreId(),
            $this->getDefaultCustomerData('Jon', 'Returner', $this::TEST_RETURN_USERNAME, $this::TEST_RETURN_USER_PHONE_NUMBER, '********'),
            null,
            new ActivationMethod(ActivationMethod::ACTIVATE_DIRECT)
        );

        $bus->dispatch($command, true);
        $manager->clear();

        // Return test user in store

        $customerId = new CustomerId(self::DATA[$store->getCode()]['userInStoreUserId']);
        $command = new RegisterCustomer(
            $customerId,
            $store->getStoreId(),
            $this->getDefaultCustomerData(
                'Jan',
                'Store',
                self::USER_IN_STORE_USERNAME,
                '0000',
                'Z1234566'
            ),
            null,
            new ActivationMethod(ActivationMethod::ACTIVATE_DIRECT)
        );

        $bus->dispatch($command, true);
        $manager->clear();
    }

    protected function loadForTransferData(ObjectManager $manager, Store $store): void
    {
        /**
         * @var CommandBusInterface $bus
         */
        $bus = $this->container->get(CommandBusInterface::class);
        $users = [
            [
                self::DATA[$store->getCode()]['userTransfer1UserId'],
                static::USER_TRANSFER_1_USERNAME,
                null,
                '243443',
            ],
            [
                self::DATA[$store->getCode()]['userTransfer2UserId'],
                static::USER_TRANSFER_2_USERNAME,
                null,
                '650521',
            ],
            [
                self::DATA[$store->getCode()]['userTransfer3UserId'],
                static::USER_TRANSFER_3_USERNAME,
                static::USER_TRANSFER_3_PASSWORD,
                '580374',
            ],
        ];
        $i = 0;
        foreach ($users as $data) {
            $customerId = new CustomerId($data[0]);
            $command = new RegisterCustomer(
                $customerId,
                $store->getStoreId(),
                $this->getDefaultCustomerData(
                    'TestUser',
                    'ForTransfersTest',
                    $data[1],
                    '123123231231231236'.$i,
                    'V1234566'.$i
                ),
                $data[2],
                new ActivationMethod(ActivationMethod::ACTIVATE_DIRECT)
            );

            $bus->dispatch($command, true);
            $manager->clear();

            ++$i;
        }
    }

    protected function loadForCouponReturnData(ObjectManager $manager, Store $store): void
    {
        /**
         * @var CommandBusInterface $bus
         */
        $bus = $this->container->get(CommandBusInterface::class);
        $users = [
            [self::DATA[$store->getCode()]['userCouponReturnId'], static::USER_COUPON_RETURN_USERNAME, static::USER_COUPON_RETURN_PASSWORD],
        ];
        $i = 0;
        foreach ($users as $data) {
            $customerId = new CustomerId($data[0]);

            $command = new RegisterCustomer(
                $customerId,
                $store->getStoreId(),
                $this->getDefaultCustomerData(
                    'TestUser',
                    'ForCouponTest',
                    $data[1],
                    '1231232312312312376'.$i,
                    'X1234566'.$i,
                ),
                null,
                new ActivationMethod(ActivationMethod::ACTIVATE_DIRECT)
            );

            $bus->dispatch($command, true);
            $manager->clear();

            ++$i;
        }
    }

    public static function getDefaultCustomerData(
        $firstName,
        $lastName,
        $email,
        $phone,
        $loyaltyCardNumber,
        $referrerCustomerId = null
    ): CustomerData {
        return new CustomerData(
            $referrerCustomerId,
            'testToken',
            $firstName,
            $lastName,
            $email,
            $phone,
            'male',
            DateTime::createFromFormat(DateTimeInterface::ATOM, '1990-09-11T00:00:00+00:00'),
            null,
            new CarbonImmutable(),
            Address::fromData([
                'street' => 'Dmowskiego',
                'address1' => '21',
                'address2' => null,
                'postal' => '50-300',
                'city' => 'Wrocław',
                'province' => 'Dolnośląskie',
                'country' => 'PL',
            ]),
            new Company(
                'test',
                'nip'
            ),
            $loyaltyCardNumber,
        );
    }

    /**
     * {@inheritdoc}
     */
    public static function getGroups(): array
    {
        return ['setup'];
    }

    /**
     * {@inheritdoc}
     */
    public function getOrder(): int
    {
        return 3;
    }
}
