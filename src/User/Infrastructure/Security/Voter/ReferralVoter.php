<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Security\Voter;

use OpenLoyalty\User\Infrastructure\Entity\User;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * @extends Voter<string, mixed>
 */
class ReferralVoter extends Voter
{
    public const LIST_REFERRALS = 'LIST_REFERRALS';

    protected function supports(string $attribute, mixed $subject): bool
    {
        return self::LIST_REFERRALS === $attribute;
    }

    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        /** @var User $user */
        $user = $token->getUser();

        if (!$user instanceof User) {
            return false;
        }

        if (self::LIST_REFERRALS === $attribute) {
            return $user->hasRole('ROLE_ADMIN');
        }

        return false;
    }
}
