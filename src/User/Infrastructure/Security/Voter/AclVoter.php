<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Security\Voter;

use OpenLoyalty\User\Infrastructure\Entity\Role;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\PermissionAccess;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * Class AclVoter.
 *
 * @extends Voter<string, mixed>
 */
class AclVoter extends Voter
{
    public const PERMISSION_RESOURCE = 'ACL';

    public const LIST = 'LIST_ROLES';
    public const EDIT = 'EDIT';
    public const VIEW = 'VIEW';
    public const CREATE_ROLE = 'CREATE_ROLE';

    /**
     * @var UserPermissionCheckerInterface
     */
    protected $permissionChecker;

    /**
     * AclVoter constructor.
     */
    public function __construct(UserPermissionCheckerInterface $permissionChecker)
    {
        $this->permissionChecker = $permissionChecker;
    }

    /**
     * {@inheritdoc}
     */
    public function supports(string $attribute, mixed $subject): bool
    {
        return ($subject instanceof Role && in_array($attribute, [self::VIEW, self::EDIT]))
            || in_array($attribute, [self::CREATE_ROLE, self::LIST]);
    }

    /**
     * {@inheritdoc}
     */
    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        /** @var User $user */
        $user = $token->getUser();

        if (!$user instanceof User) {
            return false;
        }

        $viewAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionWithoutStore(
                $user, self::PERMISSION_RESOURCE, [PermissionAccess::VIEW]
        );

        $fullAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionWithoutStore(
                $user, self::PERMISSION_RESOURCE, [PermissionAccess::VIEW, PermissionAccess::MODIFY]
        );

        switch ($attribute) {
            case self::LIST:
                return $viewAdmin;
            case self::VIEW:
                return $viewAdmin;
            case self::EDIT:
                return $fullAdmin;
            case self::CREATE_ROLE:
                return $fullAdmin;
            default:
                return false;
        }
    }
}
