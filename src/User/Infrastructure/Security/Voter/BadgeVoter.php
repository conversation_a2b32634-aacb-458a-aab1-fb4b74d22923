<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Security\Voter;

use OpenLoyalty\User\Domain\BadgeType;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\PermissionAccess;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * @extends Voter<string, mixed>
 */
final class BadgeVoter extends Voter
{
    public const PERMISSION_RESOURCE = 'BADGE';
    public const EDIT_BADGE = 'EDIT_BADGE';
    public const VIEW_BADGE = 'VIEW_BADGES';

    public function __construct(
        private readonly UserPermissionCheckerInterface $permissionChecker
    ) {
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return
            ($subject instanceof BadgeType && self::EDIT_BADGE === $attribute) ||
            (null === $subject && self::VIEW_BADGE === $attribute);
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        /** @var User $user */
        $user = $token->getUser();

        if (!$user instanceof User) {
            return false;
        }

        $viewAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionInCurrentStore(
                $user,
                self::PERMISSION_RESOURCE,
                [PermissionAccess::VIEW]
            );

        $fullAdmin = $user->hasRole('ROLE_ADMIN') && $this->permissionChecker->hasPermissionInCurrentStore(
                $user,
                self::PERMISSION_RESOURCE,
                [PermissionAccess::VIEW, PermissionAccess::MODIFY]
            );

        return match ($attribute) {
            self::VIEW_BADGE => $viewAdmin,
            self::EDIT_BADGE => $fullAdmin,
            default => false,
        };
    }
}
