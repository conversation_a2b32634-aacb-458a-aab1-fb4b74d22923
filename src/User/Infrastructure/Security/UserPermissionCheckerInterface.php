<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Security;

use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\User\Infrastructure\Entity\User;

interface UserPermissionCheckerInterface
{
    public function hasPermissionWithoutStore(User $user, string $resource, array $accesses): bool;

    public function hasPermission(?Store $store, User $user, string $resource, array $accesses): bool;

    public function hasPermissionInCurrentStore(User $user, string $resource, array $accesses): bool;
}
