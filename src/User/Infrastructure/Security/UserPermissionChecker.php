<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Security;

use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Infrastructure\Exception\StoreNotSetException;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\User\UserInterface;

class UserPermissionChecker implements UserPermissionCheckerInterface
{
    public function __construct(
        protected RequestStack $requestStack,
        protected UserPermissionEvaluatorInterface $userPermissionEvaluator,
        protected StoreContextProviderInterface $storeContextProvider
    ) {
    }

    public function hasPermissionWithoutStore(UserInterface $user, string $resource, array $accesses): bool
    {
        return $this->hasPermission(null, $user, $resource, $accesses);
    }

    public function hasPermission(?Store $store, UserInterface $user, string $resource, array $accesses): bool
    {
        $url = $this->requestStack->getCurrentRequest()?->getPathInfo();

        return $this->userPermissionEvaluator->hasPermission(
            $store,
            $user,
            $resource,
            $accesses,
            new RegexPermissionFilterQueryMatcher($url)
        );
    }

    public function hasPermissionInCurrentStore(UserInterface $user, string $resource, array $accesses): bool
    {
        try {
            $store = $this->storeContextProvider->getStore();
        } catch (StoreNotSetException $ex) {
            return false;
        }

        return $this->hasPermission($store, $user, $resource, $accesses);
    }
}
