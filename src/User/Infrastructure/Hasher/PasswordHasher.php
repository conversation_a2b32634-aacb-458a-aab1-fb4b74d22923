<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Hasher;

use OpenLoyalty\User\Domain\Hasher\PasswordHasherInterface;
use OpenLoyalty\User\Infrastructure\Entity\User;
use Symfony\Component\PasswordHasher\Hasher\PasswordHasherFactoryInterface;

class PasswordHasher implements PasswordHasherInterface
{
    public function __construct(
        private readonly PasswordHasherFactoryInterface $passwordHasherFactory
    ) {
    }

    public function hashPassword(string $plainPassword): string
    {
        return $this->passwordHasherFactory->getPasswordHasher(User::class)
            ->hash($plainPassword);
    }
}
