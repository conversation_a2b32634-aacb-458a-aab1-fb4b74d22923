<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\SystemEvent\Listener;

use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Core\Domain\Message\EventHandlerInterface;
use OpenLoyalty\Core\Domain\Model\Wallet;
use OpenLoyalty\Core\Domain\Provider\TierModeProviderInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Level\Domain\Exception\LevelNotFoundException;
use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Domain\LevelRepository;
use OpenLoyalty\Points\Domain\Transfer;
use OpenLoyalty\Points\Domain\TransferRepositoryInterface;
use OpenLoyalty\User\Application\Status\CustomerStatusProviderInterface;
use OpenLoyalty\User\Domain\AccountFacadeInterface;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;
use OpenLoyalty\User\Domain\LevelIdProviderInterface;
use OpenLoyalty\User\Domain\Provider\ExcludeDeliveryCostsProvider;
use OpenLoyalty\User\Domain\Provider\TierAssignTypeProvider;
use OpenLoyalty\User\Domain\Service\MoveLevelServiceInterface;
use OpenLoyalty\User\Infrastructure\Exception\LevelDowngradeModeNotSupportedException;
use OpenLoyalty\User\Infrastructure\LevelDowngradeModeProvider;
use OpenLoyalty\User\Infrastructure\TierWalletCodeProviderInterface;

abstract class BaseCalculateCustomerLevelListener implements EventHandlerInterface
{
    protected LevelIdProviderInterface $levelIdProvider;
    protected CustomerRepositoryInterface $customerRepository;
    protected MoveLevelServiceInterface $moveLevelService;
    protected TierAssignTypeProvider $tierAssignTypeProvider;
    protected ExcludeDeliveryCostsProvider $excludeDeliveryCostsProvider;
    protected LevelRepository $levelRepository;
    protected EventBusInterface $eventBus;
    protected CustomerStatusProviderInterface $customerStatusProvider;
    protected LevelDowngradeModeProvider $levelDowngradeModeProvider;
    protected StoreRepository $storeRepository;
    protected TransferRepositoryInterface $transferRepository;
    protected AccountFacadeInterface $accountFacade;
    protected TierWalletCodeProviderInterface $tierWalletCodeProvider;

    protected TierModeProviderInterface $tierModeProvider;

    public function __construct(
        LevelIdProviderInterface $levelIdProvider,
        CustomerRepositoryInterface $customerRepository,
        MoveLevelServiceInterface $moveLevelService,
        TierAssignTypeProvider $tierAssignTypeProvider,
        ExcludeDeliveryCostsProvider $excludeDeliveryCostsProvider,
        LevelRepository $levelRepository,
        EventBusInterface $eventBus,
        CustomerStatusProviderInterface $customerStatusProvider,
        LevelDowngradeModeProvider $levelDowngradeModeProvider,
        StoreRepository $storeRepository,
        TransferRepositoryInterface $transferRepository,
        AccountFacadeInterface $accountFacade,
        TierWalletCodeProviderInterface $tierWalletCodeProvider,
        TierModeProviderInterface $tierModeProvider
    ) {
        $this->levelIdProvider = $levelIdProvider;
        $this->customerRepository = $customerRepository;
        $this->moveLevelService = $moveLevelService;
        $this->tierAssignTypeProvider = $tierAssignTypeProvider;
        $this->excludeDeliveryCostsProvider = $excludeDeliveryCostsProvider;
        $this->levelRepository = $levelRepository;
        $this->eventBus = $eventBus;
        $this->customerStatusProvider = $customerStatusProvider;
        $this->levelDowngradeModeProvider = $levelDowngradeModeProvider;
        $this->storeRepository = $storeRepository;
        $this->transferRepository = $transferRepository;
        $this->accountFacade = $accountFacade;
        $this->tierWalletCodeProvider = $tierWalletCodeProvider;
        $this->tierModeProvider = $tierModeProvider;
    }

    /**
     * @throws LevelDowngradeModeNotSupportedException
     */
    protected function getCustomerPointsAmount(Store $store, Customer $customer, Wallet $wallet): float
    {
        $downgradeMode = $this->levelDowngradeModeProvider->getMode($store);

        if (LevelDowngradeModeProvider::MODE_X_DAYS !== $downgradeMode) {
            return $wallet->getAccount()->getActiveUnits();
        }

        $downgradeBaseMode = $this->levelDowngradeModeProvider->getBase($store);

        switch ($downgradeBaseMode) {
            case LevelDowngradeModeProvider::BASE_ACTIVE_POINTS:
                return $wallet->getAccount()->getActiveUnits();
            case LevelDowngradeModeProvider::BASE_EARNED_POINTS:
            case LevelDowngradeModeProvider::BASE_EARNED_POINTS_SINCE_LAST_LEVEL_CHANGE:
                return $this->transferRepository->getTotalByType(
                    $store->getStoreId(),
                    [Transfer::ADD_TYPE, Transfer::TRANSFER_IN_TYPE],
                    false,
                    $wallet->getWalletId(),
                    null !== $customer->getLastLevelRecalculation()
                        ? \DateTimeImmutable::createFromMutable($customer->getLastLevelRecalculation())
                        : $customer->getRegisteredAt()
                );
            default:
                throw new LevelDowngradeModeNotSupportedException();
        }
    }

    protected function getCustomerTransactionAmount(Customer $customer): float
    {
        $store = $this->storeRepository->byId($customer->getStoreId(), true);

        if ($this->excludeDeliveryCostsProvider->areExcluded($store)) {
            return $customer->getTransactionsAmountWithoutDeliveryCosts() - $customer->getTransactionsAmountExcludedForLevel();
        }

        return $customer->getTransactionsAmount() - $customer->getTransactionsAmountExcludedForLevel();
    }

    /**
     * @throws AssertionFailedException
     */
    protected function handlePoints(CustomerId $customerId, float $currentAmount, bool $isRecalculation = false): void
    {
        $customer = $this->customerRepository->load($customerId);
        $store = $this->storeRepository->byId($customer->getStoreId(), true);

        $currentLevel = $customer->getLevelId()
            ? $this->levelRepository->byId($customer->getLevelId())
            : null;

        try {
            $levelId = $this->levelIdProvider->findHighestLevelByCustomerTransactionAmount(
                $currentAmount,
                $customer->getStoreId()
            );
        } catch (LevelNotFoundException $e) {
            return;
        }

        $level = $this->levelRepository->byId($levelId);

        if ($currentLevel && $currentLevel->getConditionValue() >= $level->getConditionValue()) {
            $downgradedLevelId = $this->handlePointsDowngrade($store, $level, $customer, $currentLevel, $isRecalculation);

            if (null !== $downgradedLevelId) {
                $levelId = new LevelId($downgradedLevelId);
            }
        }

        if (!$customer->getLevelId() || (string) $customer->getLevelId() !== (string) $levelId) {
            $this->moveLevelService->moveToLevel($customerId, $levelId);
        }
    }

    /**
     * @throws AssertionFailedException
     */
    protected function handlePointsDowngrade(
        Store $store,
        Level $calculatedLevel,
        Customer $customer,
        Level $currentLevel,
        bool $isRecalculation = false
    ): ?string {
        try {
            $mode = $this->levelDowngradeModeProvider->getMode($store);
        } catch (LevelDowngradeModeNotSupportedException $e) {
            $mode = LevelDowngradeModeProvider::MODE_NONE;
        }

        if (LevelDowngradeModeProvider::MODE_NONE === $mode) {
            return (string) $currentLevel->getLevelId();
        }

        if (LevelDowngradeModeProvider::MODE_X_DAYS === $mode && !$isRecalculation) {
            return (string) $currentLevel->getLevelId();
        }

        if (LevelDowngradeModeProvider::MODE_AUTO === $mode || LevelDowngradeModeProvider::MODE_X_DAYS === $mode) {
            if ($customer->getManuallyAssignedLevelId()) {
                $manualId = (string) $customer->getManuallyAssignedLevelId();

                $manual = $this->levelRepository->byId(new LevelId($manualId));
                if ($manual->getConditionValue() > $calculatedLevel->getConditionValue()) {
                    return $manualId;
                }
            }

            return (string) $calculatedLevel->getLevelId();
        }

        return null;
    }
}
