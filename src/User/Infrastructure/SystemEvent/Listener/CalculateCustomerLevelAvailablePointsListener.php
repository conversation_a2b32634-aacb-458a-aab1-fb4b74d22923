<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\SystemEvent\Listener;

use Assert\AssertionFailedException;
use OpenLoyalty\Account\Domain\SystemEvent\AvailablePointsAmountChangedSystemEvent;
use OpenLoyalty\Core\Domain\Exception\StoreNotFoundException;
use OpenLoyalty\User\Domain\Provider\TierAssignTypeProvider;
use OpenLoyalty\User\Infrastructure\Exception\LevelDowngradeModeNotSupportedException;

class CalculateCustomerLevelAvailablePointsListener extends BaseCalculateCustomerLevelListener
{
    /**
     * @throws AssertionFailedException
     * @throws StoreNotFoundException
     * @throws LevelDowngradeModeNotSupportedException
     */
    public function __invoke(AvailablePointsAmountChangedSystemEvent $event): void
    {
        $store = $this->storeRepository->byId($event->getStoreId());
        if (null === $store) {
            throw new StoreNotFoundException($event->getStoreId());
        }

        if (!$this->tierModeProvider->isTraditionalMode($store->getStoreId())) {
            return;
        }

        if (TierAssignTypeProvider::TYPE_POINTS !== $this->tierAssignTypeProvider->getType($store)) {
            return;
        }

        $customerId = $event->getCustomerId();
        $customer = $this->customerRepository->load($customerId);
        $store = $this->storeRepository->byId($customer->getStoreId(), true);
        $walletCode = $this->tierWalletCodeProvider->getWalletCode($store);
        $wallet = $this->accountFacade->getWallet($customer->getStoreId(), $customerId, $walletCode);
        $currentPointsAmount = $this->getCustomerPointsAmount($store, $customer, $wallet);
        $this->handlePoints($customerId, $currentPointsAmount, false);
    }
}
