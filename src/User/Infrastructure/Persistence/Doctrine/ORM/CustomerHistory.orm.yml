OpenLoyalty\User\Domain\CustomerHistory:
  type: entity
  table: customer_history
  indexes:
    customerHistoryIdx:
      columns: [ store_id, customer_id ]
    customerHistoryCreatedAtIdx:
      columns: [ created_at ]
    customerHistoryTypeIdx:
      columns: [ type ]
  id:
    customerHistoryId:
      type: customer_history_id
      column: id
  fields:
    storeId:
      type: store_id
    customerId:
      type: customer_id
    accountId:
      type: account_id
      nullable: true
    type:
      type: string
    variables:
      type: labels_json_array
    createdAt:
      type: datetime_immutable_microseconds
    updatedAt:
      type: datetime_immutable_microseconds
    createdBy:
      type: string
      nullable: true
    updatedBy:
      type: string
      nullable: true
