<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Persistence\Doctrine\Type;

use Doctrine\DBAL\Platforms\AbstractPlatform;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use Ramsey\Uuid\Doctrine\UuidType;

final class CustomerIdDoctrineType extends UuidType
{
    public const NAME = 'customer_id';

    public function convertToPHPValue($value, AbstractPlatform $platform): ?CustomerId
    {
        if (empty($value)) {
            return null;
        }

        if ($value instanceof CustomerId) {
            return $value;
        }

        return new CustomerId($value);
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value instanceof CustomerId) {
            return (string) $value;
        }

        if (!empty($value)) {
            return $value;
        }

        return null;
    }

    public function getName(): string
    {
        return self::NAME;
    }

    public function requiresSQLCommentHint(AbstractPlatform $platform): bool
    {
        return true;
    }
}
