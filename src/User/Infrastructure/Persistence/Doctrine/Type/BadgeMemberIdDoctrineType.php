<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Persistence\Doctrine\Type;

use Assert\AssertionFailedException;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use OpenLoyalty\Core\Domain\Id\BadgeMemberId;
use Ramsey\Uuid\Doctrine\UuidType;

class BadgeMemberIdDoctrineType extends UuidType
{
    public const NAME = 'badge_member_id';

    /**
     * @throws AssertionFailedException
     */
    public function convertToPHPValue($value, AbstractPlatform $platform): ?BadgeMemberId
    {
        if (empty($value)) {
            return null;
        }

        if ($value instanceof BadgeMemberId) {
            return $value;
        }

        if (!is_string($value)) {
            return null;
        }

        return new BadgeMemberId($value);
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value instanceof BadgeMemberId) {
            /* @phpstan-ignore-next-line */
            return (string) $value;
        }

        if (!empty($value) && is_string($value)) {
            return $value;
        }

        return null;
    }

    public function getName(): string
    {
        return self::NAME;
    }

    public function requiresSQLCommentHint(AbstractPlatform $platform): bool
    {
        return true;
    }
}
