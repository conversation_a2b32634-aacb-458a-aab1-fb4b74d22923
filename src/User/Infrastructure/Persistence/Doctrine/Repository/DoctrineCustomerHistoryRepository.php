<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Persistence\Doctrine\Repository;

use OpenLoyalty\Account\Domain\Wallet;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Model\Label;
use OpenLoyalty\Core\Domain\Search\Context\ContextInterface;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\Core\Infrastructure\Search\Doctrine\Context;
use OpenLoyalty\User\Domain\CustomerHistory;
use OpenLoyalty\User\Domain\CustomerHistoryRepositoryInterface;
use OpenLoyalty\User\Domain\CustomerHistoryRepositoryReadContextInterface;

final class DoctrineCustomerHistoryRepository extends DoctrineRepository implements CustomerHistoryRepositoryInterface, CustomerHistoryRepositoryReadContextInterface
{
    protected function getClass(): string
    {
        return CustomerHistory::class;
    }

    public function save(CustomerHistory $customerHistory): void
    {
        $this->entityManager->persist($customerHistory);
        // without flush
    }

    public function getLatestHistoryItemOfAchievementEditionByAdmin(StoreId $storeId, CustomerId $customerId, AchievementId $achievementId, string $achievementName): ?CustomerHistory
    {
        // it could and probably should be done by single query without all this php logic
        $queryBuilder = $this->repository->createQueryBuilder('h')
            ->andWhere('h.storeId = :storeId')
            ->andWhere('h.customerId = :customerId')
            ->setParameter('storeId', $storeId)
            ->setParameter('customerId', $customerId)
            ->orderBy('h.createdAt', 'DESC')
        ;

        $results = $queryBuilder->getQuery()->getResult();

        /** @var CustomerHistory $customerHistoryItem */
        foreach ($results as $customerHistoryItem) {
            if ($this->isMatchingHistoryItemWithAchievementIdOrName($customerHistoryItem->getType(), $customerHistoryItem->getVariables(), $achievementId, $achievementName)) {
                return $customerHistoryItem;
            }
        }

        return null;
    }

    private function isMatchingHistoryItemWithAchievementIdOrName(string $customerHistoryItemType, array $variables, AchievementId $achievementId, string $achievementName): bool
    {
        if ($this->isMatchingHistoryItemWithLabelNameAndValue($customerHistoryItemType, $variables, 'achievementId', (string) $achievementId)) {
            return $this->isMatchingHistoryItemWithLabelNameAndValue($customerHistoryItemType, $variables, 'achievementId', (string) $achievementId);
        }

        return $this->isMatchingHistoryItemWithLabelNameAndValue($customerHistoryItemType, $variables, 'achievementName', $achievementName);
    }

    private function isMatchingHistoryItemWithLabelNameAndValue(string $customerHistoryItemType, array $variables, string $labelName, string $value): bool
    {
        if (
            in_array($customerHistoryItemType, [
                CustomerHistory::TYPE_MEMBER_ACHIEVEMENT_COMPLETION_COUNT_WAS_DECREASED,
                CustomerHistory::TYPE_MEMBER_ACHIEVEMENT_PROGRESS_WAS_CHANGED,
            ], true) &&
            $this->hasVariablesContainsLabel(new Label($labelName, $value), $variables)
        ) {
            return true;
        }

        if (
            CustomerHistory::TYPE_ACHIEVEMENT_COMPLETED === $customerHistoryItemType &&
            $this->hasVariablesContainsLabel(new Label('manuallyEdited', true), $variables) &&
            $this->hasVariablesContainsLabel(new Label($labelName, $value), $variables)
        ) {
            return true;
        }

        return false;
    }

    private function hasVariablesContainsLabel(Label $label, array $variables): bool
    {
        /** @var Label $variable */
        foreach ($variables as $variable) {
            if ($variable->same($label)) {
                return true;
            }
        }

        return false;
    }

    protected function getBaseContext(array $params): ContextInterface
    {
        $context = parent::getBaseContext($params);
        $context->getQueryBuilder()
            ->leftJoin(Wallet::class, 'w', 'WITH', 'w.accountId = '.Context::DEFAULT_ALIAS.'.accountId')
            ->leftJoin('w.type', 'wt')
        ;

        return $context;
    }
}
