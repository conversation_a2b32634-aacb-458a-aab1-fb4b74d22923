<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Persistence\Doctrine\Repository;

use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use OpenLoyalty\Core\Domain\Id\CodeId;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\User\Domain\Code;
use OpenLoyalty\User\Domain\CodeRepositoryInterface;

class DoctrineCodeRepository extends DoctrineRepository implements CodeRepositoryInterface
{
    protected function getClass(): string
    {
        return Code::class;
    }

    public function getById(CodeId $codeId): ?Code
    {
        /** @var Code|null $code */
        $code = $this->find($codeId);

        return $code;
    }

    public function getByCode(string $code): ?Code
    {
        /** @var Code|null $codeEntity */
        $codeEntity = $this->findOneBy(['code' => $code]);

        return $codeEntity;
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function save(Code $activationCode): void
    {
        $this->entityManager->persist($activationCode);
        $this->entityManager->flush();
    }

    /**
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByObjectAndAction(string $objectType, string $objectId, string $action): int
    {
        $qb = $this->repository->createQueryBuilder('code');
        $qb->select('COUNT(code.codeId)');

        $qb->where('code.objectType = :objectType');
        $qb->andWhere('code.objectId = :objectId');
        $qb->andWhere('code.action = :action');
        $qb->setParameter('objectType', $objectType);
        $qb->setParameter('objectId', $objectId);
        $qb->setParameter('action', $action);

        return $qb->getQuery()->getSingleScalarResult();
    }

    public function getLastByObjectAndAction(string $objectType, string $objectId, string $action): ?Code
    {
        /** @var Code|null $code */
        $code = $this->findOneBy([
            'objectType' => $objectType,
            'objectId' => $objectId,
            'action' => $action,
        ], ['createdAt' => 'desc']);

        return $code;
    }
}
