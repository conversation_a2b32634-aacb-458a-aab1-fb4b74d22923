<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\DataAnalytics\Listener;

use Assert\AssertionFailedException;
use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Message\EventHandlerInterface;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\DataAnalytics\Domain\Member\Event\MemberWasDeleted;
use OpenLoyalty\DataAnalytics\Domain\Shared\Exception\VersionValueIsInvalid;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventBusInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;
use OpenLoyalty\User\Domain\SystemEvent\CustomerDeactivatedSystemEvent;

final class CustomerDeactivatedListener implements EventHandlerInterface
{
    public function __construct(
        private readonly UuidGeneratorInterface $uuidGenerator,
        private readonly AnalyticsEventBusInterface $analyticsEventBus
    ) {
    }

    /**
     * @throws AssertionFailedException
     * @throws VersionValueIsInvalid
     */
    public function __invoke(CustomerDeactivatedSystemEvent $event): void
    {
        $this->analyticsEventBus->dispatch(
            new MemberWasDeleted(
                new AnalyticsEventId($this->uuidGenerator->generate()),
                new Version(1),
                $event->getCustomerId(),
                DateTimeImmutable::createFromInterface($event->getEventDate()),
                $event->getStoreId()
            )
        );
    }
}
