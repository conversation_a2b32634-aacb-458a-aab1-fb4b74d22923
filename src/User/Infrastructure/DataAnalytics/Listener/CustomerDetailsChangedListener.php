<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\DataAnalytics\Listener;

use Assert\AssertionFailedException;
use OpenLoyalty\Core\Domain\Message\EventHandlerInterface;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\DataAnalytics\Domain\Member\Event\MemberWasUpdated;
use OpenLoyalty\DataAnalytics\Domain\Member\Exception\GenderTypeIsInvalid;
use OpenLoyalty\DataAnalytics\Domain\Member\ValueObject\Gender;
use OpenLoyalty\DataAnalytics\Domain\Shared\Exception\VersionValueIsInvalid;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventBusInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;
use OpenLoyalty\User\Domain\Exception\CustomerNotFoundException;
use OpenLoyalty\User\Domain\SystemEvent\CustomerDetailsChangedSystemEvent;

final class CustomerDetailsChangedListener implements EventHandlerInterface
{
    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly UuidGeneratorInterface $uuidGenerator,
        private readonly AnalyticsEventBusInterface $analyticsEventBus
    ) {
    }

    /**
     * @throws AssertionFailedException
     * @throws VersionValueIsInvalid
     * @throws GenderTypeIsInvalid
     */
    public function __invoke(CustomerDetailsChangedSystemEvent $event): void
    {
        $member = $this->customerRepository->getById($event->getCustomerId());
        if (null === $member) {
            throw new CustomerNotFoundException();
        }

        $this->analyticsEventBus->dispatch(
            new MemberWasUpdated(
                new AnalyticsEventId($this->uuidGenerator->generate()),
                new Version(1),
                $member->getCustomerId(),
                new Gender($member->getGender()->getType()),
                $member->getUpdatedAt(),
                $member->getStoreId()
            )
        );
    }
}
