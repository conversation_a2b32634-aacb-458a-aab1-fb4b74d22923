<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\User\Infrastructure\Exception;

use OpenLoyalty\Core\Domain\Exception\NotFoundException;
use OpenLoyalty\Core\Domain\Exception\Translatable;

class LevelNotFoundException extends NotFoundException implements Translatable
{
    /**
     * {@inheritdoc}
     */
    public function getMessageKey(): string
    {
        return 'customer.assign_to_level.level_not_found';
    }

    /**
     * {@inheritdoc}
     */
    public function getMessageParams(): array
    {
        return [];
    }
}
