<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Message\FifoGroup;

use OpenLoyalty\Core\Domain\Message\TransportableInterface;
use OpenLoyalty\Core\Infrastructure\Message\FifoGroup\FifoGroupInterface;
use OpenLoyalty\User\Application\Job\MatchTransaction;

class MatchTransactionFifoGroup implements FifoGroupInterface
{
    public function supports(TransportableInterface $message): bool
    {
        return $message instanceof MatchTransaction;
    }

    public function getGroup(TransportableInterface $message): ?string
    {
        if (!$message instanceof MatchTransaction) {
            throw new \InvalidArgumentException(get_class($message));
        }

        // unique group for not assignable event
        if ($message->getCustomerData()->isEmpty()) {
            return md5(uniqid());
        }

        return md5(serialize($message->getCustomerData()).$message->getStoreId().get_class($message));
    }
}
