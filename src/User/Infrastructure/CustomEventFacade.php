<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\CustomEvent\Domain\CustomEventGateInterface;
use OpenLoyalty\User\Domain\CustomEventFacadeInterface;

final class CustomEventFacade implements CustomEventFacadeInterface
{
    public function __construct(
        private readonly CustomEventGateInterface $customEventGate
    ) {
    }

    public function getMinCustomEventDateInPeriod(
        StoreId $storeId,
        CustomerId $customerId,
        array $eventTypes,
        DateTimeImmutable $startPeriodDate,
        DateTimeImmutable $endPeriodDate
    ): ?DateTimeImmutable {
        return $this->customEventGate->getMinCustomEventDateInPeriod(
            $storeId,
            $customerId,
            $eventTypes,
            $startPeriodDate,
            $endPeriodDate
        );
    }
}
