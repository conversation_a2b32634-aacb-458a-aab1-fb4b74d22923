<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Entity;

use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use JMS\Serializer\Annotation as JMS;
use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;
use OpenLoyalty\Core\Domain\Store;

/**
 * @ORM\Entity()
 * @ORM\Table(name="roles", indexes={@ORM\Index(name="rolesCreatedAtIdx", columns={"created_at"})})
 * @JMS\ExclusionPolicy("all")
 * @ORM\Cache(usage="NONSTRICT_READ_WRITE", region="role")
 * @JMS\VirtualProperty(
 *     "stores",
 *     exp="object.getFlatStores()"
 *  )
 */
class Role implements TimestampableInterface, BlameableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    /**
     * @ORM\Id
     * @ORM\Column(type="integer", name="id")
     * @ORM\GeneratedValue(strategy="AUTO")
     * @JMS\Expose()
     */
    private ?int $id = null;

    /**
     * @ORM\Column(type="string", name="name", nullable=true, length=50)
     * @JMS\Expose()
     */
    private string $name;

    /**
     * @ORM\Column(type="string", name="role", length=50)
     * @JMS\Expose()
     */
    private string $role;

    /**
     * @ORM\Column(type="boolean", name="is_master", options={"default": false})
     * @JMS\Expose()
     */
    private bool $master;

    /**
     * @ORM\Column(type="boolean", name="is_default", options={"default": false})
     * @JMS\Expose()
     */
    private bool $default;

    /**
     * @var Collection|Permission[]
     * @ORM\OneToMany(targetEntity="OpenLoyalty\User\Infrastructure\Entity\Permission", orphanRemoval=true,
     *     cascade={"persist", "remove"}, mappedBy="role")*
     * @JMS\Expose()
     * @ORM\Cache(usage="NONSTRICT_READ_WRITE", region="role")
     */
    private Collection $permissions;

    /**
     * @var Collection|Store[]
     * @ORM\ManyToMany(targetEntity="OpenLoyalty\Core\Domain\Store")
     * @ORM\JoinTable(name="roles_stores")
     * @ORM\Cache(usage="NONSTRICT_READ_WRITE", region="role")
     */
    private Collection $stores;

    /**
     * @ORM\Column(type="datetime_immutable_microseconds", name="created_at", options={"default": "CURRENT_TIMESTAMP"})
     */
    protected DateTimeImmutable $createdAt;

    /**
     * @ORM\Column(type="datetime_immutable_microseconds", name="updated_at", options={"default": "CURRENT_TIMESTAMP"})
     */
    protected DateTimeImmutable $updatedAt;

    /**
     * @ORM\Column(type="string", name="created_by", nullable=true)
     */
    protected ?string $createdBy = null;

    /**
     * @ORM\Column(type="string", name="updated_by", nullable=true)
     */
    protected ?string $updatedBy = null;

    public function __construct(string $role, string $name = null, bool $isMaster = false, bool $default = false)
    {
        $this->role = $role;
        $this->master = $isMaster;
        $this->permissions = new ArrayCollection();
        $this->name = $name ?? $role;
        $this->default = $default;
        $this->stores = new ArrayCollection();
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getRole()
    {
        return $this->role;
    }

    public function __toString(): string
    {
        return (string) $this->role;
    }

    public function getPermissions(): Collection
    {
        return $this->permissions;
    }

    /**
     * @param Permission[] $permissions
     */
    public function setPermissions(array $permissions): void
    {
        $this->permissions = new ArrayCollection($permissions);
    }

    public function addPermission(Permission $permission): void
    {
        $permission->setRole($this);
        $this->permissions->add($permission);
    }

    public function isMaster(): bool
    {
        return $this->master;
    }

    public function setMaster(bool $master): void
    {
        $this->master = $master;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function isDefault(): bool
    {
        return $this->default;
    }

    public function setDefault(bool $default): void
    {
        $this->default = $default;
    }

    public function getFlatStores(): array
    {
        return array_map(function (Store $store) {
            return (string) $store->getCode();
        }, $this->getStores()->toArray());
    }

    /**
     * @return Store[]|Collection
     */
    public function getStores(): Collection
    {
        return $this->stores;
    }

    public function addStore(Store $store): void
    {
        if (!$this->stores->contains($store)) {
            $this->stores->add($store);
        }
    }
}
