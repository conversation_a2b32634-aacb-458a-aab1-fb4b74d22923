<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\Entity\Repository;

use OpenLoyalty\Core\Domain\Repository;
use OpenLoyalty\User\Infrastructure\Entity\Role;

interface RoleRepositoryInterface extends Repository
{
    public function save(Role $role): void;

    public function findById(int $roleId): Role;

    public function remove(int $roleId): void;
}
