<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\User\Infrastructure\Entity;

use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;

/**
 * @ORM\Entity
 * @ORM\Table(name="permission")
 * @ORM\Cache(usage="NONSTRICT_READ_WRITE", region="permission")
 */
class Permission implements TimestampableInterface, BlameableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    /**
     * @var int
     * @ORM\Id
     * @ORM\Column(type="integer", name="id")
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     * @ORM\Column(type="string", name="resource", length=64)
     */
    private $resource;

    /**
     * @var string
     * @ORM\Column(type="string", name="access")
     */
    private $access;

    /**
     * @var string|null
     * @ORM\Column(type="string", name="filter_query", nullable=true)
     */
    private $filterQuery;

    /**
     * @var Role
     * @ORM\ManyToOne(targetEntity="OpenLoyalty\User\Infrastructure\Entity\Role", inversedBy="permissions")
     * @ORM\JoinColumn(onDelete="CASCADE")
     */
    private $role;

    /**
     * @ORM\Column(type="datetime_immutable_microseconds", name="created_at", options={"default": "CURRENT_TIMESTAMP"})
     */
    protected DateTimeImmutable $createdAt;

    /**
     * @ORM\Column(type="datetime_immutable_microseconds", name="updated_at", options={"default": "CURRENT_TIMESTAMP"})
     */
    protected DateTimeImmutable $updatedAt;

    /**
     * @ORM\Column(type="string", name="created_by", nullable=true)
     */
    protected ?string $createdBy = null;

    /**
     * @ORM\Column(type="string", name="updated_by", nullable=true)
     */
    protected ?string $updatedBy = null;

    /**
     * Permission constructor.
     */
    public function __construct(string $resource, string $access, string $filterQuery = null)
    {
        $this->resource = $resource;
        $this->access = $access;
        $this->filterQuery = $filterQuery;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getResource(): string
    {
        return $this->resource;
    }

    public function setResource(string $resource): void
    {
        $this->resource = $resource;
    }

    public function getAccess(): string
    {
        return $this->access;
    }

    public function setAccess(string $access): void
    {
        $this->access = $access;
    }

    public function getRole(): Role
    {
        return $this->role;
    }

    public function setRole(Role $role): void
    {
        $this->role = $role;
    }

    public function getFilterQuery(): ?string
    {
        return $this->filterQuery;
    }

    public function setFilterQuery(?string $filterQuery): void
    {
        $this->filterQuery = $filterQuery;
    }
}
