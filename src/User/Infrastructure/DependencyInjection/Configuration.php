<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Infrastructure\DependencyInjection;

use Symfony\Component\Config\Definition\Builder\TreeBuilder;
use Symfony\Component\Config\Definition\ConfigurationInterface;

class Configuration implements ConfigurationInterface
{
    /**
     * {@inheritdoc}
     */
    public function getConfigTreeBuilder(): TreeBuilder
    {
        $treeBuilder = new TreeBuilder('open_loyalty_user');

        $rootNode = $treeBuilder->getRootNode();
        $rootNode->children()
            ->enumNode('code_type')->values(
                [
                    'alphanum',
                    'num',
                ]
            )->defaultValue('num')->end();

        return $treeBuilder;
    }
}
