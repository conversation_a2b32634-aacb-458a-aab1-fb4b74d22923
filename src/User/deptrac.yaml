deptrac:
    paths:
        - ./src
    layers:
        -   name: UserDomain
            collectors:
                -   type: directory
                    regex: src/User/Domain/*
        -   name: UserApplication
            collectors:
                -   type: directory
                    regex: src/User/Application/*
        -   name: UserInfrastructure
            collectors:
                -   type: directory
                    regex: src/User/Infrastructure/*
        -   name: UserUi
            collectors:
                -   type: directory
                    regex: src/User/Ui/*
    ruleset:
        UserDomain:
            - UserInfrastructure # dependency to remove
            - CoreDomain
            - TransactionDomain
            - RewardDomain
            - InternalEventDomain
            - MessagingDomain
            - CustomEventDomain
            - LevelDomain
            - AccountDomain
        UserApplication:
            - UserDomain
            - UserInfrastructure # dependency to remove
            - CoreDomain
            - CoreApplication
            - CoreInfrastructure #dependency to remove
            - ImportDomain
            - ImportInfrastructure # dependency to remove
            - AuditApplication
            - LevelDomain
            - LevelInfrastructure # dependency to remove
            - SegmentDomain
            - MessagingDomain
            - CustomEventDomain
            - TransactionDomain
            - PointsDomain
            - RewardDomain
        UserInfrastructure:
            - UserDomain
            - UserApplication
            - CoreDomain
            - CoreApplication
            - CoreInfrastructure
            - ImportDomain
            - ImportInfrastructure
            - PointsDomain
            - PointsInfrastructure
            - LevelDomain
            - LevelInfrastructure
            - SettingsDomain
            - SettingsInfrastructure
            - ChannelDomain
            - ChannelInfrastructure
            - TransactionDomain
            - TransactionInfrastructure
            - TranslationInfrastructure
            - SegmentDomain
            - SegmentInfrastructure
            - CustomEventDomain
            - CustomEventInfrastructure
            - AccountDomain
            - AccountInfrastructure
            - RewardInfrastructure
            - MessagingInfrastructure
            - DataAnalyticsDomain
            - DataAnalyticsInfrastructure
            - CampaignInfrastructure
            - AuditInfrastructure
            - AnalyticsInfrastructure
            - LevelApplication
            - ExportInfrastructure
            - AchievementDomain
        UserUi:
            - UserDomain
            - UserApplication
            - UserInfrastructure
            - CoreDomain
            - CoreInfrastructure
            - MessagingDomain
            - ImportInfrastructure
            - ImportUi
            - UtilityApplication
            - AuditApplication
            - SegmentInfrastructure
            - SettingsDomain
            - SettingsInfrastructure
            - TransactionInfrastructure
            - LevelDomain
    skip_violations:
        OpenLoyalty\User\Domain\Event\Listener\CustomerHistoryListener:
            - OpenLoyalty\Core\Application\PublicEvent\Shared\MemberCompletedAchievement
            - OpenLoyalty\Core\Application\PublicEvent\Shared\MemberAchievementCompletionCountWasDecreased
            - OpenLoyalty\Core\Application\PublicEvent\Shared\MemberAchievementProgressWasChanged
        OpenLoyalty\User\Application\JobHandler\CustomerHasBecomeActiveBatchHandler:
            - OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventBusInterface
            - OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId
            - OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version
            - OpenLoyalty\DataAnalytics\Domain\Member\Event\MemberHasBecomeActiveBatch
            - OpenLoyalty\DataAnalytics\Domain\Member\ValueObject\ActiveMemberSettings
            - OpenLoyalty\DataAnalytics\Domain\Member\Event\MemberHasBecomeActive
        OpenLoyalty\User\Application\JobHandler\CustomerHasBecomeInactiveBatchHandler:
            - OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventBusInterface
            - OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId
            - OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version
            - OpenLoyalty\DataAnalytics\Domain\Member\Event\MemberHasBecomeInactiveBatch
            - OpenLoyalty\DataAnalytics\Domain\Member\ValueObject\ActiveMemberSettings
            - OpenLoyalty\DataAnalytics\Domain\Member\Event\MemberHasBecomeInactive
        OpenLoyalty\User\Infrastructure\Service\AclManager:
            - OpenLoyalty\Tools\BulkActions\Infrastructure\Security\Voter\BulkActionsVoter
            - OpenLoyalty\Event\Infrastructure\Security\Voter\EventVoter
