<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\UseCase\Customer;

use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\ORM\TransactionRequiredException;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\User\Application\Command\DeactivateCustomer;

class DeactivateCustomerUseCase
{
    public function __construct(
        private CommandBusInterface $commandBus
    ) {
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws TransactionRequiredException
     */
    public function execute(CustomerId $customerId): void
    {
        $this->commandBus->dispatch(
            new DeactivateCustomer($customerId)
        );
    }
}
