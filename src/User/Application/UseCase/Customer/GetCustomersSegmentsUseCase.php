<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\UseCase\Customer;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Segment\Domain\Model\SegmentCustomer;
use OpenLoyalty\Segment\Domain\SegmentCustomerRepositoryReadContextInterface;

final readonly class GetCustomersSegmentsUseCase
{
    public function __construct(
        private SegmentCustomerRepositoryReadContextInterface $segmentCustomerRepository
    ) {
    }

    public function execute(CustomerId $customerId): array
    {
        $segments = $this->segmentCustomerRepository->findBy(['customerId' => $customerId]);

        return array_map(
            static fn (SegmentCustomer $segment): string => (string) $segment->getSegment()->getSegmentId(),
            $segments
        );
    }
}
