<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\UseCase\Customer;

use OpenLoyalty\Core\Domain\Id\ChannelId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\User\Application\Command\AssignChannelToCustomer;

class AssignChannelToCustomerUseCase
{
    /**
     * @var CommandBusInterface
     */
    private $commandBus;

    public function __construct(CommandBusInterface $commandBus)
    {
        $this->commandBus = $commandBus;
    }

    public function execute(CustomerId $customerId, ChannelId $channelId): void
    {
        $this->commandBus->dispatch(
            new AssignChannelToCustomer($customerId, $channelId)
        );
    }
}
