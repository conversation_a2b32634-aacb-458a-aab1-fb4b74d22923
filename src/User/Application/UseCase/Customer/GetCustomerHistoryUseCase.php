<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\UseCase\Customer;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\UuidCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponse;
use OpenLoyalty\User\Application\DataMapper\CustomerHistoryDataMapper;
use OpenLoyalty\User\Domain\CustomerHistoryRepositoryReadContextInterface;
use Ramsey\Uuid\Uuid;

class GetCustomerHistoryUseCase
{
    public function __construct(
        private readonly CustomerHistoryRepositoryReadContextInterface $customerHistoryRepository,
        private readonly SearchableResponderInterface $searchableResponder,
        private readonly CustomerHistoryDataMapper $customerHistoryDataMapper
    ) {
    }

    public function execute(CustomerId $customerId, CriteriaCollectionInterface $criteria): SearchableResponse
    {
        $criteria->add((new UuidCriteria(
            'customerId',
            CriteriaInterface::EQUAL,
            Uuid::fromString((string) $customerId)
        ))->setAsInternal());

        $searchableResponse = $this->searchableResponder->fromCriteria($this->customerHistoryRepository, $criteria, true);
        $customers = $this->customerHistoryDataMapper->mapList($searchableResponse->getItems());
        $total = $searchableResponse->getTotal();

        return new SearchableResponse($customers, $total->getAll(), $total->getFiltered(), $total->isEstimated());
    }
}
