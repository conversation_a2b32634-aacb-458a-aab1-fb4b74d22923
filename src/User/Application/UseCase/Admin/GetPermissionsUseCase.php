<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\UseCase\Admin;

use OpenLoyalty\User\Application\Response\PermissionsResponse;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionEvaluatorInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class GetPermissionsUseCase
{
    public function __construct(
        private UserPermissionEvaluatorInterface $userPermissionEvaluator
    ) {
    }

    public function execute(UserInterface $user): PermissionsResponse
    {
        return new PermissionsResponse(
            $this->userPermissionEvaluator->isSuperAdmin($user),
            $user->getRoles(),
            $this->getPermissionsData($user)
        );
    }

    protected function getPermissionsData(UserInterface $user): array
    {
        $permissions = [];

        foreach ($this->userPermissionEvaluator->getPermissions($user) as $permission) {
            $resource = $permission->getResource();
            if (!array_key_exists($resource, $permissions)) {
                $permissions[$resource] = [];
            }
            $permissions[$resource] = array_unique(array_merge(
                $permissions[$resource],
                [$permission->getAccess()]
            ));
        }

        return $permissions;
    }
}
