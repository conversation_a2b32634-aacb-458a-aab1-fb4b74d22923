<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\UseCase\Acl;

use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponse;
use OpenLoyalty\User\Infrastructure\Service\AclManagerInterface;

class GetAclResourcesListUseCase
{
    private AclManagerInterface $aclManager;

    public function __construct(AclManagerInterface $aclManager)
    {
        $this->aclManager = $aclManager;
    }

    public function execute(): SearchableResponse
    {
        $resources = $this->aclManager->getAvailableResources();

        return new SearchableResponse($resources, count($resources), null);
    }
}
