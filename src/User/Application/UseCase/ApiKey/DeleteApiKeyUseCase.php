<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\UseCase\ApiKey;

use OpenLoyalty\Core\Domain\Id\ApiKeyId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\User\Application\Command\DeleteApiKey;

final readonly class DeleteApiKeyUseCase
{
    public function __construct(
        private CommandBusInterface $commandBus
    ) {
    }

    public function execute(ApiKeyId $apiKeyId): ApiKeyId
    {
        $this->commandBus->dispatch(
            new DeleteApiKey(
                $apiKeyId,
            )
        );

        return $apiKeyId;
    }
}
