<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\UseCase;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use OpenLoyalty\User\Application\Exception\BadConfirmationTokenException;
use OpenLoyalty\User\Application\Request\ResetPasswordRequest;
use OpenLoyalty\User\Domain\Hasher\PasswordHasherInterface;
use OpenLoyalty\User\Domain\Service\CodeManagerInterface;
use OpenLoyalty\User\Infrastructure\Entity\Admin;
use OpenLoyalty\User\Infrastructure\Entity\Customer;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Service\UserManager;

final readonly class ResetUserPasswordUseCase
{
    public function __construct(
        private UserManager $userManager,
        private CodeManagerInterface $codeManager,
        private EntityManagerInterface $entityManager,
        private PasswordHasherInterface $passwordHasher
    ) {
    }

    /**
     * @throws BadConfirmationTokenException
     */
    public function execute(ResetPasswordRequest $request): void
    {
        $class = $request->getUserClass() ?? Customer::class;

        $code = $this->codeManager->findValidCode(
            $request->getToken(),
            $class,
            Customer::class === $class
                ? CodeManagerInterface::MEMBER_PASSWORD_RESET
                : CodeManagerInterface::USER_PASSWORD_RESET
        );
        if (null === $code) {
            throw new BadConfirmationTokenException();
        }

        /** @var EntityRepository $repository */
        $repository = $this->entityManager->getRepository($class);
        /** @var User|Admin|Customer $user */
        $user = $repository->find($code->getObjectId());

        if (null === $user) {
            throw new BadConfirmationTokenException();
        }

        $user->setPassword(
            $this->passwordHasher->hashPassword(
                (string) $request->getPlainPassword()
            )
        );
        $this->userManager->updateUser($user);
        $this->codeManager->markCodeAsUsed($code);
    }
}
