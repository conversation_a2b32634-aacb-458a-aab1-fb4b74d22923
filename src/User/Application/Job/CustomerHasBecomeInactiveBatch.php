<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\Job;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Message\JobInterface;
use OpenLoyalty\User\Domain\ValueObject\CustomerActivationData;

final class CustomerHasBecomeInactiveBatch implements JobInterface
{
    private array $customersActivationData;

    public function __construct(
    ) {
        $this->customersActivationData = [];
    }

    public function addCustomerData(CustomerId $customerId, DateTimeImmutable $eventDate, array $activeMemberSettings): void
    {
        $this->customersActivationData[] = new CustomerActivationData(
            $customerId,
            $eventDate,
            $activeMemberSettings
        );
    }

    /**
     * @return CustomerActivationData[]
     */
    public function getCustomersActivationData(): array
    {
        return $this->customersActivationData;
    }
}
