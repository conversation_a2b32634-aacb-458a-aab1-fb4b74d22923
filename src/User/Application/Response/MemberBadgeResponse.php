<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\Response;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\BadgeTypeId;

final readonly class MemberBadgeResponse
{
    public function __construct(
        public BadgeTypeId $badgeTypeId,
        public string $name,
        public int $completedCount,
        public DateTimeImmutable $createdAt,
        public DateTimeImmutable $updatedAt
    ) {
    }
}
