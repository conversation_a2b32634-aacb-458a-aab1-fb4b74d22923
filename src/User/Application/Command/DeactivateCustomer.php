<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\Command;

use DateTime;
use OpenLoyalty\Core\Domain\Id\CustomerId;

class DeactivateCustomer extends CustomerCommand
{
    /**
     * @var DateTime
     */
    private $deactivatedAt;

    public function __construct(CustomerId $customerId, ?DateTime $deactivatedAt = null)
    {
        parent::__construct($customerId);

        $this->deactivatedAt = $deactivatedAt ?: new DateTime();
    }

    public function getDeactivatedAt(): DateTime
    {
        return $this->deactivatedAt;
    }
}
