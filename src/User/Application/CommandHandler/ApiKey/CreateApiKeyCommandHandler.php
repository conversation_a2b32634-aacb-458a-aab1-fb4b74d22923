<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\CommandHandler\ApiKey;

use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\User\Application\Command\CreateApiKey;
use OpenLoyalty\User\Domain\AdminRepository;
use OpenLoyalty\User\Domain\ApiKey;
use OpenLoyalty\User\Domain\ApiKeyRepositoryInterface;

final readonly class CreateApiKeyCommandHandler implements CommandHandlerInterface
{
    public function __construct(
        private ApiKeyRepositoryInterface $apiKeyRepository,
        private AdminRepository $adminRepository
    ) {
    }

    public function __invoke(CreateApiKey $command): void
    {
        $admin = $this->adminRepository->findById($command->getAdminId());
        $apiKey = new ApiKey(
            $command->getApiKeyId(),
            $admin,
            $command->getName(),
            $command->getToken(),
            $command->getExpirationDate(),
            $command->isLegacy()
        );

        $this->apiKeyRepository->save($apiKey);
    }
}
