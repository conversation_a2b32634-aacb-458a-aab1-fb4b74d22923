<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\CommandHandler\CustomAttribute;

use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\User\Application\Command\CustomAttribute\DeleteCustomAttributes;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;
use OpenLoyalty\User\Domain\SystemEvent\CustomerDetailsChangedSystemEvent;

class DeleteCustomAttributesCommandHandler implements CommandHandlerInterface
{
    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly EventBusInterface $eventBus
    ) {
    }

    public function __invoke(DeleteCustomAttributes $command): void
    {
        $customer = $this->customerRepository->load($command->getCustomerId());
        $customer->deleteCustomAttributes($command->getCustomAttributes());

        $this->customerRepository->save($customer);

        $this->eventBus->dispatch(
            new CustomerDetailsChangedSystemEvent($command->getCustomerId(), $customer->getStoreId()),
        );
    }
}
