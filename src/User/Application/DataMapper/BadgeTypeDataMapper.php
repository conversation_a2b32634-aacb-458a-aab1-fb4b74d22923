<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\DataMapper;

use OpenLoyalty\Core\Application\DataMapper\DataMapperInterface;
use OpenLoyalty\User\Application\Response\BadgeTypeResponse;
use OpenLoyalty\User\Domain\BadgeType;

final readonly class BadgeTypeDataMapper implements DataMapperInterface
{
    public function map(BadgeType $entity): BadgeTypeResponse
    {
        return new BadgeTypeResponse(
            $entity->getBadgeTypeId(),
            $entity->getName(),
            $entity->getCreatedAt(),
            $entity->getUpdatedAt(),
        );
    }

    /**
     * @param array<BadgeType> $entities
     *
     * @return array<BadgeTypeResponse>
     */
    public function mapList(array $entities): array
    {
        $badgeTypes = [];
        foreach ($entities as $entity) {
            $badgeTypes[] = $this->map($entity);
        }

        return $badgeTypes;
    }
}
