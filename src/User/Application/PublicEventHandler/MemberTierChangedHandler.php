<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\PublicEventHandler;

use OpenLoyalty\Core\Application\PublicEvent\Message\PublicEventHandlerInterface;
use OpenLoyalty\Core\Application\PublicEvent\Shared\MemberTierChanged;
use OpenLoyalty\Core\Domain\Message\DomainEventBusInterface;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Level\Domain\Exception\LevelNotFoundException;
use OpenLoyalty\User\Application\Exception\CustomerNotFoundException;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;
use OpenLoyalty\User\Domain\Event\CustomerWasMovedToLevel;
use OpenLoyalty\User\Domain\SystemEvent\CustomerLevelChangedSystemEvent;
use OpenLoyalty\User\Domain\SystemEvent\CustomerUpdatedSystemEvent;
use OpenLoyalty\User\Domain\TierFacadeInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageSubscriberInterface;

final readonly class MemberTierChangedHandler implements PublicEventHandlerInterface, MessageSubscriberInterface
{
    public function __construct(
        private EventBusInterface $eventBus,
        private DomainEventBusInterface $domainEventBus,
        private LoggerInterface $logger,
        private CustomerRepositoryInterface $customerRepository,
        private TierFacadeInterface $tierFacade
    ) {
    }

    public function handleMemberTierChanged(MemberTierChanged $event): void
    {
        $customer = $this->customerRepository->load($event->customerId);

        $newTierId = $event->newLevelId;
        if (null === $newTierId) {
            throw new LevelNotFoundException();
        }

        /* @todo remove levelId from customer in 11134 */
        if ($this->tierFacade->isTierFromDefaultTierSet($newTierId)) {
            $customer->addToLevel($newTierId, $event->manually);
            $this->customerRepository->save($customer);
        } else {
            $customerId = $customer->getCustomerId();
            if (null === $customerId) {
                throw new CustomerNotFoundException();
            }

            $this->domainEventBus->dispatch(
                new CustomerWasMovedToLevel(
                    $customerId,
                    $newTierId,
                    $event->oldLevelId,
                    $event->manually,
                )
            );
        }

        $this->eventBus->dispatch(
            new CustomerUpdatedSystemEvent($event->storeId, $event->customerId),
        );

        $this->eventBus->dispatch(new CustomerLevelChangedSystemEvent(
            $event->storeId,
            $event->customerId,
            !$event->downgrade,
            $event->newLevelId,
        ));

        $this->logger->info(sprintf(
            '[Customer][%s]: Level changed to %s (manually = %s)',
            $event->customerId,
            $event->newLevelId,
            $event->manually
        ));
    }

    public static function getHandledMessages(): iterable
    {
        yield MemberTierChanged::class => [
            'method' => 'handleMemberTierChanged',
            'bus' => 'public.event.bus',
        ];
    }
}
