<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Acl;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\User\Application\UseCase\Acl\CreateRoleUseCase;
use OpenLoyalty\User\Infrastructure\Form\Type\RoleFormType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Security("is_granted('ROLE_ADMIN')")
 */
class PostRole extends AbstractFOSRestController
{
    private CreateRoleUseCase $useCase;
    private FormFactoryInterface $formFactory;

    public function __construct(CreateRoleUseCase $useCase, FormFactoryInterface $formFactory)
    {
        $this->useCase = $useCase;
        $this->formFactory = $formFactory;
    }

    /**
     * @Route(methods={"POST"}, name="oloy.user.role.create", path="/acl/role")
     *
     * @Security("is_granted('CREATE_ROLE')")
     *
     * @Operation(
     *     tags={"ACL"},
     *     summary="Add a new role",
     *     operationId="aclPostRole",
     *     @OA\RequestBody(
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(property="role", ref=@Model(type=RoleFormType::class))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="201",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="204",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="400",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="401",
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 oneOf={
     *                     @OA\Schema(ref="#/components/schemas/ExpiredToken"),
     *                     @OA\Schema(ref="#/components/schemas/InvalidToken"),
     *                     @OA\Schema(ref="#/components/schemas/Unauthorized")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     )
     * )
     */
    public function __invoke(Request $request): View
    {
        $form = $this->formFactory->createNamed(
            'role',
            RoleFormType::class,
            [],
            [
                'validation_groups' => ['create', 'Default'],
            ]);
        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $this->useCase->execute(
            $form->get('name')->getData(),
            $form->get('permissions')->getData(),
            $form->get('default')->getData(),
            $form->get('stores')->getData()
        );

        return $this->view(null, Response::HTTP_NO_CONTENT);
    }
}
