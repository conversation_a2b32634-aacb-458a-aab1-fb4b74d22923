<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Acl;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Ui\Rest\Responder\ErrorResponderInterface;
use OpenLoyalty\User\Application\Exception\RoleIsNotDeletableException;
use OpenLoyalty\User\Application\UseCase\Acl\DeleteRoleUseCase;
use OpenLoyalty\User\Infrastructure\Entity\Role;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Security("is_granted('ROLE_ADMIN')")
 */
class DeleteRole extends AbstractFOSRestController
{
    private DeleteRoleUseCase $useCase;
    private TranslatorInterface $translator;
    private ErrorResponderInterface $formErrorResponder;

    public function __construct(
        DeleteRoleUseCase $useCase,
        TranslatorInterface $translator,
        ErrorResponderInterface $formErrorResponder
    ) {
        $this->useCase = $useCase;
        $this->translator = $translator;
        $this->formErrorResponder = $formErrorResponder;
    }

    /**
     * @Route(methods={"DELETE"}, name="oloy.user.acl.role.delete", path="/acl/role/{role}", requirements={"role"="\d+"})
     *
     * @Security("is_granted('EDIT', role)")
     *
     * @Operation(
     *     tags={"ACL"},
     *     summary="Delete role",
     *     operationId="aclDeleteRole",
     *     @OA\Parameter(ref="#/components/parameters/role"),
     *     @OA\Response(
     *         response="204",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="401",
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 oneOf={
     *                     @OA\Schema(ref="#/components/schemas/ExpiredToken"),
     *                     @OA\Schema(ref="#/components/schemas/InvalidToken"),
     *                     @OA\Schema(ref="#/components/schemas/Unauthorized")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Role $role): View
    {
        try {
            $this->useCase->execute($role);
        } catch (RoleIsNotDeletableException $ex) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans('user.acl.can_not_delete_master_role')
            );
        }

        return $this->view(
            null,
            Response::HTTP_NO_CONTENT
        );
    }
}
