<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Admin;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\User\Application\Exception\PasswordChangeNotAllowed;
use OpenLoyalty\User\Application\UseCase\ChangeUserPasswordUseCase;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Form\Type\ChangePasswordFormType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Security("is_granted('ROLE_USER')")
 */
class PutPassword extends AbstractFOSRestController
{
    private ChangeUserPasswordUseCase $useCase;
    private FormFactoryInterface $formFactory;
    private TranslatorInterface $translator;

    public function __construct(
        ChangeUserPasswordUseCase $useCase,
        FormFactoryInterface $formFactory,
        TranslatorInterface $translator
    ) {
        $this->useCase = $useCase;
        $this->formFactory = $formFactory;
        $this->translator = $translator;
    }

    /**
     * @Route(methods={"PUT"}, name="oloy.user.change_password", path="/admin/password")
     *
     * @Security("is_granted('ROLE_ADMIN')")
     *
     * @Operation(
     *     tags={"Admin"},
     *     summary="Changged logged admin’s password",
     *     operationId="adminPutPassword",
     *     @OA\RequestBody(
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 required={"currentPassword", "plainPassword"},
     *                 @OA\Property(property="currentPassword", type="string"),
     *                 @OA\Property(property="plainPassword", type="string")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="204",
     *         description="Returned when successful",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="400",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="401",
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 oneOf={
     *                     @OA\Schema(ref="#/components/schemas/ExpiredToken"),
     *                     @OA\Schema(ref="#/components/schemas/InvalidToken"),
     *                     @OA\Schema(ref="#/components/schemas/Unauthorized")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     )
     * )
     */
    public function __invoke(Request $request): View
    {
        /** @var User $user */
        $user = $this->getUser();
        $form = $this->formFactory->createNamed('', ChangePasswordFormType::class, null, [
            'method' => 'PUT',
        ]);

        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        try {
            $this->useCase->execute($user, $form->getData()['plainPassword']);

            return $this->view();
        } catch (PasswordChangeNotAllowed $e) {
            $form->addError(new FormError($this->translator->trans('user.password_change.not_allowed')));
        }

        return $this->view($form, Response::HTTP_BAD_REQUEST);
    }
}
