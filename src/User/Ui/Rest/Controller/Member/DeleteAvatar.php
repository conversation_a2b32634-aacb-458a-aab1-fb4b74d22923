<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Member;

use Exception;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Ui\Rest\Responder\ErrorResponderInterface;
use OpenLoyalty\User\Application\UseCase\Customer\DeleteCustomerAvatarUseCase;
use OpenLoyalty\User\Domain\Customer;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

class DeleteAvatar extends AbstractFOSRestController
{
    private DeleteCustomerAvatarUseCase $useCase;
    private TranslatorInterface $translator;
    private ErrorResponderInterface $formErrorResponder;

    public function __construct(
        DeleteCustomerAvatarUseCase $useCase,
        TranslatorInterface $translator,
        ErrorResponderInterface $formErrorResponder
    ) {
        $this->useCase = $useCase;
        $this->translator = $translator;
        $this->formErrorResponder = $formErrorResponder;
    }

    /**
     * @Route(methods={"DELETE"}, name="oloy.member.remove_photo", path="/{storeCode}/member/{member}/avatar", requirements={"member"="%routing.member-query%"})
     *
     * @Security("is_granted('EDIT', member)")
     */
    public function __invoke(Customer $member): View
    {
        $avatarPath = $member->getAvatar()?->getPath();
        if (null === $avatarPath) {
            throw $this->createNotFoundException();
        }

        try {
            $this->useCase->execute($member->getCustomerId(), $avatarPath);

            return $this->view(null, Response::HTTP_NO_CONTENT);
        } catch (Exception $ex) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans($ex->getMessage())
            );
        }
    }
}
