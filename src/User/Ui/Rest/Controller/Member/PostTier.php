<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Member;

use Assert\AssertionFailedException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Ui\Rest\Responder\ErrorResponderInterface;
use OpenLoyalty\User\Application\Exception\InvalidStoreException;
use OpenLoyalty\User\Application\UseCase\Customer\AssignLevelToCustomerUseCase;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Infrastructure\Exception\LevelNotFoundException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

class PostTier extends AbstractFOSRestController
{
    private AssignLevelToCustomerUseCase $useCase;
    private TranslatorInterface $translator;
    private ErrorResponderInterface $formErrorResponder;

    public function __construct(
        AssignLevelToCustomerUseCase $useCase,
        TranslatorInterface $translator,
        ErrorResponderInterface $formErrorResponder
    ) {
        $this->useCase = $useCase;
        $this->translator = $translator;
        $this->formErrorResponder = $formErrorResponder;
    }

    /**
     * @Route(methods={"POST"}, name="oloy.member.add_member_to_tier", path="/{storeCode}/member/{member}/tier", requirements={"member"="%routing.member-query%"})
     *
     * @Security("is_granted('ASSIGN_CUSTOMER_LEVEL', member)")
     */
    public function __invoke(Request $request, Customer $member): View
    {
        $levelId = $request->request->get('levelId');

        if (!$levelId) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans('Field "levelId" is required'),
            );
        }

        try {
            $levelId = new LevelId($levelId);
            $this->useCase->execute($member->getCustomerId(), $levelId);

            return $this->view(null, Response::HTTP_NO_CONTENT);
        } catch (AssertionFailedException $e) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans('Invalid value'),
            );
        } catch (InvalidStoreException $e) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans('store.code_invalid')
            );
        } catch (LevelNotFoundException $e) {
            throw $this->createNotFoundException();
        }
    }
}
