<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Member;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Infrastructure\Service\AvatarUploader;
use OpenLoyalty\User\Ui\Rest\Controller\Member\Responder\AvatarResponder;
use Symfony\Component\HttpFoundation\Response;

class GetAvatar extends AbstractFOSRestController
{
    private AvatarUploader $avatarUploader;
    private AvatarResponder $avatarResponder;

    public function __construct(AvatarUploader $avatarUploader, AvatarResponder $avatarResponder)
    {
        $this->avatarUploader = $avatarUploader;
        $this->avatarResponder = $avatarResponder;
    }

    /**
     * @Route(methods={"GET"}, name="oloy.member.get_avatar", path="/{storeCode}/member/{member}/avatar", requirements={"member"="%routing.member-query%"})
     */
    public function __invoke(Customer $member): Response
    {
        $avatarPath = $member->getAvatar()?->getPath();
        if (null === $avatarPath) {
            throw $this->createNotFoundException();
        }

        $content = $this->avatarUploader->get($avatarPath);
        if (!$content) {
            throw $this->createNotFoundException();
        }

        return $this->avatarResponder->__invoke($content, $member->getAvatar()?->getMime());
    }
}
