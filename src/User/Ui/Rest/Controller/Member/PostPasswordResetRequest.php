<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Member;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Ui\Rest\Responder\ErrorResponderInterface;
use OpenLoyalty\User\Application\Exception\PasswordChangeNotAllowed;
use OpenLoyalty\User\Application\Exception\PasswordResetAlreadyRequested;
use OpenLoyalty\User\Application\Exception\PasswordResetRequestFailed;
use OpenLoyalty\User\Application\UseCase\RequestResetCustomerPasswordUseCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Contracts\Translation\TranslatorInterface;

class PostPasswordResetRequest extends AbstractFOSRestController
{
    private RequestResetCustomerPasswordUseCase $useCase;
    private TranslatorInterface $translator;
    private ErrorResponderInterface $formErrorResponder;

    public function __construct(
        RequestResetCustomerPasswordUseCase $useCase,
        TranslatorInterface $translator,
        ErrorResponderInterface $formErrorResponder
    ) {
        $this->useCase = $useCase;
        $this->translator = $translator;
        $this->formErrorResponder = $formErrorResponder;
    }

    /**
     * @Route(methods={"POST"}, name="oloy.user.member.request_member", path="/{storeCode}/member/password/reset/request")
     *
     * @Operation(
     *     tags={"Member"},
     *     deprecated=true,
     *     summary="Request member’s password reset",
     *     operationId="memberPostPasswordResetRequest",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\RequestBody(
     *         description="",
     *         required=false,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="username",
     *                     type="string"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="204",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Returned when username parameter is not present or resetting password already requested",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Request $request): View
    {
        $username = $request->request->get('username');
        if (!$username) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans('Field "username" should not be empty'),
            );
        }

        try {
            $this->useCase->execute($username);

            return $this->view();
        } catch (UserNotFoundException $exception) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans($exception->getMessageKey()),
                Response::HTTP_NOT_FOUND
            );
        } catch (PasswordResetAlreadyRequested) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans('user.password_reset.already_requested')
            );
        } catch (PasswordResetRequestFailed) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans('user.password_reset.failed')
            );
        } catch (PasswordChangeNotAllowed) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans('user.password_change.not_allowed')
            );
        }
    }
}
