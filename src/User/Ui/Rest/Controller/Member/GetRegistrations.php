<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Member;

use Carbon\Carbon;
use DateTimeImmutable;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\QueryParam;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\User\Application\UseCase\Customer\GetRegistrationHistogramUseCase;
use Symfony\Component\HttpFoundation\Response;

class GetRegistrations extends AbstractFOSRestController
{
    private GetRegistrationHistogramUseCase $useCase;
    private ParamFetcherInterface $paramFetcher;
    private StoreContextProviderInterface $storeContextProvider;

    public function __construct(
        GetRegistrationHistogramUseCase $useCase,
        ParamFetcherInterface $paramFetcher,
        StoreContextProviderInterface $storeContextProvider
    ) {
        $this->useCase = $useCase;
        $this->paramFetcher = $paramFetcher;
        $this->storeContextProvider = $storeContextProvider;
    }

    /**
     * @Route(methods={"GET"}, name="oloy.member.get_customers_registrations", path="/{storeCode}/member/registrations")
     *
     * @QueryParam(name="lastDays", default="30", nullable=true, description="Range from last X days")
     * @QueryParam(
     *     name="interval",
     *     nullable=false,
     *     default="day",
     *     requirements="(day|month|year)", description="Group by interval"
     * )
     *
     * @Operation(
     *     tags={"Member"},
     *     summary="Get number of registered members",
     *     operationId="memberGetRegistrations",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(
     *         name="lastDays",
     *         in="query",
     *         description="Range from last X days",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="interval",
     *         in="query",
     *         description="Group by interval",
     *         required=true,
     *         @OA\Schema(
     *             type="string",
     *             format="day|month|year"
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 format="date-time",
     *                 @OA\AdditionalProperties(type="integer")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(): View
    {
        $lastDays = (int) $this->paramFetcher->get('lastDays');
        $interval = $this->paramFetcher->get('interval');

        $date = new Carbon();
        $date->setTime(0, 0, 0);
        $date->modify(sprintf('-%d days', $lastDays));

        $response = $this->useCase->execute(
            $this->storeContextProvider->getStore()->getStoreId(),
            DateTimeImmutable::createFromMutable($date),
            $interval
        );

        return $this->view($response, Response::HTTP_OK);
    }
}
