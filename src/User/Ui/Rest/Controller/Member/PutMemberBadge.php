<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Member;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Infrastructure\Provider\CurrentUserProviderInterface;
use OpenLoyalty\User\Application\Command\UpdateMemberBadge;
use OpenLoyalty\User\Domain\BadgeType;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Infrastructure\Form\Type\UpdateMemberBadgeFormType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class PutMemberBadge extends AbstractFOSRestController
{
    public function __construct(
        private readonly FormFactoryInterface $formFactory,
        private readonly CommandBusInterface $commandBus,
        private readonly CurrentUserProviderInterface $currentUserProvider,
    ) {
    }

    /**
     * @Route(methods={"PUT"}, name="oloy.member.edit_member_badge", path="/{storeCode}/member/{member}/badge/{badgeTypeId}", requirements={"member"="%routing.member-query%"})
     *
     * @Security("is_granted('EDIT_BADGE', badgeType)")
     */
    public function __invoke(Request $request, Customer $member, BadgeType $badgeType): View
    {
        $form = $this->formFactory->createNamed(
            'badge',
            UpdateMemberBadgeFormType::class,
            [],
            [
                'method' => 'PUT',
                'memberId' => $member->getCustomerId(),
                'storeId' => $member->getStoreId(),
                'badgeTypeId' => $badgeType->getBadgeTypeId(),
                'adminId' => $this->currentUserProvider->getLoggedInAdminId(),
            ]
        );

        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }
        /** @var UpdateMemberBadge $command */
        $command = $form->getData();
        $this->commandBus->dispatch($command);

        return $this->view();
    }
}
