<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Member;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\User\Application\Response\CustomerStatus;
use OpenLoyalty\User\Application\Status\CustomerStatusProviderInterface;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Domain\Exception\CustomerNotFoundException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;

final class GetStatus extends AbstractFOSRestController
{
    public function __construct(private readonly CustomerStatusProviderInterface $customerStatusProvider)
    {
    }

    /**
     * @Route(methods={"GET"}, name="oloy.member.get_status", path="/{storeCode}/member/{member}/status", requirements={"member"="%routing.member-query%"})
     *
     * @Security("is_granted('VIEW_STATUS', member)")
     *
     * @Operation(
     *     tags={"Member"},
     *     deprecated=true,
     *     summary="Get member’s loyalty status",
     *     operationId="memberGetStatus",
     *     @OA\Parameter(
     *         name="member",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Response(
     *         response="200",
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 ref=@Model(type=CustomerStatus::class)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Customer $member): View
    {
        try {
            $response = $this->customerStatusProvider->getStatus($member->getCustomerId());

            return $this->view($response, Response::HTTP_OK);
        } catch (CustomerNotFoundException) {
            throw $this->createNotFoundException();
        }
    }
}
