<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Member;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Ui\Rest\Responder\ErrorResponderInterface;
use OpenLoyalty\User\Application\Exception\CustomerAnonymizeFailedException;
use OpenLoyalty\User\Application\UseCase\Customer\AnonymizeCustomerUseCase;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Domain\Exception\CustomerAlreadyAnonymizedException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

class PostAnonymize extends AbstractFOSRestController
{
    public function __construct(
        private AnonymizeCustomerUseCase $useCase,
        private ErrorResponderInterface $formErrorResponder,
        private TranslatorInterface $translator
    ) {
    }

    /**
     * @Route(methods={"POST"}, name="oloy.member.anonymize_customer", path="/{storeCode}/member/{member}/anonymize", requirements={"member"="%routing.member-query%"})
     *
     * @Security("is_granted('ANONYMIZE', member)")
     *
     * @Operation(
     *     tags={"Member"},
     *     summary="Anonymize member’s data",
     *     operationId="memberPostAnonymize",
     *     @OA\Parameter(
     *         name="member",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Response(
     *         response="204",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="400",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     */
    public function __invoke(Customer $member): View
    {
        try {
            $this->useCase->execute($member->getCustomerId());

            return $this->view(null, Response::HTTP_NO_CONTENT);
        } catch (CustomerAnonymizeFailedException|CustomerAlreadyAnonymizedException $e) {
            return $this->formErrorResponder->fromString(
                $this->translator->trans($e->getMessage()),
            );
        }
    }
}
