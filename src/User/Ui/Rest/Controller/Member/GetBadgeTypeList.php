<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Member;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchFormFactoryInterface;
use OpenLoyalty\User\Application\UseCase\Badge\GetBadgeTypeListUseCase;
use OpenLoyalty\User\Infrastructure\Form\Type\BadgeTypeSearchType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class GetBadgeTypeList extends AbstractFOSRestController
{
    public function __construct(
        private readonly SearchFormFactoryInterface $searchFormFactory,
        private readonly GetBadgeTypeListUseCase $useCase,
    ) {
    }

    /**
     * @Route(methods={"GET"}, name="oloy.member.get_badge_type", path="/{storeCode}/badge-type")
     *
     * @Security("is_granted('VIEW_BADGES')")
     */
    public function __invoke(Request $request): View
    {
        $form = $this->searchFormFactory->createAndHandle(
            BadgeTypeSearchType::class,
            $request->query->all(),
            $request->getLocale()
        );

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        /** @var CriteriaCollectionInterface $criteria */
        $criteria = $form->getData();
        $result = $this->useCase->execute($criteria);

        return $this->view($result, Response::HTTP_OK);
    }
}
