<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Member;

use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\ORM\TransactionRequiredException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\User\Application\UseCase\Customer\DeactivateCustomerUseCase;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Domain\Exception\CustomerAlreadyInactiveException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;

class PostDeactivate extends AbstractFOSRestController
{
    private DeactivateCustomerUseCase $useCase;

    public function __construct(DeactivateCustomerUseCase $useCase)
    {
        $this->useCase = $useCase;
    }

    /**
     * @Route(methods={"POST"}, name="oloy.member.deactivate_member", path="/{storeCode}/member/{member}/deactivate", requirements={"member"="%routing.member-query%"})
     *
     * @Security("is_granted('DEACTIVATE', member)")
     *
     * @Operation(
     *     tags={"Member"},
     *     summary="Deactivate a member",
     *     operationId="memberPostDeactivate",
     *     @OA\Parameter(
     *         name="member",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Response(
     *         response="204",
     *         ref="#/components/responses/NoContent"
     *     ),
     *     @OA\Response(
     *         response="400",
     *         ref="#/components/responses/BadRequest"
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     ),
     *     @OA\Response(
     *         response="404",
     *         ref="#/components/responses/NotFound"
     *     )
     * )
     *
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws TransactionRequiredException
     */
    public function __invoke(Customer $member): View
    {
        try {
            $this->useCase->execute($member->getCustomerId());

            return $this->view(null, Response::HTTP_NO_CONTENT);
        } catch (CustomerAlreadyInactiveException $e) {
            return $this->view(null, Response::HTTP_BAD_REQUEST);
        }
    }
}
