<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Member\Responder;

use FOS\RestBundle\Context\Context;
use FOS\RestBundle\View\View;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class CustomerListResponder.
 */
class CustomerListResponder
{
    public function __invoke(SearchableResponse $response, string $locale): View
    {
        $view = View::create($response, Response::HTTP_OK);

        $context = new Context();
        $context->addGroup('Default');
        $context->setAttribute('locale', $locale);

        $view->setContext($context);

        return $view;
    }
}
