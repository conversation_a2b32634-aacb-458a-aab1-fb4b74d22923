<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Ui\Rest\Controller\Member;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\User\Application\Command\UpdateMemberBadge;
use OpenLoyalty\User\Domain\BadgeType;
use OpenLoyalty\User\Infrastructure\Form\Type\UpdateBadgeTypeFormType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class PutBadgeType extends AbstractFOSRestController
{
    public function __construct(
        private readonly FormFactoryInterface $formFactory,
        private readonly CommandBusInterface $commandBus,
        private readonly StoreContextProviderInterface $storeContextProvider
    ) {
    }

    /**
     * @Route(methods={"PUT"}, name="oloy.member.edit_badge_type", path="/{storeCode}/badge-type/{badgeType}", requirements={"badgeTypeId"="%routing.uuid%"})
     *
     * @Security("is_granted('EDIT_BADGE', badgeType)")
     */
    public function __invoke(Request $request, BadgeType $badgeType): View
    {
        $form = $this->formFactory->createNamed(
            'badgeType',
            UpdateBadgeTypeFormType::class,
            [],
            [
                'method' => 'PUT',
                'store' => $this->storeContextProvider->getStore(),
                'badgeTypeId' => $badgeType->getBadgeTypeId(),
            ]
        );

        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }
        /** @var UpdateMemberBadge $command */
        $command = $form->getData();
        $this->commandBus->dispatch($command);

        return $this->view();
    }
}
