<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Event;

use DateTime;
use OpenLoyalty\Core\Domain\Id\CustomerId;

class CouponWasRedeemed extends CustomerEvent
{
    public function __construct(
        private CustomerId $customerId,
        private string $couponCode,
        private DateTime $usageDate
    ) {
        parent::__construct($this->customerId);
    }

    public function getCouponCode(): string
    {
        return $this->couponCode;
    }

    public function getUsageDate(): DateTime
    {
        return $this->usageDate;
    }

    public static function deserialize(array $data): self
    {
        $usageDate = new DateTime();
        $usageDate->setTimestamp($data['usageDate']);

        return new self(
            new CustomerId($data['customerId']),
            $data['couponCode'],
            $usageDate
        );
    }
}
