<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\User\Domain\Event;

use DateTime;
use OpenLoyalty\Core\Domain\Id\CustomerId;

class CustomerWasAnonymized extends CustomerEvent implements CustomerHistoryItemInterface
{
    public function __construct(private CustomerId $customerId, private DateTime $date)
    {
        parent::__construct($this->customerId);
    }

    public function getDate(): DateTime
    {
        return $this->date;
    }

    public function getEventOccurrenceDate(): \DateTimeImmutable
    {
        return \DateTimeImmutable::createFromInterface($this->date);
    }

    public static function deserialize(array $data): self
    {
        $id = $data['customerId'];

        $date = new DateTime();
        if (isset($data['activatedAt'])) {
            $date->setTimestamp($data['activatedAt']);
        }

        return new self(new CustomerId($id), $date);
    }
}
