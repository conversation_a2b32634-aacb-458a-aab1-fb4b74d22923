<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Event\Listener;

use DateTimeInterface;
use OpenLoyalty\Account\Domain\Event\PointsWereTransferred;
use OpenLoyalty\Core\Application\PublicEvent\Shared\MemberAchievementCompletionCountWasDecreased;
use OpenLoyalty\Core\Application\PublicEvent\Shared\MemberAchievementProgressWasChanged;
use OpenLoyalty\Core\Application\PublicEvent\Shared\MemberCompletedAchievement;
use OpenLoyalty\Core\Domain\Message\DomainEventHandlerInterface;
use OpenLoyalty\Core\Domain\Model\Label;
use OpenLoyalty\Core\Domain\ValueObject\Achievement\RuleValuesDetails;
use OpenLoyalty\Level\Domain\LevelRepository;
use OpenLoyalty\User\Domain\AccountFacadeInterface;
use OpenLoyalty\User\Domain\CustomerHistory;
use OpenLoyalty\User\Domain\CustomerHistoryFactoryInterface;
use OpenLoyalty\User\Domain\CustomerHistoryRepositoryInterface;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;
use OpenLoyalty\User\Domain\Event\AccountHistoryItemInterface;
use OpenLoyalty\User\Domain\Event\AssignedCustomEventToCustomer;
use OpenLoyalty\User\Domain\Event\AssignedTransactionToCustomer;
use OpenLoyalty\User\Domain\Event\CustomAttributesWereDeleted;
use OpenLoyalty\User\Domain\Event\CustomAttributesWereUpdated;
use OpenLoyalty\User\Domain\Event\CustomAttributeWasAdded;
use OpenLoyalty\User\Domain\Event\CustomerHistoryItemInterface;
use OpenLoyalty\User\Domain\Event\CustomerWasMovedToLevel;
use OpenLoyalty\User\Domain\Event\RewardWasBought;
use OpenLoyalty\User\Domain\SystemEvent\MemberBadgeCompletedCountWasChanged;
use OpenLoyalty\User\Domain\SystemEvent\MemberCompletedBadge;
use Symfony\Component\Messenger\Handler\MessageSubscriberInterface;

final readonly class CustomerHistoryListener implements DomainEventHandlerInterface, MessageSubscriberInterface
{
    public function __construct(
        private CustomerRepositoryInterface $customerRepository,
        private AccountFacadeInterface $accountFacade,
        private CustomerHistoryRepositoryInterface $customerHistoryRepository,
        private CustomerHistoryFactoryInterface $customerHistoryFactory,
        private LevelRepository $levelRepository
    ) {
    }

    public function handleCustomerEvent(CustomerHistoryItemInterface $event): void
    {
        if (in_array(get_class($event),
            [
                RewardWasBought::class,
                CustomerWasMovedToLevel::class,
                AssignedTransactionToCustomer::class,
                AssignedCustomEventToCustomer::class,
                CustomAttributeWasAdded::class,
                CustomAttributesWereUpdated::class,
                CustomAttributesWereDeleted::class,
            ], true)
        ) {
            return;
        }

        $item = $this->buildCustomerEvent($event);
        if (null === $item) {
            return;
        }
        $this->customerHistoryRepository->save($item);
    }

    public function handleAccountEvent(AccountHistoryItemInterface $event): void
    {
        if (PointsWereTransferred::class === get_class($event)) {
            return;
        }

        $item = $this->buildAccountEvent($event);
        $this->customerHistoryRepository->save($item);
    }

    public function handleAchievementCompleted(MemberCompletedAchievement $event): void
    {
        $createdAt = $event->createdAt;

        // for BC layer
        if (null === $createdAt) {
            $createdAt = $event->completedAt;
        }

        $item = $this->customerHistoryFactory->create(
            $event->storeId,
            $event->customerId,
            'AchievementCompleted', // for backward compatibility
            $createdAt,
        );

        $item->addVariable(new Label('adminId', (string) $event->adminId));
        $item->addVariable(new Label('manuallyEdited', $event->manuallyEdited));
        $item->addVariable(new Label('achievementName', $event->achievementName));
        $item->addVariable(new Label('achievementId', (string) $event->achievementId));
        $item->addVariable(new Label('previousCompletionCount', $event->previousCompletionCount));
        $item->addVariable(new Label('currentCompletionCount', $event->currentCompletionCount));
        $item->addVariable(new Label('completedAt', $event->completedAt->format(DateTimeInterface::ATOM)));

        $this->customerHistoryRepository->save($item);
    }

    public function handleMemberAchievementCompletionCountWasDecreased(MemberAchievementCompletionCountWasDecreased $event): void
    {
        $item = $this->customerHistoryFactory->create(
            $event->storeId,
            $event->customerId,
            $this->getEventTypeBasedOnObjectClass($event),
            $event->decreasedAt,
        );

        $item->addVariable(new Label('adminId', (string) $event->adminId));
        $item->addVariable(new Label('previousCompletionCount', $event->previousCompletionCount));
        $item->addVariable(new Label('currentCompletionCount', $event->currentCompletionCount));
        $item->addVariable(new Label('achievementName', $event->achievementName));
        $item->addVariable(new Label('achievementId', (string) $event->achievementId));
        $this->customerHistoryRepository->save($item);
    }

    public function handleMemberMemberAchievementProgressWasChanged(MemberAchievementProgressWasChanged $event): void
    {
        if ($this->isRulesBeforeAndAfterChangesAreSame($event->rulesBeforeChanges, $event->rulesAfterChanges)) {
            return;
        }

        $item = $this->customerHistoryFactory->create(
            $event->storeId,
            $event->customerId,
            $this->getEventTypeBasedOnObjectClass($event),
            $event->changedAt,
        );

        $item->addVariable(new Label('adminId', (string) $event->adminId));
        $item->addVariable(new Label('achievementName', $event->achievementName));
        $item->addVariable(new Label('achievementId', (string) $event->achievementId));
        $item->addVariable(new Label('rulesBeforeChanges', $event->rulesBeforeChanges));
        $item->addVariable(new Label('rulesAfterChanges', $event->rulesAfterChanges));
        $this->customerHistoryRepository->save($item);
    }

    public function handleAssignedCustomEventToCustomer(AssignedCustomEventToCustomer $event): void
    {
        $item = $this->buildCustomerEvent($event);
        if (null === $item) {
            return;
        }
        $item->addVariables([
            new Label('customEventId', (string) $event->getCustomEventId()),
            new Label('type', $event->getType()),
        ]);
        $this->customerHistoryRepository->save($item);
    }

    public function handleAssignedTransactionToCustomer(AssignedTransactionToCustomer $event): void
    {
        $item = $this->buildCustomerEvent($event);
        if (null === $item) {
            return;
        }
        $item->addVariables([
            new Label('transactionId', (string) $event->getTransactionId()),
            new Label('documentNumber', $event->getDocumentNumber()),
            new Label('documentType', $event->getDocumentType()),
            new Label('grossValue', $event->getGrossValue()),
        ]);
        $this->customerHistoryRepository->save($item);
    }

    public function handleCustomerWasMovedToLevel(CustomerWasMovedToLevel $event): void
    {
        $item = $this->buildCustomerEvent($event);
        if (null === $item) {
            return;
        }
        $item->addVariables([
            new Label('manually', $event->isManually()),
            new Label('removedManually', $event->isRemoveLevelManually()),
        ]);

        if (null !== $event->getLevelId()) {
            $newLevel = $this->levelRepository->byId($event->getLevelId());
            $tierSet = $newLevel?->getTierSet();

            $item->addVariables([
                new Label('newLevelId', (string) $newLevel->getLevelId()),
                new Label('newLevelName', $newLevel->getName()),
                new Label('tierSetName', $tierSet?->getName()),
                new Label('tierSetId', (string) $tierSet?->getTierSetId()),
            ]);
        }

        if (null !== $event->getOldLevelId()) {
            $oldLevel = $this->levelRepository->byId($event->getOldLevelId());
            $item->addVariables([
                new Label('oldLevelId', (string) $oldLevel->getLevelId()),
                new Label('oldLevelName', (string) $oldLevel->getName()),
            ]);
        }

        $this->customerHistoryRepository->save($item);
    }

    public function handleRewardWasBought(RewardWasBought $event): void
    {
        $item = $this->buildCustomerEvent($event);
        if (null === $item) {
            return;
        }
        $item->addVariables([
            new Label('rewardId', (string) $event->getRewardId()),
            new Label('issuedRewardId', (string) $event->getIssuedRewardId()),
            new Label('rewardName', $event->getRewardName()),
        ]);
        $this->customerHistoryRepository->save($item);
    }

    public function handlePointsWereTransferred(PointsWereTransferred $event): void
    {
        $item = $this->buildAccountEvent($event);
        $receiverWallet = $this->accountFacade->getWalletById($event->getReceiverId(), false);
        $receiverCustomer = $this->customerRepository->getById($receiverWallet->getOwner());

        if (null !== $receiverCustomer) {
            $item->addVariables([
                new Label('receiverFirstName', $receiverCustomer->getFirstName()),
                new Label('receiverLastName', $receiverCustomer->getLastName()),
            ]);
        }
        $this->customerHistoryRepository->save($item);
    }

    public function handleMemberCustomAttributeWasAdded(CustomAttributeWasAdded $event): void
    {
        $item = $this->buildCustomerEvent($event);
        $item->addVariables([
            new Label('customAttributeKey', $event->getCustomAttribute()->getKey()),
            new Label('customAttributeValue', $event->getCustomAttribute()->getValue()),
        ]);

        $this->customerHistoryRepository->save($item);
    }

    public function handleMemberCustomAttributesWereUpdated(CustomAttributesWereUpdated $event): void
    {
        foreach ($event->getCustomAttributes() as $customAttribute) {
            $item = $this->buildCustomerEvent($event);
            $item->addVariables([
                new Label('customAttributeKey', $customAttribute->getKey()),
                new Label('customAttributeValue', $customAttribute->getValue()),
            ]);

            $this->customerHistoryRepository->save($item);
        }
    }

    public function handleMemberCustomAttributeWereDeleted(CustomAttributesWereDeleted $event): void
    {
        foreach ($event->getCustomAttributes() as $customAttribute) {
            $item = $this->buildCustomerEvent($event);
            $item->addVariables([
                new Label('customAttributeKey', $customAttribute->getKey()),
            ]);

            $this->customerHistoryRepository->save($item);
        }
    }

    public function handleMemberCompletedBadge(MemberCompletedBadge $event): void
    {
        $createdAt = $event->createdAt;

        $item = $this->customerHistoryFactory->create(
            $event->storeId,
            $event->customerId,
            'BadgeCompleted',
            $createdAt,
        );

        $item->addVariable(new Label('badgeTypeId', (string) $event->badgeTypeId));
        $item->addVariable(new Label('previousCompletedCount', $event->previousCompletedCount));
        $item->addVariable(new Label('currentCompletedCount', $event->currentCompletedCount));
        $item->addVariable(new Label('badgeName', $event->badgeName));

        $this->customerHistoryRepository->save($item);
    }

    public function handleMemberBadgeCompletedCountWasChanged(MemberBadgeCompletedCountWasChanged $event): void
    {
        $createdAt = $event->createdAt;

        $item = $this->customerHistoryFactory->create(
            $event->storeId,
            $event->customerId,
            'MemberBadgeCompletedCountWasChanged',
            $createdAt,
        );

        $item->addVariable(new Label('adminId', (string) $event->adminId));
        $item->addVariable(new Label('manuallyEdited', $event->manuallyEdited));
        $item->addVariable(new Label('badgeTypeId', (string) $event->badgeTypeId));
        $item->addVariable(new Label('previousCompletedCount', $event->previousCompletedCount));
        $item->addVariable(new Label('currentCompletedCount', $event->currentCompletedCount));
        $item->addVariable(new Label('badgeName', $event->badgeName));

        $this->customerHistoryRepository->save($item);
    }

    public static function getHandledMessages(): iterable
    {
        yield PointsWereTransferred::class => [
            'method' => 'handlePointsWereTransferred',
            'bus' => 'domain.event.bus',
        ];

        yield RewardWasBought::class => [
            'method' => 'handleRewardWasBought',
            'bus' => 'domain.event.bus',
        ];

        yield CustomerWasMovedToLevel::class => [
            'method' => 'handleCustomerWasMovedToLevel',
            'bus' => 'domain.event.bus',
        ];

        yield AssignedTransactionToCustomer::class => [
            'method' => 'handleAssignedTransactionToCustomer',
            'bus' => 'domain.event.bus',
        ];

        yield AssignedCustomEventToCustomer::class => [
            'method' => 'handleAssignedCustomEventToCustomer',
            'bus' => 'domain.event.bus',
        ];

        yield CustomerHistoryItemInterface::class => [
            'method' => 'handleCustomerEvent',
            'bus' => 'domain.event.bus',
        ];

        yield AccountHistoryItemInterface::class => [
            'method' => 'handleAccountEvent',
            'bus' => 'domain.event.bus',
        ];

        yield CustomAttributeWasAdded::class => [
            'method' => 'handleMemberCustomAttributeWasAdded',
            'bus' => 'domain.event.bus',
        ];

        yield CustomAttributesWereUpdated::class => [
            'method' => 'handleMemberCustomAttributesWereUpdated',
            'bus' => 'domain.event.bus',
        ];

        yield CustomAttributesWereDeleted::class => [
            'method' => 'handleMemberCustomAttributeWereDeleted',
            'bus' => 'domain.event.bus',
        ];

        yield MemberCompletedAchievement::class => [
            'method' => 'handleAchievementCompleted',
            'bus' => 'public.event.bus',
        ];

        yield MemberAchievementCompletionCountWasDecreased::class => [
            'method' => 'handleMemberAchievementCompletionCountWasDecreased',
            'bus' => 'public.event.bus',
        ];

        yield MemberAchievementProgressWasChanged::class => [
            'method' => 'handleMemberMemberAchievementProgressWasChanged',
            'bus' => 'public.event.bus',
        ];

        yield MemberCompletedBadge::class => [
            'method' => 'handleMemberCompletedBadge',
            'bus' => 'event.bus',
        ];

        yield MemberBadgeCompletedCountWasChanged::class => [
            'method' => 'handleMemberBadgeCompletedCountWasChanged',
            'bus' => 'event.bus',
        ];
    }

    private function buildCustomerEvent(CustomerHistoryItemInterface $event): ?CustomerHistory
    {
        $customer = $this->customerRepository->getById($event->getCustomerId());

        if (null === $customer) {
            return null;
        }

        return $this->customerHistoryFactory->create(
            $customer->getStoreId(),
            $customer->getCustomerId(),
            $this->getEventTypeBasedOnObjectClass($event),
            $event->getEventOccurrenceDate(),
        );
    }

    private function buildAccountEvent(AccountHistoryItemInterface $event): CustomerHistory
    {
        $wallet = $this->accountFacade->getWalletById($event->getAccountId(), true);
        $item = $this->customerHistoryFactory->create(
            $wallet->getStoreId(),
            $wallet->getOwner(),
            $this->getEventTypeBasedOnObjectClass($event),
            $event->getEventOccurrenceDate()
        );
        $item->setAccountId($event->getAccountId());
        $item->addVariables([
            new Label('transferId', (string) $event->getTransferId()),
            new Label('walletId', (string) $wallet->getWalletId()),
            new Label('walletTypeCode', $wallet->getWalletType()->getCode()),
            new Label('walletTypeName', $wallet->getWalletType()->getName()),
            new Label('walletUnitSingularName', $wallet->getWalletType()->getUnitSingularName()),
            new Label('walletUnitPluralName', $wallet->getWalletType()->getUnitPluralName()),
            new Label('points', $event->getValue()),
            new Label('comment', $event->getComment()),
            new Label('activePoints', $wallet->getAccount()->getActiveUnits()),
        ]);

        return $item;
    }

    protected function getEventTypeBasedOnObjectClass(object $object): string
    {
        return (new \ReflectionClass($object))->getShortName();
    }

    /**
     * @param RuleValuesDetails[]|null $rulesBeforeChanges
     * @param RuleValuesDetails[]|null $rulesAfterChanges
     */
    private function isRulesBeforeAndAfterChangesAreSame(?array $rulesBeforeChanges, ?array $rulesAfterChanges): bool
    {
        if (null === $rulesBeforeChanges && null === $rulesAfterChanges) {
            return true;
        }

        if (null === $rulesBeforeChanges || null === $rulesAfterChanges) {
            return false;
        }

        foreach ($rulesBeforeChanges as $ruleBeforeChange) {
            foreach ($rulesAfterChanges as $ruleAfterChange) {
                if ($ruleBeforeChange->achievementRuleId === $ruleAfterChange->achievementRuleId) {
                   if (!$ruleBeforeChange->isSame($ruleAfterChange)) {
                       return false;
                   }
                }
            }
        }

        return true;
    }
}
