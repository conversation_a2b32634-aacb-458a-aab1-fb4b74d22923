<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Event;

use Carbon\Carbon;
use DateTime;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\TransactionId;

class AssignedTransactionToCustomer extends CustomerEvent implements CustomerHistoryItemInterface
{
    public function __construct(
        private CustomerId $customerId,
        private TransactionId $transactionId,
        private float $grossValue,
        private float $grossValueWithoutDeliveryCosts,
        private string $documentNumber,
        private string $documentType,
        private float $amountExcludedForLevel,
        private ?DateTime $purchaseDate = null,
        private ?DateTime $assignedAt = null
    ) {
        parent::__construct($this->customerId);
    }

    public function getTransactionId(): TransactionId
    {
        return $this->transactionId;
    }

    public function getGrossValue(): float
    {
        return $this->grossValue;
    }

    public function getGrossValueWithoutDeliveryCosts(): float
    {
        return $this->grossValueWithoutDeliveryCosts;
    }

    public function getDocumentNumber(): string
    {
        return $this->documentNumber;
    }

    public function getDocumentType(): string
    {
        return $this->documentType;
    }

    public function getAmountExcludedForLevel(): float
    {
        return $this->amountExcludedForLevel;
    }

    public function getAssignedAt(): ?DateTime
    {
        return $this->assignedAt;
    }

    public function getPurchaseDate(): ?DateTime
    {
        return $this->purchaseDate;
    }

    public function getEventOccurrenceDate(): \DateTimeImmutable
    {
        return \DateTimeImmutable::createFromMutable($this->getAssignedAt());
    }

    /* @phpstan-ignore-next-line */
    public static function deserialize(array $data): self
    {
        $assignedAt = null;
        if (isset($data['assignedAt'])) {
            $assignedAt = new Carbon();
            $assignedAt->setTimestamp($data['assignedAt']);
        }

        $purchaseDate = null;
        if (isset($data['purchaseDate'])) {
            $purchaseDate = new Carbon();
            $purchaseDate->setTimestamp($data['purchaseDate']);
        }

        return new self(
            new CustomerId($data['customerId']),
            new TransactionId($data['transactionId']),
            $data['grossValue'],
            $data['grossValueWithoutDeliveryCosts'],
            $data['documentNumber'],
            $data['documentType'],
            $data['amountExcludedForLevel'],
            $purchaseDate,
            $assignedAt
        );
    }
}
