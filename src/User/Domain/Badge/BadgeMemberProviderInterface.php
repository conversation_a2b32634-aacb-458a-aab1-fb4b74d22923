<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Badge;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\User\Domain\BadgeMember;
use OpenLoyalty\User\Domain\BadgeType;

interface BadgeMemberProviderInterface
{
    public function getBadgeMember(
        BadgeType $badgeType,
        CustomerId $memberId,
        StoreId $storeId
    ): BadgeMember;
}
