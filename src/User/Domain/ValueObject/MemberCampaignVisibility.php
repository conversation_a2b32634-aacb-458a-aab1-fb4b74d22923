<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\ValueObject;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;

final readonly class MemberCampaignVisibility
{
    /**
     * @param string[] $campaignSegmentIds
     * @param string[] $campaignTierIds
     * @param string[] $campaignAllIds
     */
    public function __construct(
        private StoreId $storeId,
        private CustomerId $customerId,
        private ?string $email,
        private ?string $phone,
        private ?string $loyaltyCardNumber,
        private array $campaignSegmentIds,
        private array $campaignTierIds,
        private array $campaignAllIds
    ) {
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getCustomerId(): CustomerId
    {
        return $this->customerId;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function getLoyaltyCardNumber(): ?string
    {
        return $this->loyaltyCardNumber;
    }

    /**
     * @return string[]
     */
    public function getCampaignSegmentIds(): array
    {
        return $this->campaignSegmentIds;
    }

    /**
     * @return string[]
     */
    public function getCampaignTierIds(): array
    {
        return $this->campaignTierIds;
    }

    /**
     * @return string[]
     */
    public function getCampaignAllIds(): array
    {
        return $this->campaignAllIds;
    }

    /**
     * @return string[]
     */
    public function getVisibileCampaignIds(): array
    {
        return array_values(array_unique(array_merge($this->campaignSegmentIds, $this->campaignTierIds, $this->campaignAllIds)));
    }
}
