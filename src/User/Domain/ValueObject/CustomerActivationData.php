<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\ValueObject;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;

final readonly class CustomerActivationData
{
    public function __construct(
        public CustomerId $customerId,
        public DateTimeImmutable $eventDate,
        public array $activeMemberSettings
    ) {
    }
}
