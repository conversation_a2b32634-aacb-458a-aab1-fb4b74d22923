<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\User\Domain\Service;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Transaction\Domain\ValueObject\CustomerBasicData;
use OpenLoyalty\User\Domain\Model\Transaction;

interface TransactionMatcherInterface
{
    public function match(
        StoreId $storeId,
        Transaction $transaction,
        CustomerBasicData $customerData,
        DateTimeImmutable $assignDate,
        bool $isSync = false
    ): void;
}
