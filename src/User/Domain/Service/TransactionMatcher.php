<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Service;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Transaction\Domain\ValueObject\CustomerBasicData;
use OpenLoyalty\User\Domain\Model\Transaction;
use OpenLoyalty\User\Domain\Provider\CustomerIdProvider;

final readonly class TransactionMatcher implements TransactionMatcherInterface
{
    public function __construct(
        private TransactionAssignerInterface $transactionAssigner,
        private CustomerIdProvider $customerIdProvider
    ) {
    }

    public function match(
        StoreId $storeId,
        Transaction $transaction,
        CustomerBasicData $customerData,
        DateTimeImmutable $assignDate,
        bool $isSync = false
    ): void {
        $customerId = $this->customerIdProvider->getId(
                [
                    'phone' => $customerData->getPhone(),
                    'email' => $customerData->getEmail(),
                    'loyaltyCardNumber' => $customerData->getLoyaltyCardNumber(),
                    'customerId' => $customerData->getCustomerId(),
                ],
                $storeId
            );

        if (null === $customerId) {
            return;
        }

        $this->transactionAssigner->assignTransaction(
            $customerId,
            $transaction,
            $assignDate,
            isSync: $isSync
        );
    }
}
