<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Service;

use OpenLoyalty\Core\Domain\Id\ChannelId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;
use OpenLoyalty\User\Domain\SystemEvent\CustomerUpdatedSystemEvent;

class ChannelAssigner implements ChannelAssignerInterface
{
    public function __construct(
        private CustomerRepositoryInterface $repository,
        private EventBusInterface $eventBus
    ) {
    }

    public function assignChannel(CustomerId $customerId, ChannelId $channelId): void
    {
        $customer = $this->repository->load($customerId);
        $customer->assignChannelToCustomer($channelId);
        $this->repository->save($customer);

        $this->eventBus->dispatch(
            new CustomerUpdatedSystemEvent($customer->getStoreId(), $customerId)
        );
    }
}
