<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Service;

use DateTimeInterface;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\IssuedRewardId;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Core\Domain\ValueObject\ActionCause;
use OpenLoyalty\User\Domain\Model\Coupon;

interface RewardBuyerInterface
{
    public function buy(
        CustomerId $customerId,
        IssuedRewardId $issuedRewardId,
        RewardId $rewardId,
        DateTimeInterface $redemptionDate,
        string $rewardType,
        ?string $rewardName,
        float $costInPoints,
        string $status,
        ?Coupon $coupon = null,
        ?string $walletCode = null,
        ?ActionCause $actionCause = null
    ): void;
}
