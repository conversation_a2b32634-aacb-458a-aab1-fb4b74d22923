<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;

interface CustomEventFacadeInterface
{
    public function getMinCustomEventDateInPeriod(
        StoreId $storeId,
        CustomerId $customerId,
        array $eventTypes,
        DateTimeImmutable $startPeriodDate,
        DateTimeImmutable $endPeriodDate
    ): ?DateTimeImmutable;
}
