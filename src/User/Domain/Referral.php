<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain;

use Assert\Assertion;
use Assert\AssertionFailedException;
use Carbon\CarbonImmutable;
use DateTime;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\ReferralId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;

class Referral implements TimestampableInterface, BlameableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    private ReferralId $referralId;
    private CustomerId $referrerId;
    private CustomerId $refereeId;
    private StoreId $storeId;
    private string $referrerToken;
    private string $refereeToken;
    private string $referrerName;
    private string $refereeName;

    /**
     * @throws AssertionFailedException
     */
    public function __construct(
        ReferralId $referralId,
        CustomerId $referrerId,
        CustomerId $refereeId,
        StoreId $storeId,
        string $referrerToken,
        string $refereeToken,
        string $referrerName,
        string $refereeName
    ) {
        Assertion::uuid((string) $referralId);
        Assertion::uuid((string) $referrerId);
        Assertion::uuid((string) $refereeId);
        Assertion::uuid((string) $storeId);
        Assertion::notEmpty($referrerToken);
        Assertion::notEmpty($refereeToken);
        Assertion::notEmpty($referrerName);
        Assertion::notEmpty($refereeName);

        $this->referralId = $referralId;
        $this->referrerId = $referrerId;
        $this->refereeId = $refereeId;
        $this->storeId = $storeId;
        $this->referrerToken = $referrerToken;
        $this->refereeToken = $refereeToken;
        $this->referrerName = $referrerName;
        $this->refereeName = $refereeName;

        $this->createdAt = new CarbonImmutable();
    }

    public function getReferralId(): ReferralId
    {
        return $this->referralId;
    }

    public function getReferrerId(): CustomerId
    {
        return $this->referrerId;
    }

    public function getRefereeId(): CustomerId
    {
        return $this->refereeId;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getReferrerToken(): string
    {
        return $this->referrerToken;
    }

    public function getRefereeToken(): string
    {
        return $this->refereeToken;
    }

    public function getReferrerName(): string
    {
        return $this->referrerName;
    }

    public function getRefereeName(): string
    {
        return $this->refereeName;
    }

    public function getCreatedAt(): DateTime
    {
        return DateTime::createFromInterface($this->createdAt);
    }
}
