<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Activity\Strategy;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\User\Domain\SystemEvent\CustomerSystemEvent;
use OpenLoyalty\User\Domain\SystemEvent\CustomEventAssignedToCustomerSystemEvent;
use OpenLoyalty\User\Domain\SystemEvent\TransactionAssignedToCustomerSystemEvent;
use OpenLoyalty\User\Domain\ValueObject\MemberActivitySettings;

final class MemberCustomEventActivityStrategy implements MemberActivityStrategyInterface
{
    public function getMemberActivityDate(
        StoreId $storeId,
        CustomerId $customerId,
        array $settings,
        TransactionAssignedToCustomerSystemEvent|CustomEventAssignedToCustomerSystemEvent $event,
        DateTimeImmutable $today,
        DateTimeImmutable $endPeriodDate,
    ): ?DateTimeImmutable {
        $periodStartDate = $today->modify(sprintf('-%d days', $settings[MemberActivitySettings::CUSTOM_EVENTS_X_DAYS]['days']));
        if ($event->getCustomEventDate() >= $periodStartDate && $event->getCustomEventDate() <= $endPeriodDate) {
            if (
                !$settings[MemberActivitySettings::CUSTOM_EVENTS_X_DAYS]['allEvents'] &&
                !in_array(
                    $event->getCustomEventType(),
                    $settings[MemberActivitySettings::CUSTOM_EVENTS_X_DAYS]['eventTypes'],
                    true
                )
            ) {
                return null;
            }

            return $event->getCustomEventDate();
        }

        return null;
    }

    public function supports(CustomerSystemEvent $event, array $settings): bool
    {
        return isset($settings[MemberActivitySettings::CUSTOM_EVENTS_X_DAYS])
            && !isset($settings[MemberActivitySettings::TRANSACTION_X_DAYS])
            && $event instanceof CustomEventAssignedToCustomerSystemEvent;
    }
}
