<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Activity\Strategy;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Domain\SystemEvent\CustomEventAssignedToCustomerSystemEvent;
use OpenLoyalty\User\Domain\SystemEvent\TransactionAssignedToCustomerSystemEvent;
use OpenLoyalty\User\Domain\ValueObject\MemberActivitySettings;

final class MemberActivityStrategy
{
    /**
     * @var array<MemberActivityStrategyInterface>
     */
    private array $strategies = [];

    public function __construct(iterable $strategies)
    {
        foreach ($strategies as $strategy) {
            if ($strategy instanceof MemberActivityStrategyInterface) {
                $this->strategies[] = $strategy;
            }
        }
    }

    public function getMemberActivityDate(
        StoreId $storeId,
        Customer $customer,
        MemberActivitySettings $memberActivitySettings,
        TransactionAssignedToCustomerSystemEvent|CustomEventAssignedToCustomerSystemEvent $event,
        DateTimeImmutable $today,
        DateTimeImmutable $periodEndDate,
    ): ?DateTimeImmutable {
        if ($customer->isActiveMember()) {
            return null;
        }

        if (!$memberActivitySettings->settings) {
            return null;
        }

        foreach ($this->strategies as $strategy) {
            if ($strategy->supports($event, $memberActivitySettings->settings)) {
                return $strategy->getMemberActivityDate(
                    $storeId,
                    $customer->getCustomerId(),
                    $memberActivitySettings->settings,
                    $event,
                    $today,
                    $periodEndDate
                );
            }
        }

        return null;
    }
}
