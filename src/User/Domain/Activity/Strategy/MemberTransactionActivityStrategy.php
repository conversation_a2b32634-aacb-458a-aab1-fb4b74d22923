<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Activity\Strategy;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\User\Domain\SystemEvent\CustomerSystemEvent;
use OpenLoyalty\User\Domain\SystemEvent\CustomEventAssignedToCustomerSystemEvent;
use OpenLoyalty\User\Domain\SystemEvent\TransactionAssignedToCustomerSystemEvent;
use OpenLoyalty\User\Domain\ValueObject\MemberActivitySettings;

final class MemberTransactionActivityStrategy implements MemberActivityStrategyInterface
{
    public function getMemberActivityDate(
        StoreId $storeId,
        CustomerId $customerId,
        array $settings,
        TransactionAssignedToCustomerSystemEvent|CustomEventAssignedToCustomerSystemEvent $event,
        DateTimeImmutable $today,
        DateTimeImmutable $endPeriodDate,
    ): ?DateTimeImmutable {
        $periodStartDate = $today->modify(sprintf('-%d days', $settings[MemberActivitySettings::TRANSACTION_X_DAYS]));
        if ($event->getPurchasedAt() >= $periodStartDate && $event->getPurchasedAt() <= $endPeriodDate) {
            return DateTimeImmutable::createFromInterface($event->getPurchasedAt());
        }

        return null;
    }

    public function supports(CustomerSystemEvent $event, array $settings): bool
    {
        return isset($settings[MemberActivitySettings::TRANSACTION_X_DAYS])
            && !isset($settings[MemberActivitySettings::CUSTOM_EVENTS_X_DAYS])
            && $event instanceof TransactionAssignedToCustomerSystemEvent;
    }
}
