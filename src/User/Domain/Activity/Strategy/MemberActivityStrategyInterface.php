<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Activity\Strategy;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\User\Domain\SystemEvent\CustomerSystemEvent;
use OpenLoyalty\User\Domain\SystemEvent\CustomEventAssignedToCustomerSystemEvent;
use OpenLoyalty\User\Domain\SystemEvent\TransactionAssignedToCustomerSystemEvent;

interface MemberActivityStrategyInterface
{
    public function getMemberActivityDate(
        StoreId $storeId,
        CustomerId $customerId,
        array $settings,
        TransactionAssignedToCustomerSystemEvent|CustomEventAssignedToCustomerSystemEvent $event,
        DateTimeImmutable $today,
        DateTimeImmutable $endPeriodDate,
    ): ?DateTimeImmutable;

    public function supports(CustomerSystemEvent $event, array $settings): bool;
}
