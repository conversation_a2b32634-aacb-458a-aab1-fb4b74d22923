<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Provider;

use OpenLoyalty\Core\Domain\Exception\StoreNotFoundException;
use OpenLoyalty\Core\Domain\GeneralSettingsManagerInterface;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;

readonly class FallbackFieldsCustomerIdProvider implements CustomerIdProvider
{
    public function __construct(
        private GeneralSettingsManagerInterface $settingsManager,
        private CustomerRepositoryInterface $customerRepository,
        private StoreRepository $storeRepository
    ) {
    }

    /**
     * @param array<string, mixed> $customerData
     *
     * @throws StoreNotFoundException
     */
    public function getId(array $customerData, StoreId $storeId): ?CustomerId
    {
        if (array_key_exists('customerId', $customerData) && $customerData['customerId'] instanceof CustomerId) {
            /** @var Customer|null $customer */
            $customer = $this->customerRepository->findOneBy([
                'id' => $customerData['customerId'],
                'storeId' => $storeId,
                'active' => true,
            ]);

            if (null !== $customer) {
                return $customer->getCustomerId();
            }
        }

        $store = $this->storeRepository->byId($storeId, true);
        if (!$store instanceof Store) {
            throw new StoreNotFoundException();
        }

        $customersIdentificationConfigurations = $this->settingsManager->getCustomersIdentificationConfigurations($store, true);

        if (0 === count($customersIdentificationConfigurations)) {
            return null;
        }

        foreach ($customersIdentificationConfigurations as $field) {
            if (!isset($customerData[$field['field']]) || '' === $customerData[$field['field']]) { // @phpstan-ignore-line
                continue;
            }

            $queryField = $field['field']; // @phpstan-ignore-line
            $query = [
                $queryField => 'email' === $queryField
                    /* @phpstan-ignore-next-line */
                    ? strtolower($customerData[$field['field']])
                    : $customerData[$field['field']], // @phpstan-ignore-line
                'storeId' => $store->getStoreId(),
            ];

            /** @var Customer $customer */
            $customer = $this->customerRepository->findOneBy($query);

            if (null !== $customer && $customer->isActive()) {
                return $customer->getCustomerId();
            }
        }

        return null;
    }
}
