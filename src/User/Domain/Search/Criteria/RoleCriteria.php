<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Search\Criteria;

use OpenLoyalty\Core\Domain\Search\Criteria\AbstractCriteria;

final class RoleCriteria extends AbstractCriteria
{
    /**
     * @var string|null
     */
    protected $value;

    public function __construct(string $name, string $operator, ?string $value)
    {
        parent::__construct($name, $operator);

        $this->value = $value;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }
}
