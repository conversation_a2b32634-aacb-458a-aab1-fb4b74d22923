<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\SystemEvent;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\RewardId;

class CouponWasRedeemedSystemEvent extends CustomerSystemEvent
{
    private RewardId $rewardId;
    private string $couponCode;
    private DateTimeImmutable $usageAt;

    public function __construct(
        CustomerId $customerId,
        RewardId $rewardId,
        string $couponCode,
        DateTimeImmutable $usageAt
    ) {
        parent::__construct($customerId);

        $this->rewardId = $rewardId;
        $this->couponCode = $couponCode;
        $this->usageAt = $usageAt;
    }

    public function getRewardId(): RewardId
    {
        return $this->rewardId;
    }

    public function getCouponCode(): string
    {
        return $this->couponCode;
    }

    public function getUsageAt(): DateTimeImmutable
    {
        return $this->usageAt;
    }
}
