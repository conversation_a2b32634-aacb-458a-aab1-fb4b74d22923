<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\SystemEvent;

use Carbon\Carbon;
use DateTime;
use DateTimeInterface;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventInterface;
use OpenLoyalty\Core\Domain\Message\InternalEventInterface;
use OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepository;

class CustomerWasActivatedSystemEvent implements EventInterface, InternalEventInterface
{
    public const NAME = InternalEventSchemaRepository::MEMBER_WAS_ACTIVATED;

    private CustomerId $customerId;
    private StoreId $storeId;
    private ?DateTime $eventDate;

    public function __construct(
        CustomerId $customerId,
        StoreId $storeId,
        ?DateTime $eventDate = null
    ) {
        $this->customerId = $customerId;
        $this->storeId = $storeId;
        $this->eventDate = $eventDate ?: new Carbon();
    }

    public function getName(): string
    {
        return self::NAME;
    }

    public function getCustomerId(): CustomerId
    {
        return $this->customerId;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getEventDate(): DateTimeInterface
    {
        return $this->eventDate;
    }

    public function getEventBody(): array
    {
        return [];
    }

    public function isTriggerCampaign(): bool
    {
        return true;
    }
}
