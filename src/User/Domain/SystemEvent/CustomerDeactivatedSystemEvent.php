<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\SystemEvent;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Webhook\Event\WebhookEventInterface;

class CustomerDeactivatedSystemEvent extends CustomerSystemEvent implements WebhookEventInterface
{
    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }
}
