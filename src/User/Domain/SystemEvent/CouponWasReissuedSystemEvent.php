<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\SystemEvent;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\RewardId;

class CouponWasReissuedSystemEvent extends CustomerSystemEvent
{
    private RewardId $rewardId;
    private string $couponCode;

    public function __construct(
        CustomerId $customerId,
        RewardId $rewardId,
        string $couponCode
    ) {
        parent::__construct($customerId);

        $this->rewardId = $rewardId;
        $this->couponCode = $couponCode;
    }

    public function getRewardId(): RewardId
    {
        return $this->rewardId;
    }

    public function getCouponCode(): string
    {
        return $this->couponCode;
    }
}
