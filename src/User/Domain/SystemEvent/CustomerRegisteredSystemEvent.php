<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\SystemEvent;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Webhook\Event\WebhookEventInterface;
use OpenLoyalty\User\Domain\ValueObject\CustomerData;

class CustomerRegisteredSystemEvent extends CustomerSystemEvent implements WebhookEventInterface
{
    public function __construct(
        StoreId $storeId,
        CustomerId $customerId,
        private CustomerData $customerData
    ) {
        parent::__construct($customerId, $storeId);
    }

    public function getCustomerData(): CustomerData
    {
        return $this->customerData;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }
}
