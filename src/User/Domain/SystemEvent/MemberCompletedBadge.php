<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\SystemEvent;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\BadgeTypeId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventInterface;

final readonly class MemberCompletedBadge implements EventInterface
{
    public function __construct(
        public CustomerId $customerId,
        public BadgeTypeId $badgeTypeId,
        public StoreId $storeId,
        public string $badgeName,
        public int $previousCompletedCount,
        public int $currentCompletedCount,
        public DateTimeImmutable $createdAt,
    ) {
    }
}
