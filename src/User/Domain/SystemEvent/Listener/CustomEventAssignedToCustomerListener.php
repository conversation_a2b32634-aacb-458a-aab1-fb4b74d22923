<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\SystemEvent\Listener;

use OpenLoyalty\Core\Domain\Message\EventHandlerInterface;
use OpenLoyalty\User\Domain\CustomerCustomEventType;
use OpenLoyalty\User\Domain\CustomerCustomEventTypeRepositoryInterface;
use OpenLoyalty\User\Domain\SystemEvent\CustomEventAssignedToCustomerSystemEvent;

final readonly class CustomEventAssignedToCustomerListener implements EventHandlerInterface
{
    public function __construct(
        private CustomerCustomEventTypeRepositoryInterface $customerCustomEventTypeRepository,
    ) {
    }

    public function __invoke(CustomEventAssignedToCustomerSystemEvent $event): void
    {
        /**
         * @var CustomerCustomEventType $customerCustomEventType
         */
        $customerCustomEventType = $this->customerCustomEventTypeRepository->findOneBy([
            'customerId' => $event->getCustomerId(),
            'customEventType' => $event->getCustomEventType(),
        ]);

        if (null === $customerCustomEventType) {
            $customerCustomEventType = new CustomerCustomEventType(
                $event->getCustomerId(),
                $event->getCustomEventType(),
                $event->getCustomEventDate()
            );
        } elseif ($customerCustomEventType->getLastCustomEventDate() < $event->getCustomEventDate()) {
            $customerCustomEventType->changeLastCustomEventDate($event->getCustomEventDate());
        }

        $this->customerCustomEventTypeRepository->save($customerCustomEventType);
    }
}
