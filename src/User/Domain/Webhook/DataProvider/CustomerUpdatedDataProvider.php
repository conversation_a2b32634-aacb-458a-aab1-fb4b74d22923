<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Webhook\DataProvider;

use OpenLoyalty\Core\Domain\Exception\StoreNotFoundException;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\Webhook\DataProvider\WebhookDataProviderInterface;
use OpenLoyalty\Core\Domain\Webhook\Event\WebhookEventInterface;
use OpenLoyalty\Core\Domain\Webhook\Factory\MemberResponseFactoryInterface;
use OpenLoyalty\Core\Domain\Webhook\Response\WebhookData;
use OpenLoyalty\User\Domain\SystemEvent\CustomerUpdatedSystemEvent;

final readonly class CustomerUpdatedDataProvider implements WebhookDataProviderInterface
{
    public function __construct(
        private StoreRepository $storeRepository,
        private MemberResponseFactoryInterface $memberResponseFactory
    ) {
    }

    public function getEventName(): string
    {
        return 'CustomerUpdated';
    }

    /**
     * @throws StoreNotFoundException
     */
    public function getData(
        WebhookEventInterface|CustomerUpdatedSystemEvent $event
    ): ?WebhookData {
        if (!$event->isPrimaryData()) {
            return null;
        }

        $store = $this->storeRepository->byId($event->getStoreId());
        if (null === $store) {
            throw new StoreNotFoundException($event->getStoreId());
        }

            $data = [
                'customer' => $this->memberResponseFactory->create($event->getCustomerId()),
            ];

        return new WebhookData(
            $this->getEventName(),
            $store->getCode(),
            $data,
        );
    }

    public function supports(string $eventClassName): bool
    {
        return CustomerUpdatedSystemEvent::class === $eventClassName;
    }
}
