<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Webhook\DataProvider;

use OpenLoyalty\Core\Domain\Exception\NotFoundException;
use OpenLoyalty\Core\Domain\Exception\StoreNotFoundException;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\Webhook\DataProvider\WebhookDataProviderInterface;
use OpenLoyalty\Core\Domain\Webhook\Event\WebhookEventInterface;
use OpenLoyalty\Core\Domain\Webhook\Factory\MemberResponseFactoryInterface;
use OpenLoyalty\Core\Domain\Webhook\Response\WebhookData;
use OpenLoyalty\User\Domain\CodeRepositoryInterface;
use OpenLoyalty\User\Domain\Service\CodeManagerInterface;
use OpenLoyalty\User\Domain\SystemEvent\CustomerEmailWasChangedSystemEvent;

final readonly class CustomerEmailWasChangedDataProvider implements WebhookDataProviderInterface
{
    public function __construct(
        private CodeManagerInterface $codeManager,
        private StoreRepository $storeRepository,
        private CodeRepositoryInterface $codeRepository,
        private MemberResponseFactoryInterface $memberResponseFactory
    ) {
    }

    public function getEventName(): string
    {
        return 'CustomerEmailWasChanged';
    }

    /**
     * @throws StoreNotFoundException
     * @throws NotFoundException
     */
    public function getData(
        WebhookEventInterface|CustomerEmailWasChangedSystemEvent $event
    ): ?WebhookData {
        $member = $this->memberResponseFactory->create($event->getCustomerId());

        $code = $this->codeRepository->getById($event->getCodeId());
        if (null === $code) {
            throw new NotFoundException(sprintf('Code with ID: %s not found', $event->getCodeId()));
        }

        $codeNo = $this->codeManager->getCodeSequenceNumber($code);

        if (null === $codeNo) {
            return null;
        }

        $store = $this->storeRepository->byId($event->getStoreId(), true);

        if (null === $store) {
            throw new StoreNotFoundException($event->getStoreId());
        }

            $data = [
                'customer' => $member,
                'code' => $code->getCode(),
                'codeNumber' => $codeNo,
            ];

        return new WebhookData(
             $this->getEventName(),
            $store->getCode(),
            $data
        );
    }

    public function supports(string $eventClassName): bool
    {
        return CustomerEmailWasChangedSystemEvent::class === $eventClassName;
    }
}
