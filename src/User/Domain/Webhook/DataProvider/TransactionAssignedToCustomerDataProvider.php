<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Webhook\DataProvider;

use OpenLoyalty\Core\Domain\Exception\StoreNotFoundException;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\Webhook\DataProvider\WebhookDataProviderInterface;
use OpenLoyalty\Core\Domain\Webhook\Event\WebhookEventInterface;
use OpenLoyalty\Core\Domain\Webhook\Factory\MemberResponseFactoryInterface;
use OpenLoyalty\Core\Domain\Webhook\Response\WebhookData;
use OpenLoyalty\User\Domain\SystemEvent\TransactionAssignedToCustomerSystemEvent;

final readonly class TransactionAssignedToCustomerDataProvider implements WebhookDataProviderInterface
{
    public function __construct(
        private StoreRepository $storeRepository,
        private MemberResponseFactoryInterface $memberResponseFactory
    ) {
    }

    public function getEventName(): string
    {
        return 'TransactionAssignedToCustomer';
    }

    /**
     * @throws StoreNotFoundException
     */
    public function getData(
        WebhookEventInterface|TransactionAssignedToCustomerSystemEvent $event
    ): ?WebhookData {
        if ($event->isSync()) {
            return null;
        }

        $storeCode = $this->storeRepository->byId($event->getStoreId())?->getCode();
        if (null === $storeCode) {
            throw new StoreNotFoundException($event->getStoreId());
        }

            $data = [
                'transactionId' => (string) $event->getTransactionId(),
                'grossValue' => $event->getGrossValue(),
                'customer' => $this->memberResponseFactory->create($event->getCustomerId()),
            ];

        return new WebhookData(
            $this->getEventName(),
            $storeCode,
            $data,
        );
    }

    public function supports(string $eventClassName): bool
    {
        return TransactionAssignedToCustomerSystemEvent::class === $eventClassName;
    }
}
