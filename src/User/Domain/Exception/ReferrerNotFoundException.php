<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Exception;

use LogicException;
use OpenLoyalty\Core\Domain\Exception\Translatable;

class ReferrerNotFoundException extends LogicException implements Translatable
{
    public function getMessageKey(): string
    {
        return 'user.referrer.not_found';
    }

    public function getMessageParams(): array
    {
        return [];
    }
}
