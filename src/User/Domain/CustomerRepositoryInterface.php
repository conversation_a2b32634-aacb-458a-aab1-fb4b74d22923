<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain;

use DateTimeImmutable;
use Generator;
use InvalidArgumentException;
use OpenLoyalty\Core\Domain\Exception\NotFoundException;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Repository;
use OpenLoyalty\Core\Domain\ValueObject\Condition\Operator;
use OpenLoyalty\User\Domain\ValueObject\MemberCampaignVisibility;

interface CustomerRepositoryInterface extends Repository
{
    public function save(Customer $customer): void;

    /**
     * @throws NotFoundException
     * @throws InvalidArgumentException
     */
    public function load(CustomerId $id): Customer;

    public function getById(CustomerId $customerId): ?Customer;

    public function getCountByLevelId(StoreId $storeId): array;

    public function getCountBy(
        StoreId $storeId,
        ?bool $withTransactions = null,
        ?DateTimeImmutable $fromRegisteredAt = null
    ): int;

    public function getTransactionTotalAmountBy(
        StoreId $storeId,
        bool $withoutDeliveryCosts = false
    ): float;

    public function getAllActive(StoreId $storeId): iterable;

    public function getAllActiveCount(StoreId $storeId): int;

    public function getByIds(array $customerIds): iterable;

    public function getAllForLevelRecalculation(StoreId $storeId, DateTimeImmutable $currentDate): iterable;

    public function getRegistrationHistogram(StoreId $storeId, string $interval, DateTimeImmutable $fromRegisteredAt): array;

    public function findByGenders(StoreId $storeId, array $genders): Generator;

    public function findByAddressFields(StoreId $storeId, string $addressFiled, array $values): Generator;

    public function findByAge(StoreId $storeId, int $from, int $to): Generator;

    public function getAllActiveIdsIterable(StoreId $storeId): Generator;

    public function getAllActiveIds(StoreId $storeId): array;

    public function getByBirthdayAnniversary(
        StoreId $storeId,
        DateTimeImmutable $from,
        DateTimeImmutable $to,
        bool $onlyActive = true
    ): iterable;

    public function getByRegistrationAnniversary(
        StoreId $storeId,
        DateTimeImmutable $from,
        DateTimeImmutable $to,
        bool $onlyActive = true
    ): iterable;

    public function getCustomerIdsByBirthdayAnniversary(
        StoreId $storeId,
        DateTimeImmutable $from,
        DateTimeImmutable $to,
        bool $onlyActive = true
    ): Generator;

    public function getCustomerIdsByRegistrationAnniversary(
        StoreId $storeId,
        DateTimeImmutable $from,
        DateTimeImmutable $to,
        bool $onlyActive = true
    ): Generator;

    /**
     * @return array{id: string, firstTransactionDate: string, firstCustomEventDate: string}
     */
    public function getMembersToActivate(
        DateTimeImmutable $lastTransactionDateArgument,
        DateTimeImmutable $endPeriodDateTime,
        StoreId $storeId,
        array $activeMemberSetting
    ): iterable;

    /**
     * @return array{id: string}
     */
    public function getMembersToDeactivate(
        DateTimeImmutable $lastTransactionDateArgument,
        StoreId $storeId,
        array $activeMemberSetting
    ): iterable;

    public function getRandomActiveCustomers(StoreId $storeId, int $maxResults): array;

    public function getAllActiveMembersCount(
        ?DateTimeImmutable $fromLastTransactionAt = null,
        ?DateTimeImmutable $fromLastCustomEventAt = null,
        ?StoreId $storeId = null
    ): int;

    public function getOneByPhone(string $phoneNumber, StoreId $storeId): ?Customer;

    public function updateActiveMember(array $membersIds, bool $activeMember): void;

    public function getAllMemberIdsWithSpecificCustomEventType(
        StoreId $storeId,
        DateTimeImmutable $from,
        DateTimeImmutable $to,
        ?string $customEventType
    ): Generator;

    public function getAllMemberIdsRegisteredInDateFrom(StoreId $storeId, Operator $operator, ?int $days, ?int $fromDays, ?int $toDays): Generator;

    /**
     * @return Generator|MemberCampaignVisibility[]
     */
    public function getCampaignVisibility(): Generator;

    public function exist(StoreId $storeId, CustomerId $customerId): ?Customer;

    /**
     * @return array<Customer>
     */
    public function findByPhone(string $phoneNumber, StoreId $storeId): array;
}
