<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Translation\Domain\DTO;

use DateTime;

class LanguageData
{
    private ?string $code;
    private ?string $name;
    private ?bool $adminDefault;
    private ?bool $apiDefault;
    private ?int $order;
    private ?DateTime $updatedAt;

    public function __construct(
        ?string $code = null,
        ?string $name = null,
        ?DateTime $updatedAt = null,
        ?int $order = null,
        ?bool $adminDefault = false,
        ?bool $apiDefault = false
    ) {
        $this->code = $code;
        $this->name = $name;
        $this->updatedAt = $updatedAt;
        $this->order = $order;
        $this->adminDefault = $adminDefault;
        $this->apiDefault = $apiDefault;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): void
    {
        $this->code = $code;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function isAdminDefault(): bool
    {
        return $this->adminDefault;
    }

    public function setAdminDefault(bool $adminDefault): void
    {
        $this->adminDefault = $adminDefault;
    }

    public function isApiDefault(): bool
    {
        return $this->apiDefault;
    }

    public function setApiDefault(bool $apiDefault): void
    {
        $this->apiDefault = $apiDefault;
    }

    public function getOrder(): ?int
    {
        return $this->order;
    }

    public function setOrder(int $order): void
    {
        $this->order = $order;
    }

    public function getUpdatedAt(): DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(DateTime $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }
}
