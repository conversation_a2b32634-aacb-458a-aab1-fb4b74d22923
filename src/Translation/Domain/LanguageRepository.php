<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Translation\Domain;

use DateTimeInterface;
use OpenLoyalty\Core\Domain\Id\LocaleCode;
use OpenLoyalty\Core\Domain\Repository;

interface LanguageRepository extends Repository
{
    public function byId(LocaleCode $localeCode): ?Language;

    public function findAll(): array;

    public function byCode(string $code): ?Language;

    public function getAdminDefault(): ?Language;

    public function getApiDefault(): ?Language;

    public function save(Language $language): void;

    public function remove(Language $language): void;

    public function getLastUpdatedAt(): ?DateTimeInterface;
}
