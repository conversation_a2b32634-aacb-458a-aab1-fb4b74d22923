<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Translation\Infrastructure\Persistence\Doctrine\Repository;

use DateTimeImmutable;
use Doctrine\ORM\NoResultException;
use OpenLoyalty\Core\Domain\Id\LocaleCode;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\Translation\Domain\Language;
use OpenLoyalty\Translation\Domain\LanguageRepository;

class DoctrineLanguageRepository extends DoctrineRepository implements LanguageRepository
{
    public function byId(LocaleCode $localeCode): ?Language
    {
        return parent::find($localeCode);
    }

    public function findAll(): array
    {
        return parent::findBy([], ['order' => 'asc']);
    }

    public function byCode(string $code): ?Language
    {
        return $this->find(new LocaleCode($code));
    }

    public function getAdminDefault(): ?Language
    {
        return $this->findOneBy(['adminDefault' => 1]);
    }

    public function getApiDefault(): ?Language
    {
        return $this->findOneBy(['apiDefault' => 1]);
    }

    public function save(Language $language): void
    {
        if ($language->isAdminDefault()) {
            $query = $this->repository->createQueryBuilder('l')
                ->update()
                ->set('l.adminDefault', '?1')
                ->where('l.localeCode <> ?2')
                ->setParameter(1, 'false')
                ->setParameter(2, (string) $language->getLocaleCode())
                ->getQuery();

            $query->execute();
        }

        if ($language->isApiDefault()) {
            $query = $this->repository->createQueryBuilder('l')
                ->update()
                ->set('l.apiDefault', '?1')
                ->where('l.localeCode <> ?2')
                ->setParameter(1, 'false')
                ->setParameter(2, (string) $language->getLocaleCode())
                ->getQuery();

            $query->execute();
        }

        $this->entityManager->persist($language);
        $this->entityManager->flush();
        $this->entityManager->getCache()?->evictEntityRegion(Language::class);
    }

    public function remove(Language $language): void
    {
        $this->entityManager->remove($language);
        $this->entityManager->flush();
    }

    public function getLastUpdatedAt(): ?DateTimeImmutable
    {
        $qb = $this->repository->createQueryBuilder('te');

        try {
            $result = $qb->select('MAX(te.updatedAt)')
                ->getQuery()->getSingleScalarResult();
        } catch (NoResultException) {
            return null;
        }

        return null !== $result ? new DateTimeImmutable($result) : null;
    }

    protected function getClass(): string
    {
        return Language::class;
    }
}
