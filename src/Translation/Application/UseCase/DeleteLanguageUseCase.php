<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Translation\Application\UseCase;

use OpenLoyalty\Core\Domain\Id\LocaleCode;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Translation\Application\Command\RemoveLanguage;
use OpenLoyalty\Translation\Domain\Exception\LanguageDoesNotExist;
use OpenLoyalty\Translation\Domain\Exception\RemoveAdminDefaultLanguage;

class DeleteLanguageUseCase
{
    protected CommandBusInterface $commandBus;

    public function __construct(
        CommandBusInterface $commandBus
    ) {
        $this->commandBus = $commandBus;
    }

    /**
     * @throws LanguageDoesNotExist
     * @throws RemoveAdminDefaultLanguage
     */
    public function execute(string $code): void
    {
        $this->commandBus->dispatch(new RemoveLanguage(new LocaleCode($code)));
    }
}
