<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Translation\Application\UseCase;

use OpenLoyalty\Core\Domain\Id\LocaleCode;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Translation\Application\Command\UpdateLanguage;
use OpenLoyalty\Translation\Domain\DTO\LanguageData;

class UpdateLanguageUseCase
{
    /**
     * @var CommandBusInterface
     */
    protected $commandBus;

    public function __construct(
        CommandBusInterface $commandBus
    ) {
        $this->commandBus = $commandBus;
    }

    /**
     * @throws \OpenLoyalty\Translation\Domain\Exception\AdminDefaultLanguageHasNoTranslations
     * @throws \OpenLoyalty\Translation\Domain\Exception\LanguageDoesNotExist
     * @throws \OpenLoyalty\Translation\Domain\Exception\NoDefaultLanguageSelected
     */
    public function execute(LanguageData $entry): void
    {
        $this->commandBus->dispatch(new UpdateLanguage(new LocaleCode($entry->getCode()), $entry));
    }
}
