<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Translation\Application\UseCase;

use OpenLoyalty\Core\Domain\Cache\ResponseCacheInterface;
use OpenLoyalty\Translation\Domain\LanguageRepository;

class GetTranslationsForLanguage
{
    private const CACHE_KEY = 'translations';

    public function __construct(
        private LanguageRepository $languageRepository,
        private ResponseCacheInterface $responseCache
    ) {
    }

    public function execute(?string $locale): array
    {
        if (null === $locale) {
            $locale = (string) $this->languageRepository->getAdminDefault()?->getLocaleCode();
        }

        return $this->responseCache->get($locale.'_'.self::CACHE_KEY, function () use ($locale) {
            $language = $this->languageRepository->byCode($locale);

            if (!$language) {
                return [];
            }
            $response = [];
            $translationEntries = $language->getTranslations()->toArray();
            foreach ($translationEntries as $entry) {
                $response[$entry->getKey()] = $entry->getValue();
            }

            return $response;
        });
    }
}
