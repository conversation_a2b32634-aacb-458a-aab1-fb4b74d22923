<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Translation\Application\UseCase;

use OpenLoyalty\Core\Domain\Id\LocaleCode;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Translation\Application\Command\CreateLanguage;
use OpenLoyalty\Translation\Domain\DTO\LanguageData;
use OpenLoyalty\Translation\Domain\Exception\AdminDefaultLanguageHasNoTranslations;
use OpenLoyalty\Translation\Domain\Exception\LanguageAlreadyExists;
use OpenLoyalty\Translation\Domain\LanguageRepository;

class CreateLanguageUseCase
{
    /**
     * @var \OpenLoyalty\Translation\Domain\LanguageRepository
     */
    protected $languageRepository;

    /**
     * @var CommandBusInterface
     */
    protected $commandBus;

    public function __construct(
        LanguageRepository $languageRepository,
        CommandBusInterface $commandBus
    ) {
        $this->languageRepository = $languageRepository;
        $this->commandBus = $commandBus;
    }

    /**
     * @throws AdminDefaultLanguageHasNoTranslations
     * @throws \OpenLoyalty\Translation\Domain\Exception\LanguageAlreadyExists
     */
    public function execute(LanguageData $entry): void
    {
        if ($this->languageRepository->byCode($entry->getCode())) {
            throw new LanguageAlreadyExists();
        }

        if ($entry->isAdminDefault()) {
            throw new AdminDefaultLanguageHasNoTranslations();
        }

        $this->commandBus->dispatch(new CreateLanguage(new LocaleCode($entry->getCode()), $entry));
    }
}
