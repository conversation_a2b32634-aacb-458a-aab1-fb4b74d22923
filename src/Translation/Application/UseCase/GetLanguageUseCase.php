<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Translation\Application\UseCase;

use OpenLoyalty\Translation\Domain\Exception\LanguageDoesNotExist;
use OpenLoyalty\Translation\Domain\Language;
use OpenLoyalty\Translation\Domain\LanguageRepository;

class GetLanguageUseCase
{
    protected LanguageRepository $languageRepository;

    public function __construct(
        LanguageRepository $languageRepository
    ) {
        $this->languageRepository = $languageRepository;
    }

    /**
     * @throws LanguageDoesNotExist
     */
    public function execute(string $code): Language
    {
        $language = $this->languageRepository->byCode($code);

        if (!$language) {
            throw new LanguageDoesNotExist();
        }

        return $language;
    }
}
