<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Translation\Application\Command;

use OpenLoyalty\Core\Domain\Id\LocaleCode;
use OpenLoyalty\Translation\Domain\DTO\LanguageData;

class CreateLanguage extends LanguageCommand
{
    protected LanguageData $languageData;

    public function __construct(LocaleCode $localeCode, LanguageData $languageData)
    {
        parent::__construct($localeCode);
        $this->languageData = $languageData;
    }

    public function getLanguageData(): LanguageData
    {
        return $this->languageData;
    }
}
