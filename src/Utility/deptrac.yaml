deptrac:
    paths:
        - ./src
    layers:
        -   name: UtilityDomain
            collectors:
                -   type: directory
                    regex: src/Utility/Domain/*
        -   name: UtilityApplication
            collectors:
                -   type: directory
                    regex: src/Utility/Application/*
        -   name: UtilityInfrastructure
            collectors:
                -   type: directory
                    regex: src/Utility/Infrastructure/*
        -   name: UtilityUi
            collectors:
                -   type: directory
                    regex: src/Utility/Ui/*
    ruleset:
        UtilityDomain:
        UtilityApplication:
            - UtilityDomain
            - UtilityInfrastructure # dependency to remove
            - CoreDomain
            - UserDomain
            - UserApplication
            - SegmentDomain
        UtilityInfrastructure:
            - UtilityDomain
            - UtilityApplication
        UtilityUi:
            - UtilityDomain
            - UtilityApplication
            - UtilityInfrastructure
    skip_violations:
        OpenLoyalty\Utility\Ui\Console\Command\CreateInvalidEventCommand:
            - OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventBusInterface
            - OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId
            - OpenLoyalty\DataAnalytics\Domain\Member\Event\MemberWasRegistered
            - OpenLoyalty\Core\Domain\StoreRepository
            - OpenLoyalty\Core\Domain\Id\CustomerId
            - OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version
            - OpenLoyalty\DataAnalytics\Domain\Member\ValueObject\Gender