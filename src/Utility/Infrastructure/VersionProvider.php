<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Utility\Infrastructure;

/**
 * Class VersionProvider.
 */
class VersionProvider implements VersionProviderInterface
{
    private const VERSION_FILE_NAME = 'VERSION';

    /**
     * @var string
     */
    private $kernelRootDir;

    /**
     * VersionProvider constructor.
     */
    public function __construct(string $kernelRootDir)
    {
        $this->kernelRootDir = $kernelRootDir;
    }

    /**
     * {@inheritdoc}
     */
    public function getApplicationVersion(): string
    {
        try {
            return trim(preg_replace(
                '/\s\s+/',
                ' ',
                file_get_contents($this->kernelRootDir.'/'.self::VERSION_FILE_NAME)
            ));
        } catch (\Exception $ex) {
            return 'unknown';
        }
    }
}
