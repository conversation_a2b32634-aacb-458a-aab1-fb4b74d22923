<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Event\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Event\Application\UseCase\CreateEventUseCase;
use OpenLoyalty\Event\Domain\DTO\Event;
use OpenLoyalty\Event\Infrastructure\Form\Type\CreateEventFormType;
use OpenLoyalty\Tools\FeatureFlag\EnvironmentFeatureFlag;
use OpenLoyalty\Tools\FeatureFlag\FeatureFlag;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class Post extends AbstractFOSRestController
{
    public function __construct(
        private readonly CreateEventUseCase $useCase,
        private readonly FormFactoryInterface $formFactory,
        private readonly EnvironmentFeatureFlag $environmentFeatureFlag,
        private readonly StoreContextProviderInterface $storeContextProvider,
    ) {
    }

    /**
     * @Route(methods={"POST"}, name="oloy.event.create", path="/{storeCode}/event")
     * @Security("is_granted('REGISTER_EVENT')")
     */
    public function __invoke(Request $request): View
    {
        if (!$this->environmentFeatureFlag->isEnabled(FeatureFlag::CREATE_EVENT)) {
            return $this->view([], Response::HTTP_NOT_FOUND);
        }

        $form = $this->formFactory->createNamed(
            'event',
            CreateEventFormType::class,
            null,
            [
                'eventType' => $request->get('event')['type'], //@phpstan-ignore-line
            ]
        );

        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        /** @var Event $event */
        $event = $form->getData();
        $response = $this->useCase->execute(
            $this->storeContextProvider->getStore()->getStoreId(),
            $event
        );

        return $this->view($response);
    }
}
