<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Ui\Rest\Controller\Wallet;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use OpenLoyalty\Account\Application\Response\Wallet;
use OpenLoyalty\Account\Application\UseCase\GetMemberWalletListUseCase;
use OpenLoyalty\Account\Infrastructure\Form\Type\WalletSearchType;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableTotalResponse;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchFormFactoryInterface;
use OpenLoyalty\User\Domain\Customer;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class GetMemberList extends AbstractFOSRestController
{
    public function __construct(
        private readonly GetMemberWalletListUseCase $useCase,
        private readonly SearchFormFactoryInterface $searchFormFactory
    ) {
    }

    /**
     * @Route(methods={"GET"}, name="oloy.wallet.memberList", path="/{storeCode}/member/{member}/wallet", requirements={"member"="%routing.member-query%"})
     *
     * @Security("is_granted('LIST_WALLET', member)")
     * @Operation(
     *     tags={"Wallet"},
     *     summary="Get member’s wallets details",
     *     operationId="memberWalletGetList",
     *     @OA\Parameter(ref="#/components/parameters/storeCode"),
     *     @OA\Parameter(
     *         name="member",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="name",
     *         in="query",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="createdAt",
     *         in="query",
     *         required=false,
     *         @OA\Schema(
     *             type="string",
     *             format="date-time"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="walletTypeId",
     *         in="query",
     *         required=false,
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Parameter(ref="#/components/parameters/page"),
     *     @OA\Parameter(ref="#/components/parameters/itemsOnPage"),
     *     @OA\Parameter(ref="#/components/parameters/orderBy"),
     *     @OA\Response(
     *         response="200",
     *         description="List of wallets",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="items",
     *                     type="array",
     *                     @OA\Items(ref=@Model(type=Wallet::class))
     *                 ),
     *                 @OA\Property(property="total", ref=@Model(type=SearchableTotalResponse::class))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="401",
     *         description="",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 oneOf={
     *                     @OA\Schema(ref="#/components/schemas/ExpiredToken"),
     *                     @OA\Schema(ref="#/components/schemas/InvalidToken"),
     *                     @OA\Schema(ref="#/components/schemas/Unauthorized")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="403",
     *         ref="#/components/responses/AccessDenied"
     *     )
     * )
     */
    public function __invoke(Request $request, Customer $member): View
    {
        $form = $this->searchFormFactory->createAndHandle(
            WalletSearchType::class,
            $request->query->all(),
            $request->getLocale()
        );

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $result = $this->useCase->execute($member->getCustomerId(), $form->getData());

        return $this->view($result, Response::HTTP_OK);
    }
}
