<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Ui\Rest\Controller\WalletType;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Account\Application\UseCase\GetWalletTypeUseCase;
use OpenLoyalty\Account\Domain\WalletType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;

class Get extends AbstractFOSRestController
{
    public function __construct(
        private GetWalletTypeUseCase $useCase
    ) {
    }

    /**
     * @Route(methods={"GET"}, name="oloy.walletType.get", path="/{storeCode}/walletType/{walletType}", requirements={"walletType"="%routing.uuid%"})
     *
     * @Security("is_granted('VIEW_SETTINGS')")
     */
    public function __invoke(Request $request, WalletType $walletType): View
    {
        return $this->view($this->useCase->execute($walletType));
    }
}
