<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Ui\Rest\Controller\WalletType;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Account\Application\UseCase\CreateWalletTypeUseCase;
use OpenLoyalty\Account\Domain\Exception\WalletTypeCodeAlreadyExistsException;
use OpenLoyalty\Account\Infrastructure\Form\Type\CreateWalletTypeFormType;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

class Post extends AbstractFOSRestController
{
    public function __construct(
        private CreateWalletTypeUseCase $useCase,
        private FormFactoryInterface $formFactory,
        private UuidGeneratorInterface $uuidGenerator,
        private StoreContextProviderInterface $storeContextProvider,
        private TranslatorInterface $translator
    ) {
    }

    /**
     * @Route(methods={"POST"}, name="oloy.walletType.create", path="/{storeCode}/walletType")
     *
     * @Security("is_granted('EDIT_SETTINGS')")
     */
    public function __invoke(Request $request): View
    {
        $walletTypeId = new WalletTypeId($this->uuidGenerator->generate());

        $form = $this->formFactory->createNamed(
            'walletType',
            CreateWalletTypeFormType::class,
            null,
            [
                'walletTypeId' => $walletTypeId,
                'store' => $this->storeContextProvider->getStore(),
            ]
        );

        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        try {
            $this->useCase->execute($form->getData());

            return $this->view(['walletTypeId' => (string) $walletTypeId]);
        } catch (WalletTypeCodeAlreadyExistsException $ex) {
            $form->get('code')->addError(
                new FormError($this->translator->trans($ex->getMessageKey(), $ex->getMessageParams()))
            );
        }

        return $this->view($form, Response::HTTP_BAD_REQUEST);
    }
}
