<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Application\JobHandler;

use OpenLoyalty\Account\Application\Job\CreateWalletsForTypeJob;
use OpenLoyalty\Account\Domain\Exception\WalletTypeNotFoundException;
use OpenLoyalty\Account\Domain\WalletCreatorInterface;
use OpenLoyalty\Account\Domain\WalletTypeRepositoryInterface;
use OpenLoyalty\Core\Domain\Message\JobHandlerInterface;

final class CreateWalletsForTypeHandler implements JobHandlerInterface
{
    public function __construct(
        private readonly WalletCreatorInterface $walletCreator,
        private readonly WalletTypeRepositoryInterface $walletTypeRepository
    ) {
    }

    public function __invoke(CreateWalletsForTypeJob $job): void
    {
        $this->walletCreator->createAllForType($job->getStoreId(), $job->getWalletTypeId(), new \DateTimeImmutable());

        $walletType = $this->walletTypeRepository->byId($job->getWalletTypeId());

        if (null === $walletType) {
            throw new WalletTypeNotFoundException();
        }

        if ($job->willActivate()) {
            $walletType->activate();
        }

        $walletType->unblock();

        $this->walletTypeRepository->save($walletType);
    }
}
