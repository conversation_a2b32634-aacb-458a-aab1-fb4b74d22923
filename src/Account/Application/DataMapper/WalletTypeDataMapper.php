<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Application\DataMapper;

use OpenLoyalty\Account\Application\Response\WalletType as WalletTypeResponse;
use OpenLoyalty\Account\Application\Response\WalletTypeBasicData;
use OpenLoyalty\Account\Domain\WalletType;
use OpenLoyalty\Core\Application\DataMapper\DataMapperInterface;
use OpenLoyalty\Core\Application\DataMapper\TranslationsDataMapper;

class WalletTypeDataMapper implements DataMapperInterface
{
    public function __construct(
        private TranslationsDataMapper $translationsDataMapper,
    ) {
    }

    public function map(WalletType $entity): WalletTypeResponse
    {
        return new WalletTypeResponse(
            $entity->getWalletTypeId(),
            $entity->getCode(),
            $entity->getUnitSingularName(),
            $entity->getUnitPluralName(),
            $entity->isActive(),
            $this->translationsDataMapper->map($entity->getTranslations()),
            $entity->isDefault(),
            $entity->getUnitDaysExpiryAfter(),
            $entity->getUnitDaysActiveCount(),
            $entity->getUnitYearsActiveCount(),
            $entity->getUnitDaysLocked(),
            $entity->getAllTimeNotLocked(),
            $entity->getCreatedAt(),
            $entity->getLimits(),
            $entity->isNegativeBalance(),
            unitExpiryDate: null !== $entity->getUnitExpiryDate() ? $entity->getUnitExpiryDate()->format('m-d') : null
        );
    }

    public function mapBasic(WalletType $entity): WalletTypeBasicData
    {
        return new WalletTypeBasicData(
            $entity->getWalletTypeId(),
            $entity->getCode(),
            $entity->getName() ?? '',
            $entity->getUnitSingularName(),
            $entity->getUnitPluralName(),
            $entity->isActive(),
            $entity->isDefault(),
            $entity->getCreatedAt(),
            $entity->getLimits(),
            $entity->isNegativeBalance()
        );
    }

    public function mapList(array $entities): array
    {
        $walletTypes = [];
        /* @var \OpenLoyalty\Account\Domain\WalletType $entity */
        foreach ($entities as $entity) {
            $walletTypes[] = $this->mapBasic($entity);
        }

        return $walletTypes;
    }
}
