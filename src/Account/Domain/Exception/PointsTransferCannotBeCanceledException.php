<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Account\Domain\Exception;

use OpenLoyalty\Core\Domain\Exception\Translatable;

/**
 * Class PointsTransferCannotBeCanceled.
 */
class PointsTransferCannotBeCanceledException extends \Exception implements Translatable
{
    /**
     * @var string
     */
    protected $id;

    public function __construct(string $id)
    {
        parent::__construct(sprintf('Points transfer #%s cannot be cancelled', $id));
        $this->id = $id;
    }

    /**
     * {@inheritdoc}
     */
    public function getMessageKey(): string
    {
        return 'account.points_transfer.cannot_be_cancelled';
    }

    /**
     * {@inheritdoc}
     */
    public function getMessageParams(): array
    {
        return [
            '%id%' => $this->id,
        ];
    }
}
