<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Account\Domain\Provider;

use OpenLoyalty\Account\Domain\Wallet;
use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\ValueObject\Wallet\Account as AccountVO;

interface AccountProviderInterface
{
    public function provide(Wallet $wallet, bool $readConnection = false): AccountVO;

    /**
     * @param  array<AccountId> $walletsIds
     * @return array<AccountVO>
     */
    public function provideAccountsForMember(array $walletsIds, bool $readConnection = false): array;
}
