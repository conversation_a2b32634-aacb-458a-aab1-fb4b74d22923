<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Domain\Provider;

use OpenLoyalty\Account\Domain\Factory\WalletTypeFactoryInterface;
use OpenLoyalty\Account\Domain\WalletType;
use OpenLoyalty\Account\Domain\WalletTypeTranslation;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Points\Domain\Expiring\ExpiringTransferMode;

class DefaultWalletTypeProvider implements DefaultWalletTypeProviderInterface
{
    public const DEFAULT_UNIT_CODE = 'default';

    public function __construct(
        private WalletTypeFactoryInterface $walletTypeFactory,
        private UuidGeneratorInterface $uuidGenerator
    ) {
    }

    public function createDefault(Store $store): WalletType
    {
        $walletTypeId = new WalletTypeId($this->uuidGenerator->generate());

        $translations = new WalletTypeTranslation();
        $translations->setName('Default Wallet');
        $translations->setDescription('Default Wallet');

        $walletType = $this->walletTypeFactory->create(
            $walletTypeId,
            self::DEFAULT_UNIT_CODE,
            $store,
            'Point',
            'Points',
            true,
            null,
            true
        );

        $walletType->markAsExpireAfterXDays(ExpiringTransferMode::DEFAULT_ACTIVE_DURATION_DAYS);
        $walletType->markAsPendingForXDays(10);

        $walletType->assignTranslations(['en' => $translations]);

        return $walletType;
    }
}
