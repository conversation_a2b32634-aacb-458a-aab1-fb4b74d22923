<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Domain\Event;

use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;

class AccountWasCreated extends AccountEvent
{
    protected CustomerId $customerId;
    protected StoreId $storeId;

    public function __construct(
        AccountId $accountId,
        StoreId $storeId,
        CustomerId $customerId
    ) {
        parent::__construct($accountId);
        $this->customerId = $customerId;
        $this->storeId = $storeId;
    }

    /**
     * @return \OpenLoyalty\Core\Domain\Id\CustomerId
     */
    public function getCustomerId()
    {
        return $this->customerId;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public static function deserialize(array $data): self
    {
        return new self(
            new AccountId($data['accountId']),
            new StoreId($data['storeId']),
            new CustomerId($data['customerId'])
        );
    }
}
