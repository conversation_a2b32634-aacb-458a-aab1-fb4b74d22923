<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Domain\Event;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\User\Domain\Event\AccountHistoryItemInterface;

class PointsWereTransferred extends AccountEvent implements AccountHistoryItemInterface
{
    private TransferId $transferId;
    private float $value;
    private AccountId $receiverId;
    private DateTimeImmutable $createdAt;

    public function __construct(AccountId $accountId, TransferId $transferId, float $value, DateTimeImmutable $createdAt, AccountId $receiverId)
    {
        parent::__construct($accountId);
        $this->transferId = $transferId;
        $this->value = $value;
        $this->receiverId = $receiverId;
        $this->createdAt = $createdAt;
    }

    public function getTransferId(): TransferId
    {
        return $this->transferId;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getReceiverId(): AccountId
    {
        return $this->receiverId;
    }

    public function getEventOccurrenceDate(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getComment(): ?string
    {
        return null;
    }

    public static function deserialize(array $data): self
    {
        return new self(
            new AccountId($data['accountId']),
            new TransferId($data['transferId']),
            $data['value'],
            DateTimeImmutable::createFromFormat('U', (string) $data['createdAt']),
            new AccountId($data['receiverId']),
        );
    }
}
