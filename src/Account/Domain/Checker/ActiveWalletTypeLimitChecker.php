<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Domain\Checker;

use OpenLoyalty\Account\Domain\WalletTypeRepositoryInterface;

final readonly class ActiveWalletTypeLimitChecker implements ActiveWalletTypeLimitCheckerInterface
{
    public function __construct(
        private WalletTypeRepositoryInterface $walletTypeRepository,
        private int $globalActiveWalletTypeLimit
    ) {
    }

    public function isGlobalLimitReached(): bool
    {
        return $this->walletTypeRepository->getCountAllActive() >= $this->globalActiveWalletTypeLimit;
    }

    public function getGlobalActiveWalletTypeLimit(): int
    {
        return $this->globalActiveWalletTypeLimit;
    }
}
