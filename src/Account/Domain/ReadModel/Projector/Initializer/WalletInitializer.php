<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Domain\ReadModel\Projector\Initializer;

use Generator;
use OpenLoyalty\Account\Domain\ReadModel\Projector\SingleWalletRequestProjection;
use OpenLoyalty\Account\Domain\ReadModel\Projector\WalletProjector;
use OpenLoyalty\Account\Domain\WalletRepositoryInterface;
use OpenLoyalty\Core\Domain\ReadModel\ProjectionInitializerInterface;
use OpenLoyalty\Core\Domain\ReadModel\ProjectorInterface;
use OpenLoyalty\Core\Domain\ReadModel\Request\InitializeRequestProjection;

final class WalletInitializer implements ProjectionInitializerInterface
{
    public function __construct(
        private readonly WalletRepositoryInterface $walletRepository
    ) {
    }

    public function initialize(InitializeRequestProjection $requestProjection): Generator
    {
        $walletIds = $this->walletRepository->getAllWithoutProjection($requestProjection->storeId);

        foreach ($walletIds as $walletId) {
            yield new SingleWalletRequestProjection(
                $requestProjection->storeId,
                $walletId,
            );
        }
    }

    public function supports(ProjectorInterface $projector): bool
    {
        return $projector instanceof WalletProjector;
    }
}
