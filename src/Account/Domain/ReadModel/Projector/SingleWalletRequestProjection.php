<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Domain\ReadModel\Projector;

use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\ReadModel\Request\RequestProjectionInterface;

final class SingleWalletRequestProjection implements RequestProjectionInterface
{
    public function __construct(
        public readonly StoreId $storeId,
        public readonly AccountId $walletId
    ) {
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }
}
