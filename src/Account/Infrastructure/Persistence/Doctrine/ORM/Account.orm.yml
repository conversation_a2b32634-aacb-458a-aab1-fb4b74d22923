OpenLoyalty\Account\Domain\Account:
    type: entity
    table: account
    id:
        id:
            type: account_id
            column: id
    fields:
        customerId:
            type: customer_id
        storeId:
            type: store_id
        earnedTotal:
            type: points
            scale: 12
            precision: 24
        transferredTotal:
            type: points
            scale: 12
            precision: 24
        spentTotal:
            type: points
            scale: 12
            precision: 24
        activeTotal:
            type: points
            scale: 12
            precision: 24
        pendingTotal:
            type: points
            scale: 12
            precision: 24
        blockedTotal:
            type: points
            scale: 12
            precision: 24
        expiredTotal:
            type: points
            scale: 12
            precision: 24
        version:
            type: integer
            version: true
        createdAt:
            type: datetime_immutable_microseconds
        updatedAt:
            type: datetime_immutable_microseconds
        createdBy:
            type: string
            nullable: true
        updatedBy:
            type: string
            nullable: true
