<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Infrastructure\Persistence\ReadModel\Repository;

use OpenLoyalty\Account\Domain\ReadModel\Entity\Wallet;
use OpenLoyalty\Account\Domain\ReadModel\Projector\MassWalletRequestProjection;
use OpenLoyalty\Account\Domain\ReadModel\Projector\QueryWalletRequestProjection;
use OpenLoyalty\Account\Domain\ReadModel\Projector\SingleWalletRequestProjection;
use OpenLoyalty\Account\Domain\ReadModel\Repository\WalletReadModelRepositoryInterface;
use OpenLoyalty\Account\Domain\ReadModel\Repository\WalletReadModelRepositoryReadContextInterface;
use OpenLoyalty\Core\Domain\ReadModel\Request\RequestProjectionInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\UuidCriteria;
use OpenLoyalty\Core\Domain\Search\Criteria\UuidListCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\Core\Infrastructure\Persistence\ReadModel\Repository\ReadModelRepository;

class WalletReadModelRepository extends ReadModelRepository implements WalletReadModelRepositoryInterface, WalletReadModelRepositoryReadContextInterface
{
    protected function getClass(): string
    {
        return Wallet::class;
    }

    public function getProjections(RequestProjectionInterface $requestProjection, bool $useQuickResultCache = false): \Generator
    {
        yield from parent::getProjections($requestProjection, $useQuickResultCache);

        if ($requestProjection instanceof SingleWalletRequestProjection) {
            $projection = $this->repository->findOneBy([
                'storeId' => $requestProjection->storeId,
                'walletId' => $requestProjection->walletId,
            ]);
            if (null != $projection) {
                yield $projection;
            }
        } elseif ($requestProjection instanceof QueryWalletRequestProjection) {
            $criteriaCollection = new CriteriaCollection();
            $criteriaCollection->add(
                UuidCriteria::fromIdentifier(
                    'storeId',
                    CriteriaInterface::EQUAL,
                    $requestProjection->storeId
                )
            );
            $criteriaCollection->add(
                UuidCriteria::fromIdentifier(
                    'walletTypeId',
                    CriteriaInterface::EQUAL,
                    $requestProjection->walletTypeId
                )
            );
            yield from $this->findByCriteria($criteriaCollection);
        } elseif ($requestProjection instanceof MassWalletRequestProjection) {
            $criteriaCollection = new CriteriaCollection();
            $criteriaCollection->add(
                UuidCriteria::fromIdentifier(
                    'storeId',
                    CriteriaInterface::EQUAL,
                    $requestProjection->storeId
                )
            );
            $criteriaCollection->add(
                UuidListCriteria::fromIdentifiers(
                    'walletId',
                    CriteriaInterface::IN,
                    $requestProjection->walletIds,
                    true,
                )
            );

            yield from $this->findByCriteria($criteriaCollection);
        }
    }
}
