<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Infrastructure\ParamConverter;

use OpenLoyalty\Account\Domain\WalletType;
use OpenLoyalty\Account\Domain\WalletTypeRepositoryInterface;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\UuidCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\Core\Infrastructure\ParamConverter\AbstractParamConverter;
use Ramsey\Uuid\Exception\InvalidUuidStringException;
use Ramsey\Uuid\Uuid;

class WalletTypeParamConverter extends AbstractParamConverter
{
    public function __construct(
        private readonly WalletTypeRepositoryInterface $walletTypeRepository,
        private readonly StoreContextProviderInterface $storeContextProvider,
    ) {
    }

    protected function getObject(string $value, ?array $options = null): ?object
    {
        try {
            $criteria = new CriteriaCollection();
            $criteria->add(new UuidCriteria(
                'store',
                CriteriaInterface::EQUAL,
                Uuid::fromString((string) $this->storeContextProvider->getStore()->getStoreId())
            ));

            $criteria->add(new UuidCriteria(
                'walletTypeId',
                CriteriaInterface::EQUAL,
                Uuid::fromString($value)
            ));

            return $this->walletTypeRepository->findOneByCriteria($criteria);
        } catch (InvalidUuidStringException) {
            return null;
        }
    }

    protected function getSupportedClass(): string
    {
        return WalletType::class;
    }
}
