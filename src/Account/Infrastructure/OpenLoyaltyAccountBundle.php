<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Infrastructure;

use Doctrine\Bundle\DoctrineBundle\DependencyInjection\Compiler\DoctrineOrmMappingsPass;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Bundle\Bundle;

class OpenLoyaltyAccountBundle extends Bundle
{
    public function build(ContainerBuilder $container): void
    {
        parent::build($container);

        $container->addCompilerPass($this->buildMappingCompilerPass());
    }

    public function buildMappingCompilerPass(): DoctrineOrmMappingsPass
    {
        return DoctrineOrmMappingsPass::createYamlMappingDriver(
            [
                __DIR__.'/Persistence/Doctrine/ORM' => 'OpenLoyalty\Account\Domain',
                __DIR__.'/Persistence/ReadModel/ORM' => 'OpenLoyalty\Account\Domain\ReadModel\Entity',
            ],
        );
    }
}
