<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Infrastructure\DataFixtures\ORM\Demo;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use OpenLoyalty\Account\Application\Command\CreateWalletType;
use OpenLoyalty\Account\Domain\WalletTypeTranslation;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Infrastructure\UuidGenerator;

final class LoadDefaultWalletTypeData extends Fixture implements OrderedFixtureInterface, FixtureGroupInterface
{
    public function __construct(
        private readonly UuidGenerator $uuidGenerator,
        private readonly CommandBusInterface $commandBus,
    ) {
    }

    public static function getGroups(): array
    {
        return ['demo'];
    }

    public function load(ObjectManager $manager): void
    {
        $stores = $manager->getRepository(Store::class)
            ->findAll();

        /** @var Store $store */
        foreach ($stores as $store) {
            $this->commandBus->dispatch($this->createWalletTypeCreateCommand($store->getStoreId()));
        }

        $manager->clear();
    }

    public function getOrder(): int
    {
        return 2;
    }

    private function createWalletTypeCreateCommand(StoreId $storeId): CreateWalletType
    {
        $translations = new WalletTypeTranslation();
        $translations->setName('Stars Wallet');
        $translations->setDescription('Stars Wallet');

        return new CreateWalletType(
            new WalletTypeId($this->uuidGenerator->generate()),
            'stars',
            $storeId,
            'Star',
            'Stars',
            true,
            ['en' => $translations],
            'after_x_days',
            30,
            0,
            10,
            true,
            null
        );
    }
}
