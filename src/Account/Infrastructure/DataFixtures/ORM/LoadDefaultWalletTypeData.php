<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Infrastructure\DataFixtures\ORM;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use OpenLoyalty\Account\Application\Command\UpdateWalletType;
use OpenLoyalty\Account\Domain\WalletType;
use OpenLoyalty\Account\Domain\WalletTypeRepositoryInterface;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;

class LoadDefaultWalletTypeData extends Fixture implements OrderedFixtureInterface, FixtureGroupInterface
{
    public function __construct(
        private readonly WalletTypeRepositoryInterface $walletTypeRepository,
        private readonly CommandBusInterface $commandBus,
    ) {
    }

    public static function getGroups(): array
    {
        return ['setup', 'basic-setup', 'demo', 'async-test-setup'];
    }

    public function load(ObjectManager $manager): void
    {
        $stores = $manager->getRepository(Store::class)
            ->findAll();

        foreach ($stores as $store) {
            if (LoadSettingsData::STORE_B_ID === (string) $store->getStoreId()) {
                $this->setAllTimeNotLockedInWalletType(
                    $this->walletTypeRepository->getDefault($store->getStoreId()),
                    false
                );
            } else {
                $this->setAllTimeNotLockedInWalletType(
                    $this->walletTypeRepository->getDefault($store->getStoreId()),
                    true
                );
            }
        }
    }

    public function getOrder(): int
    {
        return 1;
    }

    private function setAllTimeNotLockedInWalletType(WalletType $walletType, bool $isAllTimeNotLocked): void
    {
        $updateWalletTypeCommand = new UpdateWalletType(
          $walletType->getWalletTypeId(),
          $walletType->getUnitSingularName(),
          $walletType->getUnitPluralName(),
          $walletType->isActive(),
          $walletType->getTranslations()->toArray(),
          $walletType->getUnitDaysExpiryAfter(),
          $walletType->getUnitDaysActiveCount(),
          $walletType->getUnitYearsActiveCount(),
          $walletType->getUnitDaysLocked(),
          $isAllTimeNotLocked,
          $walletType->getLimits(),
        );

        $this->commandBus->dispatch($updateWalletTypeCommand);
    }
}
