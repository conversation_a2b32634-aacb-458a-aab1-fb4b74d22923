<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Infrastructure\Form\Type\DataTransformer;

use OpenLoyalty\Core\Domain\ValueObject\UnitsLimit\Limits;
use OpenLoyalty\Core\Domain\ValueObject\UnitsLimit\UnitsLimit;
use OpenLoyalty\Core\Domain\ValueObject\UnitsLimit\UnitsPerMemberLimit;
use Symfony\Component\Form\DataTransformerInterface;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
class LimitsDataTransformer implements DataTransformerInterface
{
    public function transform(mixed $value): ?array
    {
        if (!is_array($value)) {
            return null;
        }

        return $value;
    }

    public function reverseTransform(mixed $value): Limits
    {
        $unitsLimit = null;
        if (isset($value['points']['value'])) {
            $unitsValue = (float) $value['points']['value'];
            $unitsInterval = $value['points']['interval'] ?? null;
            $unitsLimit = UnitsLimit::create($unitsValue, $unitsInterval);
        }

        $unitsPerMemberLimit = null;
        if (isset($value['pointsPerMember']['value'])) {
            $unitsPerMemberValue = (float) $value['pointsPerMember']['value'];
            $unitsPerMemberInterval = $value['pointsPerMember']['interval'] ?? null;
            $unitsPerMemberLimit = UnitsPerMemberLimit::create($unitsPerMemberValue, $unitsPerMemberInterval);
        }

        return Limits::create($unitsLimit, $unitsPerMemberLimit);
    }
}
