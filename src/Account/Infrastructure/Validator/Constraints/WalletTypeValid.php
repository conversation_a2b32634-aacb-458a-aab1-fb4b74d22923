<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Infrastructure\Validator\Constraints;

use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 */
class WalletTypeValid extends Constraint
{
    public ?string $customMessage = null;
    public string $message = 'account.walletType.code_doest_not_exist';
}
