<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Ui\Web\Controller\Documentation;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class Get extends AbstractController
{
    /**
     * @Route("/doc", name="documentation")
     */
    public function __invoke(): Response
    {
        return $this->render('documentation/index.html.twig');
    }
}
