<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Application\UseCase;

use Carbon\CarbonImmutable;
use OpenLoyalty\Achievement\Application\Command\UpdateMemberAchievementProgress;
use OpenLoyalty\Achievement\Application\DTO\ProgressRule;
use OpenLoyalty\Achievement\Domain\Entity\Achievement;
use OpenLoyalty\Achievement\Domain\Progress\CurrentProgress;
use OpenLoyalty\Achievement\Domain\Progress\ProgressProviderInterface;
use OpenLoyalty\Core\Domain\Id\AchievementRuleId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Provider\LockProviderInterface;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Infrastructure\Provider\CurrentUserProviderInterface;

final readonly class UpdateMemberAchievementProgressUseCase
{
    public function __construct(
        private CommandBusInterface $commandBus,
        private ProgressProviderInterface $progressProvider,
        private StoreContextProviderInterface $storeContextProvider,
        private CurrentUserProviderInterface $currentUserProvider,
        private LockProviderInterface $lockProvider
    ) {
    }

    public function execute(
        array $formData,
        Achievement $achievement,
        CustomerId $customerId
    ): void {
        $lockResource = [
            get_class($this),
            (string) $achievement->getAchievementId(),
            (string) $customerId,
        ];

        $this->lockProvider->acquire($lockResource, true);

        try {
            $currentProgress = $this->progressProvider->getCurrent(
                $achievement,
                $customerId,
                new CarbonImmutable()
            );

            $progressRules = $this->prepareProgressRulesToUpdate($formData, $currentProgress);

            $this->commandBus->dispatch(
                new UpdateMemberAchievementProgress(
                    $this->storeContextProvider->getStore()->getStoreId(),
                    $achievement->getAchievementId(),
                    $this->currentUserProvider->getLoggedInAdminId(),
                    $customerId,
                    $formData['completedCount'] ?? $currentProgress->getCompletedCount(),
                    new CarbonImmutable(),
                    $progressRules,
                ));
        } finally {
            $this->lockProvider->release($lockResource);
        }
    }

    private function prepareProgressRulesToUpdate(
        array $formData,
        CurrentProgress $currentProgress
    ): array {
        $progressRules = [];

        if (!isset($formData['rules'])) {
            return $progressRules;
        }

        foreach ($formData['rules'] as $formProgressRule) {
            if (!isset($formProgressRule['achievementRuleId'])) {
                continue;
            }

            foreach ($currentProgress->getMemberProgressRules() as $memberProgressRule) {
                if ((string) $memberProgressRule->getAchievementRuleId() !== $formProgressRule['achievementRuleId']) {
                    continue;
                }

                $progressRules[] = new ProgressRule(
                    new AchievementRuleId($formProgressRule['achievementRuleId']),
                    $formProgressRule['currentPeriodValue'] ?? $memberProgressRule->getCurrentPeriodValue(),
                    isset($formProgressRule['completedConsecutivePeriods']) ? (int) $formProgressRule['completedConsecutivePeriods'] : null
                );
            }
        }

        return $progressRules;
    }
}
