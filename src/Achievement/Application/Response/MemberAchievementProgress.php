<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Application\Response;

final readonly class MemberAchievementProgress
{
    public function __construct(
        private int $completedCount,
        /**
         * @var $rules array<\OpenLoyalty\Achievement\Domain\ValueObject\RuleProgress>|array<mixed>
         */
        private array $rules
    ) {
    }

    public function getCompletedCount(): int
    {
        return $this->completedCount;
    }

    /**
     * @return array<\OpenLoyalty\Achievement\Domain\ValueObject\RuleProgress>|array<mixed>
     */
    public function getRules(): array
    {
        return $this->rules;
    }
}
