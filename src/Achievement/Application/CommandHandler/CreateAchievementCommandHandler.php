<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Application\CommandHandler;

use OpenLoyalty\Achievement\Application\Command\CreateAchievement;
use OpenLoyalty\Achievement\Domain\AchievementRepositoryInterface;
use OpenLoyalty\Achievement\Domain\Exception\ActiveAchievementLimitReachedException;
use OpenLoyalty\Achievement\Domain\Factory\AchievementFactoryInterface;
use OpenLoyalty\Achievement\Domain\Factory\RuleFactoryInterface;
use OpenLoyalty\Achievement\Domain\MemberFacadeInterface;
use OpenLoyalty\Achievement\Domain\SystemEvent\AchievementWasCreated;
use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Core\Domain\StoreRepository;

final readonly class CreateAchievementCommandHandler implements CommandHandlerInterface
{
    public function __construct(
        private StoreRepository $storeRepository,
        private RuleFactoryInterface $ruleFactory,
        private AchievementFactoryInterface $achievementFactory,
        private AchievementRepositoryInterface $achievementRepository,
        private EventBusInterface $eventBus,
        private MemberFacadeInterface $memberFacade
    ) {
    }

    /**
     * @throws ActiveAchievementLimitReachedException
     */
    public function __invoke(CreateAchievement $command): void
    {
        $rules = [];
        foreach ($command->getRules() as $ruleArray) {
            $rules[] = $this->ruleFactory->create(
                $ruleArray['trigger'],
                $ruleArray['completeRule'],
                $ruleArray['aggregation'],
                $ruleArray['conditions'],
                $ruleArray['type'],
                $ruleArray['event'] ?? null,
                $ruleArray['uniqueReferee'] ?? null,
                $ruleArray['limit'] ?? null,
                $ruleArray['translations']['en']['name'] ?? null,
                $ruleArray['translations']['en']['description'] ?? null,
            );
        }

        $achievement = $this->achievementFactory->create(
            $command->getAchievementId(),
            $this->storeRepository->byId($command->getStoreId()),
            $command->getActive(),
            $command->getActivity(),
            $command->getLimit(),
            $rules
        );

        $achievement->assignTranslations($command->getTranslations());

        //create badgeType for new achievement, if badge name is null take name from achievement
        $achievementName = $achievement->getName() ?? 'Default achievement badge';
        // this will be removed, after we add badge management
        $badgeTypeId = $this->memberFacade->createBadgeType(
            $command->getStoreId(),
            $achievementName
        );
        $achievement->assignBadgeTypeId($badgeTypeId);

        $this->achievementRepository->save($achievement);

        $this->eventBus->dispatch(new AchievementWasCreated($achievement));
    }
}
