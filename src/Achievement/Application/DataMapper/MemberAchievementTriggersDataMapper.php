<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Application\DataMapper;

use DateTimeImmutable;
use OpenLoyalty\Achievement\Application\Response\MemberAchievementTrigger;
use OpenLoyalty\Core\Application\DataMapper\DataMapperInterface;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use Ramsey\Uuid\Lazy\LazyUuidFromString;

final readonly class MemberAchievementTriggersDataMapper implements DataMapperInterface
{
    /**
     * @param array<array{
     *     achievementId: AchievementId,
     *     achievementName: string,
     *     ruleReference: string,
     *     ruleName: string,
     *     triggerId: LazyUuidFromString,
     *     createdAt: DateTimeImmutable
     * }> $entities
     *
     * @return MemberAchievementTrigger[]
     */
    public function mapList(array $entities): array
    {
        $result = [];

        foreach ($entities as $entity) {
            $result[] = new MemberAchievementTrigger(
                (string) $entity['achievementId'],
                $entity['achievementName'],
                $entity['ruleReference'],
                $entity['ruleName'],
                (string) $entity['triggerId'],
                $entity['createdAt'],
            );
        }

        return $result;
    }
}
