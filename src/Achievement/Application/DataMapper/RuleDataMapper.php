<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Application\DataMapper;

use OpenLoyalty\Achievement\Application\Response\Rule as RuleResponse;
use OpenLoyalty\Achievement\Domain\Entity\Rule;
use OpenLoyalty\Core\Application\DataMapper\DataMapperInterface;

final readonly class RuleDataMapper implements DataMapperInterface
{
    public function __construct(
        private ConditionDataMapper $conditionDataMapper,
        private TranslationDataMapper $translationDataMapper
    ) {
    }

    /**
     * @param array<Rule> $rules
     *
     * @return array<RuleResponse>
     */
    public function map(array $rules): array
    {
        $rulesArray = [];
        foreach ($rules as $rule) {
            $rulesArray[] = new RuleResponse(
                $rule->getAchievementRuleId(),
                $rule->getTrigger(),
                $rule->getCompleteRule(),
                $rule->getAggregation(),
                $this->conditionDataMapper->map($rule->getConditions()),
                $rule->getType(),
                $rule->isUniqueReferee(),
                $this->translationDataMapper->map($rule->getName(), $rule->getDescription()),
                $rule->getEvent(),
                $rule->getLimit(),
            );
        }

        return $rulesArray;
    }
}
