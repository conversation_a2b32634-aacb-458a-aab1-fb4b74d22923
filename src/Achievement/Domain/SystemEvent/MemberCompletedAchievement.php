<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Domain\SystemEvent;

use DateTimeImmutable;
use OpenLoyalty\Achievement\Domain\Webhook\Response\Trigger as TriggerResponse;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\AdminId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventInterface;

final readonly class MemberCompletedAchievement implements EventInterface
{
    public function __construct(
        private StoreId $storeId,
        private AchievementId $achievementId,
        private CustomerId $customerId,
        private DateTimeImmutable $completedAt,
        private ?TriggerResponse $lastTriggerResponse = null,
        private bool $manuallyEdited = false,
        private ?AdminId $adminId = null,
        private ?int $previousCompletionCount = null,
        private ?int $currentCompletionCount = null,
    ) {
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getAchievementId(): AchievementId
    {
        return $this->achievementId;
    }

    public function getCustomerId(): CustomerId
    {
        return $this->customerId;
    }

    public function getCompletedAt(): DateTimeImmutable
    {
        return $this->completedAt;
    }

    public function getLastTriggerResponse(): ?TriggerResponse
    {
        return $this->lastTriggerResponse;
    }

    public function isManuallyEdited(): bool
    {
        return $this->manuallyEdited;
    }

    public function getAdminId(): ?AdminId
    {
        return $this->adminId;
    }

    public function getPreviousCompletionCount(): ?int
    {
        return $this->previousCompletionCount;
    }

    public function getCurrentCompletionCount(): ?int
    {
        return $this->currentCompletionCount;
    }
}
