<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Domain;

use OpenLoyalty\Achievement\Domain\Entity\Rule;
use OpenLoyalty\Core\Domain\Id\AchievementRuleId;
use OpenLoyalty\Core\Domain\Id\StoreId;

interface AchievementRuleRepositoryInterface
{
    public function isExistsActiveInStore(AchievementRuleId $achievementRuleId, StoreId $storeId): bool;

    public function findById(AchievementRuleId $achievementRuleId): ?Rule;
}
