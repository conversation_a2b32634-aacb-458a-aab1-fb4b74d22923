<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Domain\Progress\Complete;

use DateInterval;
use DatePeriod;
use DateTime;
use DateTimeImmutable;
use OpenLoyalty\Achievement\Domain\Entity\RuleProgress;
use OpenLoyalty\Achievement\Domain\RuleProgressItemRepositoryInterface;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\CompleteRule;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\Period\Type;

final class TimeCompleteRuleStrategy implements CompleteRuleStrategyInterface
{
    public function __construct(
        private readonly RuleProgressItemRepositoryInterface $ruleProgressItemRepository
    ) {
    }

    public function evaluate(
        CompleteRule $completeRule,
        RuleProgress $ruleProgress,
        DateTimeImmutable $executedAt,
        float $progressValue
    ): bool {
        $lastXDays = $completeRule->getPeriod()->getValue();
        $datePeriod = $this->calculatePeriodFromXDays($lastXDays, $executedAt);
        $memberProgress = $ruleProgress->getMemberAchievementProgress();

        if (null !== $memberProgress->getId()) {
            $progressValue += $this->ruleProgressItemRepository->getProgressValueInLastXDays(
                $ruleProgress->getRuleReference(),
                $memberProgress->getId(),
                $memberProgress->getCustomerId(),
                $datePeriod
            );
        }

        $ruleProgress->updateProgress($progressValue, $executedAt);

        if ($ruleProgress->getPeriodAggregationValue() >= $completeRule->getPeriodGoal()) {
            $ruleProgress->complete($executedAt);
        }

        return true;
    }

    public function refresh(CompleteRule $completeRule, RuleProgress $ruleProgress, DateTimeImmutable $executedAt): bool
    {
        $lastXDays = $completeRule->getPeriod()->getValue();

        $memberProgress = $ruleProgress->getMemberAchievementProgress();

        $datePeriod = $this->calculatePeriodFromXDays($lastXDays, $executedAt);

        $ruleTriggersInXDays = $this->ruleProgressItemRepository->getProgressValueInLastXDays(
            $ruleProgress->getRuleReference(),
            $memberProgress->getId(),
            $memberProgress->getCustomerId(),
            $datePeriod
        );

        if ($ruleTriggersInXDays < $ruleProgress->getPeriodQuantity()) {
            $ruleProgress->updateProgress($ruleTriggersInXDays, $executedAt);

            return true;
        }

        return false;
    }

    public function supports(CompleteRule $completeRule): bool
    {
        return $completeRule->getPeriod()->getType()->equals(Type::create(Type::LAST_DAY));
    }

    private function calculatePeriodFromXDays(int $lastXDays, DateTimeImmutable $executedAt): DatePeriod
    {
        $from = DateTime::createFromImmutable($executedAt)
            ->sub(new DateInterval('P'.$lastXDays.'D'))->setTime(0, 0);
        $from->setTime(0, 0);

        $to = DateTime::createFromImmutable($executedAt);
        $to->setTime(23, 59, 59, 999999);

        return new DatePeriod($from, new DateInterval('P1D'), $to);
    }
}
