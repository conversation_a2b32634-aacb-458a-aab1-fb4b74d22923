<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Domain\Progress\Complete;

use DateTimeImmutable;
use InvalidArgumentException;
use OpenLoyalty\Achievement\Domain\Entity\RuleProgress;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\CompleteRule;

class CompleteRuleEvaluator implements CompleteRuleEvaluatorInterface
{
    /**
     * @var array<CompleteRuleStrategyInterface>
     */
    private array $strategies = [];

    public function __construct(iterable $strategies)
    {
        foreach ($strategies as $strategy) {
            if ($strategy instanceof CompleteRuleStrategyInterface) {
                $this->strategies[] = $strategy;
            }
        }
    }

    public function evaluate(
        CompleteRule $completeRule,
        RuleProgress $ruleProgress,
        DateTimeImmutable $executedAt,
        float $progressValue
    ): bool {
        foreach ($this->strategies as $strategy) {
            if ($strategy->supports($completeRule)) {
                return $strategy->evaluate($completeRule, $ruleProgress, $executedAt, $progressValue);
            }
        }

        throw new InvalidArgumentException('Complete rule strategy is not supported');
    }

    public function refresh(
        CompleteRule $completeRule,
        RuleProgress $ruleProgress,
        DateTimeImmutable $executedAt
    ): bool {
        foreach ($this->strategies as $strategy) {
            if ($strategy->supports($completeRule)) {
                return $strategy->refresh($completeRule, $ruleProgress, $executedAt);
            }
        }

        throw new InvalidArgumentException('Complete rule strategy is not supported');
    }
}
