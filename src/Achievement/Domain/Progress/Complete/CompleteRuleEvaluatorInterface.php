<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Achievement\Domain\Progress\Complete;

use DateTimeImmutable;
use OpenLoyalty\Achievement\Domain\Entity\RuleProgress;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\CompleteRule;

interface CompleteRuleEvaluatorInterface
{
    public function evaluate(
        CompleteRule $completeRule,
        RuleProgress $ruleProgress,
        DateTimeImmutable $executedAt,
        float $progressValue
    ): bool;

    public function refresh(
        CompleteRule $completeRule,
        RuleProgress $ruleProgress,
        DateTimeImmutable $executedAt
    ): bool;
}
