<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Achievement\Domain\Progress\Aggregation;

use OpenLoyalty\Achievement\Domain\Context\Context;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\Aggregation\Aggregation;

interface AggregatorInterface
{
    public function getValue(Aggregation $aggregation, Context $context): float;

    public function supports(Aggregation $aggregation): bool;
}
