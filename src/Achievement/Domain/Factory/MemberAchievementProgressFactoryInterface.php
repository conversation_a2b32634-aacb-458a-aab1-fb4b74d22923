<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Achievement\Domain\Factory;

use DateTimeImmutable;
use OpenLoyalty\Achievement\Domain\Entity\Achievement;
use OpenLoyalty\Achievement\Domain\Entity\MemberAchievementProgress;
use OpenLoyalty\Core\Domain\Id\CustomerId;

interface MemberAchievementProgressFactoryInterface
{
    public function create(
        Achievement $achievement,
        CustomerId $customerId,
        DateTimeImmutable $createdAt,
        bool $adminEdit = false
    ): MemberAchievementProgress;
}
