<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Domain\Factory;

use DateTimeImmutable;
use OpenLoyalty\Achievement\Domain\Entity\Achievement;
use OpenLoyalty\Achievement\Domain\Entity\MemberAchievementProgress;
use OpenLoyalty\Core\Domain\Id\CustomerId;

final readonly class MemberAchievementProgressFactory implements MemberAchievementProgressFactoryInterface
{
    public function create(
        Achievement $achievement,
        CustomerId $customerId,
        DateTimeImmutable $createdAt,
        bool $adminEdit = false
    ): MemberAchievementProgress {
        return new MemberAchievementProgress(
            $achievement,
            $customerId,
            $createdAt,
            $adminEdit
        );
    }
}
