<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Domain\Factory;

use OpenLoyalty\Achievement\Domain\Entity\Achievement;
use OpenLoyalty\Achievement\Domain\ValueObject\Activity\Activity;
use OpenLoyalty\Achievement\Domain\ValueObject\Limit;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Store;

interface AchievementFactoryInterface
{
    public function create(
        AchievementId $achievementId,
        Store $store,
        bool $active,
        ?Activity $activity,
        ?Limit $limit,
        array $rules
    ): Achievement;
}
