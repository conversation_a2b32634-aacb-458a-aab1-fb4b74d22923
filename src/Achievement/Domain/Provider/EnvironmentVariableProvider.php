<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Domain\Provider;

final readonly class EnvironmentVariableProvider implements EnvironmentVariableProviderInterface
{
    public function __construct(
    private int $globalActiveAchievementLimit
    ) {
    }

    public function getGlobalActiveAchievementLimit(): int
    {
        return $this->globalActiveAchievementLimit;
    }
}
