<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\Period;

final class Period
{
    private function __construct(
        private readonly Type $type,
        private readonly ?int $consecutive,
        private readonly ?int $value
    ) {
    }

    public static function create(
        Type $type,
        ?int $consecutive = null,
        ?int $value = null
    ): self {
        return new self($type, $consecutive, $value);
    }

    public function getType(): Type
    {
        return $this->type;
    }

    public function getConsecutive(): ?int
    {
        return $this->consecutive;
    }

    public function getValue(): ?int
    {
        return $this->value;
    }
}
