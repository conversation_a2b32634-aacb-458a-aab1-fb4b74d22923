<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Domain\Webhook\Response;

use DateTimeInterface;
use OpenLoyalty\Core\Domain\ValueObject\Trigger as DomainTrigger;

final readonly class Trigger
{
    private function __construct(
        private string $triggerId,
        private string $type,
        private DateTimeInterface $createdAt,
        private ?DateTimeInterface $purchasedAt = null,
        private ?DateTimeInterface $eventDate = null,
        private ?string $documentNumber = null,
        private ?string $eventId = null,
    ) {
    }

    public static function createForTransaction(
        string $triggerId,
        DateTimeInterface $createdAt,
        DateTimeInterface $purchasedAt,
        string $documentNumber,
    ): self {
        return new self(
            $triggerId,
            DomainTrigger::TRANSACTION,
            $createdAt,
            purchasedAt: $purchasedAt,
            documentNumber: $documentNumber,
        );
    }

    public static function createForCustomEvent(
        string $triggerId,
        DateTimeInterface $createdAt,
        DateTimeInterface $eventDate,
        ?string $eventId,
    ): self {
        return new self(
            $triggerId,
            DomainTrigger::CUSTOM_EVENT,
            $createdAt,
            eventDate: $eventDate,
            eventId: $eventId,
        );
    }

    public function getTriggerId(): string
    {
        return $this->triggerId;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getPurchasedAt(): ?DateTimeInterface
    {
        return $this->purchasedAt ?? null;
    }

    public function getDocumentNumber(): ?string
    {
        return $this->documentNumber ?? null;
    }

    public function getCreatedAt(): ?DateTimeInterface
    {
        return $this->createdAt ?? null;
    }

    public function getEventDate(): ?DateTimeInterface
    {
        return $this->eventDate ?? null;
    }

    public function getEventId(): ?string
    {
        return $this->eventId ?? null;
    }
}
