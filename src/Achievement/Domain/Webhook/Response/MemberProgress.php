<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Domain\Webhook\Response;

use OpenLoyalty\Achievement\Domain\ValueObject\RuleProgress;

final readonly class MemberProgress
{
    public function __construct(
        private ?int $completedCount,
        private ?array $rules,
        private ?bool $reset
    ) {
    }

    public function getCompletedCount(): ?int
    {
        return $this->completedCount;
    }

    /**
     * @return array<RuleProgress>
     */
    public function getRules(): ?array
    {
        return $this->rules;
    }

    public function getReset(): ?bool
    {
        return $this->reset;
    }
}
