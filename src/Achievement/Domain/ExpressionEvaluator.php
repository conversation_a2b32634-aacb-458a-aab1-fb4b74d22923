<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Domain;

use DateTimeImmutable;
use OpenLoyalty\Achievement\Domain\Context\Context;
use OpenLoyalty\Achievement\Domain\Entity\Achievement;
use OpenLoyalty\Achievement\Domain\Entity\Condition;
use OpenLoyalty\Achievement\Domain\Entity\Rule;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\Aggregation\Aggregation;
use OpenLoyalty\Core\Domain\Condition\ConditionFactoryInterface;
use OpenLoyalty\Tools\ExpressionLanguage\ConditionExpressionLanguageInterface;
use OpenLoyalty\Tools\ExpressionLanguage\InvalidExpressionException;
use OpenLoyalty\Tools\ExpressionLanguage\ValueExpressionLanguageInterface;
use Psr\Log\LoggerInterface;
use function sprintf;

class ExpressionEvaluator implements ExpressionEvaluatorInterface
{
    public function __construct(
        private ConditionExpressionLanguageInterface $conditionExpressionLanguage,
        private LoggerInterface $achievementLogger,
        private ConditionFactoryInterface $conditionFactory,
        private ValueExpressionLanguageInterface $valueExpressionLanguage
    ) {
    }

    public function evalActivity(Achievement $achievement, DateTimeImmutable $currentTime): bool
    {
        $activity = $achievement->getActivity();

        if (null === $activity || null === $activity->getOperator()->getCode()) {
            return true;
        }

        $expression = $this->conditionFactory
            ->create(
                $activity->getOperator()->getCode(),
                Context::CURRENT_TIME_ITEM,
                $activity->getData()
            )
            ->getExpression();

        try {
            /* @phpstan-ignore-next-line  */
            return $this->conditionExpressionLanguage->evaluate(
                $expression,
                [
                    Context::CURRENT_TIME_ITEM => $currentTime,
                ]
            );
        } catch (InvalidExpressionException $exception) {
            $this->achievementLogger->warning(
                sprintf(
                    'Activity expression "%s" parsed failed: %s',
                    $expression,
                    $exception->getMessage()
                ),
                [
                    'achievementId' => (string) $achievement->getAchievementId(),
                ]
            );
        }

        return false;
    }

    public function evalRuleConditions(Rule $rule, Context $context): bool
    {
        $expression = $this->getJoinedConditionsDSL($rule->getConditions());
        try {
            return (bool) $this->conditionExpressionLanguage->evaluate(
                $expression,
                $context->getContextDSL()
            );
        } catch (InvalidExpressionException $exception) {
            $this->achievementLogger->warning(
                sprintf(
                    'Rule conditions expression "%s" parsed failed: %s',
                    $expression,
                    $exception->getMessage()
                ),
                [
                    'achievementId' => (string) $rule->getAchievement()->getAchievementId(),
                    'context' => $context->getContextDSL(),
                ]
            );
        }

        return false;
    }

    public function evalAggregationValue(Aggregation $aggregation, Context $context): float
    {
        try {
            return (float) $this->valueExpressionLanguage->evaluate(
                $aggregation->getRule(), //@phpstan-ignore-line
                $context->getContextDSL()
            );
        } catch (InvalidExpressionException $exception) {
            $this->achievementLogger->warning(
                sprintf(
                    'Aggregation value expression "%s" parsed failed: %s',
                    $aggregation->getRule(),
                    $exception->getMessage()
                ),
                [
                    'context' => $context->getContextDSL(),
                ]
            );
        }

        return 0.0;
    }

    private function getJoinedConditionsDSL(array $conditions): string
    {
        $conditionDSLParts = array_map(
            fn (Condition $condition) => $this->conditionFactory->create(
                $condition->getOperator()->getCode(),
                $condition->getAttribute(),
                $condition->getData()
            )->getExpression(),
            $conditions
        );

        if (empty($conditionDSLParts)) {
            return 'true';
        }

        return implode(' and ', $conditionDSLParts);
    }
}
