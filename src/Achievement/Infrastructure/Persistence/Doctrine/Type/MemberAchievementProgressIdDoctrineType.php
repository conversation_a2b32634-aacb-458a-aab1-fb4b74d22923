<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Infrastructure\Persistence\Doctrine\Type;

use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\ConversionException;
use Doctrine\DBAL\Types\IntegerType;
use OpenLoyalty\Core\Domain\Id\MemberAchievementProgressId;

final class MemberAchievementProgressIdDoctrineType extends IntegerType
{
    public const NAME = 'member_achievement_progress_id';

    public function convertToPHPValue($value, AbstractPlatform $platform): ?MemberAchievementProgressId
    {
        if (empty($value)) {
            return null;
        }

        if ($value instanceof MemberAchievementProgressId) {
            return $value;
        }
        if (!is_int($value)) {
            throw ConversionException::conversionFailed($value, self::NAME);
        }

        return new MemberAchievementProgressId($value);
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value instanceof MemberAchievementProgressId) {
            return (string) $value;
        }

        if (!empty($value)) {
            return $value;
        }

        return null;
    }

    public function getName(): string
    {
        return self::NAME;
    }

    public function requiresSQLCommentHint(AbstractPlatform $platform): bool
    {
        return true;
    }
}
