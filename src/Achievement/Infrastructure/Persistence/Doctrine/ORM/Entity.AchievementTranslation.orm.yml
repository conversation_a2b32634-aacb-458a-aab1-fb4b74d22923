OpenLoyalty\Achievement\Domain\Entity\AchievementTranslation:
    type: entity
    cache:
        usage: NONSTRICT_READ_WRITE
        region: entity_translations
    id:
        id:
            type: integer
            generator:
                strategy: AUTO
    fields:
        name:
            type: string
            nullable: true
        description:
            type: text
            nullable: true
            column: description
    manyToOne:
        translatable:
            targetEntity: OpenLoyalty\Achievement\Domain\Entity\Achievement
            inversedBy: translations
            cascade: ['persist', 'merge']
            joinColumn:
                name: translatable_id
                nullable: false
                referencedColumnName: achievement_id
                onDelete: CASCADE

