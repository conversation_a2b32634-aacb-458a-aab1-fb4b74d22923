<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Infrastructure\Form\Type;

use OpenLoyalty\Achievement\Domain\ValueObject\Type;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
class TypeFormType extends AbstractType implements DataTransformerInterface
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->addModelTransformer($this);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'required' => true,
            'constraints' => [new NotBlank()],
            'choices' => Type::ALLOWED_CODES,
        ]);
    }

    public function getParent(): string
    {
        return ChoiceType::class;
    }

    public function transform(mixed $value): ?array
    {
        return $value;
    }

    public function reverseTransform(mixed $value): ?Type
    {
        if (null === $value) {
            return null;
        }

        return Type::create($value);
    }
}
