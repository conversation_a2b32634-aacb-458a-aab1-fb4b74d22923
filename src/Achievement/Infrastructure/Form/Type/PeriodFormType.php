<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Infrastructure\Form\Type;

use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\Period\Period;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\Period\Type;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Event\PreSubmitEvent;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Range;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
class PeriodFormType extends AbstractType implements DataTransformerInterface
{
    private const CONSECUTIVE_MIN_VALUE = 1;
    private const CONSECUTIVE_MAX_VALUE = **********;
    private const MIN_X_LAST_DAY = 1;
    private const MAX_X_LAST_DAY = 60;

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('type', PeriodTypeFormType::class);
        $builder->addModelTransformer($this);

        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (PreSubmitEvent $event): void {
            $data = $event->getData();
            $form = $event->getForm();

            if (!isset($data['type'])) {
                return;
            }

            if (Type::OVERALL === $data['type']) {
                return;
            }

            if (Type::LAST_DAY === $data['type']) {
                $form->add(
                    'value',
                    IntegerType::class,
                    [
                        'constraints' => [
                            new NotBlank(),
                            new Range(['min' => self::MIN_X_LAST_DAY, 'max' => self::MAX_X_LAST_DAY]),
                        ],
                    ]
                );

                return;
            }

            $form->add(
                'consecutive',
                IntegerType::class,
                [
                    'constraints' => [
                        new NotBlank(),
                        new Range(['min' => self::CONSECUTIVE_MIN_VALUE, 'max' => self::CONSECUTIVE_MAX_VALUE]),
                    ],
                ]
            );
        });
    }

    public function transform(mixed $value): ?array
    {
        return $value;
    }

    public function reverseTransform(mixed $value): ?Period
    {
        if (!isset($value['type'])) {
            return null;
        }

        return Period::create($value['type'], $value['consecutive'] ?? null, $value['value'] ?? null);
    }
}
