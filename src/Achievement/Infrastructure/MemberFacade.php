<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Infrastructure;

use DateTimeImmutable;
use OpenLoyalty\Achievement\Domain\MemberFacadeInterface;
use OpenLoyalty\Achievement\Domain\ValueObject\Address;
use OpenLoyalty\Achievement\Domain\ValueObject\MemberData;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\BadgeTypeId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\User\Domain\CustomerGateInterface;

final readonly class MemberFacade implements MemberFacadeInterface
{
    public function __construct(
        private CustomerGateInterface $customerGate
    ) {
    }

    public function getMember(CustomerId $memberId): MemberData
    {
        $domainCustomer = $this->customerGate->getCustomer($memberId);

        return new MemberData(
            $domainCustomer->getCustomerId(),
            $domainCustomer->getRegisteredAt(),
            $domainCustomer->getFirstName(),
            $domainCustomer->getLastName(),
            $domainCustomer->getEmail(),
            $domainCustomer->getPhone(),
            $domainCustomer->getBirthDate(),
            new Address(
                $domainCustomer->getAddress()->getStreet(),
                $domainCustomer->getAddress()->getAddress1(),
                $domainCustomer->getAddress()->getAddress2(),
                $domainCustomer->getAddress()->getProvince(),
                $domainCustomer->getAddress()->getCity(),
                $domainCustomer->getAddress()->getPostal(),
                $domainCustomer->getAddress()->getCountry(),
            ),
            $domainCustomer->getLoyaltyCardNumber(),
            $domainCustomer->getGender()->getType(),
            (string) $domainCustomer->getLevelId(),
            $domainCustomer->getFirstTransactionDate(),
            $domainCustomer->getLastTransactionDate(),
            $domainCustomer->getLevelAchievementDate(),
            $domainCustomer->getTransactionsCount(),
            $domainCustomer->getTransactionsAmount(),
            $domainCustomer->getAverageTransactionAmount(),
            $domainCustomer->getLabels(),
            (string) $domainCustomer->getReferrerCustomerId(),
            $domainCustomer->isAgreement1(),
            $domainCustomer->isAgreement2(),
            $domainCustomer->isAgreement3(),
        );
    }

    public function getDateOfLatestAchievementProgressEditionByAdmin(
        StoreId $storeId,
        CustomerId $customerId,
        AchievementId $achievementId,
        string $achievementName
    ): ?DateTimeImmutable {
        return $this->customerGate->getDateOfLatestAchievementProgressEditionByAdmin($storeId, $customerId, $achievementId, $achievementName);
    }

    public function createBadgeType(StoreId $storeId, string $name): BadgeTypeId
    {
        return $this->customerGate->createBadgeType($storeId, $name);
    }
}
