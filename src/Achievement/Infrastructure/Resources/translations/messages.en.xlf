<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="en" target-language="en" datatype="plaintext" original="file.ext">
        <body>
            <trans-unit id="achievement.invalid_activity">
                <source>achievement.invalid_activity</source>
                <target>Invalid activity (operator or data).</target>
            </trans-unit>
            <trans-unit id="achievement.required_trigger">
                <source>achievement.required_trigger</source>
                <target>Trigger is required.</target>
            </trans-unit>

            <trans-unit id="achievement.global_active_achievement_limit_reached">
                <source>achievement.global_active_achievement_limit_reached</source>
                <target>You have reached the global limit of {{ limit }} active achievements. To activate this achievement, please deactivate existing ones or contact your Customer Success Manager or the support <NAME_EMAIL>.</target>
            </trans-unit>
            <trans-unit id="achievement.editing_achievement_progress_completion_count.not_null">
                <source>achievement.editing_achievement_progress_completion_count.not_null</source>
                <target>Completed count should not be null</target>
            </trans-unit>
            <trans-unit id="achievement.editing_achievement_progress_completion_count.positive_or_zero">
                <source>achievement.editing_achievement_progress_completion_count.positive_or_zero</source>
                <target>Completed count should be positive number or zero</target>
            </trans-unit>
            <trans-unit id="achievement.editing_achievement_rule_progress.positive_or_zero">
                <source>achievement.editing_achievement_rule_progress.positive_or_zero</source>
                <target>Progress should be positive number or zero.</target>
            </trans-unit>
            <trans-unit id="achievement.editing_achievement_progress_completion_count.limit_reached">
                <source>achievement.editing_achievement_progress_completion_count.limit_reached</source>
                <target>Completed count should not be greater than limit.</target>
            </trans-unit>
            <trans-unit id="achievement.editing_achievement_progress.inactive_achievement">
                <source>achievement.editing_achievement_progress.inactive_achievement</source>
                <target>Edited achievement progress must be active.</target>
            </trans-unit>
            <trans-unit id="achievement.editing_achievement_progress.extra_rules">
                <source>achievement.editing_achievement_progress.extra_rules</source>
                <target>Rules that cannot be edited have been received: {{ ruleIds }}</target>
            </trans-unit>
            <trans-unit id="achievement.editing_achievement_progress.value_over_limit">
                <source>achievement.editing_achievement_progress.value_over_limit</source>
                <target>This value must be less than or equal to {{ limit }}</target>
            </trans-unit>
            <trans-unit id="achievement.editing_achievement_progress.completed_period_over_limit">
                <source>achievement.editing_achievement_progress.completed_period_over_limit</source>
                <target>Current progress should not be greater than configured period goal: {{ maxValue }}</target>
            </trans-unit>
            <trans-unit id="achievement.editing_achievement_rule_progress.consecutive_progress_for_not_consecutive_rule">
                <source>achievement.editing_achievement_rule_progress.consecutive_progress_for_not_consecutive_rule</source>
                <target>Consecutive Progress should be defined only for consecutive rules.</target>
            </trans-unit>
            <trans-unit id="achievement.editing_achievement_progress_completion_count.single_change_limit_reached">
                <source>achievement.editing_achievement_progress_completion_count.single_change_limit_reached</source>
                <target>Completion status changes are limited to a max of {{ limit }} units per operation. For larger adjustments, please perform multiple steps.</target>
            </trans-unit>
            <trans-unit id="achievement.criteria.invalid_achievement_rule_id">
                <source>achievement.criteria.invalid_achievement_rule_id</source>
                <target>Provided achievement rule is not from provided achievement.</target>
            </trans-unit>
        </body>
    </file>
</xliff>
