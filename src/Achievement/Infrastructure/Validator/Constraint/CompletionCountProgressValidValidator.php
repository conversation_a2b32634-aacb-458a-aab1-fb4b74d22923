<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Infrastructure\Validator\Constraint;

use OpenLoyalty\Achievement\Domain\MemberAchievementProgressRepositoryInterface;
use OpenLoyalty\Achievement\Domain\Validator\UpdateAchievementProgress\UpdatedCompletionCountValidatorInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

final class CompletionCountProgressValidValidator extends ConstraintValidator
{
    public function __construct(
        private readonly TranslatorInterface $translator,
        private readonly MemberAchievementProgressRepositoryInterface $achievementProgressRepository,
        private readonly int $maxSingleChangeOfAchievementCompletionCount,
        private readonly UpdatedCompletionCountValidatorInterface $updatedCompletionCountValidator,
    ) {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof CompletionCountProgressValid) {
            return;
        }

        $currentCompletionCount = $this->achievementProgressRepository->getCompletedByLimitPeriod($constraint->achievementId, $constraint->memberId, null);

        $changeDelta = abs($currentCompletionCount - $value);

        if ($changeDelta > $this->maxSingleChangeOfAchievementCompletionCount) {
            $this->context
                ->buildViolation($this->translator->trans('achievement.editing_achievement_progress_completion_count.single_change_limit_reached'))
                ->setParameter('{{ limit }}', (string) $this->maxSingleChangeOfAchievementCompletionCount)
                ->addViolation()
            ;
        }

        // admin can't exceed total limit
        if (!$this->updatedCompletionCountValidator->isAchievementLimitsValidToEditCompletionCount($value, $constraint->achievementId)) {
            $this->context
                ->buildViolation($this->translator->trans('achievement.editing_achievement_progress_completion_count.limit_reached'))
                ->addViolation()
            ;
        }
    }
}
