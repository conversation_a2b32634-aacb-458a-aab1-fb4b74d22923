<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Infrastructure\Validator\Constraint;

use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use Symfony\Component\Validator\Constraint;

final class CanBeActive extends Constraint
{
    public ?StoreId $storeId;
    public ?AchievementId $achievementId = null;

    public function getDefaultOption(): string
    {
        return 'storeId';
    }

    public function getRequiredOptions(): array
    {
        return ['storeId'];
    }
}
