<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Infrastructure\Validator\Constraint;

use ArgumentCountError;
use OpenLoyalty\Core\Domain\Condition\ConditionFactoryInterface;
use OpenLoyalty\Core\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Core\Domain\Model\Condition;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\CustomEvent\Domain\CustomEventSchemaRepositoryInterface;
use OpenLoyalty\CustomEvent\Infrastructure\Service\SchemaMocker;
use OpenLoyalty\Tools\ExpressionLanguage\ConditionExpressionLanguageInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Contracts\Translation\TranslatorInterface;
use Throwable;

class ConditionValidValidator extends ExpressionConstraintValidator
{
    public function __construct(
        private readonly ConditionFactoryInterface $conditionFactory,
        private readonly ConditionExpressionLanguageInterface $conditionExpressionLanguage,
        private readonly TranslatorInterface $translator,
        CustomEventSchemaRepositoryInterface $customEventSchemaRepository,
        StoreContextProviderInterface $storeContextProvider,
        SchemaMocker $schemaMocker
    ) {
        parent::__construct(
            $customEventSchemaRepository,
            $storeContextProvider,
            $schemaMocker
        );
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$value instanceof Condition) {
            return;
        }

        if ($constraint instanceof ConditionValid && null === $constraint->trigger) {
            $this->context->buildViolation($this->translator->trans('achievement.required_trigger'))->addViolation();

            return;
        }

        try {
            $conditionData = $value->getData();
            $condition = $this->conditionFactory->create(
                (string) $value->getOperator(),
                $value->getAttribute(),
                reset($conditionData)
            );
        } catch (InvalidConditionException) {
            $this->context->buildViolation(
                $this->translator->trans('core.invalid_condition')
            )->addViolation();

            return;
        }

        try {
            $this->conditionExpressionLanguage->evaluate(
                $condition->getExpression(),
                $this->getExpressionContext($constraint)->getContextDSL(),
            );
        } catch (ArgumentCountError) {
            $this->context->buildViolation(
                sprintf(
                    '%s Function: %s',
                    $this->translator->trans('core.invalid_expression_function'),
                    $condition->getExpression()
                ),
                ['expression' => $condition->getExpression()]
            )->addViolation();
        } catch (Throwable $exception) {
            $this->context->buildViolation(
                $exception->getMessage(),
                ['expression' => $condition->getExpression()]
            )->addViolation();
        }
    }
}
