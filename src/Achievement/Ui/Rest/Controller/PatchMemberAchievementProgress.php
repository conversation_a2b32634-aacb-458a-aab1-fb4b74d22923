<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Achievement\Application\UseCase\UpdateMemberAchievementProgressUseCase;
use OpenLoyalty\Achievement\Domain\Entity\Achievement;
use OpenLoyalty\Achievement\Infrastructure\Form\Type\UpdateMemberAchievementProgressFormType;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\User\Domain\Customer;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class PatchMemberAchievementProgress extends AbstractFOSRestController
{
    public function __construct(
        private readonly FormFactoryInterface $formFactory,
        private readonly StoreContextProviderInterface $storeContextProvider,
        private readonly UpdateMemberAchievementProgressUseCase $useCase,
    ) {
    }

    /**
     * @Route(methods={"PATCH"}, name="oloy.achievement.put_member_achievement_progress", path="/{storeCode}/member/{member}/achievement/{achievement}/progress", requirements={"member"="%routing.member-query%"})
     *
     * @Security("is_granted('UPDATE_CAMPAIGN')")
     */
    public function __invoke(Request $request, Customer $member, Achievement $achievement): View
    {
        $form = $this->formFactory->createNamed(
            'memberProgress',
            UpdateMemberAchievementProgressFormType::class,
            [],
            [
                'achievementId' => $achievement->getAchievementId(),
                'method' => Request::METHOD_PATCH,
                'store' => $this->storeContextProvider->getStore(),
                'memberId' => $member->getCustomerId(),
            ]
        );

        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        $this->useCase->execute($form->getData(), $achievement, $member->getCustomerId());

        return $this->view();
    }
}
