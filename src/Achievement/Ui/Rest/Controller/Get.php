<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\Controller\Annotations\View;
use FOS\RestBundle\View\View as FosView;
use OpenLoyalty\Achievement\Application\UseCase\GetAchievementUseCase;
use OpenLoyalty\Achievement\Domain\Entity\Achievement;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;

class Get extends AbstractFOSRestController
{
    private GetAchievementUseCase $useCase;

    public function __construct(GetAchievementUseCase $useCase)
    {
        $this->useCase = $useCase;
    }

    /**
     * @Route(methods={"GET"}, name="oloy.achievement.get", path="/{storeCode}/achievement/{achievement}", requirements={"achievement"="%routing.uuid%"})
     *
     * @Security("is_granted('GET_CAMPAIGN')")
     *
     * @View(serializerGroups={"admin", "Default"}, template="")
     */
    public function __invoke(Request $request, Achievement $achievement): FosView
    {
        return $this->view($this->useCase->execute($achievement));
    }
}
