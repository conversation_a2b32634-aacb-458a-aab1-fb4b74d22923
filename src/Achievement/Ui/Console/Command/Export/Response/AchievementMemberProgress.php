<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Ui\Console\Command\Export\Response;

use DateTimeImmutable;
use OpenLoyalty\Achievement\Domain\ValueObject\RuleProgress;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;

final readonly class AchievementMemberProgress
{
    public function __construct(
        private AchievementId $achievementId,
        private string $achievementName,
        private ?string $achievementDescription,
        private CustomerId $customerId,
        private bool $limitReached,
        private int $completedCount,
        /**
         * @var $rules array<RuleProgress>
         */
        private array $memberProgressRules,
        private DateTimeImmutable $createdAt,
        private ?DateTimeImmutable $completedAt,
        private StoreId $tenantId,
    ) {
    }

    public function getAchievementId(): AchievementId
    {
        return $this->achievementId;
    }

    public function getCustomerId(): CustomerId
    {
        return $this->customerId;
    }

    public function getCompletedAt(): ?DateTimeImmutable
    {
        return $this->completedAt;
    }

    public function getMemberProgressRules(): array
    {
        return $this->memberProgressRules;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getAchievementName(): string
    {
        return $this->achievementName;
    }

    public function getAchievementDescription(): ?string
    {
        return $this->achievementDescription;
    }

    public function getCompletedCount(): int
    {
        return $this->completedCount;
    }

    public function isLimitReached(): bool
    {
        return $this->limitReached;
    }

    public function getTenantId(): StoreId
    {
        return $this->tenantId;
    }
}
