<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Achievement\Ui\Console\Command;

use Carbon\Carbon;
use Carbon\CarbonImmutable;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Faker\Factory;
use Faker\Generator;
use InvalidArgumentException;
use OpenLoyalty\Achievement\Application\Command\UpdateMemberAchievementProgress;
use OpenLoyalty\Achievement\Application\DTO\ProgressRule;
use OpenLoyalty\Achievement\Domain\AchievementRepositoryInterface;
use OpenLoyalty\Achievement\Domain\Entity\Achievement;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\User\Infrastructure\Entity\Customer;
use OpenLoyalty\User\Infrastructure\Service\UserManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class CreateRandomAchievementsProgressCommand extends Command
{
    private const MAX_RANDOM_CUSTOMERS = 5000;

    public function __construct(
        private readonly CommandBusInterface $commandBus,
        private readonly EntityManagerInterface $entityManager,
        private readonly StoreRepository $storeRepository,
        private readonly UserManager $userManager,
        private readonly AchievementRepositoryInterface $achievementRepository,
    ) {
        parent::__construct();
    }

    public function configure(): void
    {
        $this
            ->setName('oloy:achievement:create-random-achievements-progress')
            ->setDescription('Create random achievements progress')
            ->addArgument('storeCode')
            ->addArgument('numberOfProgresses');
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $faker = Factory::create();

        if (!is_string($input->getArgument('storeCode'))) {
            throw new InvalidArgumentException('store code should be a string');
        }
        $store = $this->storeRepository->byCode($input->getArgument('storeCode'));
        if (null === $store) {
            throw new InvalidArgumentException('store code not found');
        }
        $achievements = $this->achievementRepository->findAllActive($store->getStoreId());
        /** @var int $numberOfProgresses */
        $numberOfProgresses = $input->getArgument('numberOfProgresses');
        $maxUserResultNumber = (int) min(self::MAX_RANDOM_CUSTOMERS, $numberOfProgresses);
        $customers = $this->userManager->getRandomActiveCustomers($store->getStoreId(), $maxUserResultNumber);

        for ($c = 0; $c < $numberOfProgresses; ++$c) {
            /** @var Customer $customer */
            $customer = array_shift($customers);

            if (!$customer) {
                $customers = $this->userManager->getRandomActiveCustomers($store->getStoreId(), $maxUserResultNumber);
                $customer = array_shift($customers);
            }

            $achievement = $achievements[array_rand($achievements)];
            $achievementId = $achievement->getAchievementId();

            $this->commandBus->dispatch(
                new UpdateMemberAchievementProgress(
                    $store->getStoreId(),
                    $achievementId,
                    null,
                    new CustomerId($customer->getId()),
                    $faker->numberBetween(1, 10),
                    new DateTimeImmutable(),
                    $this->prepareProgressRulesToUpdate($achievement, $faker),
                ),
            );

            CarbonImmutable::setTestNow();
            Carbon::setTestNow();
        }

        $output->writeln('achievements were created');

        return self::SUCCESS;
    }

    /**
     * @param  Achievement    $achievement
     * @param  Generator      $faker
     * @return ProgressRule[]
     */
    private function prepareProgressRulesToUpdate(
        Achievement $achievement,
        Generator $faker
    ): array {
        $progressRules = [];

        foreach ($achievement->getRules() as $rule) {
            $progressRules[] = new ProgressRule(
                $rule->getAchievementRuleId(),
                $faker->numberBetween(1, $rule->getCompleteRule()->getPeriodGoal()),
                null,
            );
        }

        return $progressRules;
    }
}
