---
description: 
globs: *.php
alwaysApply: false
---
---
Rule Type: Auto Attached
globs: '**/*.php'
---

# PHP Rules

## File Structure

- One class per file
- Namespace declaration at the top
- Use statements after namespace
- Strict type declaration
- Class name matches file name
- PSR-4 autoloading compliance

## Class Organization

- Constants first
- Properties second
- Constructor third
- Public methods
- Protected methods
- Private methods last

# Code Quality

- PHPStan level: max
- PHP-CS-Fixer rules: @Symfony
- All code must follow PSR-12 coding standards

## Error Handling

- Use custom exceptions
- Proper exception hierarchy
- Meaningful error messages
- Log errors appropriately
- Handle edge cases
- Validate input data
- Graceful degradation

## Security

- Input validation
- Output escaping
- SQL injection prevention
- XSS prevention
- CSRF protection
- Authentication checks
- Authorization checks

## Performance

- Avoid unnecessary loops
- Use appropriate data structures
- Cache expensive operations
- Lazy loading where appropriate
- Optimize database queries
- Monitor memory usage
- Profile critical paths

## Other Rules

- Use strict typing in PHP files
- Use `declare(strict_types=1);` in classes
- All new code must have unit tests
- Integration tests for critical paths
- Use `(string) $object` instead of `$object->__toString()`
- Add fields to forms using the `$builder->add()` method, use the $builder->create() method only for own/custom types
- Always add the suffix Interface in interfaces, Trait in traits and the prefix Abstract in abstract classes
- Use DateTimeImmutable instead of DateTime or DateTimeInterface
- Compare identifiers by casting them to a string: `(string) $object->getId() == (string) $object2->getId()`
- Add a copyright signature: 
```
/* 
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details. 
 */
 ```
 to each file immediately after the `<?php` tag
