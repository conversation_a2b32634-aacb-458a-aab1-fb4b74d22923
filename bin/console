#!/usr/bin/env php
<?php

use OpenLoyalty\Application;
use OpenLoyalty\Kernel;

//We override the global function from the vendor to suppress deprecated errors in Sentry.
$appEnv = getenv('APP_ENV');
if ('prod' === $appEnv || 'prod_rabbit' === $appEnv) {
    require_once dirname(__DIR__).'/src/Utility/Infrastructure/OverrideTriggerDeprecation.php';
}

if (!is_dir(dirname(__DIR__).'/vendor')) {
    throw new LogicException('Dependencies are missing. Try running "composer install".');
}

if (!is_file(dirname(__DIR__).'/vendor/autoload_runtime.php')) {
    throw new LogicException('Symfony Runtime is missing. Try running "composer require symfony/runtime".');
}

require_once dirname(__DIR__).'/vendor/autoload_runtime.php';

return function (array $context) {
    $errorHandler = \Sentry\ErrorHandler::registerOnceFatalErrorHandler();
    $errorHandler->setMemoryLimitIncreaseOnOutOfMemoryErrorInBytes(15728640); // 15 * 1024 * 1024 = 15728640 = 15MB

    $kernel = new Kernel($context['APP_ENV'], (bool) $context['APP_DEBUG']);

    return new Application($kernel);
};

