{% extends '@!NelmioApiDoc/SwaggerUi/index.html.twig' %}

{% block javascripts %}
    {{ parent() }}
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <script>
        function modalHide() {
            $('#login-modal').hide();
            $('#login-modal').find('input,select').each(function() {
                $(this).val('');
            });
        }

        function changeType() {
            if ($('#login_user_type').val() === 'customer') {
                $('#store_section').removeClass('hidden');
            } else {
                if (!$('#store_section').hasClass('hidden')) {
                    $('#store_section').addClass('hidden')
                }
            }
        }

        function appendTokenHeader(token) {
            const storageKey = 'nelmio_api_auth';

            sessionStorage.setItem(
                storageKey,
                JSON.stringify(
                    {
                        'Bearer': {
                            'name': 'Bearer',
                            'schema': {
                                'description': 'Value: Bearer {jwt}',
                                'in': 'header',
                                'name': 'Authorization',
                                'type': 'apiKey'
                            },
                            'value': 'Bearer ' + token
                        }
                    }
                )
            );

            // if we have auth in storage use it
            if (sessionStorage.getItem(storageKey)) {
                try {
                    ui.authActions.authorize(JSON.parse(sessionStorage.getItem(storageKey)));
                } catch (ignored) {
                    // ignore error
                }
            }
        }

        $(document).ready(function () {
            $(function() {
                let token = localStorage.getItem('api_token_jwt');
                if (token) {
                    appendTokenHeader(token);
                }

                setTimeout(function(){
                     $('.auth-wrapper').each(function() {
                            let sandbox = $(this);
                            let button = $('<button type="button" class="btn authorize unlocked"><span>Login</span></button>');
                            button.click(function() {
                                $('#login-modal').show();
                            });
                            $(sandbox).append(button);
                        });
                    }, 1000);
                });
        });
    </script>

    <script>
        function getUrl() {
            if ($('#login_user_type').val() === 'customer') {
                return '/api/'+$('#login_store').val()+'/'+$('#login_user_type').val()+'/login_check';
            }

            return '/api/'+$('#login_user_type').val()+'/login_check';
        }
        $('#login-button').click(function() {
            $.ajax({
                url: getUrl(),
                type: 'POST',
                dataType: 'json',
                data: {
                    username: $('#login_login').val(),
                    password: $('#login_password').val(),
                },
                success: function(response) {
                    if (!response.token) {
                        alert('wrong credentials');
                    } else {
                        window.token = response.token;
                        localStorage.setItem("api_token_jwt", window.token);

                        modalHide();
                        appendTokenHeader(window.token);
                    }
                },
                error: function() {
                    alert('wrong credentials');
                }
            })
        });
    </script>
{% endblock javascripts %}

{% block header %}
<style>
    .hidden {display: none;}
</style>
{% endblock header %}

{% block swagger_ui %}
    <div class="motd">
        <div class="modal fade" tabindex="-1" role="dialog" id="login-modal" style="display: none; position: fixed;
            left: calc(50% - 300px);
            top: 100px;
            z-index: 10000;
            width: 600px;
            background-color: white;
            padding: 20px;
            border: 1px solid black;
        }">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" style="position: absolute; right: 0; top: 0;" data-dismiss="modal" aria-label="Close" onclick="modalHide()"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title">Login</h4>
                    </div>
                    <div class="modal-body" style="margin-top: 20px;">
                        <table style="border: none; margin: 0 auto;">
                            <tr>
                                <td colspan="2" style="text-align: left;">
                                    <label class="control-label">User type</label>
                                </td>
                                <td colspan="2" style="text-align: right;">
                                    <select id="login_user_type" style="width: 100%;" onchange="changeType()">
                                        <option value="admin">Admin</option>
                                        <option value="customer">Customer</option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="store_section" class="hidden">
                                <td style="text-align: left;"></td>
                                <td style="text-align: right;"></td>
                                <td style="text-align: left;">
                                    <label class="control-label">Store</label>
                                </td>
                                <td style="text-align: right;">
                                    <input type="text" id="login_store"/>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: left;">
                                    <label class="control-label">Login</label>
                                </td>
                                <td style="text-align: right;">
                                    <input type="text" id="login_login"/>
                                </td>
                                <td style="text-align: left;">
                                    <label class="control-label">Password</label>
                                </td>
                                <td style="text-align: right;">
                                    <input type="password" id="login_password"/>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal" onclick="modalHide()">Close</button>
                        <button type="button" class="btn btn-primary" id="login-button">Login</button>
                    </div>
                </div><!-- /.modal-content -->
            </div><!-- /.modal-dialog -->
        </div><!-- /.modal -->
    </div>

    {{ parent() }}
{% endblock swagger_ui %}

