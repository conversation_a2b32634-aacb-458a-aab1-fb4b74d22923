#!/usr/bin/env bash

pushd "$( dirname $0 )" > /dev/null
CURDIR=$( pwd -P )

## build base images
docker build $1 -t openloyalty/base-nginx:1.27 -f nginx-dockerfile $CURDIR
docker build $1 -t openloyalty/base-php-fpm:8.3 -f php-fpm-dockerfile $CURDIR
docker build $1 -t openloyalty/base-php-cli:8.3 -f php-cli-dockerfile $CURDIR
docker build $1 -t openloyalty/base-php-fpm-dev:5.8.1 -f php-fpm-dev-dockerfile $CURDIR
docker build $1 -t openloyalty/base-php-cli-dev:5.8.1 -f php-cli-dev-dockerfile $CURDIR

popd > /dev/null
