 FROM openloyalty/base-php-fpm-dev:5.8.1 AS base

COPY ./docker/prod/php/conf/php-fpm-pool.conf /usr/local/etc/php-fpm.d/www.conf
COPY ./docker/dev/php/conf/xdebug.ini /usr/local/etc/php/conf.d/xdebug.ini

RUN mv "$PHP_INI_DIR/php.ini-development" "$PHP_INI_DIR/php.ini"
COPY ./docker/dev/php/conf/php.ini "$PHP_INI_DIR/conf.d/application-php.ini"
RUN echo memory_limit=512M > "$PHP_INI_DIR/conf.d/x-php.ini"
