version: "3"

services:
  php:
    container_name: ${COMPOSE_PROJECT_NAME}_open_loyalty_framework_backend
    build:
      context: '../'
      dockerfile: './docker/dev/php/fpm-dockerfile'
    links:
      - db
      - mail
      - queue
      - redis
      - minio
    ports:
      - "9000:9000"
      - "9100:9100"
    volumes:
      - '../:/var/www/openloyalty'
    env_file:
      - .env
    ulimits:
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
  worker:
    container_name: ${COMPOSE_PROJECT_NAME}_open_loyalty_framework_worker
    build:
      context: '../'
      dockerfile: './docker/dev/php/worker-dockerfile'
    links:
      - db
      - mail
      - queue
      - redis
    volumes:
      - '../:/var/www/openloyalty'
    env_file:
      - .env
    environment:
      - ENABLED_CRON=true
  api:
    container_name: ${COMPOSE_PROJECT_NAME}_open_loyalty_framework_api
    build:
      context: '../'
      dockerfile: './docker/dev/web/api-dockerfile'
    links:
      - php
    ports:
      - "80:80"
    volumes:
      - '../public/:/var/www/openloyalty/public'
  db:
    container_name: ${COMPOSE_PROJECT_NAME}_open_loyalty_framework_db
    image: postgres:14.12
    ports:
      - "5432:5432"
    env_file:
        - .env
    volumes:
      - ./pgbouncer/init-pgbouncer.sql:/docker-entrypoint-initdb.d/init-pgbouncer.sql
    command: postgres -c shared_preload_libraries=pg_stat_statements -c pg_stat_statements.track=all -c max_connections=64
  queue:
    container_name: ${COMPOSE_PROJECT_NAME}_open_loyalty_framework_queue
    image: rabbitmq:3.13-management
    ports:
      - "8672:15672"
  mail:
    container_name: ${COMPOSE_PROJECT_NAME}_open_loyalty_framework_mail
    image: mailhog/mailhog
    ports:
      - "8186:8025"
  redis:
    container_name: ${COMPOSE_PROJECT_NAME}_open_loyalty_framework_redis
    image: redis:7.4
    ports:
      - "6379:6379"
  minio:
    container_name: ${COMPOSE_PROJECT_NAME}_open_loyalty_framework_minio
    build:
      context: '../'
      dockerfile: './docker/dev/minio-dockerfile'
    volumes:
      - minio_data:/data
    ports:
      - "9001:9000" # MinIO API (S3 Compatible)
      - "9090:9090" # MinIO Console (Web UI)

volumes:
  minio_data:
