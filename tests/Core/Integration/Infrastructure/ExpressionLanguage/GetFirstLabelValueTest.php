<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Integration\Infrastructure\ExpressionLanguage;

use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class GetFirstLabelValueTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_add_points_for_member_from_campaign_with_custom_attributes_expression(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Custom attributes campaign',
                        ],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => 'transaction',
                    'activity' => [
                        'startsAt' => '2020-12-06 00:00',
                        'endsAt' => null,
                    ],
                    'rules' => [
                        [
                            'conditions' => [
                                [
                                    'operator' => 'expression',
                                    'data' => 'to_date(agg(customer.labels).getFirstLabelValue(\'post_date\')) > to_date(\'2022-12-06T00:00:00+01:00\')',
                                ],
                            ],
                            'effects' => [
                                [
                                    'effect' => 'give_points',
                                    'pointsRule' => 10,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'Robin',
                    'lastName' => 'Locksley',
                    'email' => '<EMAIL>',
                    'birthDate' => '1991-06-16',
                    'labels' => [
                        [
                            'key' => 'post_date',
                            'value' => '2022-12-06T12:15:00+01:00',
                        ],
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        self::assertSame(0.0, $this->getCustomerActivePoints($customerId));

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => 'TEST/CA/01',
                        'documentType' => 'sell',
                        'purchasedAt' => '2022-12-06T15:00:00+01:00',
                    ],
                    'items' => [
                        [
                            'sku' => '123',
                            'name' => 'SKU123',
                            'quantity' => 1,
                            'grossValue' => 150,
                            'category' => 'Christmas gift',
                        ],
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        self::assertSame(10.0, $this->getCustomerActivePoints($customerId));
    }

    /**
     * @test
     */
    public function it_does_not_add_points_for_member_from_campaign_with_custom_attributes_expression(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Custom attributes campaign',
                        ],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => 'transaction',
                    'activity' => [
                        'startsAt' => '2020-12-06 00:00',
                        'endsAt' => null,
                    ],
                    'rules' => [
                        [
                            'conditions' => [
                                [
                                    'operator' => 'expression',
                                    'data' => 'to_date(agg(customer.labels).getFirstLabelValue(\'post_date\')) > to_date(\'2022-12-06T00:00:00+01:00\')',
                                ],
                            ],
                            'effects' => [
                                [
                                    'effect' => 'give_points',
                                    'pointsRule' => 10,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'Robin',
                    'lastName' => 'Locksley',
                    'email' => '<EMAIL>',
                    'birthDate' => '1991-06-16',
                    'labels' => [
                        [
                            'key' => 'post_date',
                            'value' => '2022-12-05T12:15:00+01:00',
                        ],
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        self::assertSame(0.0, $this->getCustomerActivePoints($customerId));

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => 'TEST/CA/01',
                        'documentType' => 'sell',
                        'purchasedAt' => '2022-12-06T15:00:00+01:00',
                    ],
                    'items' => [
                        [
                            'sku' => '123',
                            'name' => 'SKU123',
                            'quantity' => 1,
                            'grossValue' => 150,
                            'category' => 'Christmas gift',
                        ],
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        self::assertSame(0.0, $this->getCustomerActivePoints($customerId));
    }

    /**
     * @test
     */
    public function it_adds_points_for_member_5_days_after_post_date(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Custom attributes campaign',
                        ],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => 'transaction',
                    'activity' => [
                        'startsAt' => '2020-12-06 00:00',
                        'endsAt' => null,
                    ],
                    'rules' => [
                        [
                            'conditions' => [
                                [
                                    'operator' => 'expression',
                                    'data' => '(timestamp(transaction.purchasedAt) - timestamp(to_date(agg(customer.labels).getFirstLabelValue(\'post_date\')))) >= 432000',
                                ],
                            ],
                            'effects' => [
                                [
                                    'effect' => 'give_points',
                                    'pointsRule' => 10,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'Robin',
                    'lastName' => 'Locksley',
                    'email' => '<EMAIL>',
                    'birthDate' => '1991-06-16',
                    'labels' => [
                        [
                            'key' => 'post_date',
                            'value' => '2022-12-01T12:15:00+01:00',
                        ],
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        self::assertSame(0.0, $this->getCustomerActivePoints($customerId));

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => 'TEST/CA/01',
                        'documentType' => 'sell',
                        'purchasedAt' => '2022-12-07T15:00:00+01:00',
                    ],
                    'items' => [
                        [
                            'sku' => '123',
                            'name' => 'SKU123',
                            'quantity' => 1,
                            'grossValue' => 150,
                            'category' => 'Christmas gift',
                        ],
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        self::assertSame(10.0, $this->getCustomerActivePoints($customerId));
    }
}
