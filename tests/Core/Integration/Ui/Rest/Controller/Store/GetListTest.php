<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Integration\Ui\Rest\Controller\Store;

use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class GetListTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_gets_store_list(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'GET',
            '/api/store'
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_gets_store_by_code(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'GET',
            '/api/store?code=DEFAULT'
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('items', $data);
        $this->assertCount(1, $data['items']);

        $found = false;
        foreach ($data['items'] as $storeData) {
            if ('DEFAULT' === $storeData['code']) {
                $found = true;
            }
        }

        $this->assertTrue($found, 'Item has not been found');
    }

    /**
     * @test
     */
    public function it_gets_store_by_name(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'GET',
            '/api/store?name[like]=loyalty'
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('items', $data);
        $this->assertNotEmpty($data['items'], 'Item has not been found');

        foreach ($data['items'] as $storeData) {
            $this->assertNotFalse(
                strpos($storeData['name'], 'loyalty'), 'Item found has a wrong name'
            );
        }
    }
}
