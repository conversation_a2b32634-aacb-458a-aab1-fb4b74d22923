<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Test\Core\Integration\Async;

use Carbon\Carbon;
use OpenLoyalty\Application;
use OpenLoyalty\CustomEvent\Application\Command\CreateCustomEventCommand;
use OpenLoyalty\CustomEvent\Application\Command\CreateCustomEventSchemaCommand;
use OpenLoyalty\Test\Core\Integration\Infrastructure\AsyncTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\CustomEvent\Integration\Traits\CustomEventApiTrait;
use OpenLoyalty\User\Application\Job\MatchCustomEvent;
use Symfony\Component\Console\Tester\CommandTester;

final class RequestTokenTest extends AsyncTest
{
    use TenantApiTrait;
    use CustomEventApiTrait;

    /**
     * @test
     * @group async
     */
    public function it_log_token_for_request(): void
    {
        $aws_token = 'my_aws_token';
        $namespace = $this->getNamespace();
        $client = self::createAsyncClient();

        // create a tenant
        $response = $this->postTenant($client, $namespace);
        $this->assertOkResponseStatus($response);

        //create custom event schema
        $client->jsonRequest(
            'POST',
            '/api/'.$namespace.'/customEvent/schema',
            [
                'customEventSchema' => [
                    'eventType' => 'test',
                    'name' => 'test',
                    'schema' => [
                        'fields' => [
                            [
                                'type' => 'text',
                                'name' => 'test',
                                'description' => 'Filed description',
                            ],
                        ],
                    ],
                ],
            ],
            server: [
                'HTTP_X-Amzn-Trace-Id' => $aws_token,
            ]
        );
        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $this->assertTrue($this->getTestHandler()->hasInfoThatPasses(static function (array $record) use ($aws_token): bool {
            return '[Bus] Sending envelope' === $record['message'] && CreateCustomEventSchemaCommand::class === $record['context']['class'] && 'request-'.$aws_token === $record['context']['trace_token'];
        }), 'Token was not logged');

        $this->assertTrue($this->getTestHandler()->hasInfoThatPasses(static function (array $record) use ($aws_token): bool {
            return '[Bus] Processed envelope' === $record['message'] && CreateCustomEventSchemaCommand::class === $record['context']['class'] && 'request-'.$aws_token === $record['context']['trace_token'];
        }), 'Token was not logged');
    }

    /**
     * @test
     * @group async
     */
    public function it_log_token_for_console(): void
    {
        $namespace = $this->getNamespace();
        $client = self::createAsyncClient();

        // create a tenant
        $response = $this->postTenant($client, $namespace);
        $this->assertOkResponseStatus($response);

        $application = new Application(self::$kernel);
        $command = $application->find('oloy:segment:recreate');
        $commandTester = new CommandTester($command);
        $commandTester->execute(
            [
                'command' => $command->getName(),
            ]
        );

        $this->assertTrue($this->getTestHandler()->hasInfoThatPasses(static function (array $record): bool {
            if (is_null($record['context']['trace_token'])) {
                return false;
            }

            static $traceToken = null;

            if (is_null($traceToken)) {
                $traceToken = $record['context']['trace_token'];
            }

            return $traceToken === $record['context']['trace_token'];
        }), 'Token was not logged');
    }

    /**
     * @test
     * @group async
     */
    public function it_log_token_for_messages(): void
    {
        $aws_token_1 = 'my_aws_token_message_1';
        $namespace = $this->getNamespace();
        $client = self::createAsyncClient();

        //Clear queues
        $this->clearQueue('high_async');
        $this->clearQueue('high_p1_async');

        // create a tenant
        $response = $this->postTenant($client, $namespace);
        $this->assertOkResponseStatus($response);

        //create custom event schema
        $client->jsonRequest(
            'POST',
            '/api/'.$namespace.'/customEvent/schema',
            [
                'customEventSchema' => [
                    'eventType' => 'test',
                    'name' => 'test',
                    'schema' => [
                        'fields' => [
                            [
                                'type' => 'text',
                                'name' => 'test',
                                'description' => 'Filed description',
                            ],
                        ],
                    ],
                ],
            ],
            server: [
                'HTTP_X-Amzn-Trace-Id' => $aws_token_1,
            ]
        );
        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        //Asser create custom schema token
        $this->assertTrue($this->getTestHandler()->hasInfoThatPasses(static function (array $record) use ($aws_token_1): bool {
            return '[Bus] Sending envelope' === $record['message'] && CreateCustomEventSchemaCommand::class === $record['context']['class'] && 'request-'.$aws_token_1 === $record['context']['trace_token'];
        }), 'Token was not logged');

        $this->assertTrue($this->getTestHandler()->hasInfoThatPasses(static function (array $record) use ($aws_token_1): bool {
            return '[Bus] Processed envelope' === $record['message'] && CreateCustomEventSchemaCommand::class === $record['context']['class'] && 'request-'.$aws_token_1 === $record['context']['trace_token'];
        }), 'Token was not logged');

        //Post custom event
        $aws_token_2 = 'my_aws_token_message_2';
        $client->jsonRequest(
            'POST',
            '/api/'.$namespace.'/customEvent',
            [
                'event' => [
                    'type' => 'test',
                    'eventDate' => (new Carbon())->format('Y-m-d H:i:s'),
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ],
            server: [
                'HTTP_X-Amzn-Trace-Id' => $aws_token_2,
            ]
        );
        $this->assertOkResponseStatus($client->getResponse());

        //Asser create custom event token
        $this->assertTrue($this->getTestHandler()->hasInfoThatPasses(static function (array $record) use ($aws_token_2): bool {
            return '[Bus] Sending envelope' === $record['message'] && CreateCustomEventCommand::class === $record['context']['class'] && 'request-'.$aws_token_2 === $record['context']['trace_token'];
        }), 'Request token was not logged');

        //Asser create custom event message token
        $this->consumeMessage(CreateCustomEventCommand::class, 'high_async');
        $this->assertTrue($this->getTestHandler()->hasInfoThatPasses(static function (array $record) use ($aws_token_2): bool {
            return 'Consuming message' === $record['message'] && CreateCustomEventCommand::class === $record['context']['message'] && 'request-'.$aws_token_2 === $record['context']['trace_token'];
        }), 'Message token was not logged');

        $this->assertTrue($this->getTestHandler()->hasInfoThatPasses(static function (array $record) use ($aws_token_2): bool {
            return 'Consumed message' === $record['message'] && CreateCustomEventCommand::class === $record['context']['message'] && 'request-'.$aws_token_2 === $record['context']['trace_token'];
        }), 'Message token was not logged');

        //Asser match custom event message token
        $this->consumeMessage(MatchCustomEvent::class, 'high_p1_async');
        $this->assertTrue($this->getTestHandler()->hasInfoThatPasses(static function (array $record) use ($aws_token_2): bool {
            return 'Consuming message' === $record['message'] && MatchCustomEvent::class === $record['context']['message'] && 'request-'.$aws_token_2 === $record['context']['trace_token'];
        }), 'Message token was not logged');

        $this->assertTrue($this->getTestHandler()->hasInfoThatPasses(static function (array $record) use ($aws_token_2): bool {
            return 'Consumed message' === $record['message'] && MatchCustomEvent::class === $record['context']['message'] && 'request-'.$aws_token_2 === $record['context']['trace_token'];
        }), 'Message token was not logged');
    }
}
