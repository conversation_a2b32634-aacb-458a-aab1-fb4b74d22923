<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Application\UseCase;

use OpenLoyalty\Core\Application\Command\CreateStore;
use OpenLoyalty\Core\Application\UseCase\CreateStoreUseCase;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Core\Infrastructure\Model\Store;
use OpenLoyalty\Settings\Infrastructure\Entity\StringSettingEntry;
use PHPUnit\Framework\TestCase;

final class CreateStoreUseCaseTest extends TestCase
{
    /**
     * @test
     */
    public function it_dispatches_create_event_with_store_data(): void
    {
        $commandBus = $this->createMock(CommandBusInterface::class);
        $uuidGenerator = $this->createMock(UuidGeneratorInterface::class);
        $useCase = new CreateStoreUseCase($commandBus, $uuidGenerator);

        $store = $this->createMock(Store::class);
        $store->method('getCode')->willReturn('DEFAULT');
        $store->method('getCurrency')->willReturn('USD');
        $store->method('getName')->willReturn('Default store');
        $store->method('isActive')->willReturn(false);

        $uuidGenerator->method('generate')->willReturn('00000000-0000-0000-0000-000000000000');
        $storeId = new StoreId('00000000-0000-0000-0000-000000000000');

        $commandBus->expects($this->once())->method('dispatch')->with(
            new CreateStore(
                $storeId,
                'DEFAULT',
                'USD',
                'Default store',
                false,
                [
                    'tiersMode' => [
                        StringSettingEntry::class,
                        'custom',
                    ],
                ]
            )
        );

        $this->assertEquals($storeId, $useCase->execute($store));
    }
}
