<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Application\CommandHandler;

use Broadway\EventDispatcher\EventDispatcher;
use OpenLoyalty\Core\Domain\Id\LocaleCode;
use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Translation\Domain\Language;
use OpenLoyalty\Translation\Domain\LanguageRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

abstract class LanguageCommandHandlerTest extends TestCase
{
    /**
     * @var LanguageRepository&MockObject
     */
    protected $inMemoryRepository;

    /**
     * @var array
     */
    protected $languages = [];

    public function setUp(): void
    {
        $languages = &$this->languages;

        /** @var LanguageRepository&MockObject $repo */
        $repo = $this->getMockBuilder(LanguageRepository::class)->getMock();
        $repo->method('save')->with($this->isInstanceOf(Language::class))->will(
            $this->returnCallback(function (Language $language) use (&$languages): void {
                $languages[] = $language;
            })
        );
        $repo->method('remove')->with($this->isInstanceOf(Language::class))->will(
            $this->returnCallback(function (Language $removedLanguage) use (&$languages): void {
                foreach ($languages as $k => $savedLanguage) {
                    if ((string) $savedLanguage->getLocaleCode() === (string) $removedLanguage->getLocaleCode()) {
                        unset($languages[$k]);

                        return;
                    }
                }
            })
        );
        $repo->method('byId')->with($this->isInstanceOf(LocaleCode::class))->will(
            $this->returnCallback(function (LocaleCode $id) use (&$languages) {
                /** @var Language $language */
                foreach ($languages as $language) {
                    if ((string) $language->getLocaleCode() === (string) $id) {
                        return $language;
                    }
                }

                return null;
            })
        );
        $repo->method('byCode')->will(
            $this->returnCallback(function (string $code) use (&$languages) {
                /** @var \OpenLoyalty\Translation\Domain\Language $language */
                foreach ($languages as $language) {
                    if ((string) $language->getLocaleCode() === $code) {
                        return $language;
                    }
                }

                return null;
            })
        );

        $this->inMemoryRepository = $repo;
    }

    /**
     * @return LanguageRepository&MockObject
     */
    protected function getLanguageRepository(): LanguageRepository
    {
        return $this->inMemoryRepository;
    }

    /**
     * @return EventDispatcher&MockObject
     */
    protected function getEventDispatcher(): EventDispatcher
    {
        /* @var EventDispatcher&MockObject */
        return $this->getMockBuilder(EventDispatcher::class)->getMock();
    }

    abstract protected function createCommandHandler(): CommandHandlerInterface;
}
