<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Infrastructure\Provider;

use OpenLoyalty\Core\Infrastructure\Provider\LockProvider;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Lock\LockFactory;
use Symfony\Component\Lock\Store\RedisStore;

final class LockProviderTest extends TestCase
{
    /**
     * @test
     */
    public function it_test_acquire_logic(): void
    {
        $redis = $this->createMock(\Redis::class);

        $redis->expects(self::atLeast(3))
            ->method('eval')
            ->willReturnOnConsecutiveCalls(true, true, true);

        $store = new RedisStore($redis, 300);
        $lockFactory = new LockFactory($store);

        $lockProvider = new LockProvider($lockFactory);

        $this->assertTrue($lockProvider->acquire(['some_resource_part', 'some2_resource_part']));
        $this->assertFalse($lockProvider->acquire(['some_resource_part', 'some2_resource_part']));
    }
}
