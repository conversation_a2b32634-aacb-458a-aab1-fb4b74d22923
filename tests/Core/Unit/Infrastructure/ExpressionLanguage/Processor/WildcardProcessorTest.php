<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Infrastructure\ExpressionLanguage\Processor;

use OpenLoyalty\Tools\ExpressionLanguage\Processor\WildcardProcessor;
use PHPUnit\Framework\TestCase;

final class WildcardProcessorTest extends TestCase
{
    /**
     * @test
     * @dataProvider getExpressionProvider
     */
    public function it_runs(string $inputExpression, string $outputExpression): void
    {
        $processor = new WildcardProcessor();
        $result = $processor->process($inputExpression);

        $this->assertSame($outputExpression, $result);
    }

    public function getExpressionProvider(): array
    {
        return [
            ['1+1', '1+1'],
            ['1+1+<#>12+81</#>+2', '1+1+12+81+2'],
            ['1+1+<#>roundup(\'<#>value</#>\')</#>+2', '1+1+roundup(\'value\')+2'],
            ['<#>1+1+<#>roundup(\'<#>value</#>\')</#>+2</#>', '1+1+roundup(\'value\')+2'],
            ['<#></#>', ''],
            ['<#>     </#>', '     '],
            ['<#>value', 'value'],
            ['<#><#>value', 'value'],
            ['value</#></#>', 'value'],
            ['<#>120.0</#> + <#>150.0</#>', '120.0 + 150.0'],
            ['<#>120.0</#> + <#>150.0</#> + (<#>160.0+<#>170.0</#></#>)', '120.0 + 150.0 + (160.0+170.0)'],
        ];
    }
}
