<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Infrastructure\Persistence\Doctrine\Type;

use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\Type;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Type\StoreIdDoctrineType;
use PHPUnit\Framework\TestCase;

final class StoreIdDoctrineTypeTest extends TestCase
{
    protected Type $type;
    protected AbstractPlatform $platform;

    /**
     * {@inheritdoc}
     *
     * @throws Exception
     */
    protected function setUp(): void
    {
        if (!Type::hasType('store_id')) {
            Type::addType('store_id', StoreIdDoctrineType::class);
        }

        $this->type = Type::getType('store_id');
        $this->platform = $this->getMockForAbstractClass(AbstractPlatform::class);
    }

    /**
     * @test
     */
    public function it_converts_to_database_value(): void
    {
        $data = new StoreId('00000000-0000-0000-0000-000000000000');
        $value = $this->type->convertToDatabaseValue($data, $this->platform);

        $this->assertSame('00000000-0000-0000-0000-000000000000', $value);
    }

    /**
     * @test
     */
    public function it_converts_to_php_value(): void
    {
        $data = '00000000-0000-0000-0000-000000000000';
        $converted = $this->type->convertToPHPValue($data, $this->platform);

        $this->assertEquals(new StoreId('00000000-0000-0000-0000-000000000000'), $converted);
    }
}
