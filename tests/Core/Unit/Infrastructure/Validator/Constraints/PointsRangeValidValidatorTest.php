<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Infrastructure\Validator\Constraints;

use OpenLoyalty\Core\Infrastructure\Form\Type\PointsType;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\PointsRangeValid;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\PointsRangeValidValidator;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Form\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Constraints\Range;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;
use Symfony\Contracts\Translation\TranslatorInterface;

class PointsRangeValidValidatorTest extends ConstraintValidatorTestCase
{
    private MockObject&TranslatorInterface $translator;

    protected function setUp(): void
    {
        $this->translator = $this->createMock(TranslatorInterface::class);
        parent::setUp();
    }

    protected function createValidator(): ConstraintValidator
    {
        return new PointsRangeValidValidator($this->translator);
    }

    /**
     * @test
     */
    public function it_not_raises_exception_when_value_is_empty(): void
    {
        $this->validator->validate(
            null,
            new PointsRangeValid(['min' => 1, 'max' => 2])
        );

        $this->assertNoViolation();
    }

    /**
     * @test
     */
    public function it_not_raises_exception_when_constrain_is_not_excepted(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate(
            10,
            new Range(['min' => 1, 'max' => 2])
        );

        $this->assertNoViolation();
    }

    /**
     * @test
     */
    public function it_raises_exception_when_value_is_bigger_then_limit(): void
    {
        $this->translator->method('trans')->willReturn('translated message');

        $this->validator->validate(
            PointsType::MAX_POINTS + 1,
            new PointsRangeValid(['min' => PointsType::MIN_POINTS, 'max' => PointsType::MAX_POINTS])
        );

        $this->buildViolation('translated message')->assertRaised();
    }

    /**
     * @test
     */
    public function it_raises_exception_when_value_is_lower_then_limit(): void
    {
        $this->translator->method('trans')->willReturn('translated message');

        $this->validator->validate(
            0,
            new PointsRangeValid(['min' => PointsType::MIN_POINTS, 'max' => PointsType::MAX_POINTS])
        );

        $this->buildViolation('translated message')->setParameters(['{{ min }}' => '0.000001'])->assertRaised();
    }

    private function assertRaised(array $violations, string $invalidValue): void
    {
        $assertion = null;

        foreach ($violations as $violation) {
            $assertion = isset($assertion)
                ? $assertion->buildNextViolation($violation['message'])
                : $this->buildViolation($violation['message']);

            $assertion->setInvalidValue($invalidValue);

            foreach ($violation['parameters'] ?? [] as $param => $value) {
                $assertion->setParameter($param, $value);
            }

            if (isset($violation['code'])) {
                $assertion->setCode($violation['code']);
            }
        }

        $this->assertNotNull($assertion);
        $assertion->assertRaised();
    }
}
