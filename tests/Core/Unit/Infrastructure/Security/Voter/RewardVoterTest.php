<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Infrastructure\Security\Voter;

use OpenLoyalty\Core\Infrastructure\Security\Voter\RewardVoter;
use OpenLoyalty\Reward\Infrastructure\Security\Voter\RewardVoter as BaseRewardVoter;
use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseVoterTest;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\PermissionAccess;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionEvaluator;
use OpenLoyalty\User\Infrastructure\Security\Voter\CustomerVoter;
use PHPUnit\Framework\MockObject\MockObject;

final class RewardVoterTest extends BaseVoterTest
{
    /**
     * @test
     */
    public function it_works(): void
    {
        /** @var UserPermissionCheckerInterface&MockObject $permissionChecker */
        $permissionChecker = $this->getMockBuilder(UserPermissionCheckerInterface::class)->getMock();
        $permissionChecker->method('hasPermissionInCurrentStore')->willReturnCallback(function (User $user, string $resource, array $accesses) {
            return (new UserPermissionEvaluator())->hasPermission(null, $user, $resource, $accesses);
        });

        $attributes = [
            BaseRewardVoter::LIST_ALL_VISIBLE_REWARDS => [
                'customer' => false,
                'admin' => true,
                'admin_reporter' => true,
                'admin_custom' => [
                    [
                        'permissions' => [
                            BaseRewardVoter::PERMISSION_RESOURCE => [PermissionAccess::VIEW],
                        ],
                        'expected' => false,
                    ],
                    [
                        'permissions' => [
                            BaseRewardVoter::PERMISSION_RESOURCE => [PermissionAccess::VIEW],
                            CustomerVoter::PERMISSION_RESOURCE => [PermissionAccess::VIEW, PermissionAccess::MODIFY],
                        ],
                        'expected' => true,
                    ],
                ],
            ],
        ];

        $voter = new RewardVoter($permissionChecker);

        $this->assertVoterAttributes($voter, $attributes);
    }

    /**
     * {@inheritdoc}
     */
    protected function getSubjectById($id): void
    {
        return;
    }
}
