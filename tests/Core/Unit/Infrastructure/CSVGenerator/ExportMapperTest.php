<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Infrastructure\CSVGenerator;

use DateTime;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Infrastructure\CSVGenerator\ExportMapper;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class ExportMapperTest extends TestCase
{
    private SerializerInterface|MockObject $serializer;
    private ExportMapper $mapper;

    public function setUp(): void
    {
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->mapper = new ExportMapper($this->serializer);
    }

    /**
     * @test
     */
    public function it_maps_time_to_string_date(): void
    {
        $mappedValue = $this->mapper->map(new DateTime('2022-03-22 00:00:00'));
        $this->assertSame('2022-03-22T00:00:00+00:00', $mappedValue);
    }

    /**
     * @test
     */
    public function it_maps_boolean_to_string(): void
    {
        $mappedValue = $this->mapper->map(true);
        $this->assertNotSame(true, $mappedValue);
        $this->assertSame('1', $mappedValue);
    }

    /**
     * @test
     */
    public function it_does_not_map_if_map_does_not_exist(): void
    {
        $mappedValue = $this->mapper->map('original-data');
        $this->assertSame('original-data', $mappedValue);
    }

    /**
     * @test
     */
    public function it_maps_null_to_empty_string(): void
    {
        $mappedValue = $this->mapper->map(null);
        $this->assertSame('', $mappedValue);
    }

    /**
     * @test
     */
    public function it_maps_identifier_object_to_string(): void
    {
        $mappedValue = $this->mapper->map(new StoreId('a9be55ae-4813-4332-8925-a231eabd49de'));
        $this->assertSame('a9be55ae-4813-4332-8925-a231eabd49de', $mappedValue);
    }

    /**
     * @test
     */
    public function it_maps_array_to_json(): void
    {
        $this->serializer->method('serialize')->willReturn('{"key": "value"}');
        $mappedValue = $this->mapper->map(['key' => 'value']);
        $this->assertSame('{"key": "value"}', $mappedValue);
    }

    /**
     * @test
     */
    public function it_maps_object_to_json(): void
    {
        $this->serializer->method('serialize')->willReturn('{}');
        $mappedValue = $this->mapper->map(new \stdClass());
        $this->assertSame('{}', $mappedValue);
    }
}
