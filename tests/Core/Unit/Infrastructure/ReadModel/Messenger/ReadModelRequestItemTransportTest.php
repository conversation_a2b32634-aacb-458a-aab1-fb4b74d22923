<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Infrastructure\ReadModel\Messenger;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\ORM\EntityManagerInterface;
use OpenLoyalty\Core\Application\TimezoneResolverInterface;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\ReadModel\Entity\ReadModelRequestItem;
use OpenLoyalty\Core\Domain\ReadModel\Entity\ReadModelRequestItemCollection;
use OpenLoyalty\Core\Domain\ReadModel\ReadModelRequestRepositoryInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Infrastructure\ReadModel\Messenger\ReadModelRequestItemTransport;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Messenger\Envelope;

final class ReadModelRequestItemTransportTest extends TestCase
{
    private MockObject&ReadModelRequestRepositoryInterface $readModelRequestRepository;
    private MockObject&StoreRepository $storeRepository;
    private ReadModelRequestItemTransport $transport;
    private TimezoneResolverInterface $timezoneResolver;
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        $this->readModelRequestRepository = $this->createMock(ReadModelRequestRepositoryInterface::class);
        $this->storeRepository = $this->createMock(StoreRepository::class);
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->timezoneResolver = $this->createMock(TimezoneResolverInterface::class);

        $this->entityManager->method('isOpen')->willReturn(true);

        $connection = $this->createMock(Connection::class);
        $platform = $this->createMock(AbstractPlatform::class);
        $platform->method('getDummySelectSQL')->willReturn('');

        $connection->method('getDatabasePlatform')->willReturn(
            $platform
        );

        $this->entityManager->method('getConnection')->willReturn(
            $connection
        );

        $this->transport = new ReadModelRequestItemTransport(
            $this->entityManager,
            $this->readModelRequestRepository,
            $this->storeRepository,
            $this->timezoneResolver,
            10,
            0
        );
    }

    /**
     * @test
     */
    public function it_return_iterable_collection(): void
    {
        //Store loop
        $storeA = $this->createMock(Store::class);
        $storeAId = new StoreId('00000000-0000-0000-0000-000000000000');
        $storeA->expects(self::exactly(3))->method('getStoreId')->willReturn($storeAId);
        $storeB = $this->createMock(Store::class);
        $storeBId = new StoreId('00000000-0000-0000-0000-000000000001');
        $storeB->expects(self::exactly(2))->method('getStoreId')->willReturn($storeBId);

        $this->storeRepository->expects($this->once())->method('findAllActive')->willReturn([$storeA, $storeB]);

        $requestItemCollectionStoreA = [
            $this->createMock(ReadModelRequestItem::class),
            $this->createMock(ReadModelRequestItem::class),
        ];

        //Return request item collection for store
        $this->readModelRequestRepository->expects($this->exactly(2))
            ->method('findToProcess')
            ->willReturnCallback(function ($storeId, $limit, $orphansSeconds) use ($storeAId, $storeBId, $requestItemCollectionStoreA) {
                if ($storeId === $storeAId) {
                    $this->assertSame(10, $limit);
                    $this->assertSame(43200, $orphansSeconds);

                    return $requestItemCollectionStoreA;
                }
                if ($storeId === $storeBId) {
                    $this->assertSame(10, $limit);
                    $this->assertSame(43200, $orphansSeconds);

                    return [];
                }

                $this->fail("Unexpected storeId: $storeId");
            });

        //Asserts
        $envelopes = iterator_to_array($this->transport->get());
        $this->assertCount(1, $envelopes);

        $envelopeA = $envelopes[0];
        $this->assertInstanceOf(Envelope::class, $envelopeA);
        $this->assertInstanceOf(ReadModelRequestItemCollection::class, $envelopeA->getMessage());
        $this->assertSame($storeAId, $envelopeA->getMessage()->getStoreId());
        $this->assertCount(2, $envelopeA->getMessage()->getCollection());
    }

    /**
     * @test
     */
    public function it_reject_calls_remove_request(): void
    {
        $storeId = new StoreId('00000000-0000-0000-0000-000000000000');

        $requestItem1 = new ReadModelRequestItem($storeId, '{request_content: 1}', 'test1', 'unique1');
        $requestItem2 = new ReadModelRequestItem($storeId, '{request_content: 2}', 'test2', 'unique2');
        $requestItem3 = new ReadModelRequestItem($storeId, '{request_content: 1}', 'test1', 'unique1');

        $collection = new ReadModelRequestItemCollection($storeId, [$requestItem1, $requestItem2, $requestItem3]);

        $envelope = new Envelope($collection);

        $this->readModelRequestRepository->expects($this->once())
            ->method('removeRequest')
            ->with(
                $storeId,
                'unique1',
                'unique2'
            );

        $this->transport->reject($envelope);
    }
}
