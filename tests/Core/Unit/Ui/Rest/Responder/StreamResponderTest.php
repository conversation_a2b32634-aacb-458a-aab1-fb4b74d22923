<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Ui\Rest\Responder;

use InvalidArgumentException;
use OpenLoyalty\Core\Ui\Rest\Responder\StreamResponder;
use PHPUnit\Framework\TestCase;

final class StreamResponderTest extends TestCase
{
    private StreamResponder $streamResponder;

    protected function setUp(): void
    {
        $this->streamResponder = new StreamResponder();
    }

    /**
     * @test
     */
    public function it_throws_exception_while_resource_has_not_been_passed_to_method(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->streamResponder->__invoke('test');
    }

    /**
     * @test
     */
    public function it_returns_streamed_response(): void
    {
        $resource = fopen('php://stdout', 'wb');

        $response = $this->streamResponder->__invoke($resource);

        self::assertSame('application/octet-stream', $response->headers->get('Content-Type'));
        self::assertSame('no', $response->headers->get('X-Accel-Buffering'));
        self::assertNull($response->headers->get('Access-Control-Expose-Headers'));
        self::assertNull($response->headers->get('Content-Disposition'));
    }

    /**
     * @test
     */
    public function it_returns_streamed_response_with_attachment_name(): void
    {
        $resource = fopen('php://stdout', 'wb');

        $response = $this->streamResponder->__invoke($resource, 1000, 'someAttachmentName');

        self::assertSame('application/octet-stream', $response->headers->get('Content-Type'));
        self::assertSame('1000', $response->headers->get('Content-Length'));
        self::assertSame('no', $response->headers->get('X-Accel-Buffering'));
        self::assertSame('Content-Disposition', $response->headers->get('Access-Control-Expose-Headers'));
        self::assertSame('attachment; filename="someAttachmentName"', $response->headers->get('Content-Disposition'));
    }
}
