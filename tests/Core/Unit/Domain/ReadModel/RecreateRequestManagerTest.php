<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Domain\ReadModel;

use JMS\Serializer\SerializerInterface;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\ReadModel\Entity\ReadModelRequestItem;
use OpenLoyalty\Core\Domain\ReadModel\ReadModelRequestRepositoryInterface;
use OpenLoyalty\Core\Domain\ReadModel\RecreateRequestManager;
use OpenLoyalty\Core\Domain\ReadModel\Request\QueryRequestProjection;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class RecreateRequestManagerTest extends TestCase
{
    private MockObject&ReadModelRequestRepositoryInterface $readModelRequestRepository;
    private MockObject&SerializerInterface $serializer;

    protected function setUp(): void
    {
        $this->readModelRequestRepository = $this->createMock(ReadModelRequestRepositoryInterface::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
    }

    /**
     * @test
     */
    public function it_adds(): void
    {
        $projection = new QueryRequestProjection('testProjector', new StoreId('00000000-0000-0000-0000-000000000000'));

        $this->serializer->method('serialize')->willReturn('{"property": "testValue"}');
        $this->readModelRequestRepository->expects($this->once())->method('addRequest')->with($this->isInstanceOf(ReadModelRequestItem::class));

        $manager = new RecreateRequestManager($this->readModelRequestRepository, $this->serializer);
        $manager->add($projection);
    }

    /**
     * @test
     */
    public function it_removes(): void
    {
        $projection = new QueryRequestProjection('testProjector', new StoreId('00000000-0000-0000-0000-000000000000'));

        $this->serializer->method('serialize')->willReturn('{"property": "testValue"}');
        $this->readModelRequestRepository->expects($this->once())->method('removeRequest')->with(
            new StoreId('00000000-0000-0000-0000-000000000000'),
            '99416f3cdd83cb9cf34c22e6b8ea3a81'
        );

        $manager = new RecreateRequestManager($this->readModelRequestRepository, $this->serializer);
        $manager->remove($projection);
    }
}
