<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Domain\Condition\Time;

use OpenLoyalty\Core\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Core\Domain\Condition\Time\IsDayOfMonth;
use PHPUnit\Framework\TestCase;

final class IsDayOfMonthTest extends TestCase
{
    /**
     * @test
     *
     * @throws InvalidConditionException
     */
    public function it_returns_expression(): void
    {
        $condition = IsDayOfMonth::fromArray('currentTime', 16);

        self::assertSame(
            'day_of_month(currentTime) == 16',
            $condition->getExpression()
        );
    }
}
