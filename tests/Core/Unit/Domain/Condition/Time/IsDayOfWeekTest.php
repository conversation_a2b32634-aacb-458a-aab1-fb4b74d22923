<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Domain\Condition\Time;

use OpenLoyalty\Core\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Core\Domain\Condition\Time\IsDayOfWeek;
use PHPUnit\Framework\TestCase;

final class IsDayOfWeekTest extends TestCase
{
    /**
     * @test
     *
     * @throws InvalidConditionException
     */
    public function it_returns_expression(): void
    {
        $condition = IsDayOfWeek::fromArray('currentTime', ['Monday', 'Friday']);

        self::assertSame(
            "lower(day(currentTime)) in ['monday','friday']",
            $condition->getExpression()
        );
    }
}
