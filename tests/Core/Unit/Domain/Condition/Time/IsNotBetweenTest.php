<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Domain\Condition\Time;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Core\Domain\Condition\Time\IsNotBetween;
use PHPUnit\Framework\TestCase;

final class IsNotBetweenTest extends TestCase
{
    /**
     * @test
     *
     * @throws InvalidConditionException
     */
    public function it_returns_expression(): void
    {
        $condition = IsNotBetween::fromArray(
            'currentTime',
            [
                'from' => new DateTimeImmutable('2022-06-16'),
                'to' => new DateTimeImmutable('2022-06-17'),
            ]
        );

        self::assertSame(
            "is_not_between(currentTime, to_date('2022-06-16T00:00:00+00:00'), to_date('2022-06-17T00:00:00+00:00'))",
            $condition->getExpression()
        );
    }
}
