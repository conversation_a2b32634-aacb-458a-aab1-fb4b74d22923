<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Domain\Condition\String;

use OpenLoyalty\Core\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Core\Domain\Condition\String\StartsWith;
use PHPUnit\Framework\TestCase;

final class StartsWithTest extends TestCase
{
    /**
     * @test
     *
     * @throws InvalidConditionException
     */
    public function it_returns_expression(): void
    {
        $condition = StartsWith::fromArray('customer.lastName', 'M');

        self::assertSame("starts_with(lower(customer.lastName), 'm')", $condition->getExpression());
    }
}
