<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Core\Unit\Domain\Condition;

use DateTime;
use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Condition\ConditionFactory;
use OpenLoyalty\Core\Domain\Condition\Custom\ExpressionLanguage;
use OpenLoyalty\Core\Domain\Condition\Generic\HasAtLeastOneLabel;
use OpenLoyalty\Core\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Core\Domain\Condition\Numeric\IsLess;
use OpenLoyalty\Core\Domain\Condition\String\StartsWith;
use OpenLoyalty\Core\Domain\Condition\Time\IsBetween;
use PHPUnit\Framework\TestCase;

final class ConditionFactoryTest extends TestCase
{
    /**
     * @test
     *
     * @dataProvider getTestData
     */
    public function it_creates_condition(
        string $operator,
        ?string $attribute,
        $data,
        string $conditionInstance = null
    ): void {
        $factory = new ConditionFactory();

        if (null === $conditionInstance) {
            $this->expectException(InvalidConditionException::class);
        }

        $result = $factory->create($operator, $attribute, $data);

        if (null !== $conditionInstance) {
            self::assertInstanceOf($conditionInstance, $result);
        }
    }

    public function getTestData(): iterable
    {
        return [
            ['expression', null, 'test', ExpressionLanguage::class],
            ['is_less', 'transaction.grossValue', 50, IsLess::class],
            ['has_at_least_one_label', 'transaction', null],
            ['has_at_least_one_label', 'transaction', ['test'], HasAtLeastOneLabel::class],
            ['starts_with', 'customer.firstName', null],
            ['starts_with', 'customer.firstName', 'P', StartsWith::class],
            ['is_between', 'currentTime', ['from' => '2022-06-16', 'to' => '2022-07-16']],
            [
                'is_between',
                'currentTime',
                [
                    'from' => new DateTime('2022-06-16'),
                    'to' => new DateTime('2022-07-16'),
                ],
            ],
            [
                'is_between',
                'currentTime',
                [
                    'from' => new DateTimeImmutable('2022-06-16'),
                    'to' => new DateTimeImmutable('2022-07-16'),
                ],
                IsBetween::class,
            ],
        ];
    }
}
