<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Account\Integration\Ui\Rest\WalletType;

use OpenLoyalty\Integration\Helpers\ResponseChecker;
use OpenLoyalty\Test\Account\Integration\Traits\WalletTypeApiTrait;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class GlobalLimitTest extends AbstractApiTest
{
    use WalletTypeApiTrait;
    private HttpKernelBrowser $client;
    private int $defaultGlobalWalletTypeLimit;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
        $this->deactivateAllWalletTypes();

        $this->defaultGlobalWalletTypeLimit = (int) $_ENV['GLOBAL_ACTIVE_WALLET_TYPE_LIMIT'];
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $_ENV['GLOBAL_ACTIVE_WALLET_TYPE_LIMIT'] = $this->defaultGlobalWalletTypeLimit;
    }

    /**
     * @test
     */
    public function it_reaches_the_limit_when_create(): void
    {
        $_ENV['GLOBAL_ACTIVE_WALLET_TYPE_LIMIT'] = 1;
        $this->postWalletType($this->client, code: 'code1');
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->postWalletType($this->client, code: 'code2');
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertBadRequestResponseStatus($response);
        ResponseChecker::assertHasError($data, 'active', 'You have reached the global limit of 1 active wallet types. To activate this wallet type, please deactivate existing ones or contact your Customer Success Manager or the support <NAME_EMAIL>.');
    }

    /**
     * @test
     */
    public function it_reaches_the_limit_when_update(): void
    {
        $_ENV['GLOBAL_ACTIVE_WALLET_TYPE_LIMIT'] = 1;
        $this->postWalletType($this->client, code: 'code3');
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->postWalletType($this->client, code: 'code4', active: false);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $walletTypeId = $data['walletTypeId'];
        $this->putWalletType($this->client, $walletTypeId);
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertBadRequestResponseStatus($response);
        ResponseChecker::assertHasError($data, 'active', 'You have reached the global limit of 1 active wallet types. To activate this wallet type, please deactivate existing ones or contact your Customer Success Manager or the support <NAME_EMAIL>.');
    }

    /**
     * @test
     */
    public function it_does_not_reach_the_limit_when_deactivate(): void
    {
        $_ENV['GLOBAL_ACTIVE_WALLET_TYPE_LIMIT'] = 2;
        $this->postWalletType($this->client, code: 'code5');
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->postWalletType($this->client, code: 'code6');
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $_ENV['GLOBAL_ACTIVE_WALLET_TYPE_LIMIT'] = 1;

        $walletTypeId = $data['walletTypeId'];
        $this->putWalletType($this->client, $walletTypeId, active: false);
        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);
    }

    private function deactivateAllWalletTypes(): void
    {
        $entityManager = self::$kernel->getContainer()->get('doctrine.orm.default_entity_manager');
        $query = $entityManager->createQuery('UPDATE OpenLoyalty\Account\Domain\WalletType w SET w.active = false');
        $query->execute();
    }
}
