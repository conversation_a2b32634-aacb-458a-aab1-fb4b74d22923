<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Account\Integration\Ui\Rest\WalletType;

use Carbon\Carbon;
use OpenLoyalty\Test\Account\Integration\Traits\WalletTypeApiTrait;
use OpenLoyalty\Test\Campaign\Integration\Traits\CampaignApiTrait;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\Transaction\Integration\Traits\TransactionApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;

final class WalletLimitsAfterTransferCanceledTest extends AbstractApiTest
{
    use MemberApiTrait;
    use TenantApiTrait;
    use CampaignApiTrait;
    use TransactionApiTrait;
    use WalletTypeApiTrait;

    /**
     * @test
     */
    public function it_reaches_the_limit_when_create(): void
    {
        $client = self::createAuthenticatedClient();
        $tenantCode = 'canceled_transfer_campaign_limit';
        $memberEmail = '<EMAIL>';

        $response = $this->postTenant($client, $tenantCode);
        $this->assertOkResponseStatus($response);

        $walletCode = 'limitationWallet';
        $response = $this->postWalletType(
            $client,
            $walletCode,
            $tenantCode,
            limits: [
                'points' => [
                    'value' => 2000,
                    'interval' => [
                        'type' => 'calendarWeeks',
                        'value' => 1,
                    ],
                ],
                'pointsPerMember' => [
                    'value' => 100,
                    'interval' => [
                        'type' => 'calendarWeeks',
                        'value' => 1,
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $walletId = $data['walletTypeId'];

        $response = $this->postMember($client, $tenantCode, $memberEmail);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $customerId = $data['customerId'];

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '100',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                            'walletCode' => $walletCode,
                        ],
                    ],
                ],
            ],
            limits: [
                'executionsPerMember' => [
                    'value' => 2,
                    'interval' => [
                        'type' => 'calendarWeeks',
                        'value' => 1,
                    ],
                ],
                'points' => [
                    'value' => 1000,
                    'interval' => [
                        'type' => 'calendarWeeks',
                        'value' => 1,
                    ],
                ],
                'pointsPerMember' => [
                    'value' => 200,
                    'interval' => [
                        'type' => 'calendarWeeks',
                        'value' => 1,
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($response);

        $client->request('GET', sprintf('/api/%s/member/%s/wallet', $tenantCode, $customerId));
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $wallet = null;
        foreach ($data['items'] as $item) {
            if ($walletCode === $item['walletType']['code']) {
                $wallet = $item;
                break;
            }
        }

        if (null === $wallet) {
            $this->fail('Test wallet not found!');
        }
        self::assertSame(0.0, $wallet['unitsLimitUsed']);
        self::assertSame(100.0, $wallet['unitsLimitRemaining']);

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC123',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $client->request('GET', sprintf('/api/%s/wallet/%s', $tenantCode, $wallet['walletId']));
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        self::assertSame(100.0, $data['unitsLimitUsed']);
        self::assertSame(0.0, $data['unitsLimitRemaining']);

        $client->request(
            'GET',
            '/api/'.$tenantCode.'/points?member:email[eq]='.$memberEmail.'&_orderBy[createdAt]=asc',
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $transferId = $data['items'][0]['transferId'];
        $client->request(
            'POST',
            '/api/'.$tenantCode.'/points/'.$transferId.'/cancel',
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $client->request('GET', sprintf('/api/%s/wallet/%s', $tenantCode, $wallet['walletId']));
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        self::assertSame(0.0, $data['unitsLimitUsed']);
        self::assertSame(100.0, $data['unitsLimitRemaining']);
    }
}
