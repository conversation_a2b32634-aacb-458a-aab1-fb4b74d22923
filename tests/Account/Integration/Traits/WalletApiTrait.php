<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Account\Integration\Traits;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

trait WalletApiTrait
{
    private function getMemberWallet(
        HttpKernelBrowser $httpClient,
        string $customerId,
        string $storeCode = 'DEFAULT',
        ?string $walletTypeId = null,
    ): Response {
        $url = '/api/'.$storeCode.'/member/'.$customerId.'/wallet?_orderBy[createdAt]=asc';

        if (null !== $walletTypeId) {
            $url .= '&walletTypeId='.$walletTypeId;
        }

        $httpClient->request(
            'GET',
            $url
        );

        return $httpClient->getResponse();
    }

    private function getUnitsFromWallet(
        Response $responseFromGetMemberWallet,
        string $walletTypeCode,
        string $unitField
    ): ?float {
        $data = json_decode($responseFromGetMemberWallet->getContent(), true, 512, JSON_THROW_ON_ERROR);

        foreach ($data['items'] as $item) {
            if ($item['walletType']['code'] !== $walletTypeCode) {
                continue;
            }

            return $item['account'][$unitField];
        }

        return null;
    }
}
