<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Account\Unit\Domain\Provider;

use DateTimeImmutable;
use Monolog\Test\TestCase;
use OpenLoyalty\Account\Domain\Provider\ValidityTransferDecorator;
use OpenLoyalty\Account\Domain\WalletType;
use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\ValueObject\RangePeriodDate;
use OpenLoyalty\Points\Domain\Transfer;

final class ValidityTransferDecoratorTest extends TestCase
{
    /**
     * @test
     * @dataProvider getData
     */
    public function it_calculate_expiration(
        DateTimeImmutable $registeredAt,
        WalletType $walletType,
        ?RangePeriodDate $overrideLocked = null,
        ?RangePeriodDate $overrideExpired = null,
        ?DateTimeImmutable $expectedLockedUntil = null,
        ?DateTimeImmutable $expectedExpiresAt = null,
    ): void {
        $decorator = new ValidityTransferDecorator();

        $transferId = new TransferId('********-0000-0000-0000-********0005');
        $accountId = new AccountId('********-0000-0000-0000-********0003');
        $storeId = new StoreId('********-0000-0000-0000-************');

        $transfer = new Transfer(
            $transferId,
            Transfer::ADD_TYPE,
            $accountId,
            $storeId,
            200,
            $registeredAt,
            $registeredAt
        );

        $decorator->decorate($transfer, $walletType, $overrideLocked, $overrideExpired);

        $this->assertEquals($expectedLockedUntil, $transfer->getLockedUntil());
        $this->assertEquals($expectedExpiresAt, $transfer->getExpiresAt());
    }

    public function getData(): iterable
    {
        yield 'Wallet without exp and pen, without override' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $this->getWalletType(),
            null,
            null,

            null, // lockedUntil
            null, // expiredAt
        ];

        yield 'Wallet without exp and pen, with override pen' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $this->getWalletType(),
            RangePeriodDate::fromDays(5),
            null,

            new DateTimeImmutable('2012-04-08 21:00:00'), // lockedUntil
            null, // expiredAt
        ];

        yield 'Wallet without exp and pen, with override exp' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $this->getWalletType(),
            null,
            RangePeriodDate::fromDays(5),

            null, // lockedUntil
            new DateTimeImmutable('2012-04-08 21:00:00'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsExpireAfterXDays(2);
        $walletType->markAsPendingForXDays(2);

        yield 'Wallet with exp and pen, with override exp and pen (days)' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $walletType,
            RangePeriodDate::fromDays(5),
            RangePeriodDate::fromDays(8),

            new DateTimeImmutable('2012-04-08 21:00:00'), // lockedUntil
            new DateTimeImmutable('2012-04-16 21:00:00'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsExpireAfterXDays(2);
        $walletType->markAsPendingForXDays(2);

        yield 'Wallet with exp and pen, with override exp and pen (dates)' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $walletType,
            RangePeriodDate::fromDate(new DateTimeImmutable('2012-04-09 22:00:00')),
            RangePeriodDate::fromDate(new DateTimeImmutable('2012-04-17 22:00:00')),

            new DateTimeImmutable('2012-04-09 22:00:00'), // lockedUntil
            new DateTimeImmutable('2012-04-17 22:00:00'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsPendingForXDays(5);
        $walletType->markAsExpireAfterXDays(8);

        yield 'Wallet with exp and pen, without override' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $walletType,
            null,
            null,

            new DateTimeImmutable('2012-04-08 21:00:00'), // lockedUntil
            new DateTimeImmutable('2012-04-16 21:00:00'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsPendingForXDays(5);
        $walletType->markAsExpireAfterXDays(8);

        yield 'Wallet with exp and pen, with override to null' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $walletType,
            RangePeriodDate::empty(),
            RangePeriodDate::empty(),

            null, // lockedUntil
            null, // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsPendingForXDays(5);

        yield 'Wallet with 5 days pending' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $walletType,
            null,
            null,

            new DateTimeImmutable('2012-04-08 21:00:00'), // lockedUntil
            null, // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsNeverPending();

        yield 'Wallet with never pending' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $walletType,
            null,
            null,

            null, // lockedUntil
            null, // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsExpireAfterXDays(5);

        yield 'Wallet with in 5 days expired' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $walletType,
            null,
            null,

            null, // lockedUntil
            new DateTimeImmutable('2012-04-08 21:00:00'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsNeverExpire();

        yield 'Wallet with never expired' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $walletType,
            null,
            null,

            null, // lockedUntil
            null, // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsExpireAtTheEndOfTheMonth();

        yield 'Wallet with expires at the end of the month' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $walletType,
            null,
            null,

            null, // lockedUntil
            new DateTimeImmutable('2012-04-30 23:59:59'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsExpireAfterXYears(0);

        yield 'Wallet with expires at the end of this year' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $walletType,
            null,
            null,

            null, // lockedUntil
            new DateTimeImmutable('2012-12-31 23:59:59'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsExpireAfterXYears(1);

        yield 'Wallet with expires at the end of 1-th year' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $walletType,
            null,
            null,

            null, // lockedUntil
            new DateTimeImmutable('2013-12-31 23:59:59'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsExpireAtTheEndOfTheMonth();

        yield 'Wallet with expires at the end of the month, with override pen' => [
            new DateTimeImmutable('2012-04-03 21:00:00'),
            $walletType,
            RangePeriodDate::fromDays(5),
            null,

            new DateTimeImmutable('2012-04-08 21:00:00'), // lockedUntil
            new DateTimeImmutable('2012-04-30 23:59:59'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsExpireAtTheEndOfTheMonth();

        yield 'Wallet with expires at the end of the month, with override pen to next month' => [
            new DateTimeImmutable('2012-04-28 21:00:00'),
            $walletType,
            RangePeriodDate::fromDays(5),
            null,

            new DateTimeImmutable('2012-05-03 21:00:00'), // lockedUntil
            new DateTimeImmutable('2012-04-30 23:59:59'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsExpireAfterXYears(0);

        yield 'Wallet with expires at the end of this year, with override pen to next year' => [
            new DateTimeImmutable('2012-12-31 21:00:00'),
            $walletType,
            RangePeriodDate::fromDays(5),
            null,

            new DateTimeImmutable('2013-01-05 21:00:00'), // lockedUntil
            new DateTimeImmutable('2012-12-31 23:59:59'), // expiredAt
        ];

        $walletType = $this->getWalletType();

        yield 'Transfer with pending as date and expires as days' => [
            new DateTimeImmutable('2012-01-31 21:00:00'),
            $walletType,
            RangePeriodDate::fromDate(new DateTimeImmutable('2012-11-18 21:00:00')),
            RangePeriodDate::fromDays(5),

            new DateTimeImmutable('2012-11-18 21:00:00'), // lockedUntil
            new DateTimeImmutable('2012-11-23 21:00:00'), // expiredAt
        ];

        $walletType = $this->getWalletType();

        yield 'Transfer with pending before registration' => [
            new DateTimeImmutable('2012-01-05 21:00:00'),
            $walletType,
            RangePeriodDate::fromDate(new DateTimeImmutable('2012-01-04 21:00:00')),
            RangePeriodDate::fromDays(5),

            null, // lockedUntil
            new DateTimeImmutable('2012-01-10 21:00:00'), // expiredAt
        ];

        $walletType = $this->getWalletType();

        yield 'Transfer with expiration before registration' => [
            new DateTimeImmutable('2012-01-05 21:00:00'),
            $walletType,
            null,
            RangePeriodDate::fromDate(new DateTimeImmutable('2012-01-04 21:00:00')),

            null, // lockedUntil
            new DateTimeImmutable('2012-01-05 21:00:00'), // expiredAt
        ];

        $walletType = $this->getWalletType();

        yield 'Transfer with expiration before pending' => [
            new DateTimeImmutable('2012-01-05 21:00:00'),
            $walletType,
            RangePeriodDate::fromDate(new DateTimeImmutable('2012-01-07 21:00:00')),
            RangePeriodDate::fromDate(new DateTimeImmutable('2012-01-06 21:00:00')),

            new DateTimeImmutable('2012-01-07 21:00:00'), // lockedUntil
            new DateTimeImmutable('2012-01-06 21:00:00'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsExpireChosenDate(new DateTimeImmutable('2019-01-07 12:00'));

        yield 'Wallet with expire chosen date when is greater' => [
            new DateTimeImmutable('2012-01-05 21:00:00'),
            $walletType,
            null,
            null,

            null,
            new DateTimeImmutable('2012-01-07 23:59:59'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsExpireChosenDate(new DateTimeImmutable('2019-01-07 12:00'));

        yield 'Wallet with expire chosen date when is less' => [
            new DateTimeImmutable('2012-06-12 21:00:00'),
            $walletType,
            null,
            null,

            null,
            new DateTimeImmutable('2013-01-07 23:59:59'), // expiredAt
        ];

        $walletType = $this->getWalletType();
        $walletType->markAsExpireChosenDate(new DateTimeImmutable('2019-01-07 12:00'));

        yield 'Wallet with expire chosen date when is the same' => [
            new DateTimeImmutable('2012-01-07 01:00:00'),
            $walletType,
            null,
            null,

            null,
            new DateTimeImmutable('2013-01-07 23:59:59'), // expiredAt
        ];
    }

    public function getWalletType(): WalletType
    {
        return WalletType::create(
            new WalletTypeId('********-0000-0000-0000-********0003'),
            '',
            $this->createMock(Store::class),
            '',
            '',
            true,
            null,
            true,
            false,
            false
        );
    }
}
