<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Account\Unit\Domain;

use OpenLoyalty\Account\Domain\Account;
use OpenLoyalty\Account\Domain\AccountRepositoryInterface;
use OpenLoyalty\Account\Domain\Exception\WalletTypeNotFoundException;
use OpenLoyalty\Account\Domain\Factory\AccountFactoryInterface;
use OpenLoyalty\Account\Domain\WalletCreator;
use OpenLoyalty\Account\Domain\WalletRepositoryInterface;
use OpenLoyalty\Account\Domain\WalletType;
use OpenLoyalty\Account\Domain\WalletTypeRepositoryInterface;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use PHPUnit\Framework\TestCase;

final class WalletCreatorTest extends TestCase
{
    private AccountRepositoryInterface $accountRepository;
    private AccountFactoryInterface $accountFactory;
    private \OpenLoyalty\Account\Domain\WalletRepositoryInterface $walletRepository;
    private WalletTypeRepositoryInterface $walletTypeRepository;
    private UuidGeneratorInterface $uuidGenerator;

    protected function setUp(): void
    {
        $this->accountRepository = $this->createMock(\OpenLoyalty\Account\Domain\AccountRepositoryInterface::class);
        $this->accountFactory = $this->createMock(AccountFactoryInterface::class);
        $this->walletTypeRepository = $this->createMock(\OpenLoyalty\Account\Domain\WalletTypeRepositoryInterface::class);
        $this->walletRepository = $this->createMock(WalletRepositoryInterface::class);
        $this->uuidGenerator = $this->createMock(UuidGeneratorInterface::class);
    }

    /**
     * @test
     */
    public function it_creates_an_new_wallet(): void
    {
        $walletCreator = new WalletCreator(
            $this->accountRepository,
            $this->accountFactory,
            $this->walletRepository,
            $this->walletTypeRepository,
            $this->uuidGenerator
        );

        $this->walletTypeRepository->method('byCode')
            ->willReturn($this->createMock(\OpenLoyalty\Account\Domain\WalletType::class));

        $this->uuidGenerator->method('generate')->willReturn('********-0000-3333-0000-********0000');

        $this->accountFactory->method('create')
            ->willReturn($this->createMock(Account::class));

        $this->walletRepository->expects($this->once())->method('save');
        $this->accountRepository->expects($this->once())->method('save');

        $wallet = $walletCreator->create(
            new StoreId('********-0000-1111-0000-********0000'),
            new CustomerId('********-0000-2222-0000-********0000'),
            'walletCode',
            new \DateTimeImmutable()
        );
        $this->assertEquals(
            new CustomerId('********-0000-2222-0000-********0000'),
            $wallet->getOwner()
        );
    }

    /**
     * @test
     */
    public function it_creates_an_new_wallet_for_default_wallet_type(): void
    {
        $walletCreator = new \OpenLoyalty\Account\Domain\WalletCreator(
            $this->accountRepository,
            $this->accountFactory,
            $this->walletRepository,
            $this->walletTypeRepository,
            $this->uuidGenerator
        );

        $this->walletTypeRepository->method('byCode')
            ->willReturn($this->createMock(WalletType::class));

        $this->uuidGenerator->method('generate')->willReturn('********-0000-3333-0000-********0000');

        $this->accountFactory->method('create')
            ->willReturn($this->createMock(Account::class));

        $this->walletRepository->expects($this->once())->method('save');
        $this->accountRepository->expects($this->once())->method('save');

        $wallet = $walletCreator->createDefault(
            new StoreId('********-0000-1111-0000-********0000'),
            new CustomerId('********-0000-2222-0000-********0000'),
        );
        $this->assertEquals(
            new CustomerId('********-0000-2222-0000-********0000'),
            $wallet->getOwner()
        );
    }

    /**
     * @test
     */
    public function it_throws_an_exception_when_wallet_does_not_exist(): void
    {
        $walletCreator = new WalletCreator(
            $this->accountRepository,
            $this->accountFactory,
            $this->walletRepository,
            $this->walletTypeRepository,
            $this->uuidGenerator
        );

        $this->expectException(WalletTypeNotFoundException::class);

        $walletCreator->create(
            new StoreId('********-0000-1111-0000-********0000'),
            new CustomerId('********-0000-2222-0000-********0000'),
            'walletCode',
            new \DateTimeImmutable()
        );
    }
}
