<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Account\Unit\Domain\Webhook\DataProvider;

use OpenLoyalty\Account\Domain\SystemEvent\LevelWillExpire;
use OpenLoyalty\Account\Domain\Webhook\DataProvider\LevelWillExpireDataProvider;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Webhook\Response\WebhookData;
use PHPUnit\Framework\TestCase;

final class LevelWillExpireDataProviderTest extends TestCase
{
    private LevelWillExpireDataProvider $dataProvider;

    protected function setUp(): void
    {
        $this->dataProvider = new LevelWillExpireDataProvider();
    }

    /**
     * @test
     */
    public function it_returns_webhook_data(): void
    {
        $storeId = new StoreId('********-0000-0000-0000-************');

        $event = new LevelWillExpire(
            [
                'data' => 'someData',
                'storeCode' => 'DEFAULT',
            ],
            $storeId,
            'DEFAULT'
        );

        $webhookData = new WebhookData(
            'LevelWillExpire',
            'DEFAULT',
            [
                'data' => 'someData',
            ]
        );

        $this->assertEqualsWithDelta($webhookData, $this->dataProvider->getData($event), 0.01);
    }
}
