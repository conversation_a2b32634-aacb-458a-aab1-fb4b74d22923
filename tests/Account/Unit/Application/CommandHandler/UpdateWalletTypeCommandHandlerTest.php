<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Test\Account\Unit\Application\CommandHandler;

use OpenLoyalty\Account\Application\Command\UpdateWalletType;
use OpenLoyalty\Account\Application\CommandHandler\UpdateWalletTypeCommandHandler;
use OpenLoyalty\Account\Domain\Exception\WalletTypeNotFoundException;
use OpenLoyalty\Account\Domain\SystemEvent\WalletTypeWasChanged;
use OpenLoyalty\Account\Domain\WalletType;
use OpenLoyalty\Account\Domain\WalletTypeRepositoryInterface;
use OpenLoyalty\Account\Domain\WalletTypeTranslation;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventBusInterface;
use OpenLoyalty\Points\Domain\Expiring\ExpiringTransferMode;
use PHPUnit\Framework\TestCase;

final class UpdateWalletTypeCommandHandlerTest extends TestCase
{
    private UpdateWalletTypeCommandHandler $commandHandler;
    private WalletTypeRepositoryInterface $walletTypeRepository;
    private UuidGeneratorInterface $uuidGenerator;
    private EventBusInterface $eventBus;

    protected function setUp(): void
    {
        $this->walletTypeRepository = $this->createMock(WalletTypeRepositoryInterface::class);
        $this->uuidGenerator = $this->createMock(UuidGeneratorInterface::class);
        $this->eventBus = $this->createMock(EventBusInterface::class);
        $this->commandHandler = new UpdateWalletTypeCommandHandler(
            $this->walletTypeRepository,
            $this->uuidGenerator,
            $this->createMock(AnalyticsEventBusInterface::class),
            $this->eventBus
        );
    }

    /**
     * @test
     */
    public function it_updates_wallet_type(): void
    {
        $walletTypeId = new WalletTypeId('00000000-0000-0000-0000-000000000000');
        $translations = [
            'en' => $this->createMock(WalletTypeTranslation::class),
        ];
        $unitSingularName = 'point';
        $unitPluralName = 'points';

        $command = new UpdateWalletType(
            $walletTypeId,
            $unitSingularName,
            $unitPluralName,
            true,
            $translations,
            ExpiringTransferMode::EXPIRING_AFTER_X_DAYS,
            10,
            null,
            null,
            true
        );

        $walletType = $this->createMock(WalletType::class);

        $this->walletTypeRepository->method('byId')->willReturn($walletType);

        $walletType->method('isActive')->willReturn(false);
        $walletType->method('getName')->willReturn('Points wallet');

        $walletType->expects(self::once())->method('assignTranslations')->with($translations);
        $walletType->expects(self::once())->method('activate');
        $walletType->expects(self::once())->method('setUnitSingularName')->with($unitSingularName);
        $walletType->expects(self::once())->method('setUnitPluralName')->with($unitPluralName);
        $walletType->expects(self::once())->method('markAsExpireAfterXDays')->with(10);
        $walletType->expects(self::once())->method('markAsNeverPending')->with();

        $this->walletTypeRepository->expects(self::once())->method('save')->with($walletType);
        $this->uuidGenerator->method('generate')->willReturn('fa1e6a05-92d6-4c25-9db0-a63d4d876a20');

        $this->eventBus->expects(self::once())->method('dispatch')->with(new WalletTypeWasChanged($walletType, false));

        $this->commandHandler->__invoke($command);
    }

    /**
     * @test
     */
    public function it_updates_wallet_type_for_pending_without_days(): void
    {
        $walletTypeId = new WalletTypeId('00000000-0000-0000-0000-000000000000');
        $translations = [
            'en' => $this->createMock(WalletTypeTranslation::class),
        ];
        $unitSingularName = 'point';
        $unitPluralName = 'points';

        $command = new UpdateWalletType(
            $walletTypeId,
            $unitSingularName,
            $unitPluralName,
            true,
            $translations,
            ExpiringTransferMode::EXPIRING_ALL_TIME_ACTIVE,
            null,
            null,
            null,
            false
        );

        $walletType = $this->createMock(WalletType::class);

        $this->walletTypeRepository->method('byId')->willReturn($walletType);

        $walletType->method('isActive')->willReturn(false);
        $walletType->method('getName')->willReturn('Points wallet');

        $walletType->expects(self::once())->method('assignTranslations')->with($translations);
        $walletType->expects(self::once())->method('activate');
        $walletType->expects(self::once())->method('setUnitSingularName')->with($unitSingularName);
        $walletType->expects(self::once())->method('setUnitPluralName')->with($unitPluralName);
        $walletType->expects(self::once())->method('markAsNeverExpire')->with();
        $walletType->expects(self::once())->method('markAsNeverPending')->with();

        $this->walletTypeRepository->expects(self::once())->method('save')->with($walletType);
        $this->uuidGenerator->method('generate')->willReturn('fa1e6a05-92d6-4c25-9db0-a63d4d876a20');

        $this->commandHandler->__invoke($command);
    }

    /**
     * @test
     */
    public function it_throws_not_found_exception(): void
    {
        $walletTypeId = new WalletTypeId('00000000-0000-0000-0000-000000000000');
        $translations = [
            'en' => $this->createMock(WalletTypeTranslation::class),
        ];
        $unitSingularName = 'point';
        $unitPluralName = 'points';

        $command = new UpdateWalletType(
            $walletTypeId,
            $unitSingularName,
            $unitPluralName,
            true,
            $translations,
            ExpiringTransferMode::EXPIRING_AFTER_X_DAYS,
            null,
            null,
            null,
            false
        );

        $this->walletTypeRepository->method('byId')->willReturn(null);
        $this->walletTypeRepository->expects(self::never())->method('save');

        $this->expectException(WalletTypeNotFoundException::class);

        $this->commandHandler->__invoke($command);
    }
}
