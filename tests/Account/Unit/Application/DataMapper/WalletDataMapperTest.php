<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Account\Unit\Application\DataMapper;

use OpenLoyalty\Account\Application\DataMapper\WalletDataMapper;
use OpenLoyalty\Account\Application\DataMapper\WalletTypeDataMapper;
use OpenLoyalty\Account\Domain\Provider\AccountProviderInterface;
use OpenLoyalty\Account\Domain\ReadModel\Entity\Wallet as WalletReadModel;
use OpenLoyalty\Account\Domain\ReadModel\Projector\MassWalletRequestProjection;
use OpenLoyalty\Account\Domain\ReadModel\Projector\SingleWalletRequestProjection;
use OpenLoyalty\Account\Domain\Wallet;
use OpenLoyalty\Account\Domain\WalletType;
use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\ReadModel\ProjectionProviderInterface;
use OpenLoyalty\Core\Domain\ValueObject\Wallet\Account;
use PHPUnit\Framework\TestCase;

final class WalletDataMapperTest extends TestCase
{
    private WalletTypeDataMapper $walletTypeDataMapper;
    private AccountProviderInterface $accountProvider;
    private ProjectionProviderInterface $projectionProvider;
    private WalletDataMapper $walletDataMapper;

    protected function setUp(): void
    {
        $this->projectionProvider = $this->createMock(ProjectionProviderInterface::class);
        $this->walletTypeDataMapper = $this->createMock(WalletTypeDataMapper::class);
        $this->accountProvider = $this->createMock(AccountProviderInterface::class);
        $this->walletDataMapper = new WalletDataMapper(
            $this->walletTypeDataMapper,
            $this->accountProvider,
            $this->projectionProvider
        );
    }

    /**
     * @test
     */
    public function it_maps_single_object(): void
    {
        $wallet1 = $this->createMock(Wallet::class);
        $walletType1 = $this->createMock(WalletType::class);
        $walletTypeId1 = $this->createMock(WalletTypeId::class);

        $ownerId = new CustomerId('********-0000-aaaa-0000-************');

        $walletId1 = new AccountId('********-0000-aaaa-0000-********0001');

        $storeId = new StoreId('********-0000-aaaa-0000-************');

        $wallet1->method('getAccountId')->willReturn($walletId1);
        $wallet1->method('getWalletId')->willReturn($walletId1);
        $wallet1->method('getOwner')->willReturn($ownerId);
        $wallet1->method('getStoreId')->willReturn($storeId);
        $wallet1->method('getType')->willReturn($walletType1);

        $walletProjection1 = $this->createMock(WalletReadModel::class);
        $walletProjection1->method('getWalletId')->willReturn($walletId1);
        $walletProjection1->method('getOwner')->willReturn($ownerId);
        $walletProjection1->method('getStoreId')->willReturn($storeId);
        $walletProjection1->method('getWalletTypeId')->willReturn($walletTypeId1);
        $walletProjection1->method('getUsedLimitUnits')->willReturn(0.0);

        $account1 = $this->createMock(Account::class);

        $this->projectionProvider
            ->method('getProjection')
            ->with(
                new SingleWalletRequestProjection($storeId, $walletId1),
            )->willReturn(
                $walletProjection1
            );

        $this->accountProvider
            ->method('provide')
            ->with($wallet1, true)
            ->willReturn(
                $account1
            );

        $walletResponse = $this->walletDataMapper->map(
            $wallet1
        );

        $this->assertSame($walletId1, $walletResponse->getWalletId());
    }

    /**
     * @test
     */
    public function it_maps_for_list(): void
    {
        $wallet1 = $this->createMock(Wallet::class);
        $wallet2 = $this->createMock(Wallet::class);
        $walletType1 = $this->createMock(WalletType::class);
        $walletType2 = $this->createMock(WalletType::class);
        $walletTypeId1 = $this->createMock(WalletTypeId::class);
        $walletTypeId2 = $this->createMock(WalletTypeId::class);

        $ownerId = new CustomerId('********-0000-aaaa-0000-************');

        $walletId1 = new AccountId('********-0000-aaaa-0000-********0001');
        $walletId2 = new AccountId('********-0000-aaaa-0000-********0002');

        $storeId = new StoreId('********-0000-aaaa-0000-************');

        $wallet1->method('getAccountId')->willReturn($walletId1);
        $wallet1->method('getWalletId')->willReturn($walletId1);
        $wallet1->method('getOwner')->willReturn($ownerId);
        $wallet1->method('getStoreId')->willReturn($storeId);
        $wallet1->method('getType')->willReturn($walletType1);

        $wallet2->method('getAccountId')->willReturn($walletId2);
        $wallet2->method('getWalletId')->willReturn($walletId2);
        $wallet2->method('getOwner')->willReturn($ownerId);
        $wallet2->method('getStoreId')->willReturn($storeId);
        $wallet2->method('getType')->willReturn($walletType2);

        $walletProjection1 = $this->createMock(WalletReadModel::class);
        $walletProjection1->method('getWalletId')->willReturn($walletId1);
        $walletProjection1->method('getOwner')->willReturn($ownerId);
        $walletProjection1->method('getStoreId')->willReturn($storeId);
        $walletProjection1->method('getWalletTypeId')->willReturn($walletTypeId1);
        $walletProjection1->method('getUsedLimitUnits')->willReturn(0.0);

        $walletProjection2 = $this->createMock(WalletReadModel::class);
        $walletProjection2->method('getWalletId')->willReturn($walletId2);
        $walletProjection2->method('getOwner')->willReturn($ownerId);
        $walletProjection2->method('getStoreId')->willReturn($storeId);
        $walletProjection2->method('getWalletTypeId')->willReturn($walletTypeId2);
        $walletProjection2->method('getUsedLimitUnits')->willReturn(0.0);

        $account1 = $this->createMock(Account::class);
        $account2 = $this->createMock(Account::class);

        $this->projectionProvider
            ->method('getProjections')
            ->with(
                new MassWalletRequestProjection($storeId, [$walletId1, $walletId2]),
            )->willReturn(
                [
                    (string) $walletId1 => $walletProjection1,
                    (string) $walletId2 => $walletProjection2,
                ]
            );

        $this->accountProvider
            ->method('provideAccountsForMember')
            ->with([$walletId1, $walletId2], true)
            ->willReturn(
                [
                    (string) $walletId1 => $account1,
                    (string) $walletId2 => $account2,
                ]
            );

        $walletResponse = $this->walletDataMapper->mapList(
              [
                  $wallet1,
                  $wallet2,
              ],
            $storeId
        );

        $this->assertCount(2, $walletResponse);
    }
}
