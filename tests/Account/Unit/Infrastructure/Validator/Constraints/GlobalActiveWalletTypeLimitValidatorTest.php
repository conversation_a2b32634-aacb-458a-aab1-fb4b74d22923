<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Account\Unit\Infrastructure\Validator\Constraints;

use OpenLoyalty\Account\Domain\Checker\ActiveWalletTypeLimitCheckerInterface;
use OpenLoyalty\Account\Infrastructure\Validator\Constraints\GlobalActiveWalletTypeLimit;
use OpenLoyalty\Account\Infrastructure\Validator\Constraints\GlobalActiveWalletTypeLimitValidator;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;
use Symfony\Contracts\Translation\TranslatorInterface;

final class GlobalActiveWalletTypeLimitValidatorTest extends ConstraintValidatorTestCase
{
    private MockObject&TranslatorInterface $translator;
    private MockObject&ActiveWalletTypeLimitCheckerInterface $limitChecker;

    protected function setUp(): void
    {
        $this->translator = $this->createMock(TranslatorInterface::class);
        $this->limitChecker = $this->createMock(ActiveWalletTypeLimitCheckerInterface::class);
        parent::setUp();
    }

    protected function createValidator(): ConstraintValidator
    {
        return new GlobalActiveWalletTypeLimitValidator(
            $this->translator,
            $this->limitChecker
        );
    }

    /**
     * @test
     */
    public function it_raises_exception_when_global_limit_is_reached(): void
    {
        $this->translator->method('trans')->willReturn('LimitReachedMessage');
        $this->limitChecker->method('isGlobalLimitReached')->willReturn(true);

        $this->validator->validate(
            true,
            new GlobalActiveWalletTypeLimit()
        );

        $this->buildViolation('LimitReachedMessage')->assertRaised();
    }

    /**
     * @test
     */
    public function it_does_not_raise_exception_when_global_limit_is_reached_but_save_not_active(): void
    {
        $this->translator->method('trans')->willReturn('LimitReachedMessage');
        $this->limitChecker->method('isGlobalLimitReached')->willReturn(true);

        $this->validator->validate(
            false,
            new GlobalActiveWalletTypeLimit()
        );

        $this->assertNoViolation();
    }
}
