<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Account\Unit\Infrastructure\Security\Voter;

use OpenLoyalty\Account\Domain\Wallet;
use OpenLoyalty\Account\Infrastructure\Security\Voter\WalletVoter;
use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseVoterTest;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionEvaluator;
use PHPUnit\Framework\MockObject\MockObject;

final class WalletVoterTest extends BaseVoterTest
{
    private const WALLET_ID = '********-0000-0000-0000-************';

    /**
     * @test
     */
    public function it_works(): void
    {
        /** @var UserPermissionCheckerInterface&MockObject $permissionChecker */
        $permissionChecker = $this->getMockBuilder(UserPermissionCheckerInterface::class)->getMock();
        $permissionChecker->method('hasPermissionInCurrentStore')->willReturnCallback(function (User $user, string $resource, array $accesses) {
            return (new UserPermissionEvaluator())->hasPermission(null, $user, $resource, $accesses);
        });

        $attributes = [
            WalletVoter::VIEW_WALLET => ['customer' => false, 'admin' => true, 'admin_reporter' => true, 'id' => self::WALLET_ID],
            WalletVoter::LIST_WALLET => ['customer' => true, 'admin' => true, 'admin_reporter' => true, 'id' => self::USER_ID],
        ];

        $campaignVoter = new WalletVoter($permissionChecker);
        $this->assertVoterAttributes($campaignVoter, $attributes);
    }

    protected function getSubjectById($id): mixed
    {
        if (self::WALLET_ID === $id) {
            $wallet = $this->getMockBuilder(Wallet::class)->disableOriginalConstructor()->getMock();
            $wallet->method('getWalletId')->willReturn(new AccountId($id));

            return $wallet;
        }

        if (self::USER_ID === $id) {
            $member = $this->getMockBuilder(Customer::class)->disableOriginalConstructor()->getMock();
            $member->method('getCustomerId')->willReturn(new CustomerId($id));

            return $member;
        }

        return null;
    }
}
