<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Settings\Integration\Traits;

use Symfony\Component\HttpKernel\HttpKernelBrowser;

trait SettingsApiTrait
{
    private function updateSetting(
        HttpKernelBrowser $httpClient,
        string $storeCode,
        string $settingKey,
        mixed $settingValue,
    ): void {
        $httpClient->request(
            'GET',
            '/api/'.$storeCode.'/settings'
        );

        $response = $httpClient->getResponse();
        $currentSettings = json_decode($response->getContent(), true)['settings'];

        $currentSettings[$settingKey] = $settingValue;

        $httpClient->request(
            'PATCH',
            '/api/'.$storeCode.'/settings',
            ['settings' => $currentSettings]
        );
    }
}
