<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Settings\Unit\Infrastructure\EventSubscriber;

use OpenLoyalty\Core\Infrastructure\Service\LocaleProviderInterface;
use OpenLoyalty\Settings\Infrastructure\EventSubscriber\RequestLocaleListener;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpKernel\Event\RequestEvent as GetResponseEvent;
use Symfony\Component\HttpKernel\EventListener\LocaleListener;
use Symfony\Component\HttpKernel\HttpKernelInterface;

final class RequestLocaleListenerTest extends TestCase
{
    protected function createRequestListener(Request $request, string $defaultLocale): RequestLocaleListener
    {
        $localeProvider = $this->getMockForAbstractClass(LocaleProviderInterface::class);
        $localeProvider->expects($this->any())->method('getConfigurationDefaultLocale')->willReturn($defaultLocale);
        $requestStack = new RequestStack();
        $requestStack->push($request);

        $localeListener = $this->createMock(LocaleListener::class);

        $requestListener = new RequestLocaleListener(
            $localeListener
        );
        $requestListener->setLocaleProvider($localeProvider);

        return $requestListener;
    }

    /**
     * @test
     */
    public function it_sets_default_locale_from_configuration(): void
    {
        $request = new Request();
        $listener = $this->createRequestListener($request, 'de');
        $httpKernelInterface = $this->getMockForAbstractClass(HttpKernelInterface::class);
        $event = new GetResponseEvent($httpKernelInterface, $request, HttpKernelInterface::MASTER_REQUEST);

        $listener->onKernelRequest($event);
        $this->assertSame('de', $event->getRequest()->getLocale());
        $this->assertSame('de', $event->getRequest()->getDefaultLocale());
    }

    /**
     * @test
     */
    public function it_sets_locale_from_request(): void
    {
        $request = new Request(['_locale' => 'de']);
        $listener = $this->createRequestListener($request, 'pl');
        $httpKernelInterface = $this->getMockForAbstractClass(HttpKernelInterface::class);
        $event = new GetResponseEvent($httpKernelInterface, $request, HttpKernelInterface::MASTER_REQUEST);

        $listener->onKernelRequest($event);
        $this->assertSame('de', $event->getRequest()->getLocale());
        $this->assertSame('pl', $event->getRequest()->getDefaultLocale());
    }

    /**
     * @test
     */
    public function it_sets_locale_from_request_without_locale_provider(): void
    {
        $request = new Request(['_locale' => 'de']);
        $requestStack = new RequestStack();
        $requestStack->push($request);
        $localeListener = $this->createMock(LocaleListener::class);

        $listener = new RequestLocaleListener(
            $localeListener
        );

        $httpKernelInterface = $this->getMockForAbstractClass(HttpKernelInterface::class);
        $event = new GetResponseEvent($httpKernelInterface, $request, HttpKernelInterface::MASTER_REQUEST);

        $listener->onKernelRequest($event);
        $this->assertSame('de', $event->getRequest()->getLocale());
        $this->assertSame('en', $event->getRequest()->getDefaultLocale());
    }
}
