<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Reward\Contract\Webhook;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Reward\Application\Coupon\CouponManagerInterface;
use OpenLoyalty\Test\Common\Integration\AbstractWebhookApiTest;
use OpenLoyalty\Test\Utils\Service\WebhookDetails;

class CouponWillExpireTest extends AbstractWebhookApiTest
{
    /**
     * @test
     */
    public function it_checks_webhook_coupon_will_expire(): void
    {
        $tenantId = $this->createTenant('it_check_webhook_coupon_will_expire');
        $memberId = $this->createMember('it_check_webhook_coupon_will_expire');
        $rewardId = $this->createReward('it_check_webhook_coupon_will_expire');
        $this->buyReward('it_check_webhook_coupon_will_expire', $rewardId, $memberId);
        $this->addAllWebhooks('it_check_webhook_coupon_will_expire');

        //trigger
        self::getContainer()->get(CouponManagerInterface::class)->sendExpiringNotificationsToAllCustomers(
            new StoreId($tenantId),
            new DateTimeImmutable('2024-05-10 00:00+02:00'),
            new DateTimeImmutable('2024-05-27 00:00+02:00')
        );

        /** @var WebhookDetails[] $webhooks */
        $webhooks = $this->webhookClientMock->getSentWebhooks();
        $this->assertSame(1, $this->webhookClientMock->getWebhooksCount());
        $this->assertSame('CouponWillExpire', $webhooks[0]->getWebhookName());
        $this->assertSameJson($this->getCouponWillExpireWebhookJsonBody($memberId), $webhooks[0]->getBodyJson());
        $this->assertWebhookHeaders($webhooks[0]->getHeaders());
    }

    private function createMember(
        string $tenantCode
    ): string {
        $customerData = [
            'customer' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'loyaltyCardNumber' => '123',
                'email' => '<EMAIL>',
                'gender' => 'male',
                'birthDate' => '1991-06-16',
                'agreement1' => true,
                'phone' => '*********',
                'address' => [
                    'street' => 'street',
                    'address1' => 'building',
                    'address2' => '1',
                    'city' => 'city',
                    'country' => 'ZW',
                    'postal' => '12345',
                    'province' => 'state',
                ],
                'company' => [
                    'name' => 'company',
                    'nip' => '123',
                ],
                'labels' => [
                    [
                        'key' => 'test',
                        'value' => 'true',
                    ],
                ],
            ],
        ];

        $this->client->jsonRequest('POST', '/api/'.$tenantCode.'/member', $customerData);
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        return $data['customerId'];
    }

    private function createReward(string $tenantCode): string
    {
        $this->client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/reward',
            [
                'reward' => [
                    'reward' => 'static_coupon',
                    'active' => true,
                    'levels' => [],
                    'segments' => [],
                    'target' => 'level',
                    'usageLimit' => [
                        'general' => 10,
                        'perUser' => 1,
                    ],
                    'activity' => [
                        'allTime' => true,
                    ],
                    'visibility' => [
                        'allTime' => true,
                    ],
                    'labels' => [
                        ['key' => 'key0', 'value' => 'value0'],
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                    'taxPriceValue' => 99.95,
                    'tax' => 23.5,
                    'couponValue' => 12,
                    'translations' => [
                        'en' => [
                            'name' => 'test',
                            'shortDescription' => 'short description',
                            'brandName' => 'Somebrand EN',
                        ],
                    ],
                    'couponGenerator' => [
                        'length' => 12,
                        'characterSet' => 'alphanum',
                    ],
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        return $data['rewardId'];
    }

    private function buyReward(string $tenantCode, string $rewardId, string $memberId): void
    {
        $this->client->request(
            'POST',
            '/api/'.$tenantCode.'/reward/'.$rewardId.'/buy',
            [
                'customerId' => $memberId,
                'quantity' => 1,
                'withoutPoints' => false,
                'rewardWalletCode' => null,
                'dateValid' => '2024-05-17 00:00+02:00',
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
    }

    private function getCouponWillExpireWebhookJsonBody(string $customerId): string
    {
        return <<<EOT
        {
          "eventName": "CouponWillExpire",
          "messageId": "@uuid@",
          "storeCode": "it_check_webhook_coupon_will_expire",
          "createdAt": "@datetime@",
          "data": {
            "customer": {
              "customerId": "$customerId",
              "email": "<EMAIL>",
              "phone": "*********",
              "loyaltyCardNumber": "123"
            },
            "issuedCoupon": {
              "valueType": "money",
              "code": "@string@",
              "value": 12.0,
              "activeTo": "@datetime@"
            }
          },
          "requestId": "@uuid@"
        }
        EOT;
    }
}
