<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Reward\Contract\Webhook;

use DateTimeImmutable;
use DateTimeInterface;
use OpenLoyalty\Test\Common\Integration\AbstractWebhookApiTest;
use OpenLoyalty\Test\Utils\Service\WebhookDetails;

final class MaterialRewardUpdatedTest extends AbstractWebhookApiTest
{
    /**
     * @test
     */
    public function it_checks_reward_updated_webhook(): void
    {
        $tenantCode = 'material_reward_updated';
        $this->createTenant($tenantCode);
        $this->createWebhook($tenantCode, 'RewardUpdated');
        $rewardId = $this->createReward($tenantCode);
        $tierSetId = $this->createTierSet($tenantCode);
        $tierId = $this->addTierToTierSet($tenantCode, $tierSetId, 'Reward updated tier');

        //trigger
        $this->updateReward($tenantCode, $rewardId, $tierId);

        /** @var WebhookDetails[] $webhooks */
        $webhooks = $this->webhookClientMock->getSentWebhooks();
        $this->assertSame(1, $this->webhookClientMock->getWebhooksCount());
        $this->assertSame('RewardUpdated', $webhooks[0]->getWebhookName());
        $this->assertSameJson($this->getRewardUpdatedWebhookJsonBody($rewardId, $tierId), $webhooks[0]->getBodyJson());
        $this->assertWebhookHeaders($webhooks[0]->getHeaders());
    }

    private function createReward(string $tenantCode): string
    {
        $rewardData = [
            'reward' => [
                'reward' => 'material',
                'active' => true,
                'usageLimit' => [
                    'general' => -1,
                    'perUser' => 2,
                ],
                'costInPoints' => 10,
                'activity' => [
                    'allTime' => false,
                    'from' => (new DateTimeImmutable('2024-01-01 00:00:00'))->format(DateTimeInterface::ATOM),
                    'to' => (new DateTimeImmutable('2030-12-31 23:59:59'))->format(DateTimeInterface::ATOM),
                ],
                'visibility' => [
                    'allTime' => false,
                    'from' => (new DateTimeImmutable('2024-01-01 00:00:00'))->format(DateTimeInterface::ATOM),
                    'to' => (new DateTimeImmutable('2030-12-31 23:59:59'))->format(DateTimeInterface::ATOM),
                ],
                'labels' => [
                    ['key' => 'key0', 'value' => 'value0'],
                    ['key' => 'key1', 'value' => 'value1'],
                ],
                'taxPriceValue' => 23,
                'tax' => 23,
                'translations' => [
                    'en' => [
                        'name' => 'Material reward',
                        'shortDescription' => 'Material reward test',
                        'brandName' => 'Material reward brand',
                    ],
                ],
            ],
        ];

        $this->client->jsonRequest('POST', '/api/'.$tenantCode.'/reward', $rewardData);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        return $data['rewardId'];
    }

    private function updateReward(
        string $tenantCode,
        string $rewardId,
        string $tierId
    ): void {
        $rewardData = [
            'reward' => [
                'active' => true,
                'target' => 'level',
                'levels' => [$tierId],
                'usageLimit' => [
                    'general' => 100,
                    'perUser' => 1,
                ],
                'costInPoints' => 10,
                'activity' => [
                    'allTime' => false,
                    'from' => (new DateTimeImmutable('2024-01-01 00:00:00'))->format(DateTimeInterface::ATOM),
                    'to' => (new DateTimeImmutable('2030-12-31 23:59:59'))->format(DateTimeInterface::ATOM),
                ],
                'visibility' => [
                    'allTime' => false,
                    'from' => (new DateTimeImmutable('2024-01-01 00:00:00'))->format(DateTimeInterface::ATOM),
                    'to' => (new DateTimeImmutable('2030-12-31 23:59:59'))->format(DateTimeInterface::ATOM),
                ],
                'labels' => [
                    ['key' => 'key0', 'value' => 'value0'],
                ],
                'taxPriceValue' => 23,
                'tax' => 23,
                'translations' => [
                    'en' => [
                        'name' => 'Material reward',
                        'shortDescription' => 'Material reward updated',
                        'brandName' => 'Material reward brand',
                    ],
                ],
            ],
        ];

        $this->client->jsonRequest('PUT', '/api/'.$tenantCode.'/reward/'.$rewardId, $rewardData);
        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);
    }

    private function getRewardUpdatedWebhookJsonBody(
        string $rewardId,
        string $tierId
    ): string {
        return <<<EOT
        {
            "eventName":"RewardUpdated",
            "messageId": "@uuid@",
            "storeCode":"material_reward_updated",
            "createdAt":"@datetime@",
            "data":{
                "reward":{
                    "rewardId":"$rewardId",
                    "active":true,
                    "featured":false,
                    "public":false,
                    "fulfillmentTracking":false,
                    "canBeBoughtManually":true,
                    "type":"material",
                    "name":"Material reward",
                    "brandName":"Material reward brand",
                    "shortDescription":"Material reward updated",
                    "target":"level",
                    "costInPoints":10.0,
                    "tax":0.23,
                    "taxPriceValue":23.0,
                    "levels":["$tierId"],
                    "segments":[],
                    "categories":[],
                    "labels":[
                        {"key":"key0","value":"value0"}
                    ],
                    "photos":[],
                    "translations":{
                        "en":{
                            "name":"Material reward",
                            "shortDescription":"Material reward updated",
                            "brandName":"Material reward brand",
                            "locale":"en"
                        }
                    },
                    "createdAt":"@datetime@",
                    "activity":{
                        "allTime":false,
                        "from":"2024-01-01T00:00:00+01:00",
                        "to":"2030-12-31T23:59:59+01:00"
                    },
                    "visibility":{
                        "allTime":false,
                        "from":"2024-01-01T00:00:00+01:00",
                        "to":"2030-12-31T23:59:59+01:00"
                    },
                    "usageLimit":{
                        "general":100,
                        "perUser":1
                    }
                }
            },
            "requestId": "@uuid@"
        }
        EOT;
    }
}
