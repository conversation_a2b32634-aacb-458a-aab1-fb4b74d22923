<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Reward\Integration\Ui;

use DateTime;
use OpenLoyalty\Points\Domain\Expiring\ExpiringTransferMode;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class PostBuyTest extends AbstractApiTest
{
    private HttpKernelBrowser $client;

    public function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_can_claim_reward_for_dynamic_coupon_with_quantity(): void
    {
        $this->createReward(null, null);
        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$data['rewardId'].'/buy',
            [
                'customerId' => '00000000-0000-474c-b092-b0dd880c07f5',
                'quantity' => 2,
                'couponValue' => 1,
                'withoutPoints' => true,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertCount(2, $data, 'Should be 2 redemptions');
        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/00000000-0000-474c-b092-b0dd880c07f5/history',
            [
                '_page' => 1,
                '_itemsOnPage' => 50,
                '_orderBy[createdAt]' => 'desc',
                'type' => ['in' => 'RewardWasBought'],
            ],
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('flatVariables', $data['items'][0]);
        $this->assertArrayHasKey('rewardId', $data['items'][0]['flatVariables']);
        $this->assertArrayHasKey('issuedRewardId', $data['items'][0]['flatVariables']);
        $this->assertArrayHasKey('rewardName', $data['items'][0]['flatVariables']);
    }

    /**
     * @test
     */
    public function it_does_not_claim_dynamic_coupon_with_not_valid_coupon_value(): void
    {
        $this->createReward(null, null);
        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$data['rewardId'].'/buy',
            [
                'customerId' => '00000000-0000-474c-b092-b0dd880c07f5',
                'quantity' => 1,
                'couponValue' => 0,
                'withoutPoints' => true,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $this->assertStringContainsString('"message":"This value should be greater than 0.000001."', $response->getContent());

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$data['rewardId'].'/buy',
            [
                'customerId' => '00000000-0000-474c-b092-b0dd880c07f5',
                'quantity' => 1,
                'couponValue' => 1123234234325.34,
                'withoutPoints' => true,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $this->assertStringContainsString('"message":"The provided value is too large."', $response->getContent());
    }

    /**
     * @test
     */
    public function it_can_claim_reward_for_static_coupon_with_percentage(): void
    {
        $this->client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'translations' => [
                        'en' => [
                            'name' => 'reward percentage',
                            'shortDescription' => 'reward',
                            'usageInstruction' => 'reward',
                            'conditionsDescription' => 'reward',
                            'brandDescription' => 'reward',
                            'brandName' => 'reward',
                        ],
                    ],
                    'reward' => 'static_coupon',
                    'couponValue' => 98.11,
                    'couponValueType' => 'percentage',
                    'categories' => [],
                    'tax' => 1,
                    'price' => 1234,
                    'taxPriceValue' => 2,
                    'active' => true,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'couponGenerator' => [
                        'characterSet' => 'alphanum',
                        'length' => 5,
                    ],
                    'usageLimit' => [
                        'general' => 100,
                        'perUser' => 100,
                    ],
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'labels' => [
                        [
                            'key' => 'string',
                            'value' => 'string',
                        ],
                    ],
                    'featured' => true,
                    'public' => true,
                    'costInPoints' => 5,
                ],
            ],
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$data['rewardId'].'/buy',
            [
                'customerId' => '00000000-0000-474c-b092-b0dd880c07f5',
                'quantity' => 2,
                'withoutPoints' => true,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('issuedRewardId', $data[0], 'Item should has issuedRewardId field');
        $issuedRewardId = $data[0]['issuedRewardId'];

        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/redemption/'.$issuedRewardId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('issuedCoupon', $data);
        $this->assertArrayHasKey('value', $data['issuedCoupon']);
        $this->assertSame(98.11, $data['issuedCoupon']['value']);
        $this->assertArrayHasKey('valueType', $data['issuedCoupon']);
        $this->assertSame('percentage', $data['issuedCoupon']['valueType']);

        $this->assertArrayHasKey('actionCause', $data);
        $this->assertArrayHasKey('customerId', $data['actionCause']);
        $this->assertSame('00000000-0000-474c-b092-b0dd880c07f5', $data['actionCause']['customerId']);
    }

    /**
     * @test
     */
    public function it_can_claim_reward_for_coupon_with_very_long_code(): void
    {
        $this->client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'translations' => [
                        'en' => [
                            'name' => 'reward long code coupon',
                            'shortDescription' => 'reward',
                            'usageInstruction' => 'reward',
                            'conditionsDescription' => 'reward',
                            'brandDescription' => 'reward',
                            'brandName' => 'reward',
                        ],
                    ],
                    'reward' => 'static_coupon',
                    'couponValue' => 1.11,
                    'couponValueType' => 'percentage',
                    'categories' => [],
                    'tax' => 1,
                    'price' => 2.5,
                    'taxPriceValue' => 2,
                    'active' => true,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'usageLimit' => [
                        'perUser' => 1,
                    ],
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'labels' => [
                        [
                            'key' => 'string',
                            'value' => 'string',
                        ],
                    ],
                    'featured' => true,
                    'public' => true,
                    'costInPoints' => 5,
                ],
            ],
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->client->request(
            'POST',
            sprintf(
                '/api/%s/reward/%s/coupon',
                LoadSettingsData::DEFAULT_STORE_CODE,
                $data['rewardId']
            ),
            ['coupons' => [
                'g3QWNpSjWwZ9d4cFxDztrpp5LxH1IEpsCiUmTBZYlb3BnRJS7p7hdZeL1zDYWFzqrZ8kctvs1kc25F1Mb8Hspm0MatOtg3yh6sZLipavAlUJLsL8W9TFZqdfRBwq3E74SS3jlRDK3Qbr21XJoO2GpS9yL1bFuPRDwNHxKsdcyTsoFYrL7m2IA1eVNN7n7pzcKPPnb23jV23MNsfhuoDAREPhUk6JQTBkAjQlBr4GyogBClPfeHyxLH0uooQih63ZUu5h6Gnkey0jpBnid23HlWKt8hyRFrwkyb7FoBNcWymoQAtFwxxMOczr7e3',
            ]]
        );
        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$data['rewardId'].'/buy',
            [
                'customerId' => '00000000-0000-474c-b092-b0dd880c07f5',
                'quantity' => 1,
                'withoutPoints' => true,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('issuedRewardId', $data[0], 'Item should has issuedRewardId field');
        $issuedRewardId = $data[0]['issuedRewardId'];

        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/redemption/'.$issuedRewardId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('issuedCoupon', $data);
        $this->assertArrayHasKey('value', $data['issuedCoupon']);
        $this->assertSame(1.11, $data['issuedCoupon']['value']);
        $this->assertArrayHasKey('valueType', $data['issuedCoupon']);
        $this->assertArrayHasKey('code', $data['issuedCoupon']);
        $this->assertSame('g3QWNpSjWwZ9d4cFxDztrpp5LxH1IEpsCiUmTBZYlb3BnRJS7p7hdZeL1zDYWFzqrZ8kctvs1kc25F1Mb8Hspm0MatOtg3yh6sZLipavAlUJLsL8W9TFZqdfRBwq3E74SS3jlRDK3Qbr21XJoO2GpS9yL1bFuPRDwNHxKsdcyTsoFYrL7m2IA1eVNN7n7pzcKPPnb23jV23MNsfhuoDAREPhUk6JQTBkAjQlBr4GyogBClPfeHyxLH0uooQih63ZUu5h6Gnkey0jpBnid23HlWKt8hyRFrwkyb7FoBNcWymoQAtFwxxMOczr7e3', $data['issuedCoupon']['code']);
        $this->assertArrayHasKey('token', $data);
        $this->assertSame('g3QWNpSjWwZ9d4cFxDztrpp5LxH1IEpsCiUmTBZYlb3BnRJS7p7hdZeL1zDYWFzqrZ8kctvs1kc25F1Mb8Hspm0MatOtg3yh6sZLipavAlUJLsL8W9TFZqdfRBwq3E74SS3jlRDK3Qbr21XJoO2GpS9yL1bFuPRDwNHxKsdcyTsoFYrL7m2IA1eVNN7n7pzcKPPnb23jV23MNsfhuoDAREPhUk6JQTBkAjQlBr4GyogBClPfeHyxLH0uooQih63ZUu5h6Gnkey0jpBnid23HlWKt8hyRFrwkyb7FoBNcWymoQAtFwxxMOczr7e3', $data['token']);
    }

    /**
     * @test
     */
    public function it_can_claim_reward_for_existing_static_coupons(): void
    {
        $this->client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'translations' => [
                        'en' => [
                            'name' => 'reward random coupons',
                            'shortDescription' => 'reward',
                            'usageInstruction' => 'reward',
                            'conditionsDescription' => 'reward',
                            'brandDescription' => 'reward',
                            'brandName' => 'reward',
                        ],
                    ],
                    'reward' => 'static_coupon',
                    'couponValue' => 1.11,
                    'couponValueType' => 'percentage',
                    'categories' => [],
                    'tax' => 1,
                    'price' => 2.5,
                    'taxPriceValue' => 2,
                    'active' => true,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'usageLimit' => [
                        'perUser' => 3,
                    ],
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'labels' => [
                        [
                            'key' => 'string',
                            'value' => 'string',
                        ],
                    ],
                    'featured' => true,
                    'public' => true,
                    'costInPoints' => 5,
                ],
            ],
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->client->request(
            'POST',
            sprintf(
                '/api/%s/reward/%s/coupon',
                LoadSettingsData::DEFAULT_STORE_CODE,
                $data['rewardId']
            ),
            ['coupons' => [
                'aaa',
                'bbb',
                'ccc',
                'ddd',
                'eee',
            ]]
        );
        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$data['rewardId'].'/buy',
            [
                'customerId' => '00000000-0000-474c-b092-b0dd880c07f5',
                'quantity' => 3,
                'withoutPoints' => true,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertCount(3, $data);
    }

    /**
     * @test
     */
    public function it_can_claim_reward_for_dynamic_coupon_without_validity_dates(): void
    {
        $this->createReward(null, null);
        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$data['rewardId'].'/buy',
            [
                'customerId' => '00000000-0000-474c-b092-b0dd880c07f5',
                'quantity' => 2,
                'couponValue' => 1,
                'withoutPoints' => true,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('issuedRewardId', $data[0], 'Item should has issuedRewardId field');
        $issuedRewardId = $data[0]['issuedRewardId'];

        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/redemption/'.$issuedRewardId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('issuedCoupon', $data);
        $this->assertArrayNotHasKey('activeFrom', $data['issuedCoupon']);
        $this->assertArrayNotHasKey('activeTo', $data['issuedCoupon']);
        $this->assertArrayNotHasKey('usedAt', $data['issuedCoupon']);
        $this->assertArrayHasKey('valueType', $data['issuedCoupon']);
        $this->assertSame('money', $data['issuedCoupon']['valueType']);
    }

    /**
     * @test
     */
    public function it_can_claim_reward_for_dynamic_coupon_with_validity_dates(): void
    {
        $this->createReward(2, 5);
        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$data['rewardId'].'/buy',
            [
                'customerId' => '00000000-0000-474c-b092-b0dd880c07f5',
                'quantity' => 2,
                'couponValue' => 1,
                'withoutPoints' => true,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('issuedRewardId', $data[0], 'Item should has issuedRewardId field');
        $issuedRewardId = $data[0]['issuedRewardId'];

        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/redemption/'.$issuedRewardId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('issuedCoupon', $data);
        $this->assertArrayHasKey('activeFrom', $data['issuedCoupon']);
        $this->assertArrayHasKey('activeTo', $data['issuedCoupon']);
        $this->assertArrayNotHasKey('usedAt', $data['issuedCoupon']);
        $this->assertArrayHasKey('valueType', $data['issuedCoupon']);
        $this->assertSame('money', $data['issuedCoupon']['valueType']);

        $this->assertEqualsWithDelta(new DateTime('+2 days'), DateTime::createFromFormat(\DateTimeInterface::ATOM, $data['issuedCoupon']['activeFrom']), 10);
        $this->assertEqualsWithDelta(new DateTime('+7 days'), DateTime::createFromFormat(\DateTimeInterface::ATOM, $data['issuedCoupon']['activeTo']), 10);
    }

    /**
     * @test
     */
    public function it_does_not_claim_reward_when_customer_id_is_not_valid(): void
    {
        $this->createReward(2, 5);
        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$data['rewardId'].'/buy',
            [
                'customerId' => '00000000-0000-474c-b092-b0dd880c07f',
                'quantity' => 2,
                'couponValue' => 1,
                'withoutPoints' => true,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        self::assertSame('This value is not a valid UUID.', $data['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_does_not_claim_reward_when_customer_is_deleted(): void
    {
        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/register',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Deo94',
                    'gender' => 'male',
                    'email' => '<EMAIL>',
                    'address' => [],
                    'company' => [
                        'name' => 'string',
                        'nip' => 'string',
                    ],
                    'labels' => [],
                    'agreement1' => true,
                    'agreement2' => true,
                    'agreement3' => true,
                    'plainPassword' => '0000000094aA%',
                ],
            ],
        );

        $response = $this->client->getResponse();
        $customerId = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'DELETE',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId['customerId'],
            []
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $this->createReward(1, 1);
        $response = $this->client->getResponse();

        $rewardData = json_decode($response->getContent(), true);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$rewardData['rewardId'].'/buy',
            [
                'customerId' => $customerId['customerId'],
                'quantity' => 1,
                'withoutPoints' => true,
                'couponValue' => 1,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        self::assertSame('Customer not found', $data['errors'][0]['message']);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$rewardData['rewardId'].'/deactivate',
            []
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);
    }

    /**
     * @test
     *
     * @dataProvider conversionCouponDataProvider
     */
    public function it_can_claim_reward_for_conversion_coupon_with_units(
        float $units,
        float $conversionRatio,
        string $rounding,
        float $expectedValue,
        float $expectedCostInPoints
    ): void {
        $data = $this->createConversionCouponReward($conversionRatio, $rounding);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$data['rewardId'].'/buy',
            [
                'customerId' => '00000000-0000-474c-b092-b0dd880c07f5',
                'units' => $units,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('issuedRewardId', $data[0], 'Item should has issuedRewardId field');
        $issuedRewardId = $data[0]['issuedRewardId'];

        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/redemption/'.$issuedRewardId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('issuedCoupon', $data);
        $this->assertArrayHasKey('value', $data['issuedCoupon']);
        $this->assertSame($data['issuedCoupon']['value'], $expectedValue);
        $this->assertSame($data['costInPoints'], $expectedCostInPoints);
    }

    /**
     * @test
     */
    public function it_takes_units_from_specified_wallet_in_reward(): void
    {
        $this->client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/walletType',
            [
                'walletType' => [
                    'translations' => [
                        'en' => [
                            'name' => 'reward wallet',
                            'description' => 'reward wallet',
                        ],
                    ],
                    'code' => 'rewardunit',
                    'unitSingularName' => 'rewardunit',
                    'unitPluralName' => 'rewardunit',
                    'active' => true,
                    'unitDaysExpiryAfter' => ExpiringTransferMode::EXPIRING_ALL_TIME_ACTIVE,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $walletTypeId = $data['walletTypeId'];
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/register',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Deo942',
                    'gender' => 'male',
                    'email' => '<EMAIL>',
                    'address' => [],
                    'company' => [
                        'name' => 'string',
                        'nip' => 'string',
                    ],
                    'labels' => [],
                    'agreement1' => true,
                    'agreement2' => true,
                    'agreement3' => true,
                    'plainPassword' => '0000000094aA%',
                ],
            ],
        );

        $response = $this->client->getResponse();
        $customerId = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId['customerId'].'/activate'
        );
        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/points/add',
            [
                'transfer' => [
                    'customer' => $customerId['customerId'],
                    'points' => 100,
                    'walletCode' => 'rewardunit',
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->createReward(null, null, 'rewardunit');
        $response = $this->client->getResponse();

        $rewardData = json_decode($response->getContent(), true);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$rewardData['rewardId'].'/buy',
            [
                'customerId' => $customerId['customerId'],
                'quantity' => 1,
                'withoutPoints' => false,
                'couponValue' => 1,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId['customerId'].'/wallet?walletTypeId='.$walletTypeId,
        );

        $response = $this->client->getResponse();

        $wallet = json_decode($response->getContent(), true);

        $rewardWallet = $wallet['items'][0];

        //reward take 5 points
        self::assertSame(95.0, $rewardWallet['account']['activeUnits']);
    }

    /**
     * @test
     */
    public function it_takes_units_from_specified_wallet_in_buy_request(): void
    {
        $this->client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/walletType',
            [
                'walletType' => [
                    'translations' => [
                        'en' => [
                            'name' => 'wallet',
                            'description' => 'wallet',
                        ],
                    ],
                    'code' => 'rewardunit12',
                    'unitSingularName' => 'rewardunit12',
                    'unitPluralName' => 'rewardunit12',
                    'active' => true,
                    'unitDaysExpiryAfter' => ExpiringTransferMode::EXPIRING_ALL_TIME_ACTIVE,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $walletTypeId = $data['walletTypeId'];
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/register',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Deo942',
                    'gender' => 'male',
                    'email' => '<EMAIL>',
                    'address' => [],
                    'company' => [
                        'name' => 'string',
                        'nip' => 'string',
                    ],
                    'labels' => [],
                    'agreement1' => true,
                    'agreement2' => true,
                    'agreement3' => true,
                    'plainPassword' => '0000000094aA%',
                ],
            ],
        );

        $response = $this->client->getResponse();
        $customerId = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId['customerId'].'/activate'
        );
        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/points/add',
            [
                'transfer' => [
                    'customer' => $customerId['customerId'],
                    'points' => 100,
                    'walletCode' => 'rewardunit12',
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->createReward();
        $response = $this->client->getResponse();

        $rewardData = json_decode($response->getContent(), true);

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$rewardData['rewardId'].'/buy',
            [
                'customerId' => $customerId['customerId'],
                'quantity' => 1,
                'withoutPoints' => false,
                'couponValue' => 1,
                'rewardWalletCode' => 'rewardunit12',
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId['customerId'].'/wallet?walletTypeId='.$walletTypeId,
        );

        $response = $this->client->getResponse();

        $wallet = json_decode($response->getContent(), true);

        $rewardWallet = $wallet['items'][0];

        //reward take 5 points
        self::assertSame(95.0, $rewardWallet['account']['activeUnits']);
    }

    public function conversionCouponDataProvider(): array
    {
        return [
            [2, 1.65, 'default', 3.3, 2],
            [2, 1.65, 'up', 4, 2],
            [2, 1.1, 'up', 3, 2],
            [2, 1.1, 'down', 2, 2],
            [2, 1.45, 'down', 2, 2],
            [10, 0.5, 'down', 5, 10],
            [10, 0.3, 'default', 3.0, 10],
        ];
    }

    private function createReward(?int $daysInactive = null, ?int $daysValid = null, ?string $walletCode = null): void
    {
        $reward = [
            'translations' => [
                'en' => [
                    'name' => 'reward',
                    'shortDescription' => 'reward',
                    'usageInstruction' => 'reward',
                    'conditionsDescription' => 'reward',
                    'brandDescription' => 'reward',
                    'brandName' => 'reward',
                ],
            ],
            'reward' => 'dynamic_coupon',
            'categories' => [],
            'tax' => 1,
            'price' => 1234,
            'taxPriceValue' => 2,
            'active' => true,
            'activity' => [
                'allTime' => false,
                'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
            ],
            'couponGenerator' => [
                'characterSet' => 'alphanum',
                'length' => 5,
            ],
            'usageLimit' => [
                'general' => 100,
                'perUser' => 100,
            ],
            'visibility' => [
                'allTime' => false,
                'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
            ],
            'labels' => [
                [
                    'key' => 'string',
                    'value' => 'string',
                ],
            ],
            'featured' => true,
            'public' => true,
            'costInPoints' => 5,
            'sourceWalletTypeCode' => $walletCode,
        ];

        if (null !== $daysInactive) {
            $reward['daysInactive'] = $daysInactive;
        }

        if (null !== $daysValid) {
            $reward['daysValid'] = $daysValid;
        }

        $this->client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => $reward,
            ],
        );
    }

    private function createConversionCouponReward(float $conversionRatio, string $rounding): array
    {
        $reward = [
            'translations' => [
                'en' => [
                    'name' => 'Conversion coupon reward',
                    'shortDescription' => 'Conversion',
                    'usageInstruction' => 'Conversion',
                    'conditionsDescription' => 'Conversion',
                    'brandDescription' => 'Conversion',
                    'brandName' => 'Conversion',
                ],
            ],
            'reward' => 'conversion_coupon',
            'categories' => [],
            'tax' => 1,
            'price' => 50,
            'taxPriceValue' => 2,
            'active' => true,
            'levels' => [],
            'segments' => [],
            'activity' => [
                'allTime' => false,
                'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
            ],
            'couponGenerator' => [
                'characterSet' => 'alphanum',
                'length' => 5,
            ],
            'usageLimit' => [
                'general' => 100,
                'perUser' => 100,
            ],
            'visibility' => [
                'allTime' => false,
                'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
            ],
            'labels' => [
                [
                    'key' => 'string',
                    'value' => 'string',
                ],
            ],
            'featured' => true,
            'public' => true,
            'unitsConversion' => [
                'ratio' => $conversionRatio,
                'rounding' => $rounding,
            ],
        ];

        $this->client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => $reward,
            ],
        );

        $response = $this->client->getResponse();

        return json_decode($response->getContent(), true);
    }
}
