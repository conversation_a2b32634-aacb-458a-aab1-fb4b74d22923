<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Reward\Integration\Ui;

use DateTime;
use OpenLoyalty\Reward\Infrastructure\DataFixtures\ORM\LoadRewardData;
use OpenLoyalty\Segment\Infrastructure\DataFixtures\ORM\LoadSegmentData;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\Reward\Integration\Traits\RewardApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use OpenLoyalty\User\Infrastructure\DataFixtures\ORM\LoadUserData;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

/**
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\GetMemberReward
 */
final class GetMemberRewardTest extends AbstractApiTest
{
    use MemberApiTrait;
    use TenantApiTrait;
    use RewardApiTrait;

    private HttpKernelBrowser $authenticatedClient;

    protected function setUp(): void
    {
        parent::setUp();
        $this->authenticatedClient = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_returns_list_of_available_rewards_limited_to_segment(): void
    {
        $this->authenticatedClient->request(
            Request::METHOD_GET,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER1_USER_ID.'/reward?rewardId='.LoadRewardData::REWARD_ID,
            []
        );
        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertSame(0, $data['total']['filtered']);

        $this->authenticatedClient->jsonRequest(
            Request::METHOD_PUT,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD_ID,
            [
                'reward' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Test configured reward',
                        ],
                    ],
                    'target' => 'segment',
                    'segments' => [
                        LoadSegmentData::SEGMENT2_ID,
                        LoadSegmentData::SEGMENT11_ID,
                    ],
                    'categories' => [],
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'featured' => false,
                    'public' => true,
                    'tax' => 0,
                    'price' => 1234,
                    'taxPriceValue' => 20,
                    'costInPoints' => 30,
                    'active' => true,
                    'labels' => [],
                    'couponValue' => 12,
                    'usageLimit' => [
                        'perUser' => 3,
                    ],
                ],
            ]
        );
        $response = $this->authenticatedClient->getResponse();

        $this->assertNoContentResponseStatus($response);

        $this->authenticatedClient->request(
            Request::METHOD_PUT,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER1_USER_ID,
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'labels' => [
                        [
                            'key' => 'test',
                            'value' => 'test value',
                        ],
                    ],
                ],
            ]
        );
        $response = $this->authenticatedClient->getResponse();

        $this->assertOkResponseStatus($response);

        $this->authenticatedClient->request(
            Request::METHOD_POST,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/segment/'.LoadSegmentData::SEGMENT11_ID.'/deactivate'
        );
        $response = $this->authenticatedClient->getResponse();
        $this->assertNoContentResponseStatus($response);

        $this->authenticatedClient->request(
            Request::METHOD_POST,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/segment/'.LoadSegmentData::SEGMENT11_ID.'/activate'
        );
        $response = $this->authenticatedClient->getResponse();
        $this->assertNoContentResponseStatus($response);

        $this->authenticatedClient->request(
            Request::METHOD_GET,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER1_USER_ID.'/reward?rewardId='.LoadRewardData::REWARD_ID,
            []
        );

        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertSame(1, $data['total']['filtered']);
        $this->assertCount(1, $data['items']);
        $this->assertSame('6a861f8a-6906-4002-8b9c-4db407e810ea', $data['items'][0]['rewardId']);
    }

    /**
     * @test
     */
    public function it_returns_list_of_available_rewards_by_member_email(): void
    {
        $tenantCode = 'tenantCodeMemberRewards';
        $response = $this->postTenant($this->authenticatedClient, $tenantCode);
        $this->assertOkResponseStatus($response);

        $response = $this->postMember($this->authenticatedClient, $tenantCode, '<EMAIL>');
        $this->assertOkResponseStatus($response);

        $response = $this->postReward($this->authenticatedClient, $tenantCode);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $rewardId = $data['rewardId'];

        $this->authenticatedClient->request(
            Request::METHOD_GET,
            '/api/'.$tenantCode.'/member/email=<EMAIL>/reward?rewardId='.$rewardId,
            []
        );
        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertSame(1, $data['total']['filtered']);
    }

    /**
     * @test
     */
    public function it_returns_list_of_available_rewards_without_level_and_segment_limit(): void
    {
        $this->authenticatedClient->request(
            Request::METHOD_GET,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER1_USER_ID.'/reward?rewardId='.LoadRewardData::REWARD_ID,
            []
        );
        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertSame(0, $data['total']['filtered']);

        $this->authenticatedClient->jsonRequest(
            Request::METHOD_PUT,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD_ID,
            [
                'reward' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Test configured reward',
                        ],
                    ],
                    'segments' => [],
                    'levels' => [],
                    'categories' => [],
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'featured' => false,
                    'public' => true,
                    'tax' => 0,
                    'price' => 1234,
                    'taxPriceValue' => 20,
                    'costInPoints' => 30,
                    'active' => true,
                    'labels' => [],
                    'couponValue' => 12,
                    'usageLimit' => [
                        'perUser' => 3,
                    ],
                ],
            ]
        );
        $response = $this->authenticatedClient->getResponse();

        $this->assertNoContentResponseStatus($response);

        $this->authenticatedClient->request(
            Request::METHOD_GET,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER1_USER_ID.'/reward?rewardId='.LoadRewardData::REWARD_ID,
            []
        );

        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertSame(1, $data['total']['filtered']);
        $this->assertCount(1, $data['items']);
        $this->assertSame('6a861f8a-6906-4002-8b9c-4db407e810ea', $data['items'][0]['rewardId']);
    }

    /**
     * @test
     */
    public function it_returns_list_of_available_rewards_limited_to_level(): void
    {
        $this->authenticatedClient->request(
            Request::METHOD_GET,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER1_USER_ID.'/reward?rewardId='.LoadRewardData::REWARD_ID,
            []
        );
        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertSame(0, $data['total']['filtered']);

        $this->authenticatedClient->jsonRequest(
            Request::METHOD_PUT,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD_ID,
            [
                'reward' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Test configured reward',
                        ],
                    ],
                    'target' => 'level',
                    'segments' => [],
                    'levels' => ['e82c96cf-32a3-43bd-9034-4df343e50000'],
                    'categories' => [],
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'featured' => false,
                    'public' => true,
                    'tax' => 0,
                    'price' => 1234,
                    'taxPriceValue' => 20,
                    'costInPoints' => 30,
                    'active' => true,
                    'labels' => [],
                    'couponValue' => 12,
                    'usageLimit' => [
                        'perUser' => 3,
                    ],
                ],
            ]
        );
        $response = $this->authenticatedClient->getResponse();

        $this->assertNoContentResponseStatus($response);

        $this->authenticatedClient->request(
            Request::METHOD_GET,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER1_USER_ID.'/reward?rewardId='.LoadRewardData::REWARD_ID,
            []
        );

        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertSame(1, $data['total']['filtered']);
        $this->assertCount(1, $data['items']);
        $this->assertSame('6a861f8a-6906-4002-8b9c-4db407e810ea', $data['items'][0]['rewardId']);
    }

    /**
     * @test
     */
    public function it_returns_list_of_conversion_coupon_rewards(): void
    {
        $this->authenticatedClient->jsonRequest(
            Request::METHOD_POST,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Member Conversion coupon reward',
                            'shortDescription' => 'Conversion',
                            'usageInstruction' => 'Conversion',
                            'conditionsDescription' => 'Conversion',
                            'brandDescription' => 'Conversion',
                            'brandName' => 'Conversion',
                        ],
                    ],
                    'reward' => 'conversion_coupon',
                    'categories' => [],
                    'tax' => 1,
                    'price' => 50,
                    'taxPriceValue' => 2,
                    'active' => true,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'couponGenerator' => [
                        'characterSet' => 'alphanum',
                        'length' => 5,
                    ],
                    'usageLimit' => [
                        'general' => 100,
                        'perUser' => 100,
                    ],
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'labels' => [
                        [
                            'key' => 'string',
                            'value' => 'string',
                        ],
                    ],
                    'featured' => true,
                    'public' => true,
                    'unitsConversion' => [
                        'ratio' => 1.3,
                        'rounding' => 'default',
                    ],
                ],
            ]
        );
        $response = $this->authenticatedClient->getResponse();

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('rewardId', $data);
        $rewardId = $data['rewardId'];

        $this->authenticatedClient->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$rewardId.'/buy',
            [
                'customerId' => LoadUserData::USER_USER_ID,
                'units' => 1,
            ]
        );

        $response = $this->authenticatedClient->getResponse();

        $this->assertOkResponseStatus($response);
        $this->authenticatedClient->request(
            Request::METHOD_GET,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER_USER_ID.'/reward',
            ['rewardId' => $rewardId]
        );

        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertSame(1, $data['total']['filtered']);
        $this->assertCount(1, $data['items']);
        $this->assertSame($rewardId, $data['items'][0]['rewardId']);
        $this->assertArrayHasKey('unitsConversion', $data['items'][0]);
        $this->assertArrayHasKey('ratio', $data['items'][0]['unitsConversion']);
        $this->assertSame(1.3, $data['items'][0]['unitsConversion']['ratio']);
        $this->assertArrayHasKey('rounding', $data['items'][0]['unitsConversion']);
        $this->assertSame('default', $data['items'][0]['unitsConversion']['rounding']);
    }
}
