<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Unit\Domain\Entity;

use OpenLoyalty\Achievement\Domain\Entity\MemberAchievementProgress;
use OpenLoyalty\Achievement\Domain\Entity\RuleProgress;
use OpenLoyalty\Core\Domain\Id\AchievementRuleId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use PHPUnit\Framework\TestCase;

final class MemberAchievementProgressTest extends TestCase
{
    /**
     * @test
     */
    public function it_returns_completed_with_empty_conditions(): void
    {
        $progress = new MemberAchievementProgress(
            $this->getMockBuilder(\OpenLoyalty\Achievement\Domain\Entity\Achievement::class)->disableOriginalConstructor()->getMock(),
            new CustomerId('00000000-0000-0000-0000-000000000000'),
            new \DateTimeImmutable()
        );

        $this->assertTrue($progress->isRulesCompleted(), 'Completed should be true');
    }

    /**
     * @test
     */
    public function it_returns_completed(): void
    {
        $progress = new MemberAchievementProgress(
            $this->getMockBuilder(\OpenLoyalty\Achievement\Domain\Entity\Achievement::class)->disableOriginalConstructor()->getMock(),
            new CustomerId('00000000-0000-0000-0000-000000000000'),
            new \DateTimeImmutable()
        );

        $progressItem = new \OpenLoyalty\Achievement\Domain\Entity\RuleProgress(new AchievementRuleId('00000000-0000-0000-0000-000000000000'));
        $progressItem->complete(new \DateTimeImmutable());

        $progress->addRule($progressItem);

        $this->assertTrue($progress->isRulesCompleted(), 'Completed should be true');
    }

    /**
     * @test
     */
    public function it_returns_not_completed(): void
    {
        $progress = new MemberAchievementProgress(
            $this->getMockBuilder(\OpenLoyalty\Achievement\Domain\Entity\Achievement::class)->disableOriginalConstructor()->getMock(),
            new CustomerId('00000000-0000-0000-0000-000000000000'),
            new \DateTimeImmutable()
        );

        $progressItem = new RuleProgress(new AchievementRuleId('00000000-0000-0000-0000-000000000000'));

        $progress->addRule($progressItem);

        $this->assertFalse($progress->isRulesCompleted(), 'Completed should be false');
    }
}
