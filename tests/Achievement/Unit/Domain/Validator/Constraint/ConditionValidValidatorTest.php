<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Unit\Domain\Validator\Constraint;

use OpenLoyalty\Achievement\Infrastructure\Validator\Constraint\ConditionValid;
use OpenLoyalty\Achievement\Infrastructure\Validator\Constraint\ConditionValidValidator;
use OpenLoyalty\Core\Domain\Condition\ConditionFactory;
use OpenLoyalty\Core\Domain\Condition\Schema\ConditionExpressionBuilder;
use OpenLoyalty\Core\Domain\Condition\Schema\ConditionValueExpressionProvider;
use OpenLoyalty\Core\Domain\Exception\InvalidConditionOperatorException;
use OpenLoyalty\Core\Domain\Model\Condition;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Domain\ValueFacadeInterface;
use OpenLoyalty\Core\Domain\ValueObject\Condition\Operator;
use OpenLoyalty\CustomEvent\Domain\CustomEventSchemaRepositoryInterface;
use OpenLoyalty\CustomEvent\Infrastructure\Service\SchemaMocker;
use OpenLoyalty\Tools\ExpressionLanguage\ConditionExpressionLanguage;
use OpenLoyalty\Tools\ExpressionLanguage\Processor\WildcardProcessor;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;
use Symfony\Contracts\Translation\TranslatorInterface;

final class ConditionValidValidatorTest extends ConstraintValidatorTestCase
{
    private MockObject&TranslatorInterface $translator;
    private MockObject&CustomEventSchemaRepositoryInterface $customEventSchemaRepository;
    private MockObject&StoreContextProviderInterface $storeContextProvider;
    private MockObject&ValueFacadeInterface $valueFacade;
    private ConditionExpressionBuilder $conditionExpressionBuilder;

    protected function setUp(): void
    {
        $this->translator = $this->createMock(TranslatorInterface::class);
        $this->customEventSchemaRepository = $this->getMockBuilder(CustomEventSchemaRepositoryInterface::class)->getMock();
        $this->storeContextProvider = $this->getMockBuilder(StoreContextProviderInterface::class)->getMock();
        $this->valueFacade = $this->createMock(ValueFacadeInterface::class);
        $this->conditionExpressionBuilder = new ConditionExpressionBuilder(
            new ConditionValueExpressionProvider([])
        );

        parent::setUp();
    }

    /**
     * @test
     */
    public function it_not_raises_exception_when_value_is_not_instance_of_condition(): void
    {
        $this->validator->validate(
            ['attribute' => '', 'operator' => 'some_operator'],
            new ConditionValid(['trigger' => 'transaction', 'type' => 'direct'])
        );

        $this->assertNoViolation();
    }

    /**
     * @test
     *
     * @throws InvalidConditionOperatorException
     */
    public function it_raises_exception_when_cannot_create_condition(): void
    {
        $condition = $this->createMock(Condition::class);
        $condition->method('getData')->willReturn([]);
        $condition->method('getOperator')->willReturn(Operator::create('is_greater'));
        $condition->method('getAttribute')->willReturn('');

        $this->translator->method('trans')->willReturn('Translated exception');

        $this->validator->validate(
            $condition,
            new ConditionValid(['trigger' => 'transaction', 'type' => 'direct'])
        );
        $this->buildViolation('Translated exception')->assertRaised();
    }

    /**
     * @test
     *
     * @dataProvider getTransactionValidatorData
     *
     * @throws InvalidConditionOperatorException
     */
    public function it_tests_transaction_validator(
        string $attribute,
        string $operator,
        $data,
        ?string $raisedMessage,
        array $raisedParameters = []
    ): void {
        $condition = $this->createMock(Condition::class);
        $condition->method('getData')->willReturn([$data]);
        $condition->method('getOperator')->willReturn(Operator::create($operator));
        $condition->method('getAttribute')->willReturn($attribute);

        $this->validator->validate(
            $condition,
            new ConditionValid(['trigger' => 'transaction', 'type' => 'direct'])
        );

        if (null !== $raisedMessage) {
            $this->buildViolation($raisedMessage)->setParameters($raisedParameters)->assertRaised();
        } else {
            $this->assertNoViolation();
        }
    }

    public function getTransactionValidatorData(): iterable
    {
        return [
            ['customer.firstName', 'is_equal', 'Jan', null],
            [
                'referrer.firstName',
                'is_equal',
                'Jan',
                'Invalid expression: lower(referrer.firstName) == lower(\'Jan\'). Caused by: Variable "referrer" is not valid around position 7 for expression `lower(referrer.firstName) == lower(\'Jan\')`.',
                ['expression' => 'lower(referrer.firstName) == lower(\'Jan\')'],
            ],
            ['transaction.sku(\'123\').qty', 'is_equal', 16, null],
            ['transaction.grossValue', 'is_greater', 16, null],
            [
                'transaction.testField',
                'is_equal',
                16,
                'Invalid expression: transaction.testField == 16. Caused by: Undefined property: OpenLoyalty\Core\Domain\Context\Transaction::$testField',
                ['expression' => 'transaction.testField == 16'],
            ],
            [
                'test',
                'is_equal',
                16,
                'Invalid expression: test == 16. Caused by: Variable "test" is not valid around position 1 for expression `test == 16`.',
                ['expression' => 'test == 16'],
            ],
        ];
    }

    /**
     * @test
     */
    public function it_raises_exception_when_trigger_is_not_specified(): void
    {
        $this->translator->method('trans')->willReturnOnConsecutiveCalls('achievement.required_trigger', 'test');

        $this->validator->validate(
            new Condition('transaction.grossValue', Operator::create('is_greater'), [5]),
            new ConditionValid(['trigger' => null, 'type' => 'direct'])
        );

        $this->buildViolation('achievement.required_trigger')->assertRaised();
    }

    protected function createValidator(): ConstraintValidator
    {
        return new ConditionValidValidator(
            new ConditionFactory(),
            new ConditionExpressionLanguage(
                new WildcardProcessor(),
                $this->valueFacade,
                $this->conditionExpressionBuilder,
                100,
                100,
                100
            ),
            $this->translator,
            $this->customEventSchemaRepository,
            $this->storeContextProvider,
            new SchemaMocker()
        );
    }
}
