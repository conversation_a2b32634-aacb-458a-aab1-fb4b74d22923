<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Unit\Domain\Validator\UpdateAchievementProgress;

use OpenLoyalty\Achievement\Domain\AchievementRepositoryInterface;
use OpenLoyalty\Achievement\Domain\Entity\Achievement;
use OpenLoyalty\Achievement\Domain\Validator\UpdateAchievementProgress\UpdatedCompletionCountValidator;
use OpenLoyalty\Achievement\Domain\Validator\UpdateAchievementProgress\UpdatedCompletionCountValidatorInterface;
use OpenLoyalty\Achievement\Domain\ValueObject\Limit;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\ValueObject\LimitInterval;
use PHPUnit\Framework\TestCase;

final class UpdatedCompletionCountValidatorTest extends TestCase
{
    private AchievementRepositoryInterface $achievementRepository;
    private UpdatedCompletionCountValidatorInterface $updatedCompletionCountValidator;

    protected function setUp(): void
    {
        parent::setUp();

        $this->achievementRepository = $this->createMock(AchievementRepositoryInterface::class);

        $this->updatedCompletionCountValidator = new UpdatedCompletionCountValidator($this->achievementRepository);
    }

    /**
     * @test
     *
     * @dataProvider AchievementLimitProvider
     */
    public function it_validates_completion_count_with_achievement_limit(?Limit $limit, ?int $validatedCount, bool $expectedOutput): void
    {
        $achievement = $this->createMock(Achievement::class);

        $achievementId = new AchievementId('bf1e3a13-c50a-4457-8e9e-54182b8a59b5');
        $achievement->method('getAchievementId')->willReturn($achievementId);
        $achievement->method('getLimit')->willReturn($limit);

        $this->achievementRepository->method('byId')->willReturn($achievement);

        self::assertSame(
            $expectedOutput,
            $this->updatedCompletionCountValidator->isAchievementLimitsValidToEditCompletionCount($validatedCount, $achievementId)
        );
    }

    public function AchievementLimitProvider(): array
    {
        return [
            [Limit::create(LimitInterval::create(LimitInterval::CALENDAR_DAYS, 1), 1), null, false],
            [null, 1, true],
            [Limit::create(LimitInterval::create(LimitInterval::CALENDAR_DAYS, 1), null), 2, true],
            [Limit::create(LimitInterval::create(LimitInterval::CALENDAR_DAYS, 1), 5), 3, true],
        ];
    }
}
