<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Unit\Domain\Progress;

use DateTimeImmutable;
use OpenLoyalty\Achievement\Domain\Entity\Achievement;
use OpenLoyalty\Achievement\Domain\Entity\MemberAchievementProgress;
use OpenLoyalty\Achievement\Domain\Entity\Rule;
use OpenLoyalty\Achievement\Domain\Entity\RuleProgress;
use OpenLoyalty\Achievement\Domain\MemberAchievementProgressRepositoryInterface;
use OpenLoyalty\Achievement\Domain\Progress\Complete\CompleteRuleEvaluatorInterface;
use OpenLoyalty\Achievement\Domain\Progress\ProgressRefresher;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\Aggregation\Aggregation;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\Aggregation\Type as AggregationType;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\CompleteRule;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\Period\Period;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\Period\Type;
use OpenLoyalty\Achievement\Domain\ValueObject\Type as RuleType;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\AchievementRuleId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\MemberAchievementProgressId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class ProgressRefresherTest extends TestCase
{
    private CompleteRuleEvaluatorInterface $completeRuleEvaluator;
    private MemberAchievementProgressRepositoryInterface $achievementProgressRepository;
    private EventBusInterface $eventBus;
    private ProgressRefresher $progressRefresher;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->completeRuleEvaluator = $this->getMockBuilder(CompleteRuleEvaluatorInterface::class)->getMock();
        $this->achievementProgressRepository = $this->getMockBuilder(MemberAchievementProgressRepositoryInterface::class)->getMock();
        $this->eventBus = $this->createMock(EventBusInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->progressRefresher = new ProgressRefresher(
            $this->completeRuleEvaluator,
            $this->achievementProgressRepository,
            $this->eventBus,
            $this->logger
        );
    }

    /**
     * @test
     */
    public function it_not_refreshes_while_member_achievement_progress_is_null(): void
    {
        $this->completeRuleEvaluator->expects(self::never())->method('refresh');
        $this->achievementProgressRepository->expects(self::never())->method('save');
        $this->achievementProgressRepository->method('byId')->willReturn(null);

        $this->progressRefresher->refresh(
            [new MemberAchievementProgressId(1)],
            new DateTimeImmutable()
        );
    }

    /**
     * @test
     */
    public function it_refreshes(): void
    {
        $rules = [
            Rule::create(
                new AchievementRuleId('00000000-1111-0000-0000-000000000000'),
                Trigger::create(Trigger::TRANSACTION),
                CompleteRule::create(Period::create(Type::create(Type::DAY)), 1),
                Aggregation::create(AggregationType::create(AggregationType::QUANTITY)),
                [],
                RuleType::create(RuleType::DIRECT)
            ),
            Rule::create(
                new AchievementRuleId('00000000-2222-0000-0000-000000000000'),
                Trigger::create(Trigger::TRANSACTION),
                CompleteRule::create(Period::create(Type::create(Type::YEAR)), 1),
                Aggregation::create(AggregationType::create(AggregationType::QUANTITY)),
                [],
                RuleType::create(RuleType::DIRECT)
            ),
            Rule::create(
                new AchievementRuleId('00000000-3333-0000-0000-000000000000'),
                Trigger::create(Trigger::TRANSACTION),
                CompleteRule::create(Period::create(Type::create(Type::MONTH)), 1),
                Aggregation::create(AggregationType::create(AggregationType::QUANTITY)),
                [],
                RuleType::create(RuleType::DIRECT)
            ),
        ];

        $achievement = Achievement::create(
            new AchievementId('00000000-0000-0000-0000-000000000000'),
            new Store(
                new StoreId('00000000-0000-0000-0000-000000000000'),
                'COD',
                'EUR',
                'NAME'
            ),
            true,
            null,
            null,
            $rules
        );

        $memberProgress = new MemberAchievementProgress(
            $achievement,
            new CustomerId('00000000-0000-0000-0000-000000000000'),
            new \DateTimeImmutable()
        );
        foreach ($rules as $rule) {
            $memberProgress->addRule(new RuleProgress($rule->getAchievementRuleId()));
        }

        $this->completeRuleEvaluator
            ->expects(self::exactly(3))
            ->method('refresh')
            ->willReturnOnConsecutiveCalls(false, true, false);
        $this->achievementProgressRepository->method('byId')->willReturn($memberProgress);
        $this->achievementProgressRepository->expects(self::once())->method('save');
        $this->eventBus->expects(self::once())->method('dispatch');
        $this->logger->expects($this->never())->method('warning');

        $this->progressRefresher->refresh(
            [new MemberAchievementProgressId(1)],
            new DateTimeImmutable()
        );
    }

    /**
     * @test
     */
    public function it_does_not_reset_rule_progress_if_progress_did_not_change(): void
    {
        $rules = [
            Rule::create(
                new AchievementRuleId('00000000-1111-0000-0000-000000000000'),
                Trigger::create(Trigger::TRANSACTION),
                CompleteRule::create(Period::create(Type::create(Type::DAY)), 1),
                Aggregation::create(AggregationType::create(AggregationType::QUANTITY)),
                [],
                RuleType::create(RuleType::DIRECT)
            ),
            Rule::create(
                new AchievementRuleId('00000000-2222-0000-0000-000000000000'),
                Trigger::create(Trigger::TRANSACTION),
                CompleteRule::create(Period::create(Type::create(Type::MONTH)), 1),
                Aggregation::create(AggregationType::create(AggregationType::QUANTITY)),
                [],
                RuleType::create(RuleType::DIRECT)
            ),
            Rule::create(
                new AchievementRuleId('00000000-3333-0000-0000-000000000000'),
                Trigger::create(Trigger::TRANSACTION),
                CompleteRule::create(Period::create(Type::create(Type::YEAR)), 1),
                Aggregation::create(AggregationType::create(AggregationType::QUANTITY)),
                [],
                RuleType::create(RuleType::DIRECT)
            ),
        ];

        $achievement = Achievement::create(
            new AchievementId('00000000-0000-0000-0000-000000000000'),
            new Store(
                new StoreId('00000000-0000-0000-0000-000000000000'),
                'COD',
                'EUR',
                'NAME'
            ),
            true,
            null,
            null,
            $rules
        );

        $memberProgress = new MemberAchievementProgress(
            $achievement,
            new CustomerId('00000000-0000-0000-0000-000000000000'),
            new \DateTimeImmutable()
        );
        foreach ($rules as $rule) {
            $memberProgress->addRule(new RuleProgress($rule->getAchievementRuleId()));
        }

        $this->completeRuleEvaluator
            ->expects(self::exactly(3))
            ->method('refresh')
            ->willReturnOnConsecutiveCalls(false, false, false);
        $this->achievementProgressRepository->method('byId')->willReturn($memberProgress);
        $this->achievementProgressRepository->expects(self::never())->method('save');
        $this->eventBus->expects(self::never())->method('dispatch');
        $this->logger->expects($this->never())->method('warning');

        $this->progressRefresher->refresh(
            [new MemberAchievementProgressId(1)],
            new DateTimeImmutable()
        );
    }

    /**
     * @test
     */
    public function it_only_refreshes_consecutive_rules(): void
    {
        $rules = [
            Rule::create(
                new AchievementRuleId('00000000-1111-0000-0000-000000000000'),
                Trigger::create(Trigger::TRANSACTION),
                CompleteRule::create(Period::create(Type::create(Type::OVERALL)), 1),
                Aggregation::create(AggregationType::create(AggregationType::QUANTITY)),
                [],
                RuleType::create(RuleType::DIRECT)
            ),
            Rule::create(
                new AchievementRuleId('00000000-2222-0000-0000-000000000000'),
                Trigger::create(Trigger::TRANSACTION),
                CompleteRule::create(Period::create(Type::create(Type::OVERALL)), 1),
                Aggregation::create(AggregationType::create(AggregationType::QUANTITY)),
                [],
                RuleType::create(RuleType::DIRECT)
            ),
            Rule::create(
                new AchievementRuleId('00000000-3333-0000-0000-000000000000'),
                Trigger::create(Trigger::TRANSACTION),
                CompleteRule::create(Period::create(Type::create(Type::YEAR)), 1),
                Aggregation::create(AggregationType::create(AggregationType::QUANTITY)),
                [],
                RuleType::create(RuleType::DIRECT)
            ),
        ];

        $achievement = Achievement::create(
            new AchievementId('00000000-0000-0000-0000-000000000000'),
            new Store(
                new StoreId('00000000-0000-0000-0000-000000000000'),
                'COD',
                'EUR',
                'NAME'
            ),
            true,
            null,
            null,
            $rules
        );

        $memberProgress = new MemberAchievementProgress(
            $achievement,
            new CustomerId('00000000-0000-0000-0000-000000000000'),
            new \DateTimeImmutable()
        );
        foreach ($rules as $rule) {
            $memberProgress->addRule(new RuleProgress($rule->getAchievementRuleId()));
        }

        $this->completeRuleEvaluator
            ->expects(self::once())
            ->method('refresh')
            ->willReturn(true);
        $this->achievementProgressRepository->method('byId')->willReturn($memberProgress);
        $this->achievementProgressRepository->expects(self::once())->method('save');
        $this->eventBus->expects(self::once())->method('dispatch');
        $this->logger->expects($this->never())->method('warning');

        $this->progressRefresher->refresh(
            [new MemberAchievementProgressId(1)],
            new DateTimeImmutable()
        );
    }
}
