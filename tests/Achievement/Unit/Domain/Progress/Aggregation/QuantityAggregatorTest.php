<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Unit\Domain\Progress\Aggregation;

use OpenLoyalty\Achievement\Domain\Context\Context;
use OpenLoyalty\Achievement\Domain\Progress\Aggregation\QuantityAggregator;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\Aggregation\Aggregation;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\Aggregation\Type;
use PHPUnit\Framework\TestCase;

final class QuantityAggregatorTest extends TestCase
{
    /**
     * @test
     */
    public function it_gets_one(): void
    {
        $aggregator = new QuantityAggregator();
        $this->assertSame(1.0, $aggregator->getValue(
            Aggregation::create(
                Type::create(
                    Type::QUANTITY)),
            $this->createMock(Context::class)
        ));
    }
}
