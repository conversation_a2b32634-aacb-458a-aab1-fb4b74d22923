<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Unit\Domain\ValueObject;

use OpenLoyalty\Achievement\Domain\ValueObject\Type;
use PHPUnit\Framework\TestCase;

final class TypeTest extends TestCase
{
    /**
     * @test
     */
    public function it_test_creates_type(): void
    {
        $typeDirect = Type::create(
            Type::DIRECT);
        $typeReferral = Type::create(Type::REFERRAL);

        $this->assertTrue($typeReferral->isReferral());
        $this->assertSame(Type::REFERRAL, $typeReferral->getCode());
        $this->assertSame(Type::DIRECT, $typeDirect->getCode());
    }
}
