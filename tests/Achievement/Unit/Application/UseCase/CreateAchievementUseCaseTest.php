<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Unit\Application\UseCase;

use OpenLoyalty\Achievement\Application\Command\CreateAchievement;
use OpenLoyalty\Achievement\Application\UseCase\CreateAchievementUseCase;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use PHPUnit\Framework\TestCase;

final class CreateAchievementUseCaseTest extends TestCase
{
    private CommandBusInterface $commandBus;
    private CreateAchievementUseCase $useCase;

    protected function setUp(): void
    {
        $this->commandBus = $this->createMock(CommandBusInterface::class);
        $this->useCase = new CreateAchievementUseCase($this->commandBus);
    }

    /**
     * @test
     */
    public function it_creates_achievement(): void
    {
        $achievementId = new AchievementId('00000000-0000-0000-0000-000000000000');
        $storeId = new StoreId('00000000-0000-0000-0000-000000000000');

        $command = new CreateAchievement(
            $achievementId,
            $storeId,
            false,
            null,
            null,
            [],
            []
        );

        $this->commandBus->expects(self::once())->method('dispatch')->with($command);

        $this->useCase->execute($command);
    }
}
