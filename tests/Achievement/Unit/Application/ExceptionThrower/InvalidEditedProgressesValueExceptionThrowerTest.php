<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Test\Achievement\Unit\Application\ExceptionThrower;

use InvalidArgumentException;
use OpenLoyalty\Achievement\Application\Command\UpdateMemberAchievementProgress;
use OpenLoyalty\Achievement\Application\DTO\ProgressRule;
use OpenLoyalty\Achievement\Application\ExceptionThrower\InvalidEditedProgressesValueExceptionThrower;
use OpenLoyalty\Achievement\Domain\Entity\Achievement;
use OpenLoyalty\Achievement\Domain\Entity\Rule;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\CompleteRule;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\Period\Period;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\Period\Type;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\AchievementRuleId;
use OpenLoyalty\Core\Domain\Id\AdminId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use PHPUnit\Framework\TestCase;

final class InvalidEditedProgressesValueExceptionThrowerTest extends TestCase
{
    /**
     * @test
     */
    public function it_throws_exception_if_edited_progress_completed_value_is_invalid(): void
    {
        $this->expectException(InvalidArgumentException::class);

        $achievementRuleId = $this->createMock(AchievementRuleId::class);
        $achievement = $this->createMock(Achievement::class);
        $rule1 = $this->createMock(Rule::class);
        $achievement->method('getRules')->willReturn([$rule1]);
        $rule1->method('getCompleteRule')->willReturn(CompleteRule::create(
            Period::create(
                Type::create(Type::MONTH), 5, 1),
            5)
        );

        $progressRule1 = new ProgressRule($achievementRuleId, 10.0, 15.0);
        $updateMemberAchievementProgress = new UpdateMemberAchievementProgress(
            new StoreId('d7f11e18-541d-4a27-97ca-5bee5dfa0d1f'),
            new AchievementId('d7f11e18-541d-4a27-97ca-5bee5dfa0d1f'),
            new AdminId('d7f11e18-541d-4a27-97ca-5bee5dfa0d1f'),
            new CustomerId('d7f11e18-541d-4a27-97ca-5bee5dfa0d1f'),
            10,
            new \DateTimeImmutable(),
            [$progressRule1]
        );

        $rule1->method('getAchievementRuleId')->willReturn($achievementRuleId);

        $thrower = new InvalidEditedProgressesValueExceptionThrower();

        $thrower->throwExceptionIfProgressesValueFromCommandAreInvalid($achievement, $updateMemberAchievementProgress);
    }

    /**
     * @test
     */
    public function it_throws_exception_if_edited_progress_consecutive_value_is_invalid(): void
    {
        $this->expectException(InvalidArgumentException::class);

        $achievementRuleId = $this->createMock(AchievementRuleId::class);
        $achievement = $this->createMock(Achievement::class);
        $rule1 = $this->createMock(Rule::class);
        $achievement->method('getRules')->willReturn([$rule1]);
        $rule1->method('getCompleteRule')->willReturn(CompleteRule::create(
            Period::create(
                Type::create(Type::MONTH), 5, 1),
            5)
        );

        $progressRule1 = new ProgressRule($achievementRuleId, 4.0, 15.0);
        $updateMemberAchievementProgress = new UpdateMemberAchievementProgress(
            new StoreId('d7f11e18-541d-4a27-97ca-5bee5dfa0d1f'),
            new AchievementId('d7f11e18-541d-4a27-97ca-5bee5dfa0d1f'),
            new AdminId('d7f11e18-541d-4a27-97ca-5bee5dfa0d1f'),
            new CustomerId('d7f11e18-541d-4a27-97ca-5bee5dfa0d1f'),
            10,
            new \DateTimeImmutable(),
            [$progressRule1]
        );

        $rule1->method('getAchievementRuleId')->willReturn($achievementRuleId);

        $thrower = new InvalidEditedProgressesValueExceptionThrower();

        $thrower->throwExceptionIfProgressesValueFromCommandAreInvalid($achievement, $updateMemberAchievementProgress);
    }

    /**
     * @test
     */
    public function it_throws_exception_if_edited_progress_rule_is_not_overall(): void
    {
        $this->expectException(InvalidArgumentException::class);

        $achievementRuleId = $this->createMock(AchievementRuleId::class);
        $achievement = $this->createMock(Achievement::class);
        $rule1 = $this->createMock(Rule::class);
        $achievement->method('getRules')->willReturn([$rule1]);
        $rule1->method('getCompleteRule')->willReturn(CompleteRule::create(
            Period::create(
                Type::create(Type::MONTH), 5, 1),
            5)
        );
        $rule1->method('isCompleteRulePeriodTypeEquals')->with('overall')->willReturn(false);

        $progressRule1 = new ProgressRule($achievementRuleId, 4.0, 15.0);
        $updateMemberAchievementProgress = new UpdateMemberAchievementProgress(
            new StoreId('d7f11e18-541d-4a27-97ca-5bee5dfa0d1f'),
            new AchievementId('d7f11e18-541d-4a27-97ca-5bee5dfa0d1f'),
            new AdminId('d7f11e18-541d-4a27-97ca-5bee5dfa0d1f'),
            new CustomerId('d7f11e18-541d-4a27-97ca-5bee5dfa0d1f'),
            10,
            new \DateTimeImmutable(),
            [$progressRule1]
        );

        $rule1->method('getAchievementRuleId')->willReturn($achievementRuleId);

        $thrower = new InvalidEditedProgressesValueExceptionThrower();

        $thrower->throwExceptionIfProgressesValueFromCommandAreInvalid($achievement, $updateMemberAchievementProgress);
    }
}
