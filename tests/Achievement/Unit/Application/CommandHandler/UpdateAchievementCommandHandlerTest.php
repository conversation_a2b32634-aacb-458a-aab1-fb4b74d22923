<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Unit\Application\CommandHandler;

use OpenLoyalty\Achievement\Application\Command\UpdateAchievement;
use OpenLoyalty\Achievement\Application\CommandHandler\UpdateAchievementCommandHandler;
use OpenLoyalty\Achievement\Domain\AchievementRepositoryInterface;
use OpenLoyalty\Achievement\Domain\Checker\ActiveAchievementLimitReachedCheckerInterface;
use OpenLoyalty\Achievement\Domain\Entity\Achievement;
use OpenLoyalty\Achievement\Domain\Entity\AchievementTranslation;
use OpenLoyalty\Achievement\Domain\Entity\Rule;
use OpenLoyalty\Achievement\Domain\Exception\AchievementNotFoundException;
use OpenLoyalty\Achievement\Domain\Exception\Rule\CompleteRule\InvalidPeriodTypeException;
use OpenLoyalty\Achievement\Domain\Exception\Rule\InvalidAggregationTypeException;
use OpenLoyalty\Achievement\Domain\Factory\RuleFactoryInterface;
use OpenLoyalty\Achievement\Domain\SystemEvent\AchievementWasUpdated;
use OpenLoyalty\Achievement\Domain\ValueObject\Activity\Activity;
use OpenLoyalty\Achievement\Domain\ValueObject\Activity\Operator;
use OpenLoyalty\Achievement\Domain\ValueObject\Limit;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\Aggregation\Aggregation;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\Aggregation\Type as AggregationType;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\CompleteRule;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\Period\Period;
use OpenLoyalty\Achievement\Domain\ValueObject\Rule\CompleteRule\Period\Type;
use OpenLoyalty\Achievement\Domain\ValueObject\Type as RuleType;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Core\Domain\ValueObject\LimitInterval;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use PHPUnit\Framework\TestCase;

final class UpdateAchievementCommandHandlerTest extends TestCase
{
    private RuleFactoryInterface $ruleFactory;
    private AchievementRepositoryInterface $achievementRepository;
    private UpdateAchievementCommandHandler $commandHandler;
    private ActiveAchievementLimitReachedCheckerInterface $activeAchievementLimitReachedChecker;
    private EventBusInterface $eventBus;

    protected function setUp(): void
    {
        $this->ruleFactory = $this->createMock(RuleFactoryInterface::class);
        $this->achievementRepository = $this->createMock(AchievementRepositoryInterface::class);
        $this->activeAchievementLimitReachedChecker = $this->createMock(ActiveAchievementLimitReachedCheckerInterface::class);
        $this->eventBus = $this->createMock(EventBusInterface::class);
        $this->commandHandler = new UpdateAchievementCommandHandler(
            $this->ruleFactory,
            $this->achievementRepository,
            $this->eventBus,
        );
    }

    /**
     * @test
     *
     * @throws \OpenLoyalty\Achievement\Domain\Exception\InvalidActivityOperatorException
     * @throws \OpenLoyalty\Core\Domain\Exception\InvalidLimitIntervalTypeException
     * @throws InvalidAggregationTypeException
     * @throws InvalidPeriodTypeException
     * @throws \OpenLoyalty\Core\Domain\Exception\InvalidTriggerException
     * @throws AchievementNotFoundException
     */
    public function it_update_achievement(): void
    {
        $activity = Activity::create(Operator::create('is_month_of_year'), ['june', 'august']);
        $limit = Limit::create(LimitInterval::create('calendarYears', 1), 1);
        $translations = [
            'en' => $this->createMock(AchievementTranslation::class),
            'pl' => null,
        ];
        $rules = [
            [
                'type' => RuleType::create(RuleType::DIRECT),
                'trigger' => Trigger::create('transaction'),
                'completeRule' => CompleteRule::create(
                    Period::create(Type::create('overall')),
                    10.0
                ),
                'aggregation' => Aggregation::create(AggregationType::create('quantity')),
                'conditions' => [],
            ],
        ];

        $command = new UpdateAchievement(
            new AchievementId('00000000-0000-0000-0000-000000000000'),
            new StoreId('00000000-0000-0000-0000-000000000000'),
            true,
            $activity,
            $limit,
            $translations,
            $rules,
        );

        $achievementEntity = $this->createMock(Achievement::class);
        $achievementEntity->method('isActive')->willReturn(false);
        $ruleEntity = $this->createMock(Rule::class);

        $this->achievementRepository->method('byId')->willReturn($achievementEntity);
        $this->ruleFactory->expects(self::once())->method('create')->willReturn($ruleEntity);

        $achievementEntity->expects(self::once())->method('assignTranslations')->with($translations);
        $achievementEntity->expects(self::once())->method('activate');
        $achievementEntity->expects(self::once())->method('changeActivity')->with($activity);
        $achievementEntity->expects(self::once())->method('changeLimit')->with($limit);
        $achievementEntity->expects(self::once())->method('replaceRules')->with([$ruleEntity]);

        $this->achievementRepository->expects(self::once())->method('save')->with($achievementEntity);

        $this->eventBus->expects(self::once())->method('dispatch')->with(new AchievementWasUpdated(
            $achievementEntity,
            true,
            true,
        ));

        $this->commandHandler->__invoke($command);
    }

    /**
     * @throws AchievementNotFoundException
     */
    public function it_throws_not_found_exception(): void
    {
        $command = new UpdateAchievement(
            new AchievementId('00000000-0000-0000-0000-000000000000'),
            new StoreId('00000000-0000-0000-0000-000000000000'),
            true,
            null,
            null,
            [],
            []
        );

        $this->achievementRepository->method('byId')->willReturn(null);
        $this->achievementRepository->expects(self::never())->method('save');

        $this->expectException(AchievementNotFoundException::class);

        $this->commandHandler->__invoke($command);
    }
}
