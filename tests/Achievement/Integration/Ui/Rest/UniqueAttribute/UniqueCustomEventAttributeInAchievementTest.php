<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Integration\Ui\Rest\UniqueAttribute;

use DateTimeInterface;
use OpenLoyalty\Test\Achievement\Integration\Traits\AchievementApiTrait;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\CustomEvent\Integration\Traits\CustomEventApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class UniqueCustomEventAttributeInAchievementTest extends AbstractApiTest
{
    use MemberApiTrait;
    use AchievementApiTrait;
    use TenantApiTrait;
    use CustomEventApiTrait;

    private HttpKernelBrowser $client;

    private string $tenantCode = 'unique_custom_event_attribute_in_achievement_tenant';

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
        $this->postTenant($this->client, $this->tenantCode);

        $this->postCustomEventSchema(
            httpClient: $this->client,
            storeCode: $this->tenantCode,
            customEventName: 'test_schema_number',
            name: 'Test schema number',
            eventCodeAttribute: 'textProperty',
            attributeType: 'text'
        );
    }

    /**
     * @test
     * @dataProvider eventConfigurationProvider
     */
    public function it_increases_achievement_member_progress_only_once_if_unique_attribute_value_is_already_set(
        string $attributeType,
        string $attributeCodeProperty,
        string|bool|int|float|DateTimeInterface $attributeValue
    ): void {
        $email = '<EMAIL>';

        $memberId = json_decode($this->postMember($this->client, storeCode: $this->tenantCode, email: $email)->getContent(), true)['customerId'];

        $this->postCustomEventSchema(
            httpClient: $this->client,
            storeCode: $this->tenantCode,
            customEventName: 'test_schema_attributes',
            name: 'Test schema number',
            eventCodeAttribute: $attributeCodeProperty,
            attributeType: $attributeType
        );

        $response = $this->postAchievement(
            httpClient: $this->client,
            storeCode: $this->tenantCode,
            activity: null,
            rules: [
                [
                    'type' => 'direct',
                    'trigger' => 'custom_event',
                    'event' => 'test_schema_attributes',
                    'completeRule' => [
                        'period' => [
                            'type' => 'overall',
                        ],
                        'periodGoal' => 4,
                        'uniqueAttribute' => $attributeCodeProperty,
                    ],
                    'aggregation' => [
                        'type' => 'quantity',
                    ],
                    'conditions' => [],
                ],
            ],
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $this->createCustomEvent($email, $attributeCodeProperty, $attributeValue, 'test_schema_attributes');
        $this->createCustomEvent($email, $attributeCodeProperty, $attributeValue, 'test_schema_attributes');

        $progress = $this->getAchievementProgressForMember($achievementId, $memberId);

        $this->assertSame(1.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);
    }

    public function eventConfigurationProvider(): array
    {
        return [
            'text property test' => ['text', 'textProperty', 'attribute text test'],
            'number property test' => ['number', 'numberProperty', 10],
            'number float property test' => ['number', 'numberProperty', 10.2],
            'boolean property test' => ['boolean', 'booleanProperty', true],
            'datetime property test' => ['datetime', 'datetimeProperty', '2021-10-10T10:10:10+00:00'],
        ];
    }

    /**
     * @test
     */
    public function it_increases_achievement_member_progress_if_attribute_values_are_different(): void
    {
        $email = '<EMAIL>';

        $memberId = json_decode($this->postMember($this->client, storeCode: $this->tenantCode, email: $email)->getContent(), true)['customerId'];

        $response = $this->postAchievement(
            httpClient: $this->client,
            storeCode: $this->tenantCode,
            activity: null,
            rules: [
                [
                    'type' => 'direct',
                    'trigger' => 'custom_event',
                    'event' => 'test_schema_number',
                    'completeRule' => [
                        'period' => [
                            'type' => 'overall',
                        ],
                        'periodGoal' => 4,
                        'uniqueAttribute' => 'textProperty',
                    ],
                    'aggregation' => [
                        'type' => 'quantity',
                    ],
                    'conditions' => [],
                ],
            ],
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $this->createCustomEvent($email, 'textProperty', 'unique_value_text');
        $this->createCustomEvent($email, 'textProperty', 'unique_value_text_1');

        $progress = $this->getAchievementProgressForMember($achievementId, $memberId);
        $this->assertSame(2.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);
    }

    /**
     * @test
     */
    public function it_increases_achievement_member_progress_if_attribute_values_same_different_in_next_rule_progress_iteration(): void
    {
        $email = '<EMAIL>';

        $memberId = json_decode($this->postMember($this->client, storeCode: $this->tenantCode, email: $email)->getContent(), true)['customerId'];

        $response = $this->postAchievement(
            httpClient: $this->client,
            storeCode: $this->tenantCode,
            activity: null,
            rules: [
                [
                    'type' => 'direct',
                    'trigger' => 'custom_event',
                    'event' => 'test_schema_number',
                    'completeRule' => [
                        'period' => [
                            'type' => 'overall',
                        ],
                        'periodGoal' => 2,
                        'uniqueAttribute' => 'textProperty',
                    ],
                    'aggregation' => [
                        'type' => 'quantity',
                    ],
                    'conditions' => [],
                ],
            ],
            limit: null,
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $this->createCustomEvent($email, 'textProperty', 'unique_value_text');
        $this->createCustomEvent($email, 'textProperty', 'unique_value_text_1');

        $progress = $this->getAchievementProgressForMember($achievementId, $memberId);

        $this->assertSame(1, $progress['memberProgress']['completedCount']);
        $this->assertSame(0.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);

        $this->createCustomEvent($email, 'textProperty', 'unique_value_text');

        $progress = $this->getAchievementProgressForMember($achievementId, $memberId);

        $this->assertSame(1, $progress['memberProgress']['completedCount']);
        $this->assertSame(1.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);
    }

    private function getAchievementProgressForMember(string $achievementId, string $customerId): array
    {
        $response = $this->getMembersAchievementsStatus($this->client, $this->tenantCode, $customerId, $achievementId);

        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('items', $data);
        $this->assertNotEmpty($data['items']);

        return reset($data['items']);
    }

    private function createCustomEvent(string $customerEmail, string $propertyName, string|int|float|DateTimeInterface|bool $propertyValue, string $eventName = 'test_schema_number'): void
    {
        $this->postCustomEvent(
            httpClient: $this->client,
            storeCode: $this->tenantCode,
            customEventName: $eventName,
            email: $customerEmail,
            body: [
                $propertyName => $propertyValue,
            ]
        );
    }
}
