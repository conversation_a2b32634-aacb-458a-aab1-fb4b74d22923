<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Integration\Ui\Rest\UniqueAttribute;

use OpenLoyalty\Test\Achievement\Integration\Traits\AchievementApiTrait;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\CustomEvent\Integration\Traits\CustomEventApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class UpdatingAchievementWithUniqueCustomEventAttributeTest extends AbstractApiTest
{
    use MemberApiTrait;
    use AchievementApiTrait;
    use TenantApiTrait;
    use CustomEventApiTrait;

    private HttpKernelBrowser $client;

    private string $tenantCode = 'unique_custom_event_attribute_in_achievement_tenant';

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
        $this->postTenant($this->client, $this->tenantCode);

        $this->postCustomEventSchema(
            httpClient: $this->client,
            storeCode: $this->tenantCode,
            customEventName: 'test_schema_number',
            name: 'Test schema number',
            eventCodeAttribute: 'textProperty',
            attributeType: 'text'
        );
    }

    /**
     * @test
     */
    public function it_updates_achievement_with_unique_custom_event_attribute_value(): void
    {
        $response = $this->postAchievement(
            httpClient: $this->client,
            storeCode: $this->tenantCode,
            active: false,
            rules: [
                [
                    'type' => 'direct',
                    'trigger' => 'custom_event',
                    'event' => 'test_schema_number',
                    'completeRule' => [
                        'period' => [
                            'type' => 'overall',
                        ],
                        'periodGoal' => 2,
                        'uniqueAttribute' => 'textProperty',
                    ],
                    'aggregation' => [
                        'type' => 'expression',
                        'rule' => 'event.body.number',
                    ],
                    'conditions' => [],
                ],
            ],
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $achievement = json_decode($this->getAchievement($this->client, $this->tenantCode, $achievementId)->getContent(), true);
        $achievementRuleId = $achievement['rules'][0]['achievementRuleId'];

        $dataToUpdate = [
            'achievement' => [
                'rules' => [
                    [
                        'achievementRuleId' => $achievementRuleId,
                        'conditions' => [
                            [
                                'attribute' => 'event.body.numberProperty',
                                'operator' => 'is_greater_or_equal',
                                'data' => '10',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->patchAchievement(
            httpClient: $this->client,
            storeCode: $this->tenantCode,
            achievementId: $achievementId,
            data: $dataToUpdate
        );

        $achievement = json_decode($this->getAchievement($this->client, $this->tenantCode, $achievementId)->getContent(), true);

        $this->assertSame('textProperty', $achievement['rules'][0]['completeRule']['uniqueAttribute']);
    }

    /**
     * @test
     */
    public function it_updates_achievement_with_unique_custom_event_attribute_value_is_removed(): void
    {
        $response = $this->postAchievement(
            httpClient: $this->client,
            storeCode: $this->tenantCode,
            active: false,
            rules: [
                [
                    'type' => 'direct',
                    'trigger' => 'custom_event',
                    'event' => 'test_schema_number',
                    'completeRule' => [
                        'period' => [
                            'type' => 'overall',
                        ],
                        'periodGoal' => 2,
                        'uniqueAttribute' => 'textProperty',
                    ],
                    'aggregation' => [
                        'type' => 'expression',
                        'rule' => 'event.body.number',
                    ],
                    'conditions' => [],
                ],
            ],
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $achievement = json_decode($this->getAchievement($this->client, $this->tenantCode, $achievementId)->getContent(), true);
        $achievementRuleId = $achievement['rules'][0]['achievementRuleId'];

        $dataToUpdate = [
            'achievement' => [
                'rules' => [
                    [
                        'achievementRuleId' => $achievementRuleId,
                        'completeRule' => [
                            'period' => [
                                'type' => 'overall',
                            ],
                            'periodGoal' => 2,
                        ],
                    ],
                ],
            ],
        ];

        $this->patchAchievement(
            httpClient: $this->client,
            storeCode: $this->tenantCode,
            achievementId: $achievementId,
            data: $dataToUpdate
        );

        $achievement = json_decode($this->getAchievement($this->client, $this->tenantCode, $achievementId)->getContent(), true);

        $this->assertArrayNotHasKey('textProperty', $achievement['rules'][0]['completeRule']);
    }
}
