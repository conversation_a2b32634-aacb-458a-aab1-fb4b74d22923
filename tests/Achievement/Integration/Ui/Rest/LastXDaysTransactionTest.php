<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Integration\Ui\Rest;

use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;

final class LastXDaysTransactionTest extends AbstractApiTest
{
    use MemberApiTrait;

    /**
     * @test
     */
    public function it_test_complete_achievement_progress_with_3_transactions_in_last_10_days(): void
    {
        //3 transaction in last 10 days
        $achievementData = [
            'achievement' => [
                'active' => true,
                'translations' => [
                    'en' => [
                        'name' => 'Progress1',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'transaction',
                        'completeRule' => [
                            'period' => [
                                'type' => 'last_day',
                                'value' => 10,
                            ],
                            'periodGoal' => 3,
                        ],
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'conditions' => [],
                        'limit' => [
                            'interval' => [
                                'type' => 'calendarDays',
                                'value' => 1,
                            ],
                            'value' => 1,
                        ],
                    ],
                ],
            ],
        ];

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement',
            $achievementData
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'Larra',
                    'lastName' => 'Smith',
                    'email' => '<EMAIL>',
                    'agreement1' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $memberId = $data['customerId'];

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 51,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/1',
                        'purchasedAt' => '2023-05-05 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(1.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);
        self::assertSame(10.0, $progress['memberProgress']['rules'][0]['periodValue']);
        $this->assertArrayNotHasKey('completedConsecutivePeriods', $progress['memberProgress']['rules'][0]);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 51,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/2',
                        'purchasedAt' => '2023-05-06 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(0, $progress['memberProgress']['completedCount']);
        self::assertSame(2.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 51,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/3',
                        'purchasedAt' => '2023-05-10 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(1, $progress['memberProgress']['completedCount']);
    }

    /**
     * @test
     */
    public function it_test_achievement_progress_does_not_change_when_transaction_is_same_day(): void
    {
        $achievementData = [
            'achievement' => [
                'active' => true,
                'translations' => [
                    'en' => [
                        'name' => 'Progress2',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'transaction',
                        'completeRule' => [
                            'period' => [
                                'type' => 'last_day',
                                'value' => 10,
                            ],
                            'periodGoal' => 3,
                        ],
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'conditions' => [],
                        'limit' => [
                            'interval' => [
                                'type' => 'calendarDays',
                                'value' => 1,
                            ],
                            'value' => 1,
                        ],
                    ],
                ],
            ],
        ];

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement',
            $achievementData
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $response = $this->postMember(
            httpClient: $client,
            email: '<EMAIL>',
        );
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $memberId = $data['customerId'];

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 51,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/1',
                        'purchasedAt' => '2023-05-05 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(1.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);
        self::assertSame(10.0, $progress['memberProgress']['rules'][0]['periodValue']);

        // transaction in same day
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 51,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/2',
                        'purchasedAt' => '2023-05-05 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(1.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);
    }

    /**
     * @test
     */
    public function it_test_collect_30_gross_value_in_last_10_days(): void
    {
        //collect 30 grossValue in the last 10 days
        $achievementData = [
            'achievement' => [
                'active' => true,
                'translations' => [
                    'en' => [
                        'name' => 'Progress aggregation expression achievement',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'transaction',
                        'completeRule' => [
                            'period' => [
                                'type' => 'last_day',
                                'value' => 10,
                            ],
                            'periodGoal' => 30,
                        ],
                        'aggregation' => [
                            'type' => 'expression',
                            'rule' => 'transaction.grossValue',
                        ],
                        'conditions' => [],
                        'limit' => [
                            'interval' => [
                                'type' => 'calendarDays',
                                'value' => 1,
                            ],
                            'value' => 1,
                        ],
                    ],
                ],
            ],
        ];

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement',
            $achievementData
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $response = $this->postMember(
            httpClient: $client,
            email: '<EMAIL>',
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $memberId = $data['customerId'];

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 10,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/1',
                        'purchasedAt' => '2023-05-05 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(10.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);
        self::assertSame(10.0, $progress['memberProgress']['rules'][0]['periodValue']);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 10,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/2',
                        'purchasedAt' => '2023-05-07 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(20.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 10,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/3',
                        'purchasedAt' => '2023-05-08 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(1, $progress['memberProgress']['completedCount']);
    }

    /**
     * @test
     */
    public function it_test_multiple_rules_collect_100_grossValue_in_last_10_days_and_make_3_transaction(): void
    {
        //collect 100 grossValue in the last 10 days and 3 transaction in 10 days
        $achievementData = [
            'achievement' => [
                'active' => true,
                'translations' => [
                    'en' => [
                        'name' => 'Multiple rules',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'transaction',
                        'completeRule' => [
                            'period' => [
                                'type' => 'last_day',
                                'value' => 10,
                            ],
                            'periodGoal' => 100,
                        ],
                        'aggregation' => [
                            'type' => 'expression',
                            'rule' => 'transaction.grossValue',
                        ],
                        'conditions' => [],
                        'limit' => [
                            'interval' => [
                                'type' => 'calendarDays',
                                'value' => 1,
                            ],
                            'value' => 1,
                        ],
                    ],
                    [
                        'type' => 'direct',
                        'trigger' => 'transaction',
                        'completeRule' => [
                            'period' => [
                                'type' => 'last_day',
                                'value' => 10,
                            ],
                            'periodGoal' => 3,
                        ],
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'conditions' => [],
                        'limit' => [
                            'interval' => [
                                'type' => 'calendarDays',
                                'value' => 1,
                            ],
                            'value' => 1,
                        ],
                    ],
                ],
            ],
        ];

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement',
            $achievementData
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $response = $this->postMember(
            httpClient: $client,
            email: '<EMAIL>',
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $memberId = $data['customerId'];

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 25,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/1',
                        'purchasedAt' => '2023-05-05 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(0, $progress['memberProgress']['completedCount']);
        self::assertSame(10.0, $progress['memberProgress']['rules'][0]['periodValue']);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 25,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/3',
                        'purchasedAt' => '2023-05-07 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(0, $progress['memberProgress']['completedCount']);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 25,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/4',
                        'purchasedAt' => '2023-05-08 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(0, $progress['memberProgress']['completedCount']);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 25,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/5',
                        'purchasedAt' => '2023-05-09 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(1, $progress['memberProgress']['completedCount']);
    }

    /**
     * @test
     */
    public function it_test_achievement_progress_does_not_change_if_transaction_is_older_then_3_days(): void
    {
        //3 transaction in last 10 days
        $achievementData = [
            'achievement' => [
                'active' => true,
                'translations' => [
                    'en' => [
                        'name' => 'Progress1',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'transaction',
                        'completeRule' => [
                            'period' => [
                                'type' => 'last_day',
                                'value' => 3,
                            ],
                            'periodGoal' => 3,
                        ],
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'conditions' => [],
                        'limit' => [
                            'interval' => [
                                'type' => 'calendarDays',
                                'value' => 1,
                            ],
                            'value' => 1,
                        ],
                    ],
                ],
            ],
        ];

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement',
            $achievementData
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $response = $this->postMember(
            httpClient: $client,
            email: '<EMAIL>',
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $memberId = $data['customerId'];

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 51,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/1',
                        'purchasedAt' => '2023-05-05 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(1.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);
        self::assertSame(3.0, $progress['memberProgress']['rules'][0]['periodValue']);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 51,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/2',
                        'purchasedAt' => '2023-05-01 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(0, $progress['memberProgress']['completedCount']);
        self::assertSame(1.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 51,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/3',
                        'purchasedAt' => '2023-04-20 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(1.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);
        self::assertSame(0, $progress['memberProgress']['completedCount']);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 51,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/4',
                        'purchasedAt' => '2023-04-21 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(2.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);
        self::assertSame(0, $progress['memberProgress']['completedCount']);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'AL/1',
                            'name' => 'item 1',
                            'quantity' => 1,
                            'grossValue' => 51,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/multi/rule1s/5',
                        'purchasedAt' => '2023-04-22 11:20:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $progress = $this->getAchievementProgressForMember($client, $achievementId, $memberId);
        self::assertSame(1, $progress['memberProgress']['completedCount']);
    }

    private function getAchievementProgressForMember($client, string $achievementId, string $customerId): array
    {
        $client->request(
            'GET',
            sprintf('/api/%s/member/%s/achievement', LoadSettingsData::DEFAULT_STORE_CODE, $customerId),
            ['achievementId' => $achievementId]
        );

        $response = $client->getResponse();

        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('items', $data);
        $this->assertNotEmpty($data['items']);

        return reset($data['items']);
    }
}
