<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Integration\Ui\Rest;

use OpenLoyalty\Achievement\Domain\MemberAchievementProgressRepositoryInterface;
use OpenLoyalty\Achievement\Infrastructure\Persistence\Doctrine\Repository\MemberAchievementProgressRepository;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class PatchTest extends AbstractApiTest
{
    use MemberApiTrait;

    private HttpKernelBrowser $client;

    private string $defaultGlobalActiveAchievementLimit;

    private function getAchievement(string $tenantCode, string $achievementId): array
    {
        $this->client->request(
            'GET',
            '/api/'.$tenantCode.'/achievement/'.$achievementId,
        );
        $response = $this->client->getResponse();
        $achievement = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        return $achievement;
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
        $this->defaultGlobalActiveAchievementLimit = $_ENV['GLOBAL_ACTIVE_ACHIEVEMENT_LIMIT'];
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $_ENV['GLOBAL_ACTIVE_ACHIEVEMENT_LIMIT'] = $this->defaultGlobalActiveAchievementLimit;
    }

    /**
     * @test
     */
    public function it_patch_update_achievement_and_does_not_reset_progress(): void
    {
        $achievementData = [
            'achievement' => [
                'active' => 1,
                'translations' => [
                    'en' => [
                        'name' => 'Achievement to PATCH',
                        'description' => 'description',
                    ],
                ],
                'activity' => [
                    'operator' => 'is_day_of_week',
                    'data' => [
                        'Monday',
                        'Tuesday',
                        'Friday',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'transaction',
                        'completeRule' => [
                            'period' => [
                                'type' => 'overall',
                            ],
                            'periodGoal' => 2,
                        ],
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'conditions' => [
                            [
                                'attribute' => 'transaction.grossValue',
                                'operator' => 'is_greater_or_equal',
                                'data' => '50',
                            ],
                        ],
                    ],
                ],
                'limit' => [
                    'value' => 6,
                    'interval' => [
                        'value' => 2,
                        'type' => 'calendarYears',
                    ],
                ],
            ],
        ];

        $editedAchievementData = [
            'achievement' => [
                'active' => 1,
                'translations' => [
                    'en' => [
                        'name' => 'Edited achievement by PATCH',
                        'description' => 'Edited achievement',
                    ],
                ],
                'activity' => [
                    'operator' => 'is_month_of_year',
                    'data' => [
                        'January',
                        'June',
                        'July',
                        'August',
                    ],
                ],
                'limit' => [
                    'value' => 5,
                    'interval' => [
                        'value' => 2,
                        'type' => 'calendarYears',
                    ],
                ],
            ],
        ];

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement',
            $achievementData
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $response = $this->postMember(
            httpClient: $this->client,
            email: '<EMAIL>',
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $this->client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'A/1',
                            'name' => 'Achievement item progress',
                            'quantity' => 1,
                            'grossValue' => 60,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement not reset #1',
                        'purchasedAt' => '2023-01-27 11:05:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        /**
         * @var MemberAchievementProgressRepository $memberProgressRepository
         */
        $memberProgressRepository = self::getContainer()->get(MemberAchievementProgressRepositoryInterface::class);
        $progress = $memberProgressRepository->findCurrent(
            new AchievementId($achievementId),
            new CustomerId($customerId)
        );
        self::assertSame('2023-01-27 11:05:00', $progress->getRules()[0]->getLastPeriodEvent()->format('Y-m-d H:i:s'));

        $this->client->request(
            'PATCH',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement/'.$achievementId,
            $editedAchievementData
        );
        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $this->client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'A/12',
                            'name' => 'Achievement item progress',
                            'quantity' => 1,
                            'grossValue' => 70,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement not reset #2',
                        'purchasedAt' => '2023-01-27 12:30:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'A/1',
                            'name' => 'Achievement item progress',
                            'quantity' => 1,
                            'grossValue' => 60,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement not reset #3',
                        'purchasedAt' => '2023-01-27 13:40:00',
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $progress = $memberProgressRepository->findCurrent(
            new AchievementId($achievementId),
            new CustomerId($customerId)
        );
        self::assertSame('2023-01-27 13:40:00', $progress->getRules()[0]->getLastPeriodEvent()->format('Y-m-d H:i:s'));

        $achievement = $this->getAchievement(LoadSettingsData::DEFAULT_STORE_CODE, $achievementId);

        self::assertTrue($achievement['active']);
        self::assertArrayHasKey('translations', $achievement);
        self::assertSame('Edited achievement by PATCH', $achievement['translations']['en']['name']);
        self::assertSame('Edited achievement', $achievement['translations']['en']['description']);
        self::assertArrayHasKey('rules', $achievement);
        self::assertCount(1, $achievement['rules']);
        self::assertSame('transaction', $achievement['rules'][0]['trigger']);
        self::assertSame('overall', $achievement['rules'][0]['completeRule']['period']['type']);
        self::assertSame('2.000000', $achievement['rules'][0]['completeRule']['periodGoal']);
        self::assertSame('quantity', $achievement['rules'][0]['aggregation']['type']);
        self::assertSame('transaction.grossValue', $achievement['rules'][0]['conditions'][0]['attribute']);
        self::assertSame('is_greater_or_equal', $achievement['rules'][0]['conditions'][0]['operator']);
        self::assertSame(50, $achievement['rules'][0]['conditions'][0]['data']);
        self::assertArrayHasKey('limit', $achievement);
        self::assertCount(2, $achievement['limit']);
        self::assertSame('calendarYears', $achievement['limit']['interval']['type']);
        self::assertSame(2, $achievement['limit']['interval']['value']);
        self::assertSame(5, $achievement['limit']['value']);
        self::assertCount(4, $achievement['activity']['data']);
        self::assertSame('is_month_of_year', $achievement['activity']['operator']);
    }

    /**
     * @test
     */
    public function it_patch_update_achievement_limit_and_availability_to_none(): void
    {
        $achievementData = [
            'achievement' => [
                'active' => 1,
                'translations' => [
                    'en' => [
                        'name' => 'Achievement',
                        'description' => 'description',
                    ],
                ],
                'activity' => [
                    'operator' => 'is_day_of_week',
                    'data' => [
                        'Monday',
                        'Tuesday',
                        'Friday',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'transaction',
                        'completeRule' => [
                            'period' => [
                                'type' => 'overall',
                            ],
                            'periodGoal' => 2,
                        ],
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'conditions' => [
                            [
                                'attribute' => 'transaction.grossValue',
                                'operator' => 'is_greater_or_equal',
                                'data' => '50',
                            ],
                        ],
                    ],
                ],
                'limit' => [
                    'value' => 6,
                    'interval' => [
                        'value' => 2,
                        'type' => 'calendarYears',
                    ],
                ],
            ],
        ];

        $editedAchievementData = [
            'achievement' => [
                'active' => 1,
                'translations' => [
                    'en' => [
                        'name' => 'Edited achievement by PATCH',
                        'description' => 'Edited achievement',
                    ],
                ],
                'activity' => [
                    'operator' => '',
                    'data' => null,
                ],
            ],
        ];

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement',
            $achievementData
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $this->client->request(
            'PATCH',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement/'.$achievementId,
            $editedAchievementData
        );
        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $achievement = $this->getAchievement(LoadSettingsData::DEFAULT_STORE_CODE, $achievementId);

        self::assertTrue($achievement['active']);
        self::assertSame([], $achievement['limit']);
        self::assertSame([], $achievement['activity']);
    }

    /**
     * @test
     */
    public function it_does_not_update_blank_translations(): void
    {
        $achievementData = [
            'achievement' => [
                'active' => 1,
                'translations' => [
                    'en' => [
                        'name' => 'Achievement not blank',
                    ],
                ],
                'activity' => [
                    'operator' => 'is_day_of_week',
                    'data' => [
                        'Monday',
                        'Tuesday',
                        'Friday',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'transaction',
                        'completeRule' => [
                            'period' => [
                                'type' => 'overall',
                            ],
                            'periodGoal' => 2,
                        ],
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'conditions' => [
                            [
                                'attribute' => 'transaction.grossValue',
                                'operator' => 'is_greater_or_equal',
                                'data' => '50',
                            ],
                        ],
                    ],
                ],
                'limit' => [
                    'value' => 6,
                    'interval' => [
                        'value' => 2,
                        'type' => 'calendarYears',
                    ],
                ],
            ],
        ];

        $editedAchievementDataWithoutTranslations = [
            'achievement' => [
                'active' => 1,
                'translations' => [
                    'en' => [
                    ],
                ],
                'activity' => [
                    'operator' => 'is_month_of_year',
                    'data' => [
                        'January',
                        'June',
                        'July',
                        'August',
                    ],
                ],
                'limit' => [
                    'value' => 5,
                    'interval' => [
                        'value' => 2,
                        'type' => 'calendarYears',
                    ],
                ],
            ],
        ];

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement',
            $achievementData
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];

        $this->client->request(
            'PATCH',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement/'.$achievementId,
            $editedAchievementDataWithoutTranslations
        );
        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $this->assertEquals('Validation failed', $data['message']);
        $this->assertCount(1, $data['errors']);
        $this->assertEquals('translations', $data['errors'][0]['path']);
    }

    /**
     * @test
     */
    public function it_return_correct_message_when_uuid_is_invalid(): void
    {
        $data = [
            'achievement' => [
                'active' => 1,
                'translations' => [
                    'en' => [
                    ],
                ],
                'activity' => [
                    'operator' => 'is_month_of_year',
                    'data' => [
                        'January',
                        'June',
                        'July',
                        'August',
                    ],
                ],
                'limit' => [
                    'value' => 5,
                    'interval' => [
                        'value' => 2,
                        'type' => 'calendarYears',
                    ],
                ],
            ],
        ];

        $invalidUUID = '00000011-0000-474c-1111-32b0dd880c';
        $this->client->request(
            'PATCH',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement/'.$invalidUUID.'',
            $data
        );
        $response = $this->client->getResponse();
        $this->assertNotFoundResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertEquals('Not Found', $data['message']);
    }

    /**
     * @test
     */
    public function it_edits_rule_name_and_description_by_patch(): void
    {
        $tenantCode = 'it_edit_rule_name_and_description_by_patch';
        $this->createTenant($tenantCode);

        $response = $this->createAchievement('first achievement', true, $tenantCode);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];
        $achievement = $this->getAchievement($tenantCode, $achievementId);
        $achievementRuleId = $achievement['rules'][0]['achievementRuleId'];

        $dataToUpdate = [
            'achievement' => [
                'active' => 0,
                'rules' => [
                    [
                        'achievementRuleId' => $achievementRuleId,
                        'translations' => [
                            'en' => [
                                'description' => 'Rule Description 2',
                                'name' => 'RULE NAME 2',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->client->request(
            'PATCH',
            '/api/'.$tenantCode.'/achievement/'.$achievementId.'',
            $dataToUpdate
        );
        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $achievement = $this->getAchievement($tenantCode, $achievementId);

        $this->assertFalse($achievement['active']);
        $this->assertEquals($achievementRuleId, $achievement['rules'][0]['achievementRuleId']);
        $this->assertEquals('RULE NAME 2', $achievement['rules'][0]['translations']['en']['name']);
        $this->assertEquals('Rule Description 2', $achievement['rules'][0]['translations']['en']['description']);
    }

    /**
     * @test
     */
    public function it_does_not_allow_you_to_edit_the_same_role_twice(): void
    {
        $tenantCode = 'it_does_not_allow_you_to_edit_the_same_role_twice';
        $this->createTenant($tenantCode);

        $response = $this->createAchievement('first achievement', true, $tenantCode);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievementId = $data['achievementId'];
        $achievement = $this->getAchievement($tenantCode, $achievementId);
        $achievementRuleId = $achievement['rules'][0]['achievementRuleId'];

        $dataToUpdate = [
            'achievement' => [
                'active' => 0,
                'rules' => [
                    [
                        'achievementRuleId' => $achievementRuleId,
                        'translations' => [
                            'en' => [
                                'description' => 'Rule Description 2',
                                'name' => 'RULE NAME 2',
                            ],
                        ],
                    ],
                    [
                        'achievementRuleId' => $achievementRuleId,
                        'translations' => [
                            'en' => [
                                'description' => 'Another Rule Description 2',
                                'name' => 'Another RULE NAME 2',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->client->request(
            'PATCH',
            '/api/'.$tenantCode.'/achievement/'.$achievementId.'',
            $dataToUpdate
        );
        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $this->assertEquals('Validation failed', $data['message']);
        $this->assertCount(1, $data['errors']);
        $this->assertEquals('rules', $data['errors'][0]['path']);
    }

    /**
     * @test
     */
    public function it_checks_if_rule_id_belongs_to_achievement(): void
    {
        $tenantCode = 'it_checks_if_rule_id_belongs_to_achievement';
        $this->createTenant($tenantCode);

        $response = $this->createAchievement('first achievement', true, $tenantCode);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievement1Id = $data['achievementId'];
        $achievement1 = $this->getAchievement($tenantCode, $achievement1Id);
        $achievement1RuleId = $achievement1['rules'][0]['achievementRuleId'];

        $response = $this->createAchievement('second achievement', true, $tenantCode);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievement2Id = $data['achievementId'];

        $dataToUpdate = [
            'achievement' => [
                'active' => 0,
                'rules' => [
                    [
                        'achievementRuleId' => $achievement1RuleId,
                        'translations' => [
                            'en' => [
                                'description' => 'Rule Description 2',
                                'name' => 'RULE NAME 2',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->client->request(
            'PATCH',
            '/api/'.$tenantCode.'/achievement/'.$achievement2Id.'',
            $dataToUpdate
        );
        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $this->assertEquals('Validation failed', $data['message']);
        $this->assertCount(1, $data['errors']);
        $this->assertEquals('rules.0.achievementRuleId', $data['errors'][0]['path']);
        $this->assertEquals('Provided achievement rule is not from provided achievement.', $data['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_checks_invalid_rule_id(): void
    {
        $tenantCode = 'it_checks_invalid_rule_id';
        $this->createTenant($tenantCode);

        $response = $this->createAchievement('first achievement', true, $tenantCode);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $achievement1Id = $data['achievementId'];
        $achievement1 = $this->getAchievement($tenantCode, $achievement1Id);
        $achievement1RuleId = $achievement1['rules'][0]['achievementRuleId'];

        $dataToUpdate = [
            'achievement' => [
                'active' => 0,
                'rules' => [
                    [
                        'achievementRuleId' => '00000000-0000-474c-b092-b0dd880c07aa',
                        'translations' => [
                            'en' => [
                                'description' => 'Rule Description 2',
                                'name' => 'RULE NAME 2',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->client->request(
            'PATCH',
            '/api/'.$tenantCode.'/achievement/'.$achievement1Id.'',
            $dataToUpdate
        );
        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $this->assertEquals('Validation failed', $data['message']);
        $this->assertCount(1, $data['errors']);
        $this->assertEquals('rules.0.achievementRuleId', $data['errors'][0]['path']);
        $this->assertEquals('Provided achievement rule is not from provided achievement.', $data['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_update_rule_conditions_without_resetting_achievement_progress(): void
    {
        $storeCode = 'update_achievement_tenant';
        $this->createTenant($storeCode);
        $achievement = $this->createAchievement('Achievement to update rule conditions', true, $storeCode);
        $achievementId = json_decode($achievement->getContent(), true)['achievementId'];
        $achievementResponse = $this->getAchievement($storeCode, $achievementId);
        $achievementRuleId = $achievementResponse['rules'][0]['achievementRuleId'];
        $this->createCampaign($achievementId);
        $customerEmail = '<EMAIL>';
        $memberResponse = $this->createMember($storeCode, $customerEmail);
        $customerId = json_decode($memberResponse->getContent(), true)['customerId'];
        $this->createTransaction($storeCode, $customerEmail, 20);

        $dataToUpdate = [
            'achievement' => [
                'rules' => [
                    [
                        'achievementRuleId' => $achievementRuleId,
                        'conditions' => [
                            [
                                'attribute' => 'transaction.grossValue',
                                'operator' => 'is_greater_or_equal',
                                'data' => '30',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->client->request(
            'PATCH',
            '/api/'.$storeCode.'/achievement/'.$achievementId,
            $dataToUpdate
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $achievementResponse = $this->getAchievement($storeCode, $achievementId);
        $this->assertSame('transaction.grossValue', $achievementResponse['rules'][0]['conditions'][0]['attribute']);
        $this->assertSame('is_greater_or_equal', $achievementResponse['rules'][0]['conditions'][0]['operator']);
        $this->assertSame(30, $achievementResponse['rules'][0]['conditions'][0]['data']);

        $progress = $this->getAchievementProgressForMember($achievementId, $customerId, $storeCode);
        // progress should not be reset
        $this->assertSame(20.0, $progress['memberProgress']['rules'][0]['currentPeriodValue']);
    }

    /**
     * @test
     */
    public function it_allows_to_update_achievement_in_global_limit(): void
    {
        $_ENV['GLOBAL_ACTIVE_ACHIEVEMENT_LIMIT'] = 2;

        $this->deleteAllAchievements();

        $firstTenantCode = 'FIRST_TENANT';
        $this->createTenant($firstTenantCode);

        $response = $this->createAchievement('first achievement', true, $firstTenantCode);
        $this->assertOkResponseStatus($response);

        $response = $this->createAchievement('second achievement', true, $firstTenantCode);
        $this->assertOkResponseStatus($response);

        $achievementId = json_decode($response->getContent(), true)['achievementId'];

        $dataToUpdate = [
            'achievement' => [
                'active' => true,
                'translations' => [
                    'en' => [
                        'name' => 'New name',
                        'description' => 'achievement description',
                    ],
                ],
                'activity' => [
                    'operator' => 'is_day_of_week',
                    'data' => [
                        'Monday',
                        'Tuesday',
                        'Friday',
                    ],
                ],
                'rules' => [],
                'limit' => [
                    'value' => 1,
                    'interval' => [
                        'value' => 2,
                        'type' => 'calendarYears',
                    ],
                ],
            ],
        ];
        $this->client->request(
            'PATCH',
            '/api/'.$firstTenantCode.'/achievement/'.$achievementId.'',
            $dataToUpdate
        );
        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $achievement = $this->getAchievement($firstTenantCode, $achievementId);
        $this->assertTrue($achievement['active']);
        $this->assertEquals($achievementId, $achievement['achievementId']);
        $this->assertEquals('New name', $achievement['translations']['en']['name']);
    }

    private function createAchievement(string $name, bool $isActive, string $storeCode): Response
    {
        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/achievement',
            [
                'achievement' => [
                    'active' => $isActive,
                    'translations' => [
                        'en' => [
                            'name' => $name,
                            'description' => 'achievement description',
                        ],
                    ],
                    'activity' => [
                        'operator' => 'is_day_of_week',
                        'data' => [
                            'Monday',
                            'Tuesday',
                            'Friday',
                        ],
                    ],
                    'rules' => [
                        [
                            'type' => 'direct',
                            'trigger' => 'transaction',
                            'completeRule' => [
                                'period' => [
                                    'type' => 'overall',
                                ],
                                'periodGoal' => 30,
                            ],
                            'aggregation' => [
                                'type' => 'expression',
                                'rule' => 'transaction.grossValue',
                            ],
                            'conditions' => [
                                [
                                    'attribute' => 'transaction.grossValue',
                                    'operator' => 'is_greater_or_equal',
                                    'data' => '10',
                                ],
                            ],
                            'translations' => [
                                'en' => [
                                    'name' => 'Rule name',
                                    'description' => 'Rule description',
                                ],
                            ],
                        ],
                    ],
                    'limit' => [
                        'value' => 1,
                        'interval' => [
                            'value' => 2,
                            'type' => 'calendarYears',
                        ],
                    ],
                ],
            ]
        );

        return $this->client->getResponse();
    }

    private function createCampaign(string $achievementId): Response
    {
        $campaignData = [
            'campaign' => [
                'translations' => [
                    'en' => [
                        'name' => 'campaign name',
                    ],
                ],
                'active' => true,
                'type' => 'direct',
                'trigger' => 'achievement',
                'achievementId' => $achievementId,
                'activity' => [
                    'startsAt' => '2022-01-01 00:00:00',
                ],
                'rules' => [
                    [
                        'effects' => [
                            [
                                'effect' => 'give_points',
                                'pointsRule' => 10,
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign',
            $campaignData
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        return $response;
    }

    private function createMember(string $storeCode, string $email): Response
    {
        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/member',
            [
                'customer' => [
                    'firstName' => 'Chris',
                    'lastName' => 'Smith',
                    'email' => $email,
                    'agreement1' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        return $response;
    }

    private function createTransaction(string $storeCode, string $customerEmail, int $grossValue): Response
    {
        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/transaction',
            [
                'transaction' => [
                    'items' => [
                        [
                            'sku' => 'A/1',
                            'name' => 'Achievement item 1',
                            'quantity' => 1,
                            'grossValue' => $grossValue,
                            'category' => 'Achievement',
                        ],
                    ],
                    'header' => [
                        'documentType' => 'sell',
                        'documentNumber' => 'Achievement/reset/test/1',
                        'purchasedAt' => '2022-07-01 10:00:00',
                    ],
                    'customerData' => [
                        'email' => $customerEmail,
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        return $response;
    }

    private function getAchievementProgressForMember(string $achievementId, string $customerId, string $storeCode): array
    {
        $this->client->request(
            'GET',
            sprintf('/api/%s/member/%s/achievement', $storeCode, $customerId),
            ['achievementId' => $achievementId]
        );

        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('items', $data);
        $this->assertNotEmpty($data['items']);

        return reset($data['items']);
    }

    private function deleteAllAchievements(): void
    {
        $entityManager = self::$kernel->getContainer()->get('doctrine.orm.default_entity_manager');
        $query = $entityManager->createQuery('DELETE FROM OpenLoyalty\Achievement\Domain\Entity\Achievement a');
        $query->execute();
    }
}
