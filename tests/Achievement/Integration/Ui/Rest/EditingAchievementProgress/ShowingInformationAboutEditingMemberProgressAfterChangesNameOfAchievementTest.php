<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Integration\Ui\Rest\EditingAchievementProgress;

use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class ShowingInformationAboutEditingMemberProgressAfterChangesNameOfAchievementTest extends AbstractApiTest
{
    use MemberApiTrait;
    private HttpKernelBrowser $client;

    private string $customerId;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();

        $createMemberResponse = $this->postMember(httpClient: $this->client);
        $this->assertOkResponseStatus($createMemberResponse);
        $data = json_decode($createMemberResponse->getContent(), true);
        $this->customerId = $data['customerId'];
    }

    /**
     * @test
     */
    public function it_shows_manual_changed_completion_count_after_change_achievement_name(): void
    {
        $createAchievementResponse = $this->createAchievement(globalLimit: 5);
        $achievementId = json_decode($createAchievementResponse->getContent(), true)['achievementId'];

        $achievementProgressForMember = $this->getAchievementProgressForMember($achievementId, $this->customerId);

        $this->editAchievementProgress(
            $this->customerId,
            $achievementId,
            3,
            [
                [
                    'achievementRuleId' => $achievementProgressForMember['memberProgress']['rules'][0]['achievementRuleId'],
                    'currentPeriodValue' => 0.0,
                ],
            ]
        );

        $this->client->request(
            'PATCH',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement/'.$achievementId,
            [
                'achievement' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Achievement - new name',
                            'description' => 'description',
                        ],
                    ],
                ],
            ]
        );

        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement/'.$achievementId,
        );

        $this->assertArrayHasKey(
            'editedByAdminAt',
            $this->getAchievementProgressForMember($achievementId, $this->customerId)
        );
    }

    /**
     * @test
     */
    public function it_shows_manual_changed_rule_progress_after_change_achievement_name(): void
    {
        $createAchievementResponse = $this->createAchievement(globalLimit: 5);
        $achievementId = json_decode($createAchievementResponse->getContent(), true)['achievementId'];

        $achievementProgressForMember = $this->getAchievementProgressForMember($achievementId, $this->customerId);

        $this->editAchievementProgress(
            $this->customerId,
            $achievementId,
            0,
            [
                [
                    'achievementRuleId' => $achievementProgressForMember['memberProgress']['rules'][0]['achievementRuleId'],
                    'currentPeriodValue' => 2.0,
                ],
            ]
        );

        $this->client->request(
            'PATCH',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement/'.$achievementId,
            [
                'achievement' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Achievement - new name',
                            'description' => 'description',
                        ],
                    ],
                ],
            ]
        );

        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement/'.$achievementId,
        );

        $this->assertArrayHasKey(
            'editedByAdminAt',
            $this->getAchievementProgressForMember($achievementId, $this->customerId)
        );
    }

    private function createAchievement(?string $limitType = 'calendarYears', ?int $limitForPeriodValue = 2, int $globalLimit = 3, bool $isActive = true): Response
    {
        $achievementData = [
            'achievement' => [
                'active' => $isActive,
                'translations' => [
                    'en' => [
                        'name' => 'Member achievement progress test',
                        'description' => 'Description Member achievement progress test',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'transaction',
                        'completeRule' => [
                            'period' => [
                                'type' => 'overall',
                            ],
                            'periodGoal' => 3,
                        ],
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                    ],
                ],
                'limit' => [
                    'value' => $globalLimit,
                    'interval' => [
                        'value' => $limitForPeriodValue,
                        'type' => $limitType,
                    ],
                ],
            ],
        ];

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/achievement',
            $achievementData
        );

        return $this->client->getResponse();
    }

    private function editAchievementProgress(string $memberId, string $achievementId, ?int $completedCount, ?array $progressRules): Response
    {
        $editProgressData = [
            'memberProgress' => [
                'completedCount' => $completedCount,
                'rules' => $progressRules,
            ],
        ];

        if (null === $progressRules) {
            $editProgressData = [
                'memberProgress' => [
                    'completedCount' => $completedCount,
                ],
            ];
        }

        $uri = sprintf(
            '/api/%s/member/%s/achievement/%s/progress',
            LoadSettingsData::DEFAULT_STORE_CODE,
            $memberId,
            $achievementId
        );

        $this->client->request(
            Request::METHOD_PATCH,
            $uri,
            $editProgressData
        );

        return $this->client->getResponse();
    }

    private function getAchievementProgressForMember(string $achievementId, string $customerId): array
    {
        $this->client->request(
            'GET',
            sprintf('/api/%s/member/%s/achievement', LoadSettingsData::DEFAULT_STORE_CODE, $customerId),
            ['achievementId' => $achievementId]
        );

        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('items', $data);
        $this->assertNotEmpty($data['items']);

        return reset($data['items']);
    }
}
