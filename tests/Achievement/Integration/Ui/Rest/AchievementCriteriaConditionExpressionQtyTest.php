<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Integration\Ui\Rest;

use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use OpenLoyalty\Test\Achievement\Integration\Traits\AchievementApiTrait;
use OpenLoyalty\Test\Campaign\Integration\Traits\CampaignApiTrait;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Transaction\Integration\Traits\TransactionApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;

final class AchievementCriteriaConditionExpressionQtyTest extends AbstractApiTest
{
    use CampaignApiTrait;
    use MemberApiTrait;
    use TransactionApiTrait;
    use AchievementApiTrait;

    /**
     * @test
     */
    public function it_test_achievement_high_precision_quantity_items_criteria_condition_for_expression(): void
    {
        $client = self::createAuthenticatedClient();
        $tenantCode = 'achievement_tenant_code_expression';
        $this->createTenant($tenantCode);

        $achievementRulesData = [
            [
                'type' => 'direct',
                'trigger' => 'transaction',
                'completeRule' => [
                    'period' => [
                        'type' => 'overall',
                    ],
                    'periodGoal' => 1,
                ],
                'aggregation' => [
                    'type' => 'quantity',
                ],
                'conditions' => [
                    [
                        'operator' => 'expression',
                        'data' => 'transaction.qty == 4.654',
                    ],
                ],
            ],
        ];

        $achievementResponse = $this->postAchievement(
            $client,
            $tenantCode,
            activity: [
                'operator' => 'is_day_of_week',
                'data' => [
                    'Monday',
                    'Tuesday',
                    'Wednesday',
                    'Thursday',
                    'Friday',
                    'Saturday',
                    'Sunday',
                ],
            ],
            rules: $achievementRulesData
        );

        $data = json_decode($achievementResponse->getContent(), true);
        $achievementId = $data['achievementId'];

        $this->postCampaign(
            $client,
            $tenantCode,
            trigger: Trigger::ACHIEVEMENT,
            activity: [
                'startsAt' => '2018-12-12 00:00',
            ],
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '170',
                        ],
                    ],
                ],
            ],
            achievementId: $achievementId
        );

        $memberResponse = $this->postMember(
            $client,
            $tenantCode,
            '<EMAIL>'
        );

        $this->assertOkResponseStatus($memberResponse);
        $data = json_decode($memberResponse->getContent(), true);
        $customerId = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,

            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Achievement item 1',
                    'highPrecisionQuantity' => 2.666,
                    'grossValue' => 50,
                    'category' => 'Achievement',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => '<EMAIL>',
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $this->assertSame(
            0.0,
            $this->getCustomerActivePoints($customerId, $tenantCode),
        );

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Achievement item 1',
                    'highPrecisionQuantity' => 4.5,
                    'grossValue' => 50,
                    'category' => 'Achievement',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
                [
                    'sku' => 'A/2',
                    'name' => 'Achievement item 2',
                    'highPrecisionQuantity' => 0.154,
                    'grossValue' => 55,
                    'category' => 'Achievement',
                    'maker' => 'brand2',
                    'labels' => [
                        ['key' => 'key2', 'value' => 'value2'],
                    ],
                ],
            ],
            customerData: [
                'email' => '<EMAIL>',
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $this->assertSame(
            170.0,
            $this->getCustomerPointsByType($customerId, 'lockedPoints', $tenantCode),
        );
    }

    /**
     * @test
     */
    public function it_test_achievement_items_criteria_condition_with_expression_for_save_with_high_quantity(): void
    {
        $client = self::createAuthenticatedClient();
        $tenantCode = 'high_precision_quantity_achievement_tenant_code_expression';
        $this->createTenant($tenantCode);

        $achievementRulesData = [
            [
                'type' => 'direct',
                'trigger' => 'transaction',
                'completeRule' => [
                    'period' => [
                        'type' => 'overall',
                    ],
                    'periodGoal' => 1,
                ],
                'aggregation' => [
                    'type' => 'quantity',
                ],
                'conditions' => [
                    [
                        'operator' => 'expression',
                        'data' => 'transaction.qty == 4.894',
                    ],
                ],
            ],
        ];

        $achievementResponse = $this->postAchievement(
            $client,
            $tenantCode,
            activity: [
                'operator' => 'is_day_of_week',
                'data' => [
                    'Monday',
                    'Tuesday',
                    'Wednesday',
                    'Thursday',
                    'Friday',
                    'Saturday',
                    'Sunday',
                ],
            ],
            rules: $achievementRulesData
        );

        $data = json_decode($achievementResponse->getContent(), true);
        $achievementId = $data['achievementId'];

        $this->postCampaign(
            $client,
            $tenantCode,
            trigger: Trigger::ACHIEVEMENT,
            activity: [
                'startsAt' => '2018-12-12 00:00',
            ],
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '45',
                        ],
                    ],
                ],
            ],
            achievementId: $achievementId
        );

        $memberResponse = $this->postMember(
            $client,
            $tenantCode,
            '<EMAIL>'
        );

        $this->assertOkResponseStatus($memberResponse);
        $data = json_decode($memberResponse->getContent(), true);
        $customerId = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,

            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Achievement item 1',
                    'highPrecisionQuantity' => 2.255,
                    'grossValue' => 50,
                    'category' => 'Achievement',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => '<EMAIL>',
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $this->assertSame(
            0.0,
            $this->getCustomerActivePoints($customerId, $tenantCode),
        );

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Achievement item 1',
                    'highPrecisionQuantity' => 2.895,
                    'grossValue' => 50,
                    'category' => 'Achievement',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
                [
                    'sku' => 'A/2',
                    'name' => 'Achievement item 2',
                    'highPrecisionQuantity' => 1.999,
                    'grossValue' => 55,
                    'category' => 'Achievement',
                    'maker' => 'brand2',
                    'labels' => [
                        ['key' => 'key2', 'value' => 'value2'],
                    ],
                ],
            ],
            customerData: [
                'email' => '<EMAIL>',
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $this->assertSame(
            45.0,
            $this->getCustomerPointsByType($customerId, 'lockedPoints', $tenantCode),
        );
    }

    /**
     * @test
     */
    public function it_test_high_quantity_items_achievement_with_expression_for_save_with_quantity(): void
    {
        $client = self::createAuthenticatedClient();
        $tenantCode = 'high_precision_quantity_tenant_code_expression';
        $this->createTenant($tenantCode);

        $achievementRulesData = [
            [
                'type' => 'direct',
                'trigger' => 'transaction',
                'completeRule' => [
                    'period' => [
                        'type' => 'overall',
                    ],
                    'periodGoal' => 1,
                ],
                'aggregation' => [
                    'type' => 'quantity',
                ],
                'conditions' => [
                    [
                        'operator' => 'expression',
                        'data' => 'transaction.qty == 5',
                    ],
                ],
            ],
        ];

        $achievementResponse = $this->postAchievement(
            $client,
            $tenantCode,
            activity: [
                'operator' => 'is_day_of_week',
                'data' => [
                    'Monday',
                    'Tuesday',
                    'Wednesday',
                    'Thursday',
                    'Friday',
                    'Saturday',
                    'Sunday',
                ],
            ],
            rules: $achievementRulesData
        );

        $data = json_decode($achievementResponse->getContent(), true);
        $achievementId = $data['achievementId'];

        $this->postCampaign(
            $client,
            $tenantCode,
            trigger: Trigger::ACHIEVEMENT,
            activity: [
                'startsAt' => '2018-12-12 00:00',
            ],
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '77',
                        ],
                    ],
                ],
            ],
            achievementId: $achievementId,
        );

        $memberResponse = $this->postMember(
            $client,
            $tenantCode,
            '<EMAIL>'
        );

        $this->assertOkResponseStatus($memberResponse);
        $data = json_decode($memberResponse->getContent(), true);
        $customerId = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,

            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Achievement item 1',
                    'quantity' => 6,
                    'grossValue' => 50,
                    'category' => 'Achievement',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => '<EMAIL>',
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $this->assertSame(
            0.0,
            $this->getCustomerActivePoints($customerId, $tenantCode),
        );

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Achievement item 1',
                    'quantity' => 4,
                    'grossValue' => 50,
                    'category' => 'Achievement',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
                [
                    'sku' => 'A/2',
                    'name' => 'Achievement item 2',
                    'quantity' => 1,
                    'grossValue' => 55,
                    'category' => 'Achievement',
                    'maker' => 'brand2',
                    'labels' => [
                        ['key' => 'key2', 'value' => 'value2'],
                    ],
                ],
            ],
            customerData: [
                'email' => '<EMAIL>',
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $this->assertSame(
            77.0,
            $this->getCustomerPointsByType($customerId, 'lockedPoints', $tenantCode),
        );
    }
}
