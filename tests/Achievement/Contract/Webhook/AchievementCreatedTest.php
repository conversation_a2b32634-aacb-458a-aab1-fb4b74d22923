<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Achievement\Contract\Webhook;

use OpenLoyalty\Test\Common\Integration\AbstractWebhookApiTest;
use OpenLoyalty\Test\Utils\Service\WebhookDetails;

final class AchievementCreatedTest extends AbstractWebhookApiTest
{
    /**
     * @test
     */
    public function it_checks_achievement_created_webhook(): void
    {
        $tenantCode = 'achievement_created';
        $this->createTenant($tenantCode);
        $this->createWebhook($tenantCode, 'AchievementCreated');

        //trigger
        $achievementId = $this->createAchievement($tenantCode);

        /** @var WebhookDetails[] $webhooks */
        $webhooks = $this->webhookClientMock->getSentWebhooks();
        $this->assertSame(1, $this->webhookClientMock->getWebhooksCount());
        $this->assertSame('AchievementCreated', $webhooks[0]->getWebhookName());
        $this->assertSameJson($this->getAchievementCreatedWebhookJsonBody($achievementId), $webhooks[0]->getBodyJson());
        $this->assertWebhookHeaders($webhooks[0]->getHeaders());
    }

    private function createAchievement(
        string $tenantCode
    ): string {
        $achievementData = [
            'achievement' => [
                'active' => true,
                'activity' => [
                    'data' => '2024-05-01 00:00',
                    'operator' => 'is_after',
                ],
                'limit' => [
                    'interval' => [
                        'type' => 'calendarYears',
                        'value' => 1,
                    ],
                    'value' => 20,
                ],
                'rules' => [
                    [
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'completeRule' => [
                            'period' => [
                                'type' => 'overall',
                            ],
                            'periodGoal' => 20.50,
                        ],
                        'conditions' => [
                            [
                                'operator' => 'is_greater',
                                'data' => 1.0,
                                'attribute' => 'transaction.qty',
                            ],
                        ],
                        'limit' => [
                            'interval' => [
                                'type' => 'calendarYears',
                                'value' => 1,
                            ],
                            'value' => 10,
                        ],
                        'trigger' => 'transaction',
                        'type' => 'direct',
                        'translations' => [
                            'en' => [
                                'name' => 'First rule',
                                'description' => 'rule 1',
                            ],
                        ],
                    ],
                    [
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'completeRule' => [
                            'period' => [
                                'consecutive' => 1,
                                'type' => 'day',
                            ],
                            'periodGoal' => 1,
                        ],
                        'conditions' => [
                            [
                                'operator' => 'is_equal',
                                'data' => 1,
                                'attribute' => 'transaction.qty',
                            ],
                        ],
                        'trigger' => 'transaction',
                        'type' => 'direct',
                        'translations' => [
                            'en' => [
                                'name' => 'Second rule',
                                'description' => 'rule 2',
                            ],
                        ],
                    ],
                ],
                'translations' => [
                    'en' => [
                        'name' => 'First achievement',
                        'description' => 'first',
                    ],
                    'es' => [
                        'name' => 'Primer logro',
                        'description' => 'primer',
                    ],
                ],
            ],
        ];

        $this->client->request('POST', '/api/'.$tenantCode.'/achievement', $achievementData);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        return $data['achievementId'];
    }

    private function getAchievementCreatedWebhookJsonBody(string $achievementId): string
    {
        return <<<EOT
        {
            "eventName": "AchievementCreated",
            "messageId": "@uuid@",
            "storeCode": "achievement_created",
            "createdAt":"@datetime@",
            "data": {
                "achievement": {
                    "achievementId": "$achievementId",
                    "active": true,
                    "activity": {
                        "operator": "is_after",
                        "data": "2024-05-01T00:00:00+02:00"
                    },
                    "limit": {
                        "interval": {
                            "type": "calendarYears",
                            "value": 1
                        },
                        "value": 20
                    },
                    "translations": {
                        "en": {
                            "name": "First achievement",
                            "description": "first"
                        },
                        "es": {
                            "name": "Primer logro",
                            "description": "primer"
                        }
                    },
                    "rules": [
                        {
                            "trigger": "transaction",
                            "type": "direct",
                            "completeRule": {
                                "periodGoal": 20.50,
                                "period": {
                                "type": "overall"
                                }
                            },
                            "conditions": [
                                {
                                    "operator": "is_greater",
                                    "attribute": "transaction.qty",
                                    "data": 1.0
                                }
                            ],
                            "aggregation": {
                                "type": "quantity"
                            },
                            "limit": {
                                "interval": {
                                    "type": "calendarYears",
                                    "value": 1
                                },
                                "value": 10
                            },
                            "translations": {
                                "en": {
                                    "name": "First rule",
                                    "description": "rule 1"
                                }
                            }
                        },
                        {
                            "trigger": "transaction",
                            "type": "direct",
                            "completeRule": {
                                "periodGoal": "expr(value == 1)",
                                "period": {
                                    "type": "day",
                                    "consecutive": 1
                                }
                            },
                            "conditions": [
                                {
                                    "operator": "is_equal",
                                    "attribute": "transaction.qty",
                                    "data": 1.0
                                }
                            ],
                            "aggregation": {
                                "type": "quantity"
                            },
                            "translations": {
                                "en": {
                                    "name": "Second rule",
                                    "description": "rule 2"
                                }
                            }
                        }
                    ]
                }
            },
            "requestId": "@uuid@"
        }
        EOT;
    }
}
