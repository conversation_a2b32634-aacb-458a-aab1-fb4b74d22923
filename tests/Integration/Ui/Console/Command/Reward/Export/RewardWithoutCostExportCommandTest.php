<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Integration\Ui\Console\Command\Reward\Export;

use DateTime;
use OpenLoyalty\Application;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\RewardCategoryId;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\CommandBusInterface;
use OpenLoyalty\Core\Domain\Model\Label;
use OpenLoyalty\Reward\Application\Command\CreateReward;
use OpenLoyalty\Reward\Domain\ValueObject\CouponGenerator;
use OpenLoyalty\Reward\Domain\ValueObject\RewardActivity;
use OpenLoyalty\Reward\Domain\ValueObject\RewardTarget;
use OpenLoyalty\Reward\Domain\ValueObject\RewardUsageLimit;
use OpenLoyalty\Reward\Domain\ValueObject\RewardVisibility;
use OpenLoyalty\Reward\Domain\ValueObject\Rounding;
use OpenLoyalty\Reward\Domain\ValueObject\UnitsConversion;
use OpenLoyalty\Test\Common\Integration\AbstractKernelTest;
use Symfony\Component\Console\Tester\CommandTester;
use function file_exists;
use function unlink;

final class RewardWithoutCostExportCommandTest extends AbstractKernelTest
{
    /**
     * @test
     */
    public function it_exports_data_to_csv(): void
    {
        $filePath = 'var/storage/analytics/Rewards/rewards_2022_12_16.csv';

        if (file_exists($filePath)) {
            unlink($filePath);
        }

        $container = self::getContainer();

        $createRewardCommand = new CreateReward(
            new RewardId('bb777635-1b38-4af8-b21e-9dd1fa42c34c'),
            new StoreId('484635af-cc11-48ae-bf19-8afbe5f31fc7'),
            [],
            'conversion_coupon',
            true,
            null,
            [new LevelId('e82c96cf-32a3-43bd-9034-4df343e51111')],
            [],
            new RewardActivity(
                false,
                new DateTime('2020-12-16T00:00:00+01:00'),
                new DateTime('2022-12-24T00:00:00+01:00')
            ),
            new RewardVisibility(),
            0.23,
            11.5,
            50,
            [
                new Label('type', 'promotion'),
                new Label('type', 'cashback'),
            ],
            false,
            true,
            [new RewardCategoryId('00ca7e90-6361-4465-e76f-727900000002')],
            new DateTime('2022-12-16T00:00:00+01:00'),
            new RewardUsageLimit(100, 1),
            RewardTarget::create('level'),
            new UnitsConversion(1.0, Rounding::ROUND_UP)
        );
        $createRewardCommand->setStaticCouponOptions(100.00);
        $createRewardCommand->setCouponOptions(null, null, null, new CouponGenerator(6));

        $commandBus = $container->get(CommandBusInterface::class);
        $commandBus->dispatch($createRewardCommand);

        $application = new Application(self::$kernel);
        $command = $application->find('oloy:rewards:analytics:export');
        $commandTester = new CommandTester($command);
        $commandTester->execute(
            [
                'command' => $command->getName(),
                'date' => '2022-12-16',
            ]
        );

        $expectedHeaders = [
            'rewardId',
            'tenantId',
            'type',
            'name',
            'brandName',
            'levels',
            'segments',
            'categories',
            'tax',
            'active',
            'costInPoints',
            'activity',
            'visibility',
            'taxPriceValue',
            'price',
            'labels',
            'featured',
            'public',
            'usageLimit',
            'fulfillmentTracking',
            'createdAt',
            'pointValue',
            'daysInactive',
            'daysValid',
            'couponGenerator',
            'couponValue',
            'segmentNames',
            'levelNames',
            'categoryNames',
            'usageLeft',
            'dateValid',
        ];

        self::assertFileExists($filePath);

        $filePointer = fopen($filePath, 'rb');
        $headers = fgetcsv($filePointer);
        self::assertSame($expectedHeaders, $headers);

        while (!feof($filePointer)) {
            $row = fgetcsv($filePointer);
            if (false === $row) {
                break;
            }

            if ('bb777635-1b38-4af8-b21e-9dd1fa42c34c' === $row[0]) {
                self::assertSame('bb777635-1b38-4af8-b21e-9dd1fa42c34c', $row[0]);
                self::assertSame('484635af-cc11-48ae-bf19-8afbe5f31fc7', $row[1]);
                self::assertSame('conversion_coupon', $row[2]);
                self::assertSame('', $row[3]);
                self::assertSame('', $row[4]);
                $this->assertSerializedValue(['e82c96cf-32a3-43bd-9034-4df343e51111'], $row[5]);
                $this->assertSerializedValue([], $row[6]);
                $this->assertSerializedValue(['00ca7e90-6361-4465-e76f-727900000002'], $row[7]);
                self::assertSame('23', $row[8]);
                self::assertSame('1', $row[9]);
                self::assertSame('', $row[10]);
                $this->assertSerializedValue(
                    (object) [
                        'allTime' => false,
                        'from' => '2020-12-16T00:00:00+01:00',
                        'to' => '2022-12-24T00:00:00+01:00',
                    ],
                    $row[11]
                );
                $this->assertSerializedValue((object) ['allTime' => true], $row[12]);
                self::assertSame('11.5', $row[13]);
                self::assertSame('50', $row[14]);
                $this->assertSerializedValue(
                    [
                        (object) [
                            'key' => 'type',
                            'value' => 'promotion',
                        ],
                        (object) [
                            'key' => 'type',
                            'value' => 'cashback',
                        ],
                    ],
                    $row[15]
                );
                self::assertSame('', $row[16]);
                self::assertSame('1', $row[17]);
                $this->assertSerializedValue((object) ['general' => 100, 'perUser' => 1], $row[18]);
                self::assertSame('', $row[19]);
                self::assertSame('2022-12-16T00:00:00+01:00', $row[20]);
                self::assertSame('', $row[21]);
                self::assertSame('', $row[22]);
                self::assertSame('', $row[23]);
                $this->assertSerializedValue(
                    (object) [
                        'length' => 6,
                        'characterSet' => 'alphanum',
                    ],
                    $row[24]
                );
                self::assertSame('', $row[25]);
                $this->assertSerializedValue([], $row[26]);
                $this->assertSerializedValue((object) ['e82c96cf-32a3-43bd-9034-4df343e51111' => 'level1'], $row[27]);
                $this->assertSerializedValue((object) ['00ca7e90-6361-4465-e76f-727900000002' => 'RewardcategoryB'], $row[28]);
                self::assertSame('100', $row[29]);
            }
        }

        unlink($filePath);
    }

    private function assertSerializedValue($expectation, $result): void
    {
        $expectation = json_encode($expectation);
        $resultWithoutLineBreaks = preg_replace('~[\r\n]+~', '', $result);
        $resultWithoutWhitespaces = preg_replace('/\s+/', '', $resultWithoutLineBreaks);

        self::assertSame($expectation, $resultWithoutWhitespaces);
    }
}
