<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Integration\Ui\Rest\Controller\Language;

use OpenLoyalty\Integration\Helpers\ResponseChecker;
use OpenLoyalty\Integration\Traits\TiersModeTrait;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use Symfony\Component\HttpKernel\HttpKernelBrowser as Client;

final class LocaleTest extends AbstractApiTest
{
    use TiersModeTrait;
    private const NAME = ['en' => 'English', 'es' => 'Spanish'];
    private const ORDER = ['en' => 0, 'es' => 1];
    private const ADMIN_DEFAULT = ['en' => 1, 'es' => 0];

    protected function getClientConfigurationDefaultLocale(string $defaultLocale): Client
    {
        self::ensureKernelShutdown();
        $client = self::createAuthenticatedClient();

        $client->request(
            'PUT',
            sprintf('/api/language/%s', $defaultLocale),
            [
                'name' => self::NAME[$defaultLocale],
                'code' => $defaultLocale,
                'adminDefault' => self::ADMIN_DEFAULT[$defaultLocale],
                'apiDefault' => 1,
                'order' => self::ORDER[$defaultLocale],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        return $client;
    }

    /**
     * @test
     *
     * @dataProvider localeValidationProvider
     */
    public function it_returns_correct_validation_translations_by_locale(string $defaultLocale, ?string $locale, string $expectedString): void
    {
        $client = $this->getClientConfigurationDefaultLocale($defaultLocale);

        $entityManager = self::getContainer()->get('doctrine.orm.entity_manager');
        $this->updateTiersModeToTraditional($entityManager, LoadSettingsData::DEFAULT_STORE_ID);

        $uri = '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/tier/create';
        if ($locale) {
            $uri .= '?_locale='.$locale;
        }

        $client->request(
            'POST',
            $uri,
            [
                'level' => [
                    'conditionValue' => null,
                ],
            ]
        );

        $this->assertBadRequestResponseStatus($client->getResponse());
        $content = json_decode($client->getResponse()->getContent(), true);
        ResponseChecker::assertHasError($content, sprintf('translations.%s.name', $defaultLocale), $expectedString);
    }

    /**
     * @dataProvider
     */
    public function localeValidationProvider(): array
    {
        return [
            ['es', null, 'Este valor no debería estar vacío.'],
            ['en', null, 'This value should not be blank.'],
            ['en', 'en', 'This value should not be blank.'],
            ['en', 'es', 'Este valor no debería estar vacío.'],
        ];
    }

    protected function createLevelWithTranslations(Client $client, array $translations, int $increment = 0): string
    {
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/tier/create',
            [
                'level' => [
                    'active' => 0,
                    'conditionValue' => 9999 + $increment,
                    'translations' => $translations,
                    'rewards' => [],
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('levelId', $data);

        return $data['levelId'];
    }

    /**
     * @test
     */
    public function it_persists_translations(): void
    {
        $entityManager = self::getContainer()->get('doctrine.orm.entity_manager');
        $this->updateTiersModeToTraditional($entityManager, LoadSettingsData::DEFAULT_STORE_ID);

        $client = $this->getClientConfigurationDefaultLocale('en');
        $levelId = $this->createLevelWithTranslations($client, [
            'en' => [
                'name' => 'NAME_EN',
                'description' => 'DESC_EN',
            ],
            'es' => [
                'name' => 'NAME_ES',
                'description' => 'DESC_ES',
            ],
        ]);

        $client->request(
            'GET',
            sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/tier/%s', $levelId)
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertArrayHasKey('name', $data);
        $this->assertSame('NAME_EN', $data['name']);

        $this->assertArrayHasKey('description', $data);
        $this->assertSame('DESC_EN', $data['description']);
    }

    /**
     * @test
     */
    public function it_retrieves_translations_by_using_default_locale(): void
    {
        $entityManager = self::getContainer()->get('doctrine.orm.entity_manager');
        $this->updateTiersModeToTraditional($entityManager, LoadSettingsData::DEFAULT_STORE_ID);

        $client = $this->getClientConfigurationDefaultLocale('en');
        $levelId = $this->createLevelWithTranslations($client, [
            'en' => [
                'name' => 'NAME_EN',
                'description' => 'DESC_EN',
            ],
            'es' => [
                'name' => 'NAME_ES',
                'description' => 'DESC_ES',
            ],
        ], 10);
        $client = $this->getClientConfigurationDefaultLocale('es');
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/tier/'.$levelId,
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertArrayHasKey('name', $data);
        $this->assertSame('NAME_ES', $data['name']);

        $this->assertArrayHasKey('description', $data);
        $this->assertSame('DESC_ES', $data['description']);
    }

    /**
     * @test
     */
    public function it_retrieves_translations_by_given_locale(): void
    {
        $entityManager = self::getContainer()->get('doctrine.orm.entity_manager');
        $this->updateTiersModeToTraditional($entityManager, LoadSettingsData::DEFAULT_STORE_ID);

        $client = $this->getClientConfigurationDefaultLocale('en');
        $levelId = $this->createLevelWithTranslations($client, [
            'en' => [
                'name' => 'NAME_EN',
                'description' => 'DESC_EN',
            ],
            'es' => [
                'name' => 'NAME_ES',
                'description' => 'DESC_ES',
            ],
        ], 20);

        $client = $this->getClientConfigurationDefaultLocale('en');
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/tier/'.$levelId.'?_locale=es',
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertArrayHasKey('name', $data);
        $this->assertSame('NAME_ES', $data['name']);

        $this->assertArrayHasKey('description', $data);
        $this->assertSame('DESC_ES', $data['description']);
    }

    /**
     * @test
     */
    public function it_retrieves_on_field_fallback_translations_by_given_locale(): void
    {
        $entityManager = self::getContainer()->get('doctrine.orm.entity_manager');
        $this->updateTiersModeToTraditional($entityManager, LoadSettingsData::DEFAULT_STORE_ID);

        $client = $this->getClientConfigurationDefaultLocale('en');
        $levelId = $this->createLevelWithTranslations($client, [
            'en' => [
                'name' => 'NAME_EN',
                'description' => 'DESC_EN',
            ],
            'es' => [
                'name' => '',
                'description' => 'DESC_ES',
            ],
        ], 30);

        $client = $this->getClientConfigurationDefaultLocale('en');
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/tier/'.$levelId.'?_locale=es',
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertArrayHasKey('name', $data);
        $this->assertSame('NAME_EN', $data['name']);

        $this->assertArrayHasKey('description', $data);
        $this->assertSame('DESC_ES', $data['description']);
    }

    /**
     * @test
     */
    public function it_retrieves_all_field_fallback_translations_by_given_locale(): void
    {
        $entityManager = self::getContainer()->get('doctrine.orm.entity_manager');
        $this->updateTiersModeToTraditional($entityManager, LoadSettingsData::DEFAULT_STORE_ID);

        $client = $this->getClientConfigurationDefaultLocale('en');
        $levelId = $this->createLevelWithTranslations($client, [
            'en' => [
                'name' => 'NAME_EN',
                'description' => 'DESC_EN',
            ],
        ], 40);

        $client = $this->getClientConfigurationDefaultLocale('en');
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/tier/'.$levelId.'?_locale=es',
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertArrayHasKey('name', $data);
        $this->assertSame('NAME_EN', $data['name']);

        $this->assertArrayHasKey('description', $data);
        $this->assertSame('DESC_EN', $data['description']);
    }
}
