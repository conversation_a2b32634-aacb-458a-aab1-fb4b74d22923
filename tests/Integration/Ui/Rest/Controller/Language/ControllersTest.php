<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Integration\Ui\Rest\Controller\Language;

use OpenLoyalty\Integration\Helpers\ResponseChecker;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\User\Infrastructure\DataFixtures\ORM\LoadUserData;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

/**
 * @covers \OpenLoyalty\Ui\Rest\Controller\Language\Put
 * @covers \OpenLoyalty\Ui\Rest\Controller\Language\Get
 * @covers \OpenLoyalty\Ui\Rest\Controller\Language\Post
 * @covers \OpenLoyalty\Ui\Rest\Controller\Language\GetList
 * @covers \OpenLoyalty\Ui\Rest\Controller\Language\Delete
 */
final class ControllersTest extends AbstractApiTest
{
    protected HttpKernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_creates_a_new_language(): void
    {
        $languageData = [
            'name' => 'German',
            'code' => 'de',
            'order' => 0,
            'adminDefault' => 0,
            'apiDefault' => 0,
        ];

        $this->client->request(
            'POST',
            '/api/language',
            $languageData,
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($this->client->getResponse());
        $responseData = json_decode($response->getContent(), true);
        $this->assertSame('de', $responseData['code']);
    }

    /**
     * @dataProvider invalidLanguageCodeProvider
     * @test
     */
    public function it_validates_language_code_for_special_characters(string $invalidCode, string $expectedMessage): void
    {
        $languageData = [
            'name' => 'Invalid Language',
            'code' => $invalidCode,
            'order' => 3,
            'adminDefault' => 0,
            'apiDefault' => 0,
        ];

        $this->client->request(
            'POST',
            '/api/language',
            $languageData,
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('errors', $responseData);
        ResponseChecker::assertHasError($responseData, 'code', $expectedMessage);
    }

    public function invalidLanguageCodeProvider(): array
    {
        return [
            'path_traversal_attempt' => [
                'zu..\\..\\..\\..\\..\\..\\..\\..\\..\\..\\..\\..\\..\\..\\..\\windows\\win.ini',
                'The language code can only contain letters, numbers, underscores and hyphens.',
            ],
            'special_characters' => [
                'lang@#$',
                'This value is not a valid locale.',
            ],
            'spaces_in_code' => [
                'en us',
                'This value is not a valid locale.',
            ],
            'polish_characters' => [
                'ąć',
                'This value is not a valid locale.',
            ],
        ];
    }

    /**
     * @test
     * @depends it_creates_a_new_language
     */
    public function it_removes_a_language(): void
    {
        $languageData = [
            'name' => 'German',
            'code' => 'de',
            'order' => 0,
            'adminDefault' => 0,
            'apiDefault' => 0,
        ];

        $this->client->request(
            'POST',
            '/api/language',
            $languageData,
        );

        $this->assertOkResponseStatus($this->client->getResponse());

        $this->client->request(
            'DELETE',
            '/api/language/de'
        );

        $this->assertNoContentResponseStatus($this->client->getResponse());
    }

    /**
     * @test
     */
    public function it_returns_language_list_for_admin(): void
    {
        $this->client->request(
            'GET',
            '/api/language'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('items', $data);
        $this->assertArrayHasKey('total', $data);
        $this->assertSame(2, $data['total']['all']);

        sort($data['items']);
        $languageData = reset($data['items']);
        $this->assertArrayHasKey('name', $languageData);
        $this->assertSame('English', $languageData['name']);
        $this->assertArrayHasKey('code', $languageData);
        $this->assertSame('en', $languageData['code']);
        $this->assertArrayHasKey('updatedAt', $languageData);
    }

    /**
     * @test
     */
    public function it_returns_list_of_default_languages(): void
    {
        $this->client->request(
            'GET',
            '/api/language?apiDefault=true'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('items', $data);
        $this->assertArrayHasKey('total', $data);
        $this->assertSame(2, $data['total']['all']);
        $this->assertSame(1, $data['total']['filtered']);

        $languageData = reset($data['items']);
        $this->assertArrayHasKey('name', $languageData);
        $this->assertSame('English', $languageData['name']);
        $this->assertArrayHasKey('code', $languageData);
        $this->assertSame('en', $languageData['code']);
        $this->assertArrayHasKey('updatedAt', $languageData);
        $this->assertArrayHasKey('apiDefault', $languageData);
        $this->assertTrue($languageData['apiDefault']);
    }

    /**
     * @test
     */
    public function it_returns_list_of_languages_without_default(): void
    {
        $this->client->request(
            'GET',
            '/api/language?apiDefault=false'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('items', $data);
        $this->assertArrayHasKey('total', $data);
        $this->assertSame(2, $data['total']['all']);
        $this->assertSame(1, $data['total']['filtered']);

        $languageData = reset($data['items']);
        $this->assertArrayHasKey('name', $languageData);
        $this->assertSame('Spanish', $languageData['name']);
        $this->assertArrayHasKey('code', $languageData);
        $this->assertSame('es', $languageData['code']);
        $this->assertArrayHasKey('updatedAt', $languageData);
        $this->assertArrayHasKey('apiDefault', $languageData);
        $this->assertFalse($languageData['apiDefault']);
    }

    /**
     * @test
     */
    public function it_returns_language_by_its_locale_code(): void
    {
        $this->client->request(
            'GET',
            '/api/language/en'
        );

        $this->assertOkResponseStatus($this->client->getResponse());
    }

    /**
     * @test
     */
    public function it_throws_if_language_does_not_exist(): void
    {
        $this->client->request(
            'GET',
            '/api/language/english'
        );

        $this->assertNotFoundResponseStatus($this->client->getResponse());
    }

    /**
     * @test
     */
    public function it_returns_language_choices_for_customer(): void
    {
        self::ensureKernelShutdown();
        $client = self::createAuthenticatedClient(
            LoadUserData::USER_USERNAME,
            LoadUserData::USER_PASSWORD,
            'member',
            LoadSettingsData::DEFAULT_STORE_CODE
        );
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/settings/choices/language'
        );

        $response = $client->getResponse();

        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('choices', $data);
        $this->assertContains([
            'code' => 'en',
            'name' => 'English',
        ], $data['choices']);
    }

    /**
     * @test
     */
    public function it_returns_country_for_anonymous_user(): void
    {
        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->request(
            'GET',
            '/api/settings/choices/country'
        );

        $response = $client->getResponse();

        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('choices', $data);
        $this->assertContains([
            'code' => 'PL',
            'name' => 'Poland',
        ], $data['choices']);
    }
}
