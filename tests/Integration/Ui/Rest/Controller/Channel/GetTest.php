<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Integration\Ui\Rest\Controller\Channel;

use OpenLoyalty\Channel\Infrastructure\DataFixtures\ORM\LoadChannelData;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class GetTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_returns_channel(): void
    {
        $client = $this->createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/channel/'.LoadChannelData::CHANNEL_ID,
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('name', $data);
        $this->assertArrayHasKey('channelId', $data);
        $this->assertArrayHasKey('currency', $data);
    }
}
