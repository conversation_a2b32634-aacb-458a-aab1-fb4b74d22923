<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Integration\Ui\Rest\Controller\GroupOfValues;

use OpenLoyalty\Integration\Helpers\ResponseChecker;
use OpenLoyalty\Integration\Traits\GroupOfValueApiTrait;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class PutValueTest extends AbstractApiTest
{
    use TenantApiTrait;
    use GroupOfValueApiTrait;
    private HttpKernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_updates_value(): void
    {
        $tenantCode = 'tenant';
        $this->postTenant($this->client, $tenantCode);

        $response = $this->postGroupOfValue(
            $this->client,
            $tenantCode,
            'Skus group',
            'Skus group'
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $groupOfValueId = $data['groupOfValuesId'];

        $response = $this->postValue(
            $this->client,
            $tenantCode,
            'sku1',
            'sku product 1',
            $groupOfValueId
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $valueId = $data['valueId'];

        $this->client->request(
            'GET',
            '/api/'.$tenantCode.'/groupOfValues/value/'.$valueId,
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertSame($valueId, $data['valueId']);
        $this->assertSame($groupOfValueId, $data['groupOfValuesId']);
        $this->assertSame('sku1', $data['value']);
        $this->assertSame('sku product 1', $data['description']);

        $response = $this->putValue(
            $this->client,
            $tenantCode,
            'edited sku1',
            'edited sku product 1',
            $valueId
        );

        $this->assertNoContentResponseStatus($response);

        $this->client->request(
            'GET',
            '/api/'.$tenantCode.'/groupOfValues/value/'.$valueId,
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertSame($valueId, $data['valueId']);
        $this->assertSame($groupOfValueId, $data['groupOfValuesId']);
        $this->assertSame('edited sku1', $data['value']);
        $this->assertSame('edited sku product 1', $data['description']);
    }

    /**
     * @test
     */
    public function it_updates_only_description(): void
    {
        $tenantCode = 'tenant';
        $this->postTenant($this->client, $tenantCode);

        $response = $this->postGroupOfValue(
            $this->client,
            $tenantCode,
            'Skus group',
            'Skus group'
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $groupOfValueId = $data['groupOfValuesId'];

        $response = $this->postValue(
            $this->client,
            $tenantCode,
            'sku1',
            'sku product 1',
            $groupOfValueId
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $valueId = $data['valueId'];

        $this->client->request(
            'GET',
            '/api/'.$tenantCode.'/groupOfValues/value/'.$valueId,
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertSame($valueId, $data['valueId']);
        $this->assertSame($groupOfValueId, $data['groupOfValuesId']);
        $this->assertSame('sku1', $data['value']);
        $this->assertSame('sku product 1', $data['description']);

        $response = $this->putValue(
            $this->client,
            $tenantCode,
            'sku1',
            'edited sku product 1',
            $valueId
        );

        $this->assertNoContentResponseStatus($response);

        $this->client->request(
            'GET',
            '/api/'.$tenantCode.'/groupOfValues/value/'.$valueId,
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertSame($valueId, $data['valueId']);
        $this->assertSame($groupOfValueId, $data['groupOfValuesId']);
        $this->assertSame('sku1', $data['value']);
        $this->assertSame('edited sku product 1', $data['description']);
    }

    /**
     * @test
     */
    public function it_does_not_update_value_if_the_same_value_already_exists(): void
    {
        $tenantCode = 'tenant';
        $this->postTenant($this->client, $tenantCode);

        $response = $this->postGroupOfValue(
            $this->client,
            $tenantCode,
            'Skus group',
            'Skus group'
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $groupOfValueId = $data['groupOfValuesId'];

        $response = $this->postValue(
            $this->client,
            $tenantCode,
            'sku1',
            'sku product 1',
            $groupOfValueId
        );

        $this->assertOkResponseStatus($response);

        $response = $this->postValue(
            $this->client,
            $tenantCode,
            'sku2',
            'sku product 1',
            $groupOfValueId
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $valueId = $data['valueId'];

        $response = $this->putValue(
            $this->client,
            $tenantCode,
            'sku1',
            'edited sku product 1',
            $valueId
        );

        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        ResponseChecker::assertHasError($data, 'value', 'Value already exist in this group.');
    }
}
