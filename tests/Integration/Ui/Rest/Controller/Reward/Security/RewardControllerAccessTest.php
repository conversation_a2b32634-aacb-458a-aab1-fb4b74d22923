<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Integration\Ui\Rest\Controller\Reward\Security;

use OpenLoyalty\Level\Infrastructure\DataFixtures\ORM\LoadLevelData;
use OpenLoyalty\Reward\Infrastructure\DataFixtures\ORM\LoadRewardData;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseAccessControlTest;

final class RewardControllerAccessTest extends BaseAccessControlTest
{
    /**
     * @test
     */
    public function only_admin_should_have_access_to_all_rewards_list(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward');
    }

    /**
     * @test
     */
    public function only_admin_can_create_reward(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward', [], 'POST');
    }

    /**
     * @test
     */
    public function only_admin_can_edit_reward(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD_ID, [], 'PUT');
    }

    /**
     * @test
     */
    public function admin_can_get_all_rewards_but_customer_only_visible_for_him(): void
    {
        $this->markTestSkipped('To be fixed in OLOY-7501.');

        $adminClient = $this->getAdminClient();
        $adminClient->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Reward visible only for admin',
                        ],
                    ],
                    'reward' => 'conversion_coupon',
                    'active' => true,
                    'activity' => [
                        'allTime' => 1,
                    ],
                    'couponGenerator' => [
                        'characterSet' => 'alphanum',
                        'length' => 5,
                    ],
                    'usageLimit' => [
                        'general' => 100,
                        'perUser' => 100,
                    ],
                    'visibility' => [
                        'allTime' => 0,
                        'from' => '2016-02-01 00:00:00',
                        'to' => '2017-02-11 00:00:00',
                    ],
                    'featured' => true,
                    'public' => true,
                    'unitsConversion' => [
                        'ratio' => 1.3,
                        'rounding' => 'default',
                    ],
                ],
            ]
        );
        $response = $adminClient->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $rewardId = $data['rewardId'];

        $customerClient = $this->getCustomerClient();
        $customerClient->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$rewardId);
        $this->assertNoAccessResponseStatus($customerClient->getResponse());

        $adminClient->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$rewardId);
        $this->assertOkResponseStatus($adminClient->getResponse());

        $adminClient->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Reward visible for all',
                        ],
                    ],
                    'reward' => 'conversion_coupon',
                    'active' => true,
                    'activity' => [
                        'allTime' => 1,
                    ],
                    'levels' => [
                        LoadLevelData::LEVEL0_ID,
                    ],
                    'target' => 'level',
                    'couponGenerator' => [
                        'characterSet' => 'alphanum',
                        'length' => 5,
                    ],
                    'usageLimit' => [
                        'general' => 100,
                        'perUser' => 100,
                    ],
                    'visibility' => [
                        'allTime' => 1,
                    ],
                    'featured' => true,
                    'public' => true,
                    'unitsConversion' => [
                        'ratio' => 1.3,
                        'rounding' => 'default',
                    ],
                ],
            ]
        );
        $response = $adminClient->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $rewardId = $data['rewardId'];

        $customerClient->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$rewardId);
        $this->assertOkResponseStatus($customerClient->getResponse());

        $adminClient->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$rewardId);
        $this->assertOkResponseStatus($adminClient->getResponse());
    }

    /**
     * @test
     */
    public function only_admin_can_get_reward_customers(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD_ID.'/members?visible=true');
    }
}
