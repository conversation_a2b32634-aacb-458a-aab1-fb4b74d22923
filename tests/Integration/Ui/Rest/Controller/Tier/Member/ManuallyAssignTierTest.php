<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Integration\Ui\Rest\Controller\Tier\Member;

use OpenLoyalty\Points\Domain\Expiring\ExpiringTransferMode;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Settings\Integration\Traits\SettingsApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class ManuallyAssignTierTest extends AbstractApiTest
{
    use SettingsApiTrait;

    protected HttpKernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_assign_member_to_tier(): void
    {
        $storeCode = 'sendCodeTenant';
        $this->createTenant($storeCode);

        $this->updateSetting($this->client, $storeCode, 'tiersMode', 'custom');

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/tierSet',
            [
                'tierSet' => [
                    'translations' => [
                        'en' => [
                            'name' => 'new tier set',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'labels' => [
                        [
                            'key' => 'key1',
                            'value' => 'value1',
                        ],
                    ],
                    'conditions' => [
                        [
                            'walletType' => 'default',
                            'attribute' => 'activeUnits',
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $tierSetId = $data['tierSetId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        self::assertSame(1, count($data['conditions']));
        $conditionId = $data['conditions'][0]['id'];

        $this->client->jsonRequest(
            'PUT',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId.'/tiers',
            [
                'tiers' => [
                    [
                        'translations' => [
                            'en' => [
                                'name' => 'tier1',
                                'description' => '',
                            ],
                        ],
                        'active' => true,
                        'rewards' => [
                        ],
                        'conditions' => [
                            [
                                'value' => 100,
                                'conditionId' => $conditionId,
                            ],
                        ],
                    ],
                    [
                        'translations' => [
                            'en' => [
                                'name' => 'tier2',
                                'description' => '',
                            ],
                        ],
                        'active' => true,
                        'rewards' => [
                        ],
                        'conditions' => [
                            [
                                'value' => 200,
                                'conditionId' => $conditionId,
                            ],
                        ],
                    ],
                    [
                        'translations' => [
                            'en' => [
                                'name' => 'tier3',
                                'description' => '',
                            ],
                        ],
                        'active' => true,
                        'rewards' => [
                        ],
                        'conditions' => [
                            [
                                'value' => 500,
                                'conditionId' => $conditionId,
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $customerId = $this->createMember($storeCode, '<EMAIL>');

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        self::assertSame('Default', $data['currentTierName']);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/status'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        self::assertSame('Default', $data['levelName']);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId.'/tiers?name=tier2'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $tierId = $data['items'][0]['levelId'];

        $this->assignMemberToTier($storeCode, $customerId, $tierId);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        self::assertSame('tier2', $data['currentTierName']);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/status'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        self::assertSame('tier2', $data['levelName']);
    }

    /**
     * @test
     */
    public function it_not_move_member_to_lower_tier_when_member_was_assign_manually(): void
    {
        $storeCode = 'sendCodeTenant';
        $this->createTenant($storeCode);

        $this->updateSetting($this->client, $storeCode, 'tiersMode', 'custom');

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/walletType'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $walletType = $data['items'][0]['walletTypeId'];

        $this->updateWalletTypeNotPending($storeCode, $walletType);

        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/tierSet',
            [
                'tierSet' => [
                    'translations' => [
                        'en' => [
                            'name' => 'new tier set',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'labels' => [
                        [
                            'key' => 'key1',
                            'value' => 'value1',
                        ],
                    ],
                    'conditions' => [
                        [
                            'walletType' => 'default',
                            'attribute' => 'activeUnits',
                        ],
                    ],
                    'downgrade' => [
                        'mode' => 'automatic',
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $tierSetId = $data['tierSetId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        self::assertSame(1, count($data['conditions']));
        $conditionId = $data['conditions'][0]['id'];

        $this->client->jsonRequest(
            'PUT',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId.'/tiers',
            [
                'tiers' => [
                    [
                        'translations' => [
                            'en' => [
                                'name' => 'tier1',
                                'description' => '',
                            ],
                        ],
                        'active' => true,
                        'rewards' => [
                        ],
                        'conditions' => [
                            [
                                'value' => 100,
                                'conditionId' => $conditionId,
                            ],
                        ],
                    ],
                    [
                        'translations' => [
                            'en' => [
                                'name' => 'tier2',
                                'description' => '',
                            ],
                        ],
                        'active' => true,
                        'rewards' => [
                        ],
                        'conditions' => [
                            [
                                'value' => 200,
                                'conditionId' => $conditionId,
                            ],
                        ],
                    ],
                    [
                        'translations' => [
                            'en' => [
                                'name' => 'tier3',
                                'description' => '',
                            ],
                        ],
                        'active' => true,
                        'rewards' => [
                        ],
                        'conditions' => [
                            [
                                'value' => 500,
                                'conditionId' => $conditionId,
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $customerId = $this->createMember($storeCode, '<EMAIL>');

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId.'/tiers?name=tier3'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $tierId = $data['items'][0]['levelId'];

        $this->assignMemberToTier($storeCode, $customerId, $tierId);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        self::assertSame('tier3', $data['currentTierName']);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/status'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        self::assertSame('tier3', $data['levelName']);

        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/points/add',
            [
                'transfer' => [
                    'customer' => $customerId,
                    'points' => 100,
                ],
            ]
        );

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];

        self::assertSame('tier3', $data['currentTierName']);
        self::assertSame(100.0, $nextTierCurrentProgress[0]['currentValue']);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/status'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        self::assertSame('tier3', $data['levelName']);
        self::assertSame(100.0, $data['activePoints']);
    }

    /**
     * @test
     */
    public function it_assign_manually_member_to_same_tier(): void
    {
        $storeCode = 'sendCodeTenant';
        $this->createTenant($storeCode);

        $this->updateSetting($this->client, $storeCode, 'tiersMode', 'custom');

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/walletType'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $walletType = $data['items'][0]['walletTypeId'];

        $this->updateWalletTypeNotPending($storeCode, $walletType);

        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/tierSet',
            [
                'tierSet' => [
                    'translations' => [
                        'en' => [
                            'name' => 'new tier set',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'labels' => [
                        [
                            'key' => 'key1',
                            'value' => 'value1',
                        ],
                    ],
                    'conditions' => [
                        [
                            'walletType' => 'default',
                            'attribute' => 'activeUnits',
                        ],
                    ],
                    'downgrade' => [
                        'mode' => 'automatic',
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $tierSetId = $data['tierSetId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        self::assertSame(1, count($data['conditions']));
        $conditionId = $data['conditions'][0]['id'];

        $this->client->jsonRequest(
            'PUT',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId.'/tiers',
            [
                'tiers' => [
                    [
                        'translations' => [
                            'en' => [
                                'name' => 'tier1',
                                'description' => '',
                            ],
                        ],
                        'active' => true,
                        'rewards' => [
                        ],
                        'conditions' => [
                            [
                                'value' => 100,
                                'conditionId' => $conditionId,
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId.'/tiers?name=Default'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $tierId = $data['items'][0]['levelId'];

        $customerId = $this->createMember($storeCode, '<EMAIL>');

        //check tier on member endpoint
        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        self::assertArrayNotHasKey('manuallyAssignedLevelId', $data);
        self::assertSame('Default', $data['currentLevel']['name']);
        self::assertSame($tierId, $data['currentLevel']['levelId']);

        //check history
        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/history?type[in]=CustomerWasMovedToLevel'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $items = $data['items'];

        self::assertSame(1, count($items));
        self::assertSame(false, $items[0]['flatVariables']['manually']);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/history'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $items = $data['items'];

        self::assertSame(3, count($items));

        //assign member to current tier
        $this->assignMemberToTier($storeCode, $customerId, $tierId);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        self::assertSame($tierId, $data['manuallyAssignedLevelId']);
        self::assertSame('Default', $data['currentLevel']['name']);
        self::assertSame($tierId, $data['currentLevel']['levelId']);

        //check history
        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/history?type[in]=CustomerWasMovedToLevel'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $items = $data['items'];

        self::assertSame(1, count($items));
        self::assertSame(false, $items[0]['flatVariables']['manually']);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/history'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $items = $data['items'];

        self::assertSame(3, count($items));
    }

    private function createMember(string $storeCode, string $email): string
    {
        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/member',
            [
                'customer' => [
                    'firstName' => 'Jonny',
                    'lastName' => 'Wall',
                    'email' => $email,
                    'gender' => 'male',
                    'birthDate' => '1991-01-01',
                    'agreement1' => true,
                    'registeredAt' => '2024-02-03T14:15:22Z',
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        return $data['customerId'];
    }

    private function updateWalletTypeNotPending(string $storeCode, string $walletType): void
    {
        $this->client->jsonRequest(
            'PUT',
            '/api/'.$storeCode.'/walletType/'.$walletType,
            [
                'walletType' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Default Stars',
                            'description' => 'Default Stars',
                        ],
                    ],
                    'unitSingularName' => 'Point',
                    'unitPluralName' => 'Points',
                    'active' => true,
                    'unitDaysExpiryAfter' => ExpiringTransferMode::EXPIRING_AFTER_X_DAYS,
                    'unitDaysActiveCount' => 30,
                    'allTimeNotLocked' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);
    }

    private function assignMemberToTier(string $storeCode, string $customerId, string $tierId): void
    {
        $this->client->jsonRequest(
            'POST',
            sprintf('/api/'.$storeCode.'/member/%s/tier', $customerId),
            [
                'levelId' => $tierId,
            ]
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);
    }
}
