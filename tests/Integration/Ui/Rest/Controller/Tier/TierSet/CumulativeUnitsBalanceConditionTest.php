<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Integration\Ui\Rest\Controller\Tier\TierSet;

use OpenLoyalty\Application;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\TierSetId;
use OpenLoyalty\Level\Domain\TierSetMemberProgressRepositoryInterface;
use OpenLoyalty\Test\Account\Integration\Traits\WalletTypeApiTrait;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\Integration\Traits\TierSetApiTrait;
use OpenLoyalty\Test\Integration\Traits\TransactionTraitApi;
use OpenLoyalty\Test\UnitTransfer\Integration\Traits\UnitTransferApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use Symfony\Component\Console\Tester\CommandTester;
use Symfony\Component\HttpKernel\KernelInterface;

final class CumulativeUnitsBalanceConditionTest extends AbstractApiTest
{
    use TierSetApiTrait;
    use TenantApiTrait;
    use WalletTypeApiTrait;
    use MemberApiTrait;
    use TransactionTraitApi;
    use UnitTransferApiTrait;

    /**
     * @test
     */
    public function it_reset_progress_and_downgrade_if_not_enough_units(): void
    {
        $client = self::createAuthenticatedClient();

        $storeCode = 'code1';
        $this->postTenant($client, $storeCode);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/walletType'
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $walletTypeId = $data['items'][0]['walletTypeId'];

        $this->putWalletType($client, $walletTypeId, $storeCode);

        $response = $this->postMember($client, $storeCode);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $customerId = $data['customerId'];

        $response = $this->postTierSet(
            $client,
            $storeCode,
            'new tier set',
            conditions: [['attribute' => 'cumulatedEarnedUnits', 'walletType' => 'default']],
            downgrade: [
                'mode' => 'periodic',
                'period' => 'annual_recalculation_on_chosen_dates',
                'recalculationDates' => ['03-22', '06-19'],
            ]
        );
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $tierSetId = $data['tierSetId'];

        $client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame(1, count($data['conditions']));
        $conditionId = $data['conditions'][0]['id'];

        $this->putTiersToTierSet(
            $client,
            $storeCode,
            $tierSetId,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tier1',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 100,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tier2',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
            ]
        );

        $this->postUnitTransferAdd($client, $storeCode, $customerId, 200);

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame('tier2', $data['currentLevel']['name']);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];
        $this->assertSame($conditionId, $nextTierCurrentProgress[0]['conditionId']);
        $this->assertSame('cumulatedEarnedUnits', $nextTierCurrentProgress[0]['attribute']);
        $this->assertSame(200.0, $nextTierCurrentProgress[0]['currentValue']);

        /**
         * @var TierSetMemberProgressRepositoryInterface $memberProgressRepository
         */
        $memberProgressRepository = self::getContainer()->get(TierSetMemberProgressRepositoryInterface::class);
        $memberProgress = $memberProgressRepository->getMemberProgressByTierSet(new CustomerId($customerId), new TierSetId($tierSetId));

        $nextRecalculationDate = $memberProgress->getPeriodNextRecalculationAt()->format('Y-m-d H:i:s');

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:tier:periodic-downgrade');
        $commandTester = new CommandTester($command);
        $commandTester->execute([
            'command' => $command->getName(),
            '--date' => $nextRecalculationDate,
        ]);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertSame('tier2', $data['currentLevel']['name']);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];
        $this->assertSame($conditionId, $nextTierCurrentProgress[0]['conditionId']);
        $this->assertSame('cumulatedEarnedUnits', $nextTierCurrentProgress[0]['attribute']);
        $this->assertSame(0.0, $nextTierCurrentProgress[0]['currentValue']);

        $memberProgress = $memberProgressRepository->getMemberProgressByTierSet(new CustomerId($customerId), new TierSetId($tierSetId));
        $nextRecalculationDate = $memberProgress->getPeriodNextRecalculationAt()->format('Y-m-d H:i:s');

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:tier:periodic-downgrade');
        $commandTester = new CommandTester($command);
        $commandTester->execute([
            'command' => $command->getName(),
            '--date' => $nextRecalculationDate,
        ]);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame('Default', $data['currentLevel']['name']);
    }

    /**
     * @test
     */
    public function it_check_if_pending_points_change_progress_only_after_unlock(): void
    {
        $client = self::createAuthenticatedClient();

        $storeCode = 'code1';
        $this->postTenant($client, $storeCode);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/walletType'
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $response = $this->postTierSet(
            $client,
            $storeCode,
            'new tier set',
            conditions: [['attribute' => 'cumulatedEarnedUnits', 'walletType' => 'default']],
            downgrade: [
                'mode' => 'periodic',
                'period' => 'annual_recalculation_on_chosen_dates',
                'recalculationDates' => ['03-22', '06-19'],
            ]
        );
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $tierSetId = $data['tierSetId'];

        $client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame(1, count($data['conditions']));
        $conditionId = $data['conditions'][0]['id'];

        $this->putTiersToTierSet(
            $client,
            $storeCode,
            $tierSetId,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tier1',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 100,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tier2',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
            ]
        );

        $response = $this->postMember($client, $storeCode);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $customerId = $data['customerId'];

        $this->postUnitTransferAdd($client, $storeCode, $customerId, 200);
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $transferId = $data['transferId'];

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame('Default', $data['currentLevel']['name']);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];
        $this->assertSame('Default', $data['currentTierName']);
        $this->assertSame($conditionId, $nextTierCurrentProgress[0]['conditionId']);
        $this->assertSame('cumulatedEarnedUnits', $nextTierCurrentProgress[0]['attribute']);
        //$this->assertSame(0.0, $nextTierCurrentProgress[0]['currentValue']);

        $response = $this->postActivateUnitTransfer($client, $transferId, $storeCode);
        $this->assertNoContentResponseStatus($response);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertSame('tier2', $data['currentLevel']['name']);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];
        $this->assertSame($conditionId, $nextTierCurrentProgress[0]['conditionId']);
        $this->assertSame('cumulatedEarnedUnits', $nextTierCurrentProgress[0]['attribute']);
        $this->assertSame(200.0, $nextTierCurrentProgress[0]['currentValue']);
    }

    /**
     * @test
     */
    public function it_check_if_cancellation_points_will_not_lower_progress(): void
    {
        $client = self::createAuthenticatedClient();

        $storeCode = 'code1';
        $this->postTenant($client, $storeCode);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/walletType'
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/walletType'
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $walletTypeId = $data['items'][0]['walletTypeId'];

        $this->putWalletType($client, $walletTypeId, $storeCode);

        $response = $this->postTierSet(
            $client,
            $storeCode,
            'new tier set',
            conditions: [['attribute' => 'cumulatedEarnedUnits', 'walletType' => 'default']],
            downgrade: [
                'mode' => 'periodic',
                'period' => 'annual_recalculation_on_chosen_dates',
                'recalculationDates' => ['03-22', '06-19'],
            ]
        );
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $tierSetId = $data['tierSetId'];

        $client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame(1, count($data['conditions']));
        $conditionId = $data['conditions'][0]['id'];

        $this->putTiersToTierSet(
            $client,
            $storeCode,
            $tierSetId,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tier1',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 100,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tier2',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
            ]
        );

        $response = $this->postMember($client, $storeCode);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $customerId = $data['customerId'];

        $this->postUnitTransferAdd($client, $storeCode, $customerId, 200);
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $transferId = $data['transferId'];

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertSame('tier2', $data['currentLevel']['name']);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];
        $this->assertSame($conditionId, $nextTierCurrentProgress[0]['conditionId']);
        $this->assertSame('cumulatedEarnedUnits', $nextTierCurrentProgress[0]['attribute']);
        $this->assertSame(200.0, $nextTierCurrentProgress[0]['currentValue']);

        $this->postCancelUnitTransfer($client, $transferId, $storeCode);

        //check if progress value and tier does not change after cancellation transfer
        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];
        $this->assertSame('tier2', $data['currentTierName']);
        $this->assertSame($conditionId, $nextTierCurrentProgress[0]['conditionId']);
        $this->assertSame('cumulatedEarnedUnits', $nextTierCurrentProgress[0]['attribute']);
        $this->assertSame(200.0, $nextTierCurrentProgress[0]['currentValue']);
    }

    /**
     * @test
     */
    public function it_progress_cumulated_earned_units_condition_for_different_wallet_types(): void
    {
        $client = self::createAuthenticatedClient();

        $storeCode = 'code1';
        $this->postTenant($client, $storeCode);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/walletType'
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $walletTypeId = $data['items'][0]['walletTypeId'];

        $this->putWalletType($client, $walletTypeId, $storeCode);

        $response = $this->postWalletType($client, 'newWallet', $storeCode);
        $this->assertOkResponseStatus($response);

        $response = $this->postMember($client, $storeCode);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $customerId = $data['customerId'];

        $response = $this->postTierSet(
            $client,
            $storeCode,
            'new tier set',
            conditions: [
                ['attribute' => 'cumulatedEarnedUnits', 'walletType' => 'default'],
                ['attribute' => 'cumulatedEarnedUnits', 'walletType' => 'newWallet'],
            ],
            downgrade: [
                'mode' => 'periodic',
                'period' => 'annual_recalculation_on_chosen_dates',
                'recalculationDates' => ['03-22', '06-19'],
            ]
        );
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $tierSetId = $data['tierSetId'];

        $client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame(2, count($data['conditions']));
        $conditionId1 = $data['conditions'][0]['id'];
        $conditionId2 = $data['conditions'][1]['id'];

        $this->putTiersToTierSet(
            $client,
            $storeCode,
            $tierSetId,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tier1',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 100,
                            'conditionId' => $conditionId1,
                        ],
                        [
                            'value' => 50,
                            'conditionId' => $conditionId2,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tier2',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId1,
                        ],
                        [
                            'value' => 100,
                            'conditionId' => $conditionId2,
                        ],
                    ],
                ],
            ]
        );

        $this->postUnitTransferAdd($client, $storeCode, $customerId, 200);

        $this->postUnitTransferAdd($client, $storeCode, $customerId, 100, walletCode: 'newWallet');

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame('tier2', $data['currentLevel']['name']);

        //reset units after recalcualtion
        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];
        foreach ($nextTierCurrentProgress as $progress) {
            if ($conditionId1 === $progress['conditionId']) {
                $this->assertSame('cumulatedEarnedUnits', $progress['attribute']);
                $this->assertSame(200.0, $progress['currentValue']);
            }
            if ($conditionId2 === $progress['conditionId']) {
                $this->assertSame('cumulatedEarnedUnits', $progress['attribute']);
                $this->assertSame(100.0, $progress['currentValue']);
            }
        }

        /**
         * @var TierSetMemberProgressRepositoryInterface $memberProgressRepository
         */
        $memberProgressRepository = self::getContainer()->get(TierSetMemberProgressRepositoryInterface::class);
        $memberProgress = $memberProgressRepository->getMemberProgressByTierSet(new CustomerId($customerId), new TierSetId($tierSetId));

        $nextRecalculationDate = $memberProgress->getPeriodNextRecalculationAt()->format('Y-m-d H:i:s');

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:tier:periodic-downgrade');
        $commandTester = new CommandTester($command);
        $commandTester->execute([
            'command' => $command->getName(),
            '--date' => $nextRecalculationDate,
        ]);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertSame('tier2', $data['currentLevel']['name']);

        //reset units after recalcualtion
        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];

        foreach ($nextTierCurrentProgress as $progress) {
            if ($conditionId1 === $progress['conditionId']) {
                $this->assertSame('cumulatedEarnedUnits', $progress['attribute']);
                $this->assertSame(0.0, $progress['currentValue']);
            }
            if ($conditionId2 === $progress['conditionId']) {
                $this->assertSame('cumulatedEarnedUnits', $progress['attribute']);
                $this->assertSame(0.0, $progress['currentValue']);
            }
        }

        $memberProgress = $memberProgressRepository->getMemberProgressByTierSet(new CustomerId($customerId), new TierSetId($tierSetId));
        $nextRecalculationDate = $memberProgress->getPeriodNextRecalculationAt()->format('Y-m-d H:i:s');

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:tier:periodic-downgrade');
        $commandTester = new CommandTester($command);
        $commandTester->execute([
            'command' => $command->getName(),
            '--date' => $nextRecalculationDate,
        ]);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame('Default', $data['currentLevel']['name']);
    }

    /**
     * @test
     */
    public function it_reset_progress_only_for_tier_set_that_was_recalculated(): void
    {
        $client = self::createAuthenticatedClient();

        $storeCode = 'code1';
        $this->postTenant($client, $storeCode);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/walletType'
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $walletTypeId = $data['items'][0]['walletTypeId'];

        $this->putWalletType($client, $walletTypeId, $storeCode);

        $response = $this->postMember($client, $storeCode);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $customerId = $data['customerId'];

        $response = $this->postTierSet(
            $client,
            $storeCode,
            'new tier set',
            conditions: [['attribute' => 'cumulatedEarnedUnits', 'walletType' => 'default']],
            downgrade: [
                'mode' => 'periodic',
                'period' => 'annual_recalculation_on_chosen_dates',
                'recalculationDates' => ['03-22', '06-19'],
            ]
        );
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $tierSetId1 = $data['tierSetId'];

        $response = $this->postTierSet(
            $client,
            $storeCode,
            'new tier set',
            conditions: [['attribute' => 'cumulatedEarnedUnits', 'walletType' => 'default']],
            downgrade: [
                'mode' => 'periodic',
                'period' => 'annual_recalculation_on_chosen_dates',
                'recalculationDates' => ['07-22'],
            ]
        );
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $tierSetId2 = $data['tierSetId'];

        $client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId1
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame(1, count($data['conditions']));
        $conditionId = $data['conditions'][0]['id'];

        $this->putTiersToTierSet(
            $client,
            $storeCode,
            $tierSetId1,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tier1',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 100,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tier2',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
            ]
        );

        $client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId2
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame(1, count($data['conditions']));
        $conditionId = $data['conditions'][0]['id'];

        $this->putTiersToTierSet(
            $client,
            $storeCode,
            $tierSetId2,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tierA',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 100,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tierB',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
            ]
        );

        $this->postUnitTransferAdd($client, $storeCode, $customerId, 200);

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

       //check progress
        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId1
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $this->assertSame('tier2', $data['currentTierName']);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId2
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $this->assertSame('tierB', $data['currentTierName']);

        /**
         * @var TierSetMemberProgressRepositoryInterface $memberProgressRepository
         */
        $memberProgressRepository = self::getContainer()->get(TierSetMemberProgressRepositoryInterface::class);
        $memberProgress = $memberProgressRepository->getMemberProgressByTierSet(new CustomerId($customerId), new TierSetId($tierSetId1));

        $nextRecalculationDate = $memberProgress->getPeriodNextRecalculationAt()->format('Y-m-d H:i:s');

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:tier:periodic-downgrade');
        $commandTester = new CommandTester($command);
        $commandTester->execute([
            'command' => $command->getName(),
            '--date' => $nextRecalculationDate,
        ]);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId1,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];
        $this->assertSame('cumulatedEarnedUnits', $nextTierCurrentProgress[0]['attribute']);
        $this->assertSame(0.0, $nextTierCurrentProgress[0]['currentValue']);
        $this->assertSame('tier2', $data['currentTierName']);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId2
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];
        $this->assertSame('cumulatedEarnedUnits', $nextTierCurrentProgress[0]['attribute']);
        $this->assertSame(200.0, $nextTierCurrentProgress[0]['currentValue']);
        $this->assertSame('tierB', $data['currentTierName']);
    }

    /**
     * @test
     */
    public function it_check_if_pending_points_change_progress_only_after_recalculation(): void
    {
        $client = self::createAuthenticatedClient();

        $storeCode = 'code1';
        $this->postTenant($client, $storeCode);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/walletType'
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $response = $this->postTierSet(
            $client,
            $storeCode,
            'new tier set',
            conditions: [['attribute' => 'cumulatedEarnedUnits', 'walletType' => 'default']],
            downgrade: [
                'mode' => 'periodic',
                'period' => 'annual_recalculation_on_chosen_dates',
                'recalculationDates' => ['03-22', '06-19'],
            ]
        );
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $tierSetId = $data['tierSetId'];

        $client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$tierSetId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame(1, count($data['conditions']));
        $conditionId = $data['conditions'][0]['id'];

        $this->putTiersToTierSet(
            $client,
            $storeCode,
            $tierSetId,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tier1',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 100,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'tier2',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
            ]
        );

        $response = $this->postMember($client, $storeCode);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $customerId = $data['customerId'];

        $this->postUnitTransferAdd($client, $storeCode, $customerId, 200);
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $transferId = $data['transferId'];

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $response = $this->postActivateUnitTransfer($client, $transferId, $storeCode);
        $this->assertNoContentResponseStatus($response);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];
        $this->assertSame($conditionId, $nextTierCurrentProgress[0]['conditionId']);
        $this->assertSame('cumulatedEarnedUnits', $nextTierCurrentProgress[0]['attribute']);
        $this->assertSame(200.0, $nextTierCurrentProgress[0]['currentValue']);

        /**
         * @var TierSetMemberProgressRepositoryInterface $memberProgressRepository
         */
        $memberProgressRepository = self::getContainer()->get(TierSetMemberProgressRepositoryInterface::class);
        $memberProgress = $memberProgressRepository->getMemberProgressByTierSet(new CustomerId($customerId), new TierSetId($tierSetId));

        // Set the next recalculation date to a past timestamp to simulate the start of the next period
        $nextRecalculationDate = (new \DateTimeImmutable('now'))
            ->sub(new \DateInterval('PT1H'))
            ->format('Y-m-d H:i:s');

        $this->updateTierSetMemberProgressNextRecalculationAt(
            $nextRecalculationDate,
            null,
            (string) $memberProgress->getMemberId(),
            (string) $memberProgress->getTierSet()->getTierSetId()
        );

        // points should go to pending value
        $this->postUnitTransferAdd($client, $storeCode, $customerId, 66);
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $transferId = $data['transferId'];

        $response = $this->postActivateUnitTransfer($client, $transferId, $storeCode);
        $this->assertNoContentResponseStatus($response);

        //current progress does not change
        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];
        $this->assertSame($conditionId, $nextTierCurrentProgress[0]['conditionId']);
        $this->assertSame('cumulatedEarnedUnits', $nextTierCurrentProgress[0]['attribute']);
        $this->assertSame(200.0, $nextTierCurrentProgress[0]['currentValue']);

        $currentTime = (new \DateTimeImmutable('+1 year'));

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:tier:periodic-downgrade');
        $commandTester = new CommandTester($command);
        $commandTester->execute([
            'command' => $command->getName(),
            '--date' => $currentTime->format('Y-m-d'),
        ]);

        $client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$customerId.'/tierSet/'.$tierSetId,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $nextTierCurrentProgress = $data['nextTierCurrentProgress'];
        $this->assertSame($conditionId, $nextTierCurrentProgress[0]['conditionId']);
        $this->assertSame('cumulatedEarnedUnits', $nextTierCurrentProgress[0]['attribute']);
        $this->assertSame(66.0, $nextTierCurrentProgress[0]['currentValue']);
    }

    private function updateTierSetMemberProgressNextRecalculationAt(string $nextRecalculationAt, ?string $oldRecalculationAt, string $memberId, string $tierSetId): void
    {
        $entityManager = self::$kernel->getContainer()->get('doctrine.orm.default_entity_manager');
        $query = $entityManager->createQuery("
            UPDATE OpenLoyalty\Level\Domain\Entity\TierSetMemberProgress tsmp
            SET tsmp.periodicDowngradeRecalculation.nextRecalculationAt = :nextRecalculationAt
            WHERE tsmp.memberId = :memberId AND tsmp.tierSet = :tierSetId
        ");
        $query->setParameter('nextRecalculationAt', $nextRecalculationAt);
        $query->setParameter('memberId', $memberId);
        $query->setParameter('tierSetId', $tierSetId);
        $query->execute();

        $query = $entityManager->createQuery("
            UPDATE OpenLoyalty\Level\Domain\Entity\TierSetMemberProgress tsmp
            SET tsmp.periodicDowngradeRecalculation.oldRecalculationAt = :oldRecalculationAt
            WHERE tsmp.memberId = :memberId AND tsmp.tierSet = :tierSetId
        ");
        $query->setParameter('oldRecalculationAt', $oldRecalculationAt);
        $query->setParameter('memberId', $memberId);
        $query->setParameter('tierSetId', $tierSetId);
        $query->execute();
    }
}
