<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Integration\Infrastructure\Reward\Doctrine\ORM\Repository;

use OpenLoyalty\Core\Domain\Id\PhotoId;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Reward\Domain\Model\RewardPhoto;
use OpenLoyalty\Reward\Domain\PhotoMimeType;
use OpenLoyalty\Reward\Domain\PhotoOriginalName;
use OpenLoyalty\Reward\Domain\PhotoPath;
use OpenLoyalty\Reward\Domain\Repository\RewardPhotoRepositoryInterface;
use OpenLoyalty\Reward\Domain\Repository\RewardRepository;
use OpenLoyalty\Reward\Domain\Reward;
use OpenLoyalty\Reward\Infrastructure\Persistence\Doctrine\Repository\RewardPhotoRepository;
use OpenLoyalty\Test\Common\Integration\AbstractKernelTest;

final class RewardPhotoRepositoryTest extends AbstractKernelTest
{
    private const REWARD_ID = '6a861f8a-6906-4002-8b9c-4db407e810ea';
    private const REWARD_PHOTO_ID = '00000000-0000-0000-0000-000000000001';

    private RewardPhotoRepositoryInterface $photoRepository;
    private RewardRepository $rewardRepository;

    /**
     * @test
     */
    public function it_add_photo_to_reward(): void
    {
        $reward = $this->loadReward();
        $this->savePhoto($reward);

        $photoId = new PhotoId(self::REWARD_PHOTO_ID);
        $actual = $this->photoRepository->findOneByIdRewardId($photoId, $reward->getRewardId());
        $this->assertNotNull($actual);
    }

    /**
     * @test
     */
    public function it_remove_photo_from_reward(): void
    {
        $photoId = new PhotoId(self::REWARD_PHOTO_ID);
        $rewardId = new RewardId(self::REWARD_ID);
        $this->removePhoto($photoId, $rewardId);
        $this->assertNull($this->photoRepository->findOneByIdRewardId($photoId, $rewardId));
    }

    /**
     * @test
     */
    public function it_return_null_when_photo_not_found_by_id(): void
    {
        $nonExistsPhotoId = new PhotoId('00000000-0000-0000-0000-000000000122');
        $rewardId = new RewardId(self::REWARD_ID);
        $this->assertNull($this->photoRepository->findOneByIdRewardId($nonExistsPhotoId, $rewardId));
    }

    /**
     * @test
     */
    public function it_return_photo_for_reward(): void
    {
        $reward = $this->loadReward();
        $this->savePhoto($reward);

        $rewardId = new RewardId(self::REWARD_ID);
        $actual = $this->photoRepository->findOneByIdRewardId(new PhotoId(self::REWARD_PHOTO_ID), $rewardId);
        $this->assertNotNull($actual);
    }

    /**
     * @test
     */
    public function it_return_null_when_photo_for_reward_not_found(): void
    {
        $reward = $this->loadReward();
        $this->savePhoto($reward);

        $rewardId = new RewardId(self::REWARD_ID);
        $nonExistsPhotoId = new PhotoId('00000000-0000-0000-0000-000000000122');

        $actual = $this->photoRepository->findOneByIdRewardId($nonExistsPhotoId, $rewardId);
        $this->assertNull($actual);
    }

    /**
     * {@inheritdoc}
     */
    protected function setUp(): void
    {
        parent::setUp();
        $entityManager = self::getContainer()->get('doctrine')->getManager();
        $this->photoRepository = new RewardPhotoRepository($entityManager);
        $this->rewardRepository = self::getContainer()->get(RewardRepository::class);
    }

    /**
     * {@inheritdoc}
     */
    protected function tearDown(): void
    {
        parent::tearDown();
        $this->removePhoto(new PhotoId(self::REWARD_PHOTO_ID), new RewardId(self::REWARD_ID));
    }

    private function loadReward(): Reward
    {
        return $this->rewardRepository->byId(new RewardId(self::REWARD_ID));
    }

    private function savePhoto(Reward $reward): void
    {
        $photoPath = new PhotoPath('/path/to/photo/');
        $originalName = new PhotoOriginalName('photo1.jpg');
        $mimeType = new PhotoMimeType('image/jpg');
        $photoId = new PhotoId(self::REWARD_PHOTO_ID);
        $this->photoRepository->save(new RewardPhoto($reward, $photoId, $photoPath, $originalName, $mimeType));
    }

    private function removePhoto(PhotoId $photoId, RewardId $rewardId): void
    {
        $photo = $this->photoRepository->findOneByIdRewardId($photoId, $rewardId);
        if (null == $photo) {
            return;
        }
        $this->photoRepository->remove($photo);
    }
}
