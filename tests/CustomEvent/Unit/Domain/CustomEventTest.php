<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\CustomEvent\Unit\Domain;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\CustomEventId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\CustomEvent\Domain\CustomEvent;
use OpenLoyalty\CustomEvent\Domain\Exception\CustomEventIsAlreadyMatchedException;
use OpenLoyalty\CustomEvent\Domain\ValueObject\CustomerData;
use PHPUnit\Framework\TestCase;

final class CustomEventTest extends TestCase
{
    /**
     * @test
     */
    public function it_does_not_allow_assign_many_times(): void
    {
        $this->expectException(CustomEventIsAlreadyMatchedException::class);

        $event = new CustomEvent(
            new CustomEventId('00000000-0000-0000-0000-000000000007'),
            'type',
            'name',
            $this->createMock(Store::class),
            $this->createMock(CustomerData::class),
            [],
            new \DateTimeImmutable(),
            new \DateTimeImmutable()
        );

        $event->attachCustomerId(new CustomerId('00000000-0000-0000-0000-000000000008'));
        $event->attachCustomerId(new CustomerId('00000000-0000-0000-0000-000000000008'));
    }
}
