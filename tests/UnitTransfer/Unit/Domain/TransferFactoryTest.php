<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\UnitTransfer\Unit\Domain;

use Assert\AssertionFailedException;
use DateTimeImmutable;
use OpenLoyalty\Account\Domain\Provider\ValidityTransferDecorator;
use OpenLoyalty\Account\Domain\Provider\WalletProviderInterface;
use OpenLoyalty\Account\Domain\WalletType;
use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Core\Domain\Model\Wallet as WalletVO;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Core\Domain\ValueObject\RangePeriodDate;
use OpenLoyalty\Points\Domain\Transfer;
use OpenLoyalty\Points\Domain\TransferFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class TransferFactoryTest extends TestCase
{
    private const GENERATED_UUID = '********-**************-********0004';
    private TransferFactory $transferFactory;
    private WalletProviderInterface|MockObject $walletProvider;

    protected function setUp(): void
    {
        $uuidGenerator = $this->getMockBuilder(UuidGeneratorInterface::class)->getMock();
        $uuidGenerator->method('generate')->willReturn(self::GENERATED_UUID);
        $this->walletProvider = $this->createMock(WalletProviderInterface::class);
        $this->transferFactory = new TransferFactory(
            $uuidGenerator,
            new ValidityTransferDecorator(),
            $this->walletProvider
        );
    }

    /**
     * @test
     */
    public function it_creates_add_transfer(): void
    {
        $transferId = new TransferId('********-0000-0000-0000-********0005');
        $accountId = new AccountId('********-0000-0000-0000-********0003');
        $storeId = new StoreId('********-0000-0000-0000-********0007');
        $createdAt = new DateTimeImmutable();

        $transfer = new Transfer(
            $transferId,
            Transfer::ADD_TYPE,
            $accountId,
            $storeId,
            200,
            $createdAt,
            $createdAt
        );

        $createdTransfer = $this->transferFactory->add(
            $storeId,
            $accountId,
            200,
            $createdAt,
            $transferId,
        );

        $this->assertEquals($transfer, $createdTransfer);
    }

    /**
     * @test
     */
    public function it_creates_add_transfer_with_overrides(): void
    {
        $transferId = new TransferId('********-0000-0000-0000-********0005');
        $accountId = new AccountId('********-0000-0000-0000-********0003');
        $storeId = new StoreId('********-0000-0000-0000-********0007');
        $createdAt = new DateTimeImmutable('2024-02-01 12:00');

        $transfer = new Transfer(
            $transferId,
            Transfer::ADD_TYPE,
            $accountId,
            $storeId,
            200,
            $createdAt,
            $createdAt
        );

        $transfer->lockedUntil((clone $createdAt)->modify('+2 days'));
        $transfer->expiresAt((clone $createdAt)->modify('+7 days'));

        $createdTransfer = $this->transferFactory->add(
            $storeId,
            $accountId,
            200,
            $createdAt,
            $transferId,
            null,
            RangePeriodDate::fromDays(2),
            RangePeriodDate::fromDays(5),
            null,
            null,
            $createdAt
        );

        $this->assertEquals($transfer, $createdTransfer);
    }

    /**
     * @test
     */
    public function it_creates_add_transfer_from_wallet_type_settings(): void
    {
        $transferId = new TransferId('********-0000-0000-0000-********0005');
        $accountId = new AccountId('********-0000-0000-0000-********0003');
        $storeId = new StoreId('********-0000-0000-0000-********0007');
        $createdAt = new DateTimeImmutable('2024-02-01 12:00');

        $walletType = WalletType::create(
            new WalletTypeId('********-0000-0000-0000-********0003'),
            '',
            $this->createMock(Store::class),
            '',
            '',
            true,
            null,
            true,
            false,
            false
        );
        $walletType->markAsPendingForXDays(2);
        $walletType->markAsExpireAfterXDays(5);
        $wallet = new WalletVO($storeId, $accountId, new CustomerId('********-0000-0000-0000-********0003'), $walletType);
        $this->walletProvider->method('provideById')->willReturn($wallet);

        $transfer = new Transfer(
            $transferId,
            Transfer::ADD_TYPE,
            $accountId,
            $storeId,
            200,
            $createdAt,
            $createdAt
        );

        $transfer->lockedUntil((clone $createdAt)->modify('+2 days'));
        $transfer->expiresAt((clone $createdAt)->modify('+7 days'));

        $createdTransfer = $this->transferFactory->add(
            $storeId,
            $accountId,
            200,
            $createdAt,
            $transferId,
            null,
            null,
            null,
            null,
            null,
            $createdAt
        );

        $this->assertEquals($transfer, $createdTransfer);
    }

    /**
     * @test
     */
    public function it_creates_add_transfer_expiring_end_of_month(): void
    {
        $transferId = new TransferId('********-0000-0000-0000-********0005');
        $accountId = new AccountId('********-0000-0000-0000-********0003');
        $storeId = new StoreId('********-0000-0000-0000-********0007');
        $createdAt = new DateTimeImmutable('2024-03-10 13:30');

        $walletType = WalletType::create(
            new WalletTypeId('********-0000-0000-0000-********0003'),
            '',
            $this->createMock(Store::class),
            '',
            '',
            true,
            null,
            true,
            false,
            false
        );
        $walletType->markAsExpireAtTheEndOfTheMonth();

        $wallet = new WalletVO($storeId, $accountId, new CustomerId('********-0000-0000-0000-********0003'), $walletType);
        $this->walletProvider->method('provideById')->willReturn($wallet);

        $transfer = new Transfer(
            $transferId,
            Transfer::ADD_TYPE,
            $accountId,
            $storeId,
            200,
            $createdAt,
            $createdAt
        );

        $transfer->expiresAt(new DateTimeImmutable('2024-03-31 23:59:59'));

        $createdTransfer = $this->transferFactory->add(
            $storeId,
            $accountId,
            200,
            $createdAt,
            $transferId,
            null,
            null,
            null,
            null,
            null,
            $createdAt
        );

        $this->assertEquals($transfer, $createdTransfer);
    }

    /**
     * @test
     */
    public function it_creates_add_transfer_expiring_at_chosen_date(): void
    {
        $transferId = new TransferId('********-0000-0000-0000-********0005');
        $accountId = new AccountId('********-0000-0000-0000-********0003');
        $storeId = new StoreId('********-0000-0000-0000-********0007');
        $createdAt = new DateTimeImmutable('2024-08-10 13:30');

        $walletType = WalletType::create(
            new WalletTypeId('********-0000-0000-0000-********0003'),
            '',
            $this->createMock(Store::class),
            '',
            '',
            true,
            null,
            true,
            false,
            false
        );
        $walletType->markAsExpireChosenDate(new DateTimeImmutable('1970-06-10 00:00'));

        $wallet = new WalletVO($storeId, $accountId, new CustomerId('********-0000-0000-0000-********0003'), $walletType);
        $this->walletProvider->method('provideById')->willReturn($wallet);

        $transfer = new Transfer(
            $transferId,
            Transfer::ADD_TYPE,
            $accountId,
            $storeId,
            200,
            $createdAt,
            $createdAt
        );

        $transfer->expiresAt(new DateTimeImmutable('2025-06-10 23:59:59'));

        $createdTransfer = $this->transferFactory->add(
            $storeId,
            $accountId,
            200,
            $createdAt,
            $transferId,
            null,
            null,
            null,
            null,
            null,
            $createdAt
        );

        $this->assertEquals($transfer, $createdTransfer);
    }

    /**
     * @test
     */
    public function it_creates_add_transfer_expiring_end_of_year(): void
    {
        $transferId = new TransferId('********-0000-0000-0000-********0005');
        $accountId = new AccountId('********-0000-0000-0000-********0003');
        $storeId = new StoreId('********-0000-0000-0000-********0007');
        $createdAt = new DateTimeImmutable('2024-03-10 13:30');

        $walletType = WalletType::create(
            new WalletTypeId('********-0000-0000-0000-********0003'),
            '',
            $this->createMock(Store::class),
            '',
            '',
            true,
            null,
            true,
            false,
            false
        );
        $walletType->markAsExpireAfterXYears(0);

        $wallet = new WalletVO($storeId, $accountId, new CustomerId('********-0000-0000-0000-********0003'), $walletType);
        $this->walletProvider->method('provideById')->willReturn($wallet);

        $transfer = new Transfer(
            $transferId,
            Transfer::ADD_TYPE,
            $accountId,
            $storeId,
            200,
            $createdAt,
            $createdAt
        );

        $transfer->expiresAt(new DateTimeImmutable('2024-12-31 23:59:59'));

        $createdTransfer = $this->transferFactory->add(
            $storeId,
            $accountId,
            200,
            $createdAt,
            $transferId,
            null,
            null,
            null,
            null,
            null,
            $createdAt
        );

        $this->assertEquals($transfer, $createdTransfer);
    }

    /**
     * @test
     */
    public function it_creates_add_transfer_where_wallet_type_is_expired_but_override(): void
    {
        $transferId = new TransferId('********-0000-0000-0000-********0005');
        $accountId = new AccountId('********-0000-0000-0000-********0003');
        $storeId = new StoreId('********-0000-0000-0000-********0007');
        $createdAt = new DateTimeImmutable('2024-03-10 13:30');

        $walletType = WalletType::create(
            new WalletTypeId('********-0000-0000-0000-********0003'),
            '',
            $this->createMock(Store::class),
            '',
            '',
            true,
            null,
            true,
            false,
            false
        );
        $walletType->markAsExpireAtTheEndOfTheMonth();

        $wallet = new WalletVO($storeId, $accountId, new CustomerId('********-0000-0000-0000-********0003'), $walletType);
        $this->walletProvider->method('provideById')->willReturn($wallet);

        $transfer = new Transfer(
            $transferId,
            Transfer::ADD_TYPE,
            $accountId,
            $storeId,
            200,
            $createdAt,
            $createdAt,
        );

        $transfer->expiresAt(null);

        $createdTransfer = $this->transferFactory->add(
            $storeId,
            $accountId,
            200,
            $createdAt,
            $transferId,
            null,
            null,
            RangePeriodDate::empty(),
            null,
            null,
            $createdAt
        );

        $this->assertEquals($transfer, $createdTransfer);
    }

    /**
     * @test
     */
    public function it_creates_spend_transfer(): void
    {
        $transferId = new TransferId('********-0000-0000-0000-********0005');
        $accountId = new AccountId('********-0000-0000-0000-********0003');
        $storeId = new StoreId('********-0000-0000-0000-********0007');
        $createdAt = new DateTimeImmutable();

        $transfer = new Transfer(
            $transferId,
            Transfer::SPEND_TYPE,
            $accountId,
            $storeId,
            200,
            $createdAt,
            $createdAt
        );

        $createdTransfer = $this->transferFactory->spend($storeId, $accountId, 200, $createdAt, $transferId);

        $this->assertEquals($transfer, $createdTransfer);
    }

    /**
     * @test
     */
    public function it_creates_expire_transfer(): void
    {
        $transferId = new TransferId(self::GENERATED_UUID);
        $accountId = new AccountId('********-0000-0000-0000-********0003');
        $storeId = new StoreId('********-0000-0000-0000-********0007');
        $createdAt = new DateTimeImmutable();

        $transfer = new Transfer(
            $transferId,
            Transfer::EXPIRE_TYPE,
            $accountId,
            $storeId,
            200,
            $createdAt,
            $createdAt,
            $transferId
        );

        $createdTransfer = $this->transferFactory->expire($storeId, $accountId, 200, $createdAt, $createdAt, $transferId);

        $this->assertEquals($transfer, $createdTransfer);
    }

    /**
     * @test
     */
    public function it_creates_block_transfer(): void
    {
        $transferId = new TransferId(self::GENERATED_UUID);
        $accountId = new AccountId('********-0000-0000-0000-********0003');
        $storeId = new StoreId('********-0000-0000-0000-********0007');
        $createdAt = new DateTimeImmutable();

        $transfer = new Transfer(
            $transferId,
            Transfer::BLOCK_TYPE,
            $accountId,
            $storeId,
            200,
            $createdAt,
            $createdAt
        );

        $createdTransfer = $this->transferFactory->block($storeId, $accountId, 200, $createdAt);

        $this->assertEquals($transfer, $createdTransfer);
    }

    /**
     * @test
     * @throws AssertionFailedException
     */
    public function it_creates_transfer_transfer(): void
    {
        $transferId = new TransferId(self::GENERATED_UUID);
        $senderId = new AccountId('********-0000-0000-0000-********0003');
        $receiverId = new AccountId('********-0000-0000-0000-********0005');
        $storeId = new StoreId('********-0000-0000-0000-********0007');
        $createdAt = new DateTimeImmutable();

        $transferOut = new Transfer(
            $transferId,
            Transfer::TRANSFER_OUT_TYPE,
            $senderId,
            $storeId,
            200,
            $createdAt,
            $createdAt
        );
        $transferOut->assignReceiverId($receiverId);

        $transferIn = new Transfer(
            $transferId,
            Transfer::TRANSFER_IN_TYPE,
            $receiverId,
            $storeId,
            200,
            $createdAt,
            $createdAt
        );
        $transferIn->assignSenderId($senderId);

        $createdTransfers = $this->transferFactory->transfer($storeId, $senderId, 200, $receiverId, $createdAt);

        $this->assertEquals([$transferOut, $transferIn], $createdTransfers);
    }
}
