<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\UnitTransfer\Unit\Infrastructure\Event\Listener;

use OpenLoyalty\Account\Domain\Provider\ValidityTransferDecorator;
use OpenLoyalty\Account\Domain\Provider\WalletProviderInterface;
use OpenLoyalty\Core\Domain\GeneralSettingsManagerInterface;
use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\Model\Wallet;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Points\Domain\AccountFacadeInterface;
use OpenLoyalty\Points\Domain\Enum\SubType;
use OpenLoyalty\Points\Domain\SystemEvent\Listener\SpendPointsOnRewardListener;
use OpenLoyalty\Points\Domain\Transfer;
use OpenLoyalty\Points\Domain\TransferExecutorInterface;
use OpenLoyalty\Points\Domain\TransferFactory;
use OpenLoyalty\Reward\Domain\IssuedReward;
use OpenLoyalty\User\Domain\Model\Coupon;
use OpenLoyalty\User\Domain\SystemEvent\CustomerBoughtRewardSystemEvent;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class SpendPointsOnRewardListenerTest extends TestCase
{
    private AccountFacadeInterface|MockObject $accountFacade;
    private TransferExecutorInterface|MockObject $transferExecutor;
    private GeneralSettingsManagerInterface|MockObject $settingsManager;
    private StoreRepository|MockObject $storeRepository;
    private WalletProviderInterface|MockObject $walletProvider;

    protected function setUp(): void
    {
        $this->transferExecutor = $this->getMockBuilder(TransferExecutorInterface::class)->getMock();
        $this->accountFacade = $this->getMockBuilder(AccountFacadeInterface::class)
            ->disableOriginalConstructor()->getMock();
        $this->settingsManager = $this->getMockBuilder(GeneralSettingsManagerInterface::class)->getMock();
        $this->storeRepository = $this->getMockBuilder(StoreRepository::class)->getMock();
        $this->walletProvider = $this->createMock(WalletProviderInterface::class);
    }

    /**
     * @test
     */
    public function it_spends_points_when_customer_bought_reward(): void
    {
        $wallet = $this->createMock(Wallet::class);
        $wallet->method('getWalletId')->willReturn(new AccountId('*************-474c-b092-b0dd880c07f5'));
        $this->storeRepository->method('byId')->willReturn(new Store(new StoreId('*************-474c-b092-b0dd880c07f5'), 'code', 'EUR', ''));
        $this->accountFacade->method('getWallet')->willReturn($wallet);

        $createdAt = new \DateTime();

        $transfer = new Transfer(
            new TransferId('*************-474c-b092-b0dd880c07f5'),
            Transfer::SPEND_TYPE,
            new AccountId('*************-474c-b092-b0dd880c07f5'),
            new StoreId('*************-474c-b092-b0dd880c07f5'),
            10.0,
            \DateTimeImmutable::createFromMutable($createdAt),
            \DateTimeImmutable::createFromMutable($createdAt),
            subType: SubType::BUY_REWARD
        );
        $transfer
            ->comment('test, coupon: 123');

        $this->transferExecutor->expects($this->once())->method('create')->with($transfer);
        $generator = $this->getMockBuilder(UuidGeneratorInterface::class)->getMock();
        $generator->method('generate')->willReturn('*************-474c-b092-b0dd880c07f5');

        $listener = new SpendPointsOnRewardListener(
            $this->transferExecutor,
            new TransferFactory($generator, new ValidityTransferDecorator(), $this->walletProvider),
            $this->accountFacade,
            $this->settingsManager,
            $this->storeRepository
        );
        $listener->__invoke(
            new CustomerBoughtRewardSystemEvent(
                new CustomerId('********-0000-474c-b092-b0dd880c07f5'),
                new StoreId('*************-474c-b092-b0dd880c07f5'),
                new RewardId('*************-474c-b092-b0dd880c07f5'),
                10.0,
                new Coupon('123', 100),
                IssuedReward::ISSUED,
                $createdAt,
                'test'
            )
        );
    }
}
