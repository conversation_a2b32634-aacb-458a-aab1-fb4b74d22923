<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\UnitTransfer\Integration\Ui\Rest\Security;

use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseAccessControlTest;

final class GetTransferListAccessTest extends BaseAccessControlTest
{
    /**
     * @test
     */
    public function only_customer_should_have_access_to_his_transfers(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'not_status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/points');
    }
}
