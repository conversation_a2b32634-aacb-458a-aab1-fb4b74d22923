<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Test\UnitTransfer\Integration\Ui\Rest\Security;

use OpenLoyalty\Points\Infrastructure\DataFixtures\ORM\LoadAccountsWithTransfersData;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseAccessControlTest;

final class PointsTransferControllerAccessTest extends BaseAccessControlTest
{
    /**
     * @test
     */
    public function it_restricts_only_admins_should_have_access_to_all_points_transfer_list(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/points');
    }

    /**
     * @test
     */
    public function it_restricts_only_admins_can_add_points(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/points/add', [], 'POST');
    }

    /**
     * @test
     */
    public function it_restricts_admins_to_spend_points(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/points/spend', [], 'POST');
    }

    /**
     * @test
     */
    public function it_restricts_only_admins_can_cancel_points_transfers(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients(
            $clients,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/points/'.LoadAccountsWithTransfersData::POINTS2_ID.'/cancel',
            [],
            'POST'
        );
    }
}
