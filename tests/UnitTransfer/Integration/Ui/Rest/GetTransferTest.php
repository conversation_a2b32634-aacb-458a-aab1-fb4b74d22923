<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\UnitTransfer\Integration\Ui\Rest;

use OpenLoyalty\Points\Infrastructure\DataFixtures\ORM\LoadAccountsWithTransfersData;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class GetTransferTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_returns_transfer(): void
    {
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/points/'.LoadAccountsWithTransfersData::POINTS_ID,
        );

        $response = $client->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertSame(LoadAccountsWithTransfersData::POINTS_ID, $data['transferId']);
    }
}
