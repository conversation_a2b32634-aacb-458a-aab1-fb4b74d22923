<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Test\UnitTransfer\Integration\Ui\Rest;

use OpenLoyalty\Import\Infrastructure\ImportResultItem;
use OpenLoyalty\Integration\Infrastructure\Utility\Traits\UploadedFileTrait;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class PostTransferImportTest extends AbstractApiTest
{
    use UploadedFileTrait;

    /**
     * @test
     */
    public function it_executes(): void
    {
        $xmlContent = file_get_contents(__DIR__.'/../../Resources/fixtures/import.xml');

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/points/import',
            [],
            [
                'file' => [
                    'file' => $this->createUploadedFile($xmlContent, 'import.xml', 'application/xml', UPLOAD_ERR_OK),
                ],
            ]
        );

        $response = $client->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('items', $data);
        $this->assertCount(2, $data['items']);
        $this->assertArrayHasKey('status', $data['items'][0]);
        $this->assertTrue(ImportResultItem::SUCCESS == $data['items'][0]['status']);
    }
}
