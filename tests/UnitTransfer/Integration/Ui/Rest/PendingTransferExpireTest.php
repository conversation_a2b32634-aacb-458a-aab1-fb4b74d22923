<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\UnitTransfer\Integration\Ui\Rest;

use Carbon\CarbonImmutable;
use OpenLoyalty\Points\Domain\Expiring\ExpiringTransferMode;
use OpenLoyalty\Test\Account\Integration\Traits\WalletTypeApiTrait;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class PendingTransferExpireTest extends AbstractApiTest
{
    use WalletTypeApiTrait;
    use MemberApiTrait;
    use TenantApiTrait;

    private HttpKernelBrowser $client;
    private string $storeCode;
    private string $walletCode;
    private string $memberId;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();

        $this->storeCode = 'pointsExpireTenantCode';
        $this->postTenant($this->client, $this->storeCode);

        $this->walletCode = 'walletExpired';
        $walletResponse = $this->postWalletType(
            $this->client,
            $this->walletCode,
            $this->storeCode,
            unitDaysExpiryAfter: ExpiringTransferMode::EXPIRING_AT_THE_CHOSEN_DATE,
            allTimeNotLocked: false,
            unitDaysLocked: 7,
            unitExpiryDate: '01-03'
        );
        $this->assertOkResponseStatus($walletResponse);

        $memberResponse = $this->postMember($this->client, $this->storeCode);
        $this->assertOkResponseStatus($memberResponse);
        $data = json_decode($memberResponse->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->memberId = $data['customerId'];
    }

    /**
     * @test
     *
     * @description Test activate transfer in case when Expiry Date < Pending Until Date
     */
    public function it_test_activate_transfer_when_expiry_date_in_past(): void
    {
        self::bootKernel();

        CarbonImmutable::setTestNow(CarbonImmutable::parse('2022-01-01 12:00:00'));

        $this->client->request(
            'GET',
            '/api/'.$this->storeCode.'/member/'.$this->memberId.'/wallet?walletType:code='.$this->walletCode
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $wallet = reset($data['items']);

        //Is wallet empty
        $this->assertSame(0.0, $wallet['account']['earnedUnits']);
        $this->assertSame(0.0, $wallet['account']['transferredUnits']);
        $this->assertSame(0.0, $wallet['account']['spentUnits']);
        $this->assertSame(0.0, $wallet['account']['activeUnits']);
        $this->assertSame(0.0, $wallet['account']['lockedUnits']);
        $this->assertSame(0.0, $wallet['account']['blockedUnits']);
        $this->assertSame(0.0, $wallet['account']['expiredUnits']);

        $this->client->request(
            'POST',
            '/api/'.$this->storeCode.'/points/add',
            [
                'transfer' => [
                    'customer' => $this->memberId,
                    'points' => 100.0,
                    'walletCode' => $this->walletCode,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $transferId = $data['transferId'];

        $this->client->request(
            'GET',
            '/api/'.$this->storeCode.'/member/'.$this->memberId.'/wallet?walletType:code='.$this->walletCode
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $wallet = reset($data['items']);

        //Is wallet points locked
        $this->assertSame(0.0, $wallet['account']['earnedUnits']);
        $this->assertSame(0.0, $wallet['account']['transferredUnits']);
        $this->assertSame(0.0, $wallet['account']['spentUnits']);
        $this->assertSame(0.0, $wallet['account']['activeUnits']);
        $this->assertSame(100.0, $wallet['account']['lockedUnits']);
        $this->assertSame(0.0, $wallet['account']['blockedUnits']);
        $this->assertSame(0.0, $wallet['account']['expiredUnits']);

        CarbonImmutable::setTestNow(CarbonImmutable::parse('2022-01-04 12:00:00'));
        $this->client->request(
            'POST',
            '/api/'.$this->storeCode.'/points/'.$transferId.'/activate',
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $this->client->request(
            'GET',
            '/api/'.$this->storeCode.'/member/'.$this->memberId.'/wallet?walletType:code='.$this->walletCode
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $wallet = reset($data['items']);

        //Is wallet points expired
        $this->assertSame(100.0, $wallet['account']['earnedUnits']);
        $this->assertSame(0.0, $wallet['account']['transferredUnits']);
        $this->assertSame(0.0, $wallet['account']['spentUnits']);
        $this->assertSame(0.0, $wallet['account']['activeUnits']);
        $this->assertSame(0.0, $wallet['account']['lockedUnits']);
        $this->assertSame(0.0, $wallet['account']['blockedUnits']);
        $this->assertSame(100.0, $wallet['account']['expiredUnits']);
    }
}
