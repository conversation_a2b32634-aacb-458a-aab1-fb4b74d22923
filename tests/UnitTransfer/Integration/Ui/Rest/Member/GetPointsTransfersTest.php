<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\UnitTransfer\Integration\Ui\Rest\Member;

use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class GetPointsTransfersTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_allows_to_retrieve_transfers_points_data(): void
    {
        $client = $this->createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/points?pointType=earned',
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertIsArray($data);
    }
}
