<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Test\UnitTransfer\Integration\Ui\Rest\Member;

use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\User\Infrastructure\DataFixtures\ORM\LoadUserData;

final class PostTransferTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_transfers_points(): void
    {
        $client = self::createAuthenticatedClient(
            LoadUserData::USER_TRANSFER_3_USERNAME,
            LoadUserData::USER_TRANSFER_3_PASSWORD,
            'member',
            LoadSettingsData::DEFAULT_STORE_CODE
        );
        $senderAccount = $this->getDefaultAccount(LoadUserData::USER_TRANSFER_3_USER_ID);
        $receiverAccount = $this->getDefaultAccount(LoadUserData::USER_TRANSFER_1_USER_ID);
        $senderAmount = $senderAccount['activePoints'];
        $receiverAmount = $receiverAccount['activePoints'];

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/points',
            [
                'transfer' => [
                    'receiver' => LoadUserData::USER_TRANSFER_1_USER_ID,
                    'points' => 100,
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('transferId', $data);
        $senderAccount = $this->getDefaultAccount(LoadUserData::USER_TRANSFER_3_USER_ID);
        $receiverAccount = $this->getDefaultAccount(LoadUserData::USER_TRANSFER_1_USER_ID);

        $this->assertSame($senderAmount - 100, $senderAccount['activePoints']);
        $this->assertSame($receiverAmount + 100, $receiverAccount['activePoints']);
    }

    protected function getDefaultAccount(string $customerId): array
    {
        self::ensureKernelShutdown();
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId,
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('defaultAccount', $data);

        return $data['defaultAccount'];
    }
}
