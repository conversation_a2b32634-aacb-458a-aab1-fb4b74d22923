<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\UnitTransfer\Integration\Ui\Rest\Member;

use OpenLoyalty\Integration\Helpers\ResponseChecker;
use OpenLoyalty\Points\Domain\Expiring\ExpiringTransferMode;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\User\Infrastructure\DataFixtures\ORM\LoadUserData;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class GetTransferListTest extends AbstractApiTest
{
    private HttpKernelBrowser $client;

    /**
     * @test
     */
    public function it_executes(): void
    {
        $client = self::createAuthenticatedClient(
            LoadUserData::USER_USERNAME,
            LoadUserData::USER_PASSWORD,
            'member',
            LoadSettingsData::DEFAULT_STORE_CODE
        );
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/points?_itemsOnPage=50'
        );

        $response = $client->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertGreaterThanOrEqual(1, count($data['items']));
    }

    /**
     * @test
     */
    public function it_filters_member_transfers_by_wallet_type_code(): void
    {
        $client = self::createAuthenticatedClient(
            LoadUserData::USER_USERNAME,
            LoadUserData::USER_PASSWORD,
            'member',
            LoadSettingsData::DEFAULT_STORE_CODE
        );
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/points?walletType:code[eq]=default'
        );

        $response = $client->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertGreaterThanOrEqual(1, count($data['items']));
        foreach ($data['items'] as $item) {
            $this->assertSame('default', $item['walletType']['code']);
        }
    }

    /**
     * @test
     */
    public function it_validates_date_before_min_date_in_query(): void
    {
        $client = self::createAuthenticatedClient(
            LoadUserData::USER_USERNAME,
            LoadUserData::USER_PASSWORD,
            'member',
            LoadSettingsData::DEFAULT_STORE_CODE
        );
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/points?walletType:code[eq]=default&createdAt[lt]=1950-01-01T00:00:00Z'
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        ResponseChecker::assertHasError($data, 'createdAt', 'Date should not be before 1970-01-01');
    }

    /**
     * @test
     */
    public function it_validates_date_after_max_date_in_query(): void
    {
        $client = self::createAuthenticatedClient(
            LoadUserData::USER_USERNAME,
            LoadUserData::USER_PASSWORD,
            'member',
            LoadSettingsData::DEFAULT_STORE_CODE
        );
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/points?walletType:code[eq]=default&createdAt[lt]=2150-01-01T00:00:00Z'
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        ResponseChecker::assertHasError($data, 'createdAt', 'Date should not be after 2100-01-01');
    }

    /**
     * @test
     */
    public function it_returns_transfers_for_all_wallet_types(): void
    {
        self::ensureKernelShutdown();
        $this->createWalletType('carrots', 'carrot', 'carrots');
        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/points/add',
            [
                'transfer' => [
                    'customer' => LoadUserData::USER_USER_ID,
                    'points' => 10,
                    'walletCode' => 'carrots',
                ],
            ]
        );
        self::ensureKernelShutdown();
        $client = self::createAuthenticatedClient(
            LoadUserData::USER_USERNAME,
            LoadUserData::USER_PASSWORD,
            'member',
            LoadSettingsData::DEFAULT_STORE_CODE
        );
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/points?walletType:code[eq]=carrots'
        );

        $response = $client->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertCount(1, $data['items']);
        $this->assertSame('carrots', $data['items'][0]['walletType']['code']);
    }

    private function createWalletType(
        string $code,
        string $singularName,
        string $pluralName,
        string $unitDaysExpiryAfter = ExpiringTransferMode::EXPIRING_ALL_TIME_ACTIVE,
    ): array {
        self::ensureKernelShutdown();
        $this->client = self::createAuthenticatedClient();
        $this->client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/walletType',
            [
                'walletType' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Wallet'.$code,
                            'description' => 'Wallet',
                        ],
                    ],
                    'code' => $code,
                    'unitSingularName' => $singularName,
                    'unitPluralName' => $pluralName,
                    'active' => true,
                    'unitDaysExpiryAfter' => $unitDaysExpiryAfter,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);

        $walletTypeId = $data['walletTypeId'];

        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/walletType/'.$walletTypeId,
        );

        $response = $this->client->getResponse();

        return json_decode($response->getContent(), true);
    }
}
