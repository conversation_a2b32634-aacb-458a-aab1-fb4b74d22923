<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\UnitTransfer\Integration\Ui\Rest\BulkAction;

use Carbon\Carbon;
use Carbon\CarbonImmutable;
use DateTimeInterface;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\UnitTransfer\Integration\Traits\UnitTransferApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class PostBulkActionActivateTransfersTest extends AbstractApiTest
{
    use MemberApiTrait;
    use UnitTransferApiTrait;
    use TenantApiTrait;

    protected HttpKernelBrowser $client;
    protected string $storeCode = 'bulkActionsActivateTransfersTest';

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();

        CarbonImmutable::setTestNow('2050-07-01T01:01:01');
        Carbon::setTestNow('2050-07-01T01:01:01');

        $this->postTenant($this->client, $this->storeCode);
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        CarbonImmutable::setTestNow();
        Carbon::setTestNow();
    }

    /**
     * @test
     */
    public function it_activates_transfers(): void
    {
        $response = $this->postMember($this->client, $this->storeCode, '<EMAIL>');
        $customer = json_decode($response->getContent(), true);
        $customerId = $customer['customerId'];

        for ($i = 0; $i < 3; ++$i) {
            $this->postUnitTransferAdd($this->client, $this->storeCode, customerId: $customerId, points: 5, lockedUntilDays: 20);
        }

        $response = $this->postMember($this->client, $this->storeCode, '<EMAIL>');
        $customer = json_decode($response->getContent(), true);
        $customerId = $customer['customerId'];

        for ($i = 0; $i < 4; ++$i) {
            $this->postUnitTransferAdd($this->client, $this->storeCode, customerId: $customerId, lockedUntilDays: 20);
        }

        $this->client->jsonRequest(
            'POST',
            '/api/'.$this->storeCode.'/bulkAction/activateTransfers',
            [
                'activateTransfers' => [
                    'requestedAt' => (new CarbonImmutable('2050-07-03T01:01:01'))->format(DateTimeInterface::ATOM),
                    'criteria' => [
                        'value' => 5,
                    ],
                ],
            ],
        );

        $this->client->jsonRequest(
            'GET',
            '/api/'.$this->storeCode.'/points'
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);

        foreach ($data['items'] as $transfer) {
            if (5 === $transfer['value']) {
                $this->assertArrayHasKey('unlockedAt', $transfer);
            }
        }
    }

    /**
     * @test
     */
    public function it_does_not_activate_transfer_not_valid_to_activate(): void
    {
        $response = $this->postMember($this->client, $this->storeCode, '<EMAIL>');
        $customer = json_decode($response->getContent(), true);
        $customerId = $customer['customerId'];

        for ($i = 0; $i < 3; ++$i) {
            $this->addCustomerActivePoints($customerId);
            $this->client->request(
                'POST',
                '/api/'.$this->storeCode.'/points/spend',
                [
                    'transfer' => [
                        'customer' => $customerId,
                        'points' => 1,
                    ],
                ]
            );
        }

        for ($i = 0; $i < 3; ++$i) {
            $this->postUnitTransferAdd($this->client, $this->storeCode, customerId: $customerId);
        }

        $this->client->jsonRequest(
            'POST',
            '/api/'.$this->storeCode.'/bulkAction/activateTransfers',
            [
                'activateTransfers' => [
                    'requestedAt' => (new CarbonImmutable('2050-07-03T01:01:01'))->format(DateTimeInterface::ATOM),
                    'criteria' => [
                        'member:email' => '<EMAIL>',
                    ],
                ],
            ],
        );

        $this->client->jsonRequest(
            'GET',
            '/api/'.$this->storeCode.'/points'
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        foreach ($data['items'] as $transfer) {
            if ('adding' === $transfer['type']) {
                $this->assertArrayHasKey('unlockedAt', $transfer);
            } else {
                $this->assertArrayNotHasKey('unlockedAt', $transfer);
            }
        }
    }

    private function addCustomerActivePoints(string $customerId): void
    {
        $this->postUnitTransferAdd($this->client, $this->storeCode, customerId: $customerId);

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->client->request(
            'POST',
            sprintf(
                '/api/%s/points/%s/activate',
                $this->storeCode,
                $data['transferId']
            )
        );
    }
}
