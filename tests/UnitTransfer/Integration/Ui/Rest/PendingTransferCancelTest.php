<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\UnitTransfer\Integration\Ui\Rest;

use OpenLoyalty\Test\Common\Integration\AbstractWebhookApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\UnitTransfer\Integration\Traits\UnitTransferApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;

class PendingTransferCancelTest extends AbstractWebhookApiTest
{
    use TenantApiTrait;
    use UnitTransferApiTrait;
    use MemberApiTrait;

    /**
     * @test
     */
    public function it_check_if_canceling_pending_transfer_does_not_sent_available_amount_change_webhook(): void
    {
        $tenantCode = 'tenant-code';
        $this->postTenant($this->client, $tenantCode);

        $this->createWebhook($tenantCode, 'AvailablePointsAmountChanged');

        $response = $this->postMember($this->client, $tenantCode, email: '<EMAIL>');
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $memberId = $data['customerId'];

        $response = $this->postUnitTransferAdd($this->client, $tenantCode, $memberId, 10);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $transferId = $data['transferId'];

        $activePoints = $this->getCustomerPointsByType($memberId, 'activePoints', $tenantCode);
        $pendingPoints = $this->getCustomerPointsByType($memberId, 'lockedPoints', $tenantCode);

        $this->assertEquals(0, $activePoints);
        $this->assertEquals(10, $pendingPoints);

        $this->postCancelUnitTransfer($this->client, $transferId, $tenantCode);

        //check if any webhook was sent
        $this->assertSame(0, $this->webhookClientMock->getWebhooksCount());

        $activePoints = $this->getCustomerPointsByType($memberId, 'activePoints', $tenantCode);
        $pendingPoints = $this->getCustomerPointsByType($memberId, 'lockedPoints', $tenantCode);

        $this->assertEquals(0, $activePoints);
        $this->assertEquals(0, $pendingPoints);
    }
}
