<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Domain\Level\Progress;

use DateTimeImmutable;
use OpenLoyalty\Level\Domain\Entity\Condition;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgress;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgressCondition;
use OpenLoyalty\Level\Domain\Progress\DateCalculator\MonthCalculatorInterface;
use OpenLoyalty\Level\Domain\Progress\MonthSinceJoinProgramMemberProgressRefresher;
use OpenLoyalty\Level\Domain\TierSet;
use OpenLoyalty\Level\Domain\TierSetMemberProgressRepositoryInterface;
use OpenLoyalty\User\Domain\Customer;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class MonthSinceJoinProgramMemberProgressRefresherTest extends TestCase
{
    private MonthSinceJoinProgramMemberProgressRefresher $refresher;
    private MockObject&TierSetMemberProgressRepositoryInterface $memberProgressRepository;
    private MockObject&MonthCalculatorInterface $monthCalculator;

    protected function setUp(): void
    {
        $this->memberProgressRepository = $this->createMock(TierSetMemberProgressRepositoryInterface::class);
        $this->monthCalculator = $this->createMock(MonthCalculatorInterface::class);

        $this->refresher = new MonthSinceJoinProgramMemberProgressRefresher(
            $this->memberProgressRepository,
            $this->monthCalculator
        );
    }

    /**
     * @test
     */
    public function it_not_update_member_progress_when_there_in_no_conditions_for_month_since_join_program_in_tier_set(): void
    {
        $memberProgress = $this->createMock(TierSetMemberProgress::class);
        $customer = $this->createMock(Customer::class);
        $tierSet = $this->createMock(TierSet::class);

        $memberProgress->expects(self::once())
            ->method('getTierSet')
            ->willReturn($tierSet);

        $tierSet->expects(self::once())
            ->method('getConditionsByAttribute')
            ->willReturn([]);

        $this->memberProgressRepository->expects(self::never())->method('save');

        $this->refresher->refreshProgress($memberProgress, $customer, new DateTimeImmutable(), null);
    }

    /**
     * @test
     */
    public function it_update_member_progress(): void
    {
        $memberProgress = $this->createMock(TierSetMemberProgress::class);
        $customer = $this->createMock(Customer::class);
        $tierSet = $this->createMock(TierSet::class);

        $memberProgress->expects(self::once())
            ->method('getTierSet')
            ->willReturn($tierSet);

        $condition = $this->createMock(Condition::class);

        $tierSet->expects(self::once())
            ->method('getConditionsByAttribute')
            ->willReturn([$condition]);

        $customer->expects(self::once())
            ->method('getRegisteredAt')
            ->willReturn(new DateTimeImmutable());

        $memberProgressValue = $this->createMock(TierSetMemberProgressCondition::class);
        $memberProgress->expects(self::once())
            ->method('getMemberProgressConditionByAttribute')
            ->willReturn($memberProgressValue);

        $memberProgressValue->expects(self::once())
            ->method('setValue');

        $memberProgressValue->expects(self::once())
            ->method('markAsRecreateRequired');

        $this->memberProgressRepository->expects(self::once())
            ->method('persist');

        $this->refresher->refreshProgress($memberProgress, $customer, new DateTimeImmutable(), null);
    }
}
