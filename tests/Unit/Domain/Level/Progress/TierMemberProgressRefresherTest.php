<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Domain\Level\Progress;

use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgress;
use OpenLoyalty\Level\Domain\Progress\EarnedUnitsMemberProgressRefresher;
use OpenLoyalty\Level\Domain\Progress\TierMemberProgressRefresher;
use OpenLoyalty\Level\Domain\Progress\TierMemberProgressRefresherInterface;
use OpenLoyalty\Level\Domain\Progress\TierRefreshTriggerType;
use OpenLoyalty\User\Domain\Customer;
use PHPUnit\Framework\TestCase;

final class TierMemberProgressRefresherTest extends TestCase
{
    /**
     * @test
     */
    public function it_does_not_refresh_when_not_supported(): void
    {
        $triggerType = TierRefreshTriggerType::UnitChange;
        $memberProgress = $this->createMock(TierSetMemberProgress::class);
        $customer = $this->createMock(Customer::class);
        $currentTime = new \DateTimeImmutable();

        $progressRefresher = $this->createMock(TierMemberProgressRefresherInterface::class);
        $progressRefresher->expects($this->never())->method('refreshProgress');

        $progressRefresher->method('supports')->willReturn(false);

        $tierMemberProgressRefresher = new TierMemberProgressRefresher([$progressRefresher]);
        $tierMemberProgressRefresher->refresh($triggerType, $memberProgress, $customer, $currentTime);
    }

    /**
     * @test
     */
    public function it_refresh_when_not_supported_but_trigger_type_is_force(): void
    {
        $triggerType = TierRefreshTriggerType::All;
        $memberProgress = $this->createMock(TierSetMemberProgress::class);
        $customer = $this->createMock(Customer::class);
        $currentTime = new \DateTimeImmutable();

        $progressRefresher1 = $this->createMock(TierMemberProgressRefresherInterface::class);
        $progressRefresher1->expects(self::exactly(1))->method('refreshProgress');
        $progressRefresher2 = $this->createMock(TierMemberProgressRefresherInterface::class);
        $progressRefresher2->expects(self::exactly(1))->method('refreshProgress');

        $progressRefresher1->method('supports')->willReturn(false);
        $progressRefresher2->method('supports')->willReturn(false);

        $tierMemberProgressRefresher = new TierMemberProgressRefresher([$progressRefresher1, $progressRefresher2]);
        $tierMemberProgressRefresher->refresh($triggerType, $memberProgress, $customer, $currentTime);
    }

    /**
     * @test
     */
    public function it_refreshes_when_supported(): void
    {
        $triggerType = TierRefreshTriggerType::UnitChange;
        $memberProgress = $this->createMock(TierSetMemberProgress::class);
        $customer = $this->createMock(Customer::class);
        $currentTime = new \DateTimeImmutable();

        $progressRefresher = $this->createMock(EarnedUnitsMemberProgressRefresher::class);
        $progressRefresher->expects($this->once())->method('refreshProgress');

        $progressRefresher->method('supports')->willReturn(true);

        $tierMemberProgressRefresher = new TierMemberProgressRefresher([$progressRefresher]);
        $tierMemberProgressRefresher->refresh($triggerType, $memberProgress, $customer, $currentTime);
    }
}
