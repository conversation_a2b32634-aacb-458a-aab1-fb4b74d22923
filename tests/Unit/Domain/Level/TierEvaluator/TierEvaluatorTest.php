<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Domain\Level\TierEvaluator;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\TierSetId;
use OpenLoyalty\Level\Domain\CustomerFacadeInterface;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgress;
use OpenLoyalty\Level\Domain\Enum\DispositionTierChangeType;
use OpenLoyalty\Level\Domain\Enum\DowngradeMode;
use OpenLoyalty\Level\Domain\Factory\TierSetMemberProgressFactoryInterface;
use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Domain\LevelRepository;
use OpenLoyalty\Level\Domain\Progress\ResetCumulatedConditionProgressInterface;
use OpenLoyalty\Level\Domain\Progress\TierMemberProgressRefresher;
use OpenLoyalty\Level\Domain\Progress\TierRefreshTriggerType;
use OpenLoyalty\Level\Domain\TierCalculator\TierCalculator;
use OpenLoyalty\Level\Domain\TierChanger\TierChangerInterface;
use OpenLoyalty\Level\Domain\TierEvaluator\TierEvaluator;
use OpenLoyalty\Level\Domain\TierSet;
use OpenLoyalty\Level\Domain\TierSetMemberProgressRepositoryInterface;
use OpenLoyalty\Level\Domain\TierSetRepositoryInterface;
use OpenLoyalty\Level\Domain\ValueObject\Downgrade;
use OpenLoyalty\User\Domain\Customer;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use function PHPUnit\Framework\once;

final class TierEvaluatorTest extends TestCase
{
    private MockObject&TierSetMemberProgressRepositoryInterface $memberProgressRepository;
    private MockObject&TierMemberProgressRefresher $progressRefresher;
    private MockObject&TierCalculator $tierCalculator;
    private MockObject&LoggerInterface $tierLogger;
    private MockObject&CustomerFacadeInterface $customerFacade;
    private MockObject&TierSetRepositoryInterface $tierSetRepository;
    private TierEvaluator $tierEvaluator;
    private MockObject&TierSetMemberProgressFactoryInterface $memberProgressFactory;
    private MockObject&TierChangerInterface $tierChanger;

    private LevelRepository $levelRepository;
    private ResetCumulatedConditionProgressInterface $resetCumulatedConditionProgress;

    protected function setUp(): void
    {
        $this->memberProgressRepository = $this->createMock(TierSetMemberProgressRepositoryInterface::class);
        $this->progressRefresher = $this->createMock(TierMemberProgressRefresher::class);
        $this->tierCalculator = $this->createMock(TierCalculator::class);
        $this->tierLogger = $this->createMock(LoggerInterface::class);
        $this->tierSetRepository = $this->createMock(TierSetRepositoryInterface::class);
        $this->customerFacade = $this->createMock(CustomerFacadeInterface::class);
        $this->memberProgressFactory = $this->createMock(TierSetMemberProgressFactoryInterface::class);
        $this->tierChanger = $this->createMock(TierChangerInterface::class);
        $this->levelRepository = $this->createMock(LevelRepository::class);
        $this->resetCumulatedConditionProgress = $this->createMock(ResetCumulatedConditionProgressInterface::class);

        $this->tierEvaluator = new TierEvaluator(
            $this->memberProgressRepository,
            $this->progressRefresher,
            $this->tierCalculator,
            $this->tierLogger,
            $this->customerFacade,
            $this->tierSetRepository,
            $this->memberProgressFactory,
            $this->tierChanger,
            $this->levelRepository,
            $this->resetCumulatedConditionProgress
        );
    }

    /**
     * @test
     */
    public function it_not_change_member_tier_if_new_level_is_null(): void
    {
        $customer = $this->createMock(Customer::class);
        $customerId = $this->createMock(CustomerId::class);
        $customer->method('getCustomerId')->willReturn($customerId);
        $customer->method('isActive')->willReturn(true);

        $downgrade = $this->createMock(Downgrade::class);
        $downgrade->expects(self::once())
            ->method('getMode')
            ->willReturn(DowngradeMode::NoDowngrade);

        $tierSet = $this->createMock(TierSet::class);
        $tierSet->expects(self::once())
            ->method('getDowngrade')
            ->willReturn($downgrade);
        $this->tierSetRepository->expects(once())->method('byId')->willReturn($tierSet);

        $this->customerFacade->method('getActiveCustomer')->willReturn($customer);

        $memberProgress = $this->createMock(TierSetMemberProgress::class);
        $memberProgress->expects(self::once())->method('getCurrentLevelId')
            ->willReturn(new LevelId('70dccbd6-4313-4424-8506-112f031ce8c6'));

        $tierSetId = $this->createMock(TierSetId::class);
        $currentTime = new DateTimeImmutable();
        $triggerType = TierRefreshTriggerType::All;

        $this->memberProgressRepository->method('getMemberProgressByTierSet')
            ->willReturn($memberProgress);

        $this->tierCalculator->method('calculateTierForMember')
            ->willReturn(null);

        $this->tierLogger->expects(self::once())
            ->method('info');

        $this->tierChanger->expects(self::once())
            ->method('changeAutomatically');

        $this->tierEvaluator->evaluate(
            $customerId,
            $tierSetId,
            $currentTime,
            $triggerType,
            DispositionTierChangeType::Default,
            null
        );
    }

    /**
     * @test
     */
    public function it_reset_cumulated_condition_progress_if_disposition_type_is_downgrade(): void
    {
        $customer = $this->createMock(Customer::class);
        $customerId = $this->createMock(CustomerId::class);
        $customer->method('getCustomerId')->willReturn($customerId);
        $customer->method('isActive')->willReturn(true);

        $downgrade = $this->createMock(Downgrade::class);
        $downgrade->expects(self::once())
            ->method('getMode')
            ->willReturn(DowngradeMode::PeriodicDowngrade);

        $tierSet = $this->createMock(TierSet::class);
        $tierSet->expects(self::once())
            ->method('getDowngrade')
            ->willReturn($downgrade);
        $this->tierSetRepository->expects(once())->method('byId')->willReturn($tierSet);

        $this->customerFacade->method('getActiveCustomer')->willReturn($customer);

        $memberProgress = $this->createMock(TierSetMemberProgress::class);
        $memberProgress->expects(self::once())->method('getCurrentLevelId')
            ->willReturn(new LevelId('70dccbd6-4313-4424-8506-112f031ce8c6'));

        $tierSetId = $this->createMock(TierSetId::class);
        $currentTime = new DateTimeImmutable();
        $triggerType = TierRefreshTriggerType::All;

        $this->memberProgressRepository->method('getMemberProgressByTierSet')
            ->willReturn($memberProgress);

        $newTier = $this->createMock(Level::class);

        $this->tierCalculator->expects($this->once())->method('calculateTierForMember')
            ->willReturn($newTier);

        $this->tierLogger->expects(self::once())
            ->method('info');

        $this->tierChanger->expects(self::once())
            ->method('changeAutomatically');

        $this->resetCumulatedConditionProgress->expects($this->once())->method('reset');

        $this->tierEvaluator->evaluate(
            $customerId,
            $tierSetId,
            $currentTime,
            $triggerType,
            DispositionTierChangeType::OnlyDowngrade,
            null
        );
    }

    /**
     * @test
     */
    public function it_does_not_reset_cumulated_condition_progress_if_disposition_type_is_default(): void
    {
        $customer = $this->createMock(Customer::class);
        $customerId = $this->createMock(CustomerId::class);
        $customer->method('getCustomerId')->willReturn($customerId);
        $customer->method('isActive')->willReturn(true);

        $downgrade = $this->createMock(Downgrade::class);
        $downgrade->expects(self::once())
            ->method('getMode')
            ->willReturn(DowngradeMode::PeriodicDowngrade);

        $tierSet = $this->createMock(TierSet::class);
        $tierSet->expects(self::once())
            ->method('getDowngrade')
            ->willReturn($downgrade);
        $this->tierSetRepository->expects(once())->method('byId')->willReturn($tierSet);

        $this->customerFacade->method('getActiveCustomer')->willReturn($customer);

        $memberProgress = $this->createMock(TierSetMemberProgress::class);
        $memberProgress->expects(self::once())->method('getCurrentLevelId')
            ->willReturn(new LevelId('70dccbd6-4313-4424-8506-112f031ce8c6'));

        $tierSetId = $this->createMock(TierSetId::class);
        $currentTime = new DateTimeImmutable();
        $triggerType = TierRefreshTriggerType::All;

        $this->memberProgressRepository->method('getMemberProgressByTierSet')
            ->willReturn($memberProgress);

        $newTier = $this->createMock(Level::class);

        $this->tierCalculator->expects($this->once())->method('calculateTierForMember')
            ->willReturn($newTier);

        $this->tierLogger->expects(self::once())
            ->method('info');

        $this->tierChanger->expects(self::once())
            ->method('changeAutomatically');

        $this->resetCumulatedConditionProgress->expects($this->never())->method('reset');

        $this->tierEvaluator->evaluate(
            $customerId,
            $tierSetId,
            $currentTime,
            $triggerType,
            DispositionTierChangeType::Default,
            null
        );
    }

    /**
     * @test
     */
    public function it_does_not_change_member_tier_if_tier_not_changed(): void
    {
        $customer = $this->createMock(Customer::class);
        $customerId = $this->createMock(CustomerId::class);
        $customer->method('getCustomerId')->willReturn($customerId);
        $customer->method('isActive')->willReturn(true);

        $downgrade = $this->createMock(Downgrade::class);
        $downgrade->expects(self::once())
            ->method('getMode')
            ->willReturn(DowngradeMode::NoDowngrade);

        $tierSet = $this->createMock(TierSet::class);
        $tierSet->expects(self::once())
            ->method('getDowngrade')
            ->willReturn($downgrade);
        $this->tierSetRepository->expects(once())->method('byId')->willReturn($tierSet);

        $this->customerFacade->method('getActiveCustomer')->willReturn($customer);

        $memberProgress = $this->createMock(TierSetMemberProgress::class);
        $memberProgress->expects(self::once())->method('getCurrentLevelId')
            ->willReturn(new LevelId('70dccbd6-4313-4424-8506-112f031ce8c6'));

        $tierSetId = $this->createMock(TierSetId::class);
        $currentTime = new DateTimeImmutable();
        $triggerType = TierRefreshTriggerType::All;

        $this->memberProgressRepository->method('getMemberProgressByTierSet')
            ->willReturn($memberProgress);

        $newTier = $this->createMock(Level::class);
        $newTier->expects(self::exactly(1))
            ->method('getLevelId')
            ->willReturn(new LevelId('70dccbd6-4313-4424-8506-112f031ce8c6'));
        $this->tierCalculator->method('calculateTierForMember')
            ->willReturn($newTier);

        $this->tierLogger->expects(self::once())
            ->method('info');

        $this->tierChanger->expects(self::once())
            ->method('changeAutomatically');

        $this->tierEvaluator->evaluate(
            $customerId,
            $tierSetId,
            $currentTime,
            $triggerType,
            DispositionTierChangeType::Default,
            null
        );
    }

    /**
     * @test
     */
    public function it_create_member_progress_and_calculate_if_member_progress_does_not_exist(): void
    {
        $customer = $this->createMock(Customer::class);
        $customerId = $this->createMock(CustomerId::class);
        $customer->method('getCustomerId')->willReturn($customerId);
        $customer->method('getLevelId')->willReturn(new LevelId('70dccbd6-4313-4424-8506-112f031ce8c6'));
        $customer->method('isActive')->willReturn(true);
        $memberProgress = $this->createMock(TierSetMemberProgress::class);

        $this->customerFacade->method('getActiveCustomer')->willReturn($customer);

        $this->memberProgressFactory->expects(self::once())
            ->method('create')
            ->willReturn($memberProgress);

        $memberProgress->method('getCurrentLevelId')
            ->willReturn(new LevelId('70dccbd6-4313-4424-8506-112f031ce8c6'));

        $currentTime = new DateTimeImmutable();
        $triggerType = TierRefreshTriggerType::UnitChange;

        $this->memberProgressRepository->method('getMemberProgressByTierSet')
            ->willReturn(null);

        $downgrade = $this->createMock(Downgrade::class);
        $downgrade->expects(self::once())
            ->method('getMode')
            ->willReturn(DowngradeMode::NoDowngrade);

        $tierSet = $this->createMock(TierSet::class);
        $tierSet->method('getTierSetId')->willReturn(new TierSetId('00000000-0000-aaaa-0000-000000000000'));
        $tierSet->expects(self::once())
            ->method('getDowngrade')
            ->willReturn($downgrade);
        $this->tierSetRepository->expects(self::once())->method('byId')->willReturn($tierSet);

        $defaultTier = $this->createMock(Level::class);
        $defaultTier->method('getTierSet')->willReturn($tierSet);
        $this->levelRepository->method('byId')->willReturn($defaultTier);

        $this->progressRefresher->expects(self::once())->method('refresh');

        $this->tierCalculator->method('calculateTierForMember')
            ->willReturn($this->createMock(Level::class));

        $this->tierLogger->expects(self::exactly(1))
            ->method('info');

        $this->tierChanger->expects(self::once())
            ->method('changeAutomatically');

        $this->tierEvaluator->evaluate(
            $customerId,
            new TierSetId('00000000-0000-aaaa-0000-000000000000'),
            $currentTime,
            $triggerType,
            DispositionTierChangeType::Default,
            null
        );
    }

    /**
     * @test
     */
    public function it_evaluate_tier_for_member(): void
    {
        $customer = $this->createMock(Customer::class);
        $customerId = $this->createMock(CustomerId::class);
        $customer->method('getCustomerId')->willReturn($customerId);
        $customer->method('isActive')->willReturn(true);

        $this->customerFacade->method('getActiveCustomer')->willReturn($customer);

        $memberProgress = $this->createMock(TierSetMemberProgress::class);
        $memberProgress->expects(self::once())->method('getCurrentLevelId')
            ->willReturn(new LevelId('70dccbd6-4313-4424-8506-112f031ce8c6'));

        $this->memberProgressRepository->method('getMemberProgressByTierSet')->willReturn($memberProgress);

        $downgrade = $this->createMock(Downgrade::class);
        $downgrade->expects(self::once())
            ->method('getMode')
            ->willReturn(DowngradeMode::NoDowngrade);

        $tierSet = $this->createMock(TierSet::class);
        $tierSet->expects(self::once())
            ->method('getDowngrade')
            ->willReturn($downgrade);
        $this->tierSetRepository->expects(once())->method('byId')->willReturn($tierSet);
        $newTier = $this->createMock(Level::class);

        $tierSetId = $this->createMock(TierSetId::class);
        $currentTime = new DateTimeImmutable();
        $triggerType = TierRefreshTriggerType::UnitChange;

        $this->progressRefresher->expects(self::once())->method('refresh');

        $this->tierCalculator->method('calculateTierForMember')
            ->willReturn($this->createMock(Level::class));

        $this->tierLogger->expects(self::exactly(1))
            ->method('info');

        $this->tierCalculator->expects(self::once())
            ->method('calculateTierForMember');
        $this->tierChanger->expects(self::once())
            ->method('changeAutomatically');

        $this->tierEvaluator->evaluate(
            $customerId,
            $tierSetId,
            $currentTime,
            $triggerType,
            DispositionTierChangeType::OnlyDowngrade,
            null
        );
    }

    /**
     * @test
     */
    public function it_stops_evaluate_when_tier_id_does_not_belong_to_default_tier_set(): void
    {
        $customer = $this->createMock(Customer::class);
        $customerId = $this->createMock(CustomerId::class);
        $customer->method('getCustomerId')->willReturn($customerId);
        $customer->method('getLevelId')->willReturn(new LevelId('70dccbd6-4313-4424-8506-112f031ce8c6'));
        $customer->method('isActive')->willReturn(true);
        $memberProgress = $this->createMock(TierSetMemberProgress::class);

        $this->customerFacade->method('getActiveCustomer')->willReturn($customer);

        $this->memberProgressFactory->expects(self::never())
            ->method('create')
            ->willReturn($memberProgress);

        $memberProgress->method('getCurrentLevelId')
            ->willReturn(new LevelId('70dccbd6-4313-4424-8506-112f031ce8c6'));

        $tierSetId = new TierSetId('00000000-0000-aaaa-0000-000000000000');
        $currentTime = new DateTimeImmutable();
        $triggerType = TierRefreshTriggerType::UnitChange;

        $this->memberProgressRepository->method('getMemberProgressByTierSet')
            ->willReturn(null);

        $tierSet = $this->createMock(TierSet::class);
        $tierSet->method('getTierSetId')->willReturn(new TierSetId('00000000-0000-bbbb-0000-000000000000'));
        $tierSet->method('isDefault')->willReturn(true);

        $this->tierSetRepository->expects(self::once())->method('byId')->willReturn($tierSet);

        $defaultTier = $this->createMock(Level::class);
        $defaultTier->method('getTierSet')->willReturn($tierSet);
        $this->levelRepository->method('byId')->willReturn($defaultTier);

        $this->progressRefresher->expects(self::never())->method('refresh');

        $this->tierEvaluator->evaluate(
            $customerId,
            $tierSetId,
            $currentTime,
            $triggerType,
            DispositionTierChangeType::Default,
            null
        );
    }
}
