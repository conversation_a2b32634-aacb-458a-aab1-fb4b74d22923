<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Unit\Domain\Level\TierEvaluator;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\TierSetId;
use OpenLoyalty\Level\Domain\CustomerFacadeInterface;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgress;
use OpenLoyalty\Level\Domain\Factory\TierSetMemberProgressFactoryInterface;
use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Domain\LevelRepository;
use OpenLoyalty\Level\Domain\TierCalculator\AutomaticDowngradeTierCalculator;
use OpenLoyalty\Level\Domain\TierChanger\TierChangerInterface;
use OpenLoyalty\Level\Domain\TierEvaluator\DefaultTierInitializer;
use OpenLoyalty\Level\Domain\TierSet;
use OpenLoyalty\Level\Domain\TierSetMemberProgressRepositoryInterface;
use OpenLoyalty\Level\Domain\TierSetRepositoryInterface;
use OpenLoyalty\User\Domain\Customer;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class InitializeDefaultTierTest extends TestCase
{
    private TierSetMemberProgressRepositoryInterface $memberProgressRepository;
    private LoggerInterface $tierLogger;

    private CustomerFacadeInterface $customerFacade;

    private TierSetRepositoryInterface $tierSetRepository;
    private LevelRepository $tierRepository;

    private TierSetMemberProgressFactoryInterface $memberProgressFactory;
    private TierChangerInterface $tierChanger;

    private AutomaticDowngradeTierCalculator $tierCalculator;

    private DefaultTierInitializer $initializeDefaultTier;

    protected function setUp(): void
    {
        $this->memberProgressRepository = $this->createMock(TierSetMemberProgressRepositoryInterface::class);
        $this->tierLogger = $this->createMock(LoggerInterface::class);
        $this->tierSetRepository = $this->createMock(TierSetRepositoryInterface::class);
        $this->tierRepository = $this->createMock(LevelRepository::class);
        $this->customerFacade = $this->createMock(CustomerFacadeInterface::class);
        $this->memberProgressFactory = $this->createMock(TierSetMemberProgressFactoryInterface::class);
        $this->tierChanger = $this->createMock(TierChangerInterface::class);

        $this->initializeDefaultTier = new DefaultTierInitializer(
            $this->memberProgressRepository,
            $this->tierSetRepository,
            $this->customerFacade,
            $this->tierRepository,
            $this->memberProgressFactory,
            $this->tierChanger,
        );
    }

    /**
     * @test
     */
    public function it_create_member_progress_and_add_member_to_default_tier(): void
    {
        $customer = $this->createMock(Customer::class);
        $customerId = $this->createMock(CustomerId::class);
        $customer->method('getCustomerId')->willReturn($customerId);
        $customer->method('isActive')->willReturn(true);
        $memberProgress = $this->createMock(TierSetMemberProgress::class);
        $tierSet = $this->createMock(TierSet::class);
        $tierSetId = $this->createMock(TierSetId::class);

        $tierSet->method('getTierSetId')->willReturn($tierSetId);

        $this->tierSetRepository->expects(self::once())
            ->method('byId')
            ->willReturn($tierSet);

        $this->customerFacade->method('getCustomer')->willReturn($customer);

        $this->memberProgressRepository->method('getMemberProgressByTierSet')
            ->willReturn(null);

        $this->memberProgressFactory->expects(self::once())
            ->method('create')
            ->willReturn($memberProgress);

        $this->tierRepository->expects(self::once())
            ->method('findDefault')
            ->willReturn($this->createMock(Level::class));

        $this->tierChanger->expects(self::once())
            ->method('changeAutomatically');

        $this->memberProgressRepository->expects(self::once())
            ->method('persist');

        $this->initializeDefaultTier->initialize(
            $customerId,
            $tierSetId,
            null,
            new \DateTimeImmutable(),
        );
    }
}
