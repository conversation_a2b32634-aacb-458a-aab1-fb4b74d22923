<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Domain\Reward\Webhook\DataProvider;

use OpenLoyalty\Core\Domain\Exception\StoreNotFoundException;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\IssuedRewardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\Webhook\Factory\MemberResponseFactoryInterface;
use OpenLoyalty\Core\Domain\Webhook\Response\Customer;
use OpenLoyalty\Core\Domain\Webhook\Response\Customer as WebhookCustomer;
use OpenLoyalty\Core\Domain\Webhook\Response\WebhookData;
use OpenLoyalty\Reward\Domain\IssuedReward;
use OpenLoyalty\Reward\Domain\Repository\IssuedRewardRepositoryInterface;
use OpenLoyalty\Reward\Domain\SystemEvent\CouponWillExpire;
use OpenLoyalty\Reward\Domain\ValueObject\IssuedCoupon;
use OpenLoyalty\Reward\Domain\Webhook\DataProvider\CouponWillExpireDataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class CouponWillExpireDataProviderTest extends TestCase
{
    private MockObject&IssuedRewardRepositoryInterface $issuedRewardRepository;
    private MockObject&MemberResponseFactoryInterface $memberResponseFactory;
    private MockObject&StoreRepository $storeRepository;
    private CouponWillExpireDataProvider $dataProvider;

    protected function setUp(): void
    {
        $this->issuedRewardRepository = $this->createMock(IssuedRewardRepositoryInterface::class);
        $this->storeRepository = $this->createMock(StoreRepository::class);
        $this->memberResponseFactory = $this->createMock(MemberResponseFactoryInterface::class);
        $this->dataProvider = new CouponWillExpireDataProvider(
            $this->issuedRewardRepository,
            $this->storeRepository,
            $this->memberResponseFactory
        );
    }

    /**
     * @test
     */
    public function it_returns_null_while_not_found_issued_reward(): void
    {
        $issuedRewardId = new IssuedRewardId('00000000-0000-0000-0000-000000000001');
        $storeId = new StoreId('00000000-0000-0000-0000-000000000002');
        $event = new CouponWillExpire($issuedRewardId, $storeId);

        $this->assertEqualsWithDelta(null, $this->dataProvider->getData($event), 0.01);
    }

    /**
     * @test
     */
    public function it_returns_null_while_not_found_issued_coupon(): void
    {
        $issuedRewardId = new IssuedRewardId('00000000-0000-0000-0000-000000000001');
        $storeId = new StoreId('00000000-0000-0000-0000-000000000002');
        $event = new CouponWillExpire($issuedRewardId, $storeId);

        $issuedReward = $this->createMock(IssuedReward::class);
        $this->issuedRewardRepository->method('byId')->willReturn($issuedReward);
        $issuedReward->method('getIssuedCoupon')->willReturn(null);

        $this->assertEqualsWithDelta(null, $this->dataProvider->getData($event), 0.01);
    }

    /**
     * @test
     */
    public function it_throws_exception_while_not_found_store(): void
    {
        $issuedRewardId = new IssuedRewardId('00000000-0000-0000-0000-000000000001');
        $storeId = new StoreId('00000000-0000-0000-0000-000000000002');
        $event = new CouponWillExpire($issuedRewardId, $storeId);

        $issuedReward = $this->createMock(IssuedReward::class);
        $this->issuedRewardRepository->method('byId')->willReturn($issuedReward);
        $issuedCoupon = $this->createMock(IssuedCoupon::class);
        $issuedReward->method('getIssuedCoupon')->willReturn($issuedCoupon);

        $customerId = new CustomerId('00000000-0000-0000-0000-000000000001');
        $member = new WebhookCustomer($customerId, '<EMAIL>', '<EMAIL>', '000000000');
        $this->memberResponseFactory->method('create')->willReturn($member);
        $this->storeRepository->method('byId')->willReturn(null);

        $this->expectException(StoreNotFoundException::class);

        $this->dataProvider->getData($event);
    }

    /**
     * @test
     */
    public function it_returns_webhook_data(): void
    {
        $issuedRewardId = new IssuedRewardId('00000000-0000-0000-0000-000000000001');
        $storeId = new StoreId('00000000-0000-0000-0000-000000000002');
        $event = new CouponWillExpire($issuedRewardId, $storeId);

        $customerId = new CustomerId('00000000-0000-0000-0000-000000000003');
        $issuedReward = $this->createMock(IssuedReward::class);
        $this->issuedRewardRepository->method('byId')->willReturn($issuedReward);
        $issuedCoupon = $this->createMock(IssuedCoupon::class);
        $issuedReward->method('getIssuedCoupon')->willReturn($issuedCoupon);
        $issuedReward->method('getCustomerId')->willReturn($customerId);

        $this->memberResponseFactory->method('create')->willReturn(
            new Customer(
                $customerId,
                '<EMAIL>',
                '+48000000000',
                '000000000'
            )
        );
        $store = $this->createMock(Store::class);
        $this->storeRepository->method('byId')->willReturn($store);
        $store->method('getCode')->willReturn('DEFAULT');

        $webhookData = new WebhookData(
            'CouponWillExpire',
            'DEFAULT',
            [
                'customer' => new WebhookCustomer(
                    $customerId,
                    '<EMAIL>',
                    '+48000000000',
                    '000000000'
                ),
                'issuedCoupon' => $issuedCoupon,
            ]
        );

        $this->assertEqualsWithDelta($webhookData, $this->dataProvider->getData($event), 0.01);
    }
}
