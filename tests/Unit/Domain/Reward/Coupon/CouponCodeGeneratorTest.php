<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Domain\Reward\Coupon;

use OpenLoyalty\Core\Domain\Code\CodeGenerator;
use PHPUnit\Framework\TestCase;

final class CouponCodeGeneratorTest extends TestCase
{
    /**
     * @test
     */
    public function it_generates_numeric_coupons(): void
    {
        $generator = new CodeGenerator(8, CodeGenerator::NUM);

        $code = $generator->generate();

        $this->assertStringMatchesFormat('%d', $code);
        $this->assertSame(8, strlen($code));
    }

    /**
     * @test
     */
    public function it_generates_alpha_coupons(): void
    {
        $generator = new CodeGenerator(8, CodeGenerator::ALPHA);

        $code = $generator->generate();

        $this->assertSame(8, strlen($code), 'Code should contain 8 characters');
        $this->assertTrue((bool) \preg_match('/^[A-Z]{8}$/i', $code), 'Alpha code should not contain any characters outside of A-Z range');
    }

    /**
     * @test
     */
    public function it_generates_alphanum_coupons(): void
    {
        $generator = new CodeGenerator(8, CodeGenerator::ALPHANUM);

        $code = $generator->generate();

        $this->assertSame(8, strlen($code), 'Code should contain 8 characters');
        $this->assertTrue((bool) \preg_match('/^[A-Z0-9]{8}$/i', $code), 'Alpha code should not contain any non-digit, non-alpha characters');
    }

    /**
     * @test
     */
    public function it_generates_alphanum_coupons_with_prefix(): void
    {
        $generator = new CodeGenerator(8, CodeGenerator::ALPHANUM, 'PROMO%s');

        $code = $generator->generate();

        $this->assertSame(13, strlen($code), 'Code should contain 8 characters');
        $this->assertTrue((bool) \preg_match('/^PROMO[A-Z0-9]{8}$/i', $code), 'Alpha code should start with PROMO, and not contain any non-digit, non-alpha characters');
    }

    /**
     * @test
     */
    public function it_generates_alphanum_coupons_with_suffix(): void
    {
        $generator = new CodeGenerator(8, CodeGenerator::ALPHANUM, '%s20PL');

        $code = $generator->generate();

        $this->assertSame(12, strlen($code), 'Code should contain 8 characters');
        $this->assertTrue((bool) \preg_match('/^[A-Z0-9]{8}20PL$/i', $code), 'Alpha code should end with 20PL, and not contain any non-digit, non-alpha characters');
    }

    /**
     * @test
     */
    public function it_generates_num_coupons_with_alphanum_prefix(): void
    {
        $generator = new CodeGenerator(8, CodeGenerator::NUM, 'PROMO%s');

        $code = $generator->generate();

        $this->assertSame(13, strlen($code), 'Code should contain 8 characters');
        $this->assertTrue((bool) \preg_match('/^PROMO\d{8}$/i', $code), 'Alpha code should start with PROMO, and not contain any other non-digit characters');
    }
}
