<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Unit\Domain\InternalEvent;

use OpenLoyalty\CustomEvent\Domain\ValueObject\Schema;
use OpenLoyalty\InternalEvent\Domain\InternalEventSchema;
use OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class InternalEventSchemaRepositoryTest extends KernelTestCase
{
    private \OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepository $internalEventSchemaRepository;

    protected function setUp(): void
    {
        $this->internalEventSchemaRepository = new InternalEventSchemaRepository();
    }

    /**
     * @test
     */
    public function it_loads_all_schemas(): void
    {
        $schemas = $this->internalEventSchemaRepository->findAll();
        $this->assertNotNull($schemas);
        $this->assertCount(6, $schemas);
    }

    /**
     * @test
     * @dataProvider validInternalEventSchemas
     */
    public function it_loads_schema_by_type(string $internalEventSchema): void
    {
        $schema = $this->internalEventSchemaRepository->findByType($internalEventSchema);
        $this->assertNotNull($schema);
        $this->assertInstanceOf(InternalEventSchema::class, $schema);
        if ('MemberTierChanged' === $internalEventSchema) {
            $this->assertEquals(
                new Schema(['fields' => [
                    [
                        'name' => 'tierMoveUp',
                        'type' => Schema::BOOLEAN_FIELD_TYPE,
                    ],
                    [
                        'name' => 'tierMoveDown',
                        'type' => Schema::BOOLEAN_FIELD_TYPE,
                    ],
                ]]),
                $schema->getSchema()
            );
        }
    }

    /**
     * @test
     */
    public function it_return_null_when_schema_not_found_by_type(): void
    {
        $result = $this->internalEventSchemaRepository->findByType('notExistsEventSchema');
        $this->assertNull($result);
    }

    public function validInternalEventSchemas(): array
    {
        return [
            ['MemberWasActivated'],
            ['MemberTierChanged'],
            ['MemberDetailsChanged'],
            ['MemberAddressChanged'],
            ['MemberCompanyDetailsChanged'],
        ];
    }
}
