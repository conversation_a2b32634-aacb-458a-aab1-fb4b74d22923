<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Domain\Messaging\Webhook\Event\Listener;

use OpenLoyalty\Core\Domain\Message\JobBusInterface;
use OpenLoyalty\Core\Domain\Webhook\DataProvider\WebhookDataProviderInterface;
use OpenLoyalty\Core\Domain\Webhook\Event\WebhookEventInterface;
use OpenLoyalty\Messaging\Application\Webhook\Job\DispatchWebhookEvent;
use OpenLoyalty\Messaging\Domain\CachedWebhookSubscriptionRepositoryInterface;
use OpenLoyalty\Messaging\Domain\Webhook\Event\Listener\WebhookEventListener;
use OpenLoyalty\Messaging\Domain\Webhook\WebhookDataProviderRegistryInterface;
use OpenLoyalty\Messaging\Infrastructure\Webhook\WebhookEventNameResolver;
use PHPUnit\Framework\TestCase;

final class WebhookEventListenerTest extends TestCase
{
    /**
     * @test
     */
    public function it_not_dispatch_webhook_event_if_no_provider_is_registered(): void
    {
        $dataProviderRegistry = $this->createMock(WebhookDataProviderRegistryInterface::class);
        $subscriptionRepository = $this->createMock(CachedWebhookSubscriptionRepositoryInterface::class);
        $jobBus = $this->createMock(JobBusInterface::class);

        $dataProviderRegistry->method('getProviders')->willReturn([]);

        $listener = new WebhookEventListener(
            new WebhookEventNameResolver(),
            $dataProviderRegistry,
            $subscriptionRepository,
            $jobBus
        );
        $event = $this->createMock(WebhookEventInterface::class);

        $subscriptionRepository->expects($this->never())->method('countByEventNames');

        $listener->__invoke($event);
    }

    /**
     * @test
     */
    public function it_not_dispatch_webhook_event_if_no_subscription_is_registered(): void
    {
        $dataProviderRegistry = $this->createMock(WebhookDataProviderRegistryInterface::class);
        $subscriptionRepository = $this->createMock(CachedWebhookSubscriptionRepositoryInterface::class);
        $jobBus = $this->createMock(JobBusInterface::class);
        $dataProvider = $this->createMock(WebhookDataProviderInterface::class);

        $dataProviderRegistry->method('getProviders')->willReturn([$dataProvider]);
        $subscriptionRepository->method('countByEventNames')->willReturn(0);

        $listener = new WebhookEventListener(
            new WebhookEventNameResolver(),
            $dataProviderRegistry,
            $subscriptionRepository,
            $jobBus);
        $event = $this->createMock(WebhookEventInterface::class);

        $jobBus->expects($this->never())->method('dispatch');

        $listener->__invoke($event);
    }

    /**
     * @test
     */
    public function it_dispatch_webhook_event(): void
    {
        $dataProviderRegistry = $this->createMock(WebhookDataProviderRegistryInterface::class);
        $subscriptionRepository = $this->createMock(CachedWebhookSubscriptionRepositoryInterface::class);
        $jobBus = $this->createMock(JobBusInterface::class);
        $dataProvider = $this->createMock(WebhookDataProviderInterface::class);

        $dataProviderRegistry->method('getProviders')->willReturn([$dataProvider]);
        $subscriptionRepository->method('countByEventNames')->willReturn(1);

        $listener = new WebhookEventListener(
            new WebhookEventNameResolver(),
            $dataProviderRegistry,
            $subscriptionRepository,
            $jobBus
        );
        $event = $this->createMock(WebhookEventInterface::class);

        $jobBus->expects($this->once())->method('dispatch')->with(new DispatchWebhookEvent($event));

        $listener->__invoke($event);
    }
}
