<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Unit\Infrastructure\Reward\Security\Voter;

use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Reward\Domain\Provider\RewardVisibilityProvider;
use OpenLoyalty\Reward\Domain\Reward;
use OpenLoyalty\Reward\Infrastructure\Security\Voter\RewardVoter;
use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseVoterTest;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionEvaluator;
use PHPUnit\Framework\MockObject\MockObject;

final class RewardVoterTest extends BaseVoterTest
{
    private const REWARD_ID = '00000000-0000-474c-b092-b0dd880c0700';
    private const REWARD2_ID = '00000000-0000-474c-b092-b0dd880c0702';

    /**
     * @test
     */
    public function it_works(): void
    {
        /** @var RewardVisibilityProvider&MockObject $provider */
        $provider = $this->getMockBuilder(RewardVisibilityProvider::class)->disableOriginalConstructor()->getMock();
        $provider->method('getCustomersRewardIsVisibleFor')->with($this->isInstanceOf(Reward::class))
            ->willReturnCallback(function (Reward $reward) {
                if (self::REWARD_ID === (string) $reward->getRewardId()) {
                    return [self::USER_ID];
                }
                if (self::REWARD2_ID === (string) $reward->getRewardId()) {
                    return [];
                }

                return [];
            })
        ;

        /** @var UserPermissionCheckerInterface&MockObject $permissionChecker */
        $permissionChecker = $this->getMockBuilder(UserPermissionCheckerInterface::class)->getMock();
        $permissionChecker->method('hasPermissionInCurrentStore')->willReturnCallback(function (User $user, string $resource, array $accesses) {
            return (new UserPermissionEvaluator())->hasPermission(null, $user, $resource, $accesses);
        });

        $attributes = [
            RewardVoter::CREATE_REWARD => ['customer' => false, 'admin' => true, 'admin_reporter' => false],
            RewardVoter::EDIT => ['customer' => false, 'admin' => true, 'admin_reporter' => false, 'id' => self::REWARD_ID],
            RewardVoter::LIST_ALL_REWARDS => ['customer' => false, 'admin' => true, 'admin_reporter' => true],
            RewardVoter::LIST_ALL_VISIBLE_REWARDS => ['customer' => false, 'admin' => true, 'admin_reporter' => true],
            RewardVoter::LIST_ALL_ACTIVE_REWARDS => ['customer' => false, 'admin' => true, 'admin_reporter' => true],
            RewardVoter::LIST_ALL_BOUGHT_REWARDS => ['customer' => false, 'admin' => true, 'admin_reporter' => true],
            RewardVoter::VIEW => ['customer' => false, 'admin' => true, 'admin_reporter' => true, 'id' => self::REWARD2_ID],
            RewardVoter::LIST_REWARDS_AVAILABLE_FOR_ME => ['customer' => true, 'admin' => false, 'admin_reporter' => false],
            RewardVoter::LIST_REWARDS_BOUGHT_BY_ME => ['customer' => true, 'admin' => false, 'admin_reporter' => false],
            RewardVoter::BUY => ['customer' => true, 'admin' => false, 'admin_reporter' => false, 'id' => self::REWARD2_ID],
            RewardVoter::BUY_FOR_CUSTOMER_ADMIN => ['customer' => false, 'admin' => true, 'admin_reporter' => false],
            RewardVoter::REISSUE_COUPON => ['customer' => false, 'admin' => true, 'admin_reporter' => false],
            RewardVoter::REDEEM_COUPON => ['customer' => false, 'admin' => true, 'admin_reporter' => false],
            RewardVoter::SELF_REDEEM_COUPON => ['customer' => true, 'admin' => false, 'admin_reporter' => false],
            RewardVoter::VIEW_BUY_FOR_CUSTOMER_ADMIN => ['customer' => false, 'admin' => true, 'admin_reporter' => true],
            RewardVoter::LIST_ALL_REWARDS_CUSTOMERS => ['customer' => false, 'admin' => true, 'admin_reporter' => true],
        ];

        $voter = new RewardVoter($provider, $permissionChecker);

        $this->assertVoterAttributes($voter, $attributes);

        $this->assertEquals(true, $voter->vote($this->getCustomerToken(), $this->getSubjectById(self::REWARD_ID), [RewardVoter::VIEW]));
    }

    protected function getSubjectById($id)
    {
        $reward = $this->getMockBuilder(Reward::class)->disableOriginalConstructor()->getMock();
        $reward->method('getRewardId')->willReturn(new RewardId($id));

        return $reward;
    }
}
