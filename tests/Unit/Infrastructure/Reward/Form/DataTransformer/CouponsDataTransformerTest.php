<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Unit\Infrastructure\Reward\Form\DataTransformer;

use OpenLoyalty\Reward\Domain\Model\Coupon;
use OpenLoyalty\Reward\Infrastructure\Form\DataTransformer\CouponsDataTransformer;
use PHPUnit\Framework\TestCase;

final class CouponsDataTransformerTest extends TestCase
{
    /**
     * @test
     */
    public function it_transforms(): void
    {
        $data = [
            new Coupon('000099c0-636f-4570-ef6e-000000000001'),
            new Coupon('000099c0-636f-4570-ef6e-000050000001'),
            new Coupon('000099c0-636f-4570-ef6e-000000090001'),
        ];
        $transformer = new CouponsDataTransformer();
        $transformed = $transformer->transform($data);
        $this->assertSame([
            '000099c0-636f-4570-ef6e-000000000001',
            '000099c0-636f-4570-ef6e-000050000001',
            '000099c0-636f-4570-ef6e-000000090001',
        ], $transformed);
    }

    /**
     * @test
     */
    public function it_reverse_transforms(): void
    {
        $data = [
            '000099c0-636f-4570-ef6e-000000000001',
            '000099c0-636f-4570-ef6e-000050000001',
            '000099c0-636f-4570-ef6e-000000090001',
        ];
        $transformer = new CouponsDataTransformer();
        $transformed = $transformer->reverseTransform($data);
        $this->assertEquals([
            new Coupon('000099c0-636f-4570-ef6e-000000000001'),
            new Coupon('000099c0-636f-4570-ef6e-000050000001'),
            new Coupon('000099c0-636f-4570-ef6e-000000090001'),
        ], $transformed);
    }
}
