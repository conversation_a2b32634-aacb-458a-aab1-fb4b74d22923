<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Unit\Infrastructure\Level\Validator;

use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\TierSetId;
use OpenLoyalty\Level\Application\DTO\Tier;
use OpenLoyalty\Level\Domain\Entity\Condition;
use OpenLoyalty\Level\Domain\Entity\ConditionValue;
use OpenLoyalty\Level\Domain\ValueObject\ConditionId;
use OpenLoyalty\Level\Infrastructure\Validator\Constraints\TierValueConditionsValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Violation\ConstraintViolationBuilderInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

final class TierValueConditionsValidatorTest extends TestCase
{
    private TierValueConditionsValidator $validator;
    private ExecutionContextInterface $context;
    private TranslatorInterface $translator;
    private Constraint $constraint;

    public function setUp(): void
    {
        $this->translator = $this->createMock(TranslatorInterface::class);
        $this->context = $this->createMock(ExecutionContextInterface::class);
        $this->validator = new TierValueConditionsValidator($this->translator);
        $this->constraint = $this->createMock(Constraint::class);
        $this->validator->initialize($this->context);
    }

    /**
     * @test
     */
    public function it_adds_violation_when_current_tier_is_null(): void
    {
        $this->translator
            ->method('trans')
            ->willReturn('The entered values are incorrect. Please check and enter the appropriate data');

        $violationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $violationBuilder->expects($this->once())
            ->method('addViolation');

        $this->context
            ->expects($this->once())
            ->method('buildViolation')
            ->with('The entered values are incorrect. Please check and enter the appropriate data')
            ->willReturn($violationBuilder);

        $this->validator->validate([null, null], $this->constraint);
    }

    /**
     * @test
     */
    public function it_adds_violation_when_condition_values_are_incorrect(): void
    {
        $this->translator
            ->method('trans')
            ->willReturn('The entered values are incorrect. Please check and enter the appropriate data');

        $conditionId = new ConditionId('00000000-0000-0000-0000-000000000001');
        $currentCondition = new ConditionValue(new Condition($conditionId, 'totalSpending'), 100);
        $nextCondition = new ConditionValue(new Condition($conditionId, 'totalSpending'), 50);

        $currentTier = new Tier(
            new LevelId('00000000-0000-0000-0000-000000000003'),
            [],
            true,
            [],
            [$currentCondition],
            new TierSetId('00000000-0000-0000-0000-000000000003')
        );

        $nextTier = new Tier(
            new LevelId('00000000-0000-0000-0000-000000000003'),
            [],
            true,
            [],
            [$nextCondition],
            new TierSetId('00000000-0000-0000-0000-000000000003')
        );

        $violationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $violationBuilder->expects($this->once())
            ->method('addViolation');

        $violationBuilder->expects($this->once())
            ->method('atPath')
            ->with('1.conditions.0')
            ->willReturn($violationBuilder);

        $this->context
            ->expects($this->once())
            ->method('buildViolation')
            ->with('The entered values are incorrect. Please check and enter the appropriate data')
            ->willReturn($violationBuilder);

        $this->validator->validate([$currentTier, $nextTier], $this->constraint);
    }

    /**
     * @test
     */
    public function it_does_not_add_violation_when_values_are_correct(): void
    {
        $conditionId = new ConditionId('00000000-0000-0000-0000-000000000002');
        $currentCondition = new ConditionValue(new Condition($conditionId, 'totalSpending'), 100);
        $nextCondition = new ConditionValue(new Condition($conditionId, 'totalSpending'), 150);

        $currentTier = new Tier(
            new LevelId('00000000-0000-0000-0000-000000000003'),
            [],
            true,
            [],
            [$currentCondition],
            new TierSetId('00000000-0000-0000-0000-000000000003')
        );

        $nextTier = new Tier(
            new LevelId('00000000-0000-0000-0000-000000000003'),
            [],
            true,
            [],
            [$nextCondition],
            new TierSetId('00000000-0000-0000-0000-000000000003')
        );

        $this->context->expects($this->never())
            ->method('buildViolation');

        $this->validator->validate([$currentTier, $nextTier], $this->constraint);
    }
}
