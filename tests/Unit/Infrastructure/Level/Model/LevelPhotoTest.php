<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Infrastructure\Level\Model;

use OpenLoyalty\Level\Domain\Model\LevelPhoto as DomainPhoto;
use OpenLoyalty\Level\Infrastructure\Model\LevelPhoto;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\File\UploadedFile;

final class LevelPhotoTest extends TestCase
{
    private LevelPhoto $levelPhoto;

    public function setUp(): void
    {
        parent::setUp();

        $this->levelPhoto = new LevelPhoto();
    }

    /**
     * @test
     */
    public function it_has_right_interface(): void
    {
        $this->assertInstanceOf(DomainPhoto::class, $this->levelPhoto);
    }

    /**
     * @test
     */
    public function it_returns_right_file(): void
    {
        $file = new UploadedFile(__FILE__, 'original.name.png');
        $this->assertNull($this->levelPhoto->getFile());

        $this->levelPhoto->setFile($file);
        $this->assertSame($file, $this->levelPhoto->getFile());
        $this->assertSame(__FILE__, $this->levelPhoto->getFile()->getRealPath());
        $this->assertSame('original.name.png', $this->levelPhoto->getFile()->getClientOriginalName());
    }
}
