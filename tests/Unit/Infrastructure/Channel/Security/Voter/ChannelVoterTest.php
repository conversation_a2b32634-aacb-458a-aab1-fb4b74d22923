<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Infrastructure\Channel\Security\Voter;

use OpenLoyalty\Channel\Domain\Channel;
use OpenLoyalty\Channel\Infrastructure\Security\Voter\ChannelVoter;
use OpenLoyalty\Core\Domain\Id\ChannelId;
use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseVoterTest;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionEvaluator;
use PHPUnit\Framework\MockObject\MockObject;

final class ChannelVoterTest extends BaseVoterTest
{
    private const CHANNEL_ID = '00000000-0000-474c-b092-b0dd880c0700';

    /**
     * @test
     */
    public function it_works(): void
    {
        $attributes = [
            ChannelVoter::LIST_CHANNEL => ['customer' => false, 'admin' => true, 'admin_reporter' => true],
            ChannelVoter::CREATE_CHANNEL => ['customer' => false, 'admin' => true, 'admin_reporter' => false],
            ChannelVoter::EDIT => ['customer' => false, 'admin' => true, 'id' => self::CHANNEL_ID, 'admin_reporter' => false],
            ChannelVoter::VIEW => ['customer' => false, 'admin' => true, 'id' => self::CHANNEL_ID, 'admin_reporter' => true],
        ];

        /** @var UserPermissionCheckerInterface&MockObject $permissionChecker */
        $permissionChecker = $this->getMockBuilder(UserPermissionCheckerInterface::class)->getMock();
        $permissionChecker->method('hasPermissionInCurrentStore')->willReturnCallback(function (User $user, string $resource, array $accesses) {
            return (new UserPermissionEvaluator())->hasPermission(null, $user, $resource, $accesses);
        });

        $voter = new ChannelVoter($permissionChecker);

        $this->assertVoterAttributes($voter, $attributes);
    }

    protected function getSubjectById($id)
    {
        $level = $this->getMockBuilder(Channel::class)->disableOriginalConstructor()->getMock();
        $level->method('getChannelId')->willReturn(new ChannelId($id));

        return $level;
    }
}
