<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Infrastructure\Channel\Form\Type;

use OpenLoyalty\Channel\Infrastructure\Form\Type\EditChannelFormType;
use Symfony\Component\Form\Extension\Validator\ValidatorExtension;
use Symfony\Component\Form\PreloadedExtension;
use Symfony\Component\Form\Test\TypeTestCase;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

final class EditChannelFormTypeTest extends TypeTestCase
{
    private \PHPUnit\Framework\MockObject\MockObject&\Symfony\Component\Validator\Validator\ValidatorInterface $validator;

    protected function setUp(): void
    {
        /* @var ValidatorInterface validator */
        $this->validator = $this->getMockBuilder(ValidatorInterface::class)->getMock();
        $this->validator
            ->method('validate')
            ->will($this->returnValue(new ConstraintViolationList()));
        $metadata = $this->getMockBuilder(\Symfony\Component\Validator\Mapping\ClassMetadata::class)
            ->disableOriginalConstructor()->getMock();
        $metadata->method('addConstraint')->willReturnSelf();
        $metadata->method('addPropertyConstraint')->willReturnSelf();

        $this->validator->method('getMetadataFor')->willReturn(
            $metadata
        );

        parent::setUp();
    }

    protected function getExtensions(): array
    {
        $type = new EditChannelFormType();

        return [
            new PreloadedExtension([$type], []),
            new ValidatorExtension($this->validator),
        ];
    }

    /**
     * @test
     */
    public function it_has_valid_data_when_creating_new_pos(): void
    {
        $formData = $this->getPosData();

        $form = $this->factory->create(EditChannelFormType::class);

        $form->submit($formData);

        $this->assertTrue($form->isSynchronized());
        $this->assertTrue($form->isValid());

        $view = $form->createView();
        $children = $view->children;

        foreach (array_keys($formData) as $key) {
            $this->assertArrayHasKey($key, $children);
        }
    }

    protected function getPosData(): array
    {
        return [
            'name' => 'test channel',
            'identifier' => 'online',
            'description' => 'test',
        ];
    }
}
