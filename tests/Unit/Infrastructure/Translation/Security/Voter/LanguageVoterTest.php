<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Infrastructure\Translation\Security\Voter;

use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseVoterTest;
use OpenLoyalty\Translation\Infrastructure\Security\Voter\TranslationsVoter;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionEvaluator;
use PHPUnit\Framework\MockObject\MockObject;

final class LanguageVoterTest extends BaseVoterTest
{
    /**
     * @test
     */
    public function it_works(): void
    {
        $attributes = [
            TranslationsVoter::VIEW_TRANSLATIONS => ['seller' => false, 'customer' => false, 'admin' => true, 'admin_reporter' => true],
            TranslationsVoter::EDIT_TRANSLATIONS => ['seller' => false, 'customer' => false, 'admin' => true, 'admin_reporter' => false],
        ];

        /** @var UserPermissionCheckerInterface&MockObject $permissionChecker */
        $permissionChecker = $this->getMockBuilder(UserPermissionCheckerInterface::class)->getMock();
        $permissionChecker->method('hasPermissionWithoutStore')->willReturnCallback(function (User $user, string $resource, array $accesses) {
            return (new UserPermissionEvaluator())->hasPermission(null, $user, $resource, $accesses);
        });

        $voter = new TranslationsVoter($permissionChecker);

        $this->assertVoterAttributes($voter, $attributes);
    }

    protected function getSubjectById($id): void
    {
    }
}
