<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Application\Messaging\UseCase;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\WebhookSubscriptionId;
use OpenLoyalty\Messaging\Application\DataMapper\WebhookSubscriptionDataMapper;
use OpenLoyalty\Messaging\Application\Response\WebhookSubscriptionDetails;
use OpenLoyalty\Messaging\Application\UseCase\GetWebhookSubscriptionUseCase;
use OpenLoyalty\Messaging\Domain\Entity\WebhookSubscription;
use PHPUnit\Framework\TestCase;

final class GetWebhookSubscriptionUseCaseTest extends TestCase
{
    /**
     * @test
     */
    public function it_returns_webhook_subscription_details(): void
    {
        $dataMapper = $this->createMock(WebhookSubscriptionDataMapper::class);
        $useCase = new GetWebhookSubscriptionUseCase($dataMapper);

        $entity = $this->createMock(WebhookSubscription::class);
        $response = new WebhookSubscriptionDetails(
            new WebhookSubscriptionId('25f77656-4846-4881-9959-3d2b19e6091d'),
            'eventName',
            'url',
            null,
            null,
            [],
            false,
            new DateTimeImmutable()
        );
        $dataMapper->method('map')->willReturn($response);

        $this->assertEquals($response, $useCase->execute($entity));
    }
}
