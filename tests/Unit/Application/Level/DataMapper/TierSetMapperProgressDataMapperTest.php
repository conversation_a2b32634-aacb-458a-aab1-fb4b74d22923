<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Unit\Application\Level\DataMapper;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\TierSetId;
use OpenLoyalty\Level\Application\DataMapper\ConditionProgressDataMapper;
use OpenLoyalty\Level\Application\DataMapper\TierSetMemberProgressDataMapper;
use OpenLoyalty\Level\Application\Response\TierSetMemberProgressResponse;
use OpenLoyalty\Level\Domain\CustomerFacadeInterface;
use OpenLoyalty\Level\Domain\Entity\TierSetMemberProgress;
use OpenLoyalty\Level\Domain\Enum\DowngradeMode;
use OpenLoyalty\Level\Domain\LevelRepository;
use OpenLoyalty\Level\Domain\Progress\ProgressProviderInterface;
use OpenLoyalty\Level\Domain\TierSet;
use OpenLoyalty\Level\Domain\TierSetRepositoryInterface;
use OpenLoyalty\Level\Domain\ValueObject\Downgrade;
use OpenLoyalty\Test\Common\Integration\AbstractKernelTest;
use OpenLoyalty\User\Domain\Customer;

final class TierSetMapperProgressDataMapperTest extends AbstractKernelTest
{
    private LevelRepository $levelRepository;
    private ProgressProviderInterface $progressProvider;
    private ConditionProgressDataMapper $conditionProgressDataMapper;
    private CustomerFacadeInterface $customerFacade;
    private TierSetRepositoryInterface $tierSetRepository;

    protected function setUp(): void
    {
        $this->levelRepository = $this->createMock(LevelRepository::class);
        $this->progressProvider = $this->createMock(ProgressProviderInterface::class);
        $this->conditionProgressDataMapper = new ConditionProgressDataMapper();
        $this->customerFacade = $this->createMock(CustomerFacadeInterface::class);
        $this->tierSetRepository = $this->createMock(TierSetRepositoryInterface::class);
    }

    /**
     * @test
     */
    public function it_maps(): void
    {
        $item = $this->createMock(TierSetMemberProgress::class);
        $tierSet = $this->createMock(TierSet::class);
        $tierSet->method('getTierSetId')->willReturn(new TierSetId('00000000-0000-aaaa-0000-000000000000'));
        $tierSet->method('getName')->willReturn('tiersetname');
        $tierSet->method('getDowngrade')->willReturn(new Downgrade(DowngradeMode::NoDowngrade));
        $item->method('getTierSet')->willReturn($tierSet);

        $mapper = new TierSetMemberProgressDataMapper(
            $this->levelRepository,
            $this->progressProvider,
            $this->conditionProgressDataMapper,
            $this->customerFacade,
            $this->tierSetRepository
        );

        $mapped = $mapper->map($item);

        $this->assertEquals(new TierSetMemberProgressResponse(
            new TierSetId('00000000-0000-aaaa-0000-000000000000'),
            'tiersetname',
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            'none',
            false,
            []
        ), $mapped);
    }

    /**
     * @test
     */
    public function it_maps_for_null_progress(): void
    {
        $tierSet = $this->createMock(TierSet::class);
        $tierSet->method('getTierSetId')->willReturn(new TierSetId('00000000-0000-aaaa-0000-000000000000'));
        $tierSet->method('getName')->willReturn('tiersetname');
        $tierSet->method('getDowngrade')->willReturn(new Downgrade(DowngradeMode::NoDowngrade));

        $customer = $this->createMock(Customer::class);
        $this->customerFacade->method('getCustomer')->willReturn($customer);

        $this->tierSetRepository->method('byId')->willReturn($tierSet);

        $mapper = new TierSetMemberProgressDataMapper(
            $this->levelRepository,
            $this->progressProvider,
            $this->conditionProgressDataMapper,
            $this->customerFacade,
            $this->tierSetRepository
        );

        $mapped = $mapper->mapForNullMemberProgress(
            new CustomerId('00000000-0000-bbbb-0000-000000000000'),
            new TierSetId('00000000-0000-aaaa-0000-000000000000')
        );

        $this->assertEquals(new TierSetMemberProgressResponse(
            new TierSetId('00000000-0000-aaaa-0000-000000000000'),
            'tiersetname',
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            'none',
            false,
            []
        ), $mapped);
    }
}
