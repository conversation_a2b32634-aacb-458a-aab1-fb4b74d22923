<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Application\Reward\Service;

use DateTime;
use DateTimeImmutable;
use Faker\Provider\Uuid;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\IssuedRewardId;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Reward\Application\Coupon\CouponManager;
use OpenLoyalty\Reward\Domain\IssuedReward;
use OpenLoyalty\Reward\Domain\Repository\IssuedRewardRepositoryInterface;
use OpenLoyalty\Reward\Domain\StaticCouponReward;
use OpenLoyalty\Reward\Domain\ValueObject\CustomerData;
use OpenLoyalty\Reward\Domain\ValueObject\IssuedCoupon;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class CouponManagerTest extends TestCase
{
    private EventBusInterface|MockObject $eventBus;
    private IssuedRewardRepositoryInterface|MockObject $issuedRewardRepository;

    protected function setUp(): void
    {
        $this->eventBus = $this->getMockBuilder(EventBusInterface::class)->getMock();
        $this->issuedRewardRepository = $this->getMockBuilder(IssuedRewardRepositoryInterface::class)->getMock();
    }

    /**
     * @test
     */
    public function it_sends_expiring_notifications(): void
    {
        $issuedReward = new IssuedReward(
            new IssuedRewardId(Uuid::uuid()),
            'token123',
            new StaticCouponReward(
                new RewardId(Uuid::uuid()),
                new StoreId(Uuid::uuid()),
                true,
                10,
                new DateTimeImmutable(),
                20
            ),
            new CustomerId(Uuid::uuid()),
            new DateTime(),
            10,
            new CustomerData(
                '<EMAIL>',
                'Joe',
                null,
                null,
                null,
                '4894949494'
            )
        );
        $issuedReward->assignCoupon(new IssuedCoupon('CODE123', 20));
        $this->issuedRewardRepository->method('getCouponsExpiringAt')->willReturn([$issuedReward]);

        $this->eventBus->expects($this->exactly(1))->method('dispatch');

        $couponManager = new CouponManager($this->eventBus, $this->issuedRewardRepository);
        $couponManager->sendExpiringNotificationsToAllCustomers(
            new StoreId('00000000-1111-474c-2222-b0dd880c07e1'),
            new DateTimeImmutable(),
            new DateTimeImmutable()
        );
    }
}
