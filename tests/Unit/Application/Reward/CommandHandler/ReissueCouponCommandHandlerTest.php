<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Application\Reward\CommandHandler;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Reward\Application\Command\ReissueCoupon;
use OpenLoyalty\Reward\Application\CommandHandler\ReissueCouponCommandHandler;
use OpenLoyalty\Reward\Domain\Coupon\CouponRedeemerInterface;
use PHPUnit\Framework\TestCase;

final class ReissueCouponCommandHandlerTest extends TestCase
{
    protected CouponRedeemerInterface $couponRedeemer;

    protected function setUp(): void
    {
        $this->couponRedeemer = $this->getMockBuilder(CouponRedeemerInterface::class)->getMock();
    }

    /**
     * @test
     */
    public function it_handles_redeem(): void
    {
        $this->couponRedeemer->expects($this->once())->method('reissue');

        $handler = new ReissueCouponCommandHandler($this->couponRedeemer);
        $handler(new ReissueCoupon(
            new StoreId('00000000-0000-0000-0000-000000000000'),
            new CustomerId('00000000-0000-0000-0000-000000000000'),
            '123'
        ));
    }
}
