<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Application\Reward\CommandHandler;

use DateTime;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Model\Label;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Reward\Application\Command\UpdateReward;
use OpenLoyalty\Reward\Application\CommandHandler\UpdateRewardCommandHandler;
use OpenLoyalty\Reward\Domain\DynamicCouponReward;
use OpenLoyalty\Reward\Domain\MaterialReward;
use OpenLoyalty\Reward\Domain\Repository\RewardRepository;
use OpenLoyalty\Reward\Domain\RewardTranslation;
use OpenLoyalty\Reward\Domain\StaticCouponReward;
use OpenLoyalty\Reward\Domain\ValueObject\CouponGenerator;
use OpenLoyalty\Reward\Domain\ValueObject\RewardActivity;
use OpenLoyalty\Reward\Domain\ValueObject\RewardTarget;
use OpenLoyalty\Reward\Domain\ValueObject\RewardUsageLimit;
use OpenLoyalty\Reward\Domain\ValueObject\RewardVisibility;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class UpdateRewardCommandHandlerTest extends TestCase
{
    private MockObject&RewardRepository $rewardRepository;
    private DateTime $now;

    protected function setUp(): void
    {
        $this->now = new DateTime();
        $this->rewardRepository = $this->getMockBuilder(RewardRepository::class)->getMock();
    }

    /**
     * @test
     */
    public function it_persists_static_coupon(): void
    {
        $reward = new StaticCouponReward(
            new RewardId('000096cf-32a3-43bd-9034-4df343e5fd93'),
            $this->getStore()->getStoreId(),
            true,
            10,
            $this->now,
            22
        );

        $reward->setCouponGenerator(new CouponGenerator(12, CouponGenerator::NUMBERS_AND_LETTERS_CHARACTER_SET, 'prefix'));
        $reward->setValidity(4, 2);

        $reward->setTranslations(
            [
                'en' => new RewardTranslation(
                    'name',
                    'short description',
                    'condition description',
                    'usage instruction',
                    'brand description',
                    'brand'
                ),
                'pl' => new RewardTranslation(
                    'name pl',
                    'short description pl',
                    'condition description pl',
                    'usage instruction pl',
                    'brand description pl',
                    'brand pl'
                ),
            ],
        );
        $reward->setLevels([
            new LevelId('222096cf-32a3-43bd-9034-4df343e5fd93'),
        ]);
        $reward->setTarget(RewardTarget::create(RewardTarget::LEVEL));
        $reward->setSegments([]);
        $reward->setFeatured(false);
        $reward->setPublic(true);
        $reward->setPriceValues(30.12, 20.13, 20.34);
        $reward->updateActivity(new RewardActivity(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')));
        $reward->updateVisibility(new RewardVisibility(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')));
        $reward->updateCategories([]);
        $reward->updateLabels([
            new Label('type', 'promotion'),
        ], );
        $reward->updateUsageLimit(new RewardUsageLimit(10, 3));

        $this->rewardRepository->method('byId')->willReturn($reward);
        $this->rewardRepository->expects($this->once())->method('save')->with($this->equalTo(
            $reward
        ));

        $handler = new UpdateRewardCommandHandler($this->rewardRepository);
        $handler->__invoke($this->getCommand(StaticCouponReward::TYPE));
    }

    /**
     * @test
     */
    public function it_persists_dynamic_coupon(): void
    {
        $reward = new DynamicCouponReward(
            new RewardId('000096cf-32a3-43bd-9034-4df343e5fd93'),
            $this->getStore()->getStoreId(),
            true,
            10,
            $this->now
        );

        $reward->setCouponGenerator(new CouponGenerator(12, CouponGenerator::NUMBERS_AND_LETTERS_CHARACTER_SET, 'prefix'));
        $reward->setValidity(4, 2);

        $reward->setTranslations(
            [
                'en' => new RewardTranslation(
                    'name',
                    'short description',
                    'condition description',
                    'usage instruction',
                    'brand description',
                    'brand'
                ),
                'pl' => new RewardTranslation(
                    'name pl',
                    'short description pl',
                    'condition description pl',
                    'usage instruction pl',
                    'brand description pl',
                    'brand pl'
                ),
            ],
        );
        $reward->setLevels([
            new LevelId('222096cf-32a3-43bd-9034-4df343e5fd93'),
        ]);
        $reward->setTarget(RewardTarget::create(RewardTarget::LEVEL));
        $reward->setSegments([]);
        $reward->setFeatured(false);
        $reward->setPublic(true);
        $reward->setPriceValues(30.12, 20.13, 20.34);
        $reward->updateActivity(new RewardActivity(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')));
        $reward->updateVisibility(new RewardVisibility(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')));
        $reward->updateCategories([]);
        $reward->updateLabels([
            new Label('type', 'promotion'),
        ], );
        $reward->updateUsageLimit(new RewardUsageLimit(10, 3));

        $this->rewardRepository->method('byId')->willReturn($reward);
        $this->rewardRepository->expects($this->once())->method('save')->with($this->equalTo(
            $reward
        ));

        $handler = new UpdateRewardCommandHandler($this->rewardRepository);
        $handler->__invoke($this->getCommand(DynamicCouponReward::TYPE));
    }

    /**
     * @test
     */
    public function it_persists_material(): void
    {
        $reward = new MaterialReward(
            new RewardId('000096cf-32a3-43bd-9034-4df343e5fd93'),
            $this->getStore()->getStoreId(),
            true,
            10,
            $this->now
        );

        $reward->setFulfillmentTracking(true);

        $reward->setTranslations(
            [
                'en' => new RewardTranslation(
                    'name',
                    'short description',
                    'condition description',
                    'usage instruction',
                    'brand description',
                    'brand'
                ),
                'pl' => new RewardTranslation(
                    'name pl',
                    'short description pl',
                    'condition description pl',
                    'usage instruction pl',
                    'brand description pl',
                    'brand pl'
                ),
            ],
        );
        $reward->setLevels([
            new LevelId('222096cf-32a3-43bd-9034-4df343e5fd93'),
        ]);
        $reward->setTarget(RewardTarget::create(RewardTarget::LEVEL));
        $reward->setSegments([]);
        $reward->setFeatured(false);
        $reward->setPublic(true);
        $reward->setPriceValues(30.12, 20.13, 20.34);
        $reward->updateActivity(new RewardActivity(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')));
        $reward->updateVisibility(new RewardVisibility(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')));
        $reward->updateCategories([]);
        $reward->updateLabels([
            new Label('type', 'promotion'),
        ], );
        $reward->updateUsageLimit(new RewardUsageLimit(10, 3));

        $this->rewardRepository->method('byId')->willReturn($reward);
        $this->rewardRepository->expects($this->once())->method('save')->with($this->equalTo(
            $reward
        ));

        $handler = new UpdateRewardCommandHandler($this->rewardRepository);
        $handler->__invoke($this->getCommand(MaterialReward::TYPE));
    }

    protected function getCommand(string $rewardType): UpdateReward
    {
        $command = new UpdateReward(
            new RewardId('000096cf-32a3-43bd-9034-4df343e5fd93'),
            new StoreId('00000000-0000-0000-0000-000000000000'),
            [
                'en' => new RewardTranslation(
                    'name',
                    'short description',
                    'condition description',
                    'usage instruction',
                    'brand description',
                    'brand'
                ),
                'pl' => new RewardTranslation(
                    'name pl',
                    'short description pl',
                    'condition description pl',
                    'usage instruction pl',
                    'brand description pl',
                    'brand pl'
                ),
            ],
            true,
            10,
            [
                new LevelId('222096cf-32a3-43bd-9034-4df343e5fd93'),
            ],
            [
            ],
            new RewardActivity(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')),
            new RewardVisibility(false, new DateTime('2016-01-01'), new DateTime('2037-01-01')),
            20.13,
            20.34,
            30.12,
            [
                new Label('type', 'promotion'),
            ],
            false,
            true,
            [],
            new RewardUsageLimit(10, 3),
            RewardTarget::create(RewardTarget::LEVEL)
        );
        $command->setCouponOptions(4, 2, null, new CouponGenerator(12, CouponGenerator::NUMBERS_AND_LETTERS_CHARACTER_SET, 'prefix'));
        $command->setStaticCouponOptions(22);
        $command->setMaterialOptions(true);

        return $command;
    }

    protected function getStore(): Store
    {
        return new Store(
            new StoreId(LoadSettingsData::DEFAULT_STORE_ID),
            LoadSettingsData::DEFAULT_STORE_CODE,
            'EUR',
            'Store'
        );
    }
}
