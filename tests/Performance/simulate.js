import { check, sleep } from "k6";
import { Trend, Rate } from "k6/metrics";
import { options as generalOptions, customerLogin, campaignId } from './options.js';
import { getAdminToken } from './api/admin/login.js';
import { getCustomerToken } from './api/customer/login.js';
import { postTransaction } from './api/transaction/postTransaction.js';
import { getCustomerLevel } from "./api/customer/getCustomerLevel.js";
import { getCustomerCampaignAvailable } from "./api/customer/getAvailableCampaign.js";
import { getCustomerPointsTransfer } from "./api/customer/getPointsTransfer.js";
import { getCustomerCampaignBought } from "./api/customer/getCampaignBought.js";
import { postCustomerCampaignBuy } from "./api/customer/postCampaignBuy.js";
import { getCampaign } from "./api/campaign/getCampaign.js";
import { postCustomerCampaignCouponsMarkAsUsed } from "./api/customer/postCampaignCouponsMarkAsUsed.js";

let errorRate = new Rate("errors");

export let options = Object.assign(
    generalOptions,
    {}
);

export function setup() {
    return getAdminToken();
}

export default function(token) {

    let VUCustomerLogin = `${__VU}`+customerLogin;

    // -----------------------------------------------------------------------------------------------------------------

    // register a transaction
    let transactionResponse = postTransaction(token, VUCustomerLogin);

    check(transactionResponse, {
        "POST /api/transaction was 200": (r) => r.status === 200
    }) || errorRate.add(1);

    check(transactionResponse, {
        "POST /api/transaction duration is below 1000": (r) => r.timings.duration < 1000
    });

    sleep(1);

    // -----------------------------------------------------------------------------------------------------------------

    // login as a customer
    let customerToken = getCustomerToken(VUCustomerLogin);

    sleep(1);

    // -----------------------------------------------------------------------------------------------------------------

    // check what's nice for my level
    let customerLevel = getCustomerLevel(customerToken);

    check(customerLevel, {
        "GET /api/customer/level was 200": (r) => r.status === 200
    }) || errorRate.add(1);

    check(customerLevel, {
        "GET /api/customer/level duration is below 200": (r) => r.timings.duration < 200
    });

    sleep(1);

    // -----------------------------------------------------------------------------------------------------------------

    // browse available campaigns for me
    let customerCampaignAvailable = getCustomerCampaignAvailable(customerToken);

    check(customerCampaignAvailable, {
        "GET /api/customer/campaign/available was 200": (r) => r.status === 200
    }) || errorRate.add(1);

    check(customerCampaignAvailable, {
        "GET /api/customer/campaign/available duration is below 500": (r) => r.timings.duration < 500
    });

    sleep(1);

    // -----------------------------------------------------------------------------------------------------------------

    // browse featured campaigns
    let customerCampaignFeatured = getCustomerCampaignAvailable(customerToken, {isFeatured: true});

    check(customerCampaignFeatured, {
        "GET /api/customer/campaign/available?featured=true was 200": (r) => r.status === 200
    }) || errorRate.add(1);

    check(customerCampaignFeatured, {
        "GET /api/customer/campaign/available?featured=true duration is below 200": (r) => r.timings.duration < 200
    });

    sleep(1);

    // -----------------------------------------------------------------------------------------------------------------

    // browse exclusive campaigns
    let customerCampaignExclusive = getCustomerCampaignAvailable(customerToken, {hasSegment: true});

    check(customerCampaignExclusive, {
        "GET /api/customer/campaign/available?hasSegment=true was 200": (r) => r.status === 200
    }) || errorRate.add(1);

    check(customerCampaignExclusive, {
        "GET /api/customer/campaign/available?hasSegment=true duration is below 200": (r) => r.timings.duration < 200
    });

    sleep(1);

    // -----------------------------------------------------------------------------------------------------------------

    // browse single campaign
    let singleCampaign = getCampaign(customerToken, campaignId);

    check(singleCampaign, {
        "GET /api/campaign/(campaignId) was 200": (r) => r.status === 200
    }) || errorRate.add(1);

    check(singleCampaign, {
        "GET /api/campaign/(campaignId) duration is below 500": (r) => r.timings.duration < 500
    });

    // -----------------------------------------------------------------------------------------------------------------

    // buy campaign
    let boughtCampaign = postCustomerCampaignBuy(customerToken, campaignId);

    check(boughtCampaign, {
        "POST /api/customer/campaign/(campaignId)/buy was 200": (r) => r.status === 200
    }) || errorRate.add(1);

    check(boughtCampaign, {
        "POST /api/customer/campaign/(campaignId)/buy duration is below 800": (r) => r.timings.duration < 800
    });

    sleep(1);

    // -----------------------------------------------------------------------------------------------------------------

    // see my points history
    let customerPointsTransfer = getCustomerPointsTransfer(customerToken);

    check(customerPointsTransfer, {
        "GET /api/customer/points/transfer was 200": (r) => r.status === 200
    }) || errorRate.add(1);

    check(customerPointsTransfer, {
        "GET /api/customer/points/transfer duration is below 500": (r) => r.timings.duration < 500
    });

    sleep(1);

    // -----------------------------------------------------------------------------------------------------------------

    // see my coupons
    let customerCampaignBought = getCustomerCampaignBought(customerToken);

    check(customerCampaignBought, {
        "GET /api/customer/campaign/bought was 200": (r) => r.status === 200
    }) || errorRate.add(1);

    check(customerCampaignBought, {
        "GET /api/customer/campaign/bought duration is below 200": (r) => r.timings.duration < 200
    });

    let campaigns = JSON.parse(customerCampaignBought.body);
    let coupon = campaigns.campaigns.find((coupon) => {
        if (coupon.canBeUsed) {
            return coupon;
        }
    });

    sleep(1);

    // -----------------------------------------------------------------------------------------------------------------

    // use coupon
    let customerCampaignCouponsMarkAsUsed = postCustomerCampaignCouponsMarkAsUsed(customerToken, coupon);

    check(customerCampaignCouponsMarkAsUsed, {
        "POST /api/customer/campaign/coupons/mark_as_used/buy was 200": (r) => r.status === 200
    }) || errorRate.add(1);

    check(customerCampaignCouponsMarkAsUsed, {
        "POST /api/customer/campaign/coupons/mark_as_used/buy duration is below 500": (r) => r.timings.duration < 500
    });

    // -----------------------------------------------------------------------------------------------------------------

    sleep(1);
};
