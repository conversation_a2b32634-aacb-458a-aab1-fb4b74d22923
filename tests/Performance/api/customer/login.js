import http from 'k6/http';
import { check } from "k6";
import { Trend } from "k6/metrics";
import { apiUrl, customerLogin, customerPassword, options as generalOptions } from '../../options.js';

export let options = Object.assign(
    generalOptions,
    {
        thresholds: {
            "GET /api/customer/login_check": [
                "avg<1000"
            ]
        }
    }
);

let loginMetric = new Trend('GET /api/customer/login_check');

export function loginCustomer(username = customerLogin, password = customerPassword) {
    let body = {username: username, password: password};

    return http.post(apiUrl + '/customer/login_check', body);
}

export function getCustomerToken(login = customerLogin, password = customerPassword) {
    let response = loginCustomer(login, password);

    return JSON.parse(response.body);
}

export default function() {
    let response = loginCustomer(customerLogin, customerPassword);

    loginMetric.add(response.timings.duration);
    check(response, {
        "GET /api/customer/login_check is 200": (r) => r.status === 200,
        "GET /api/customer/login_check returned a token": (r) => r.json().hasOwnProperty('token')
    });
};
