<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\DataAnalytics\Unit\Application\UseCase;

use OpenLoyalty\Analytics\Application\DataMapper\CampaignExecutionsStatsDataMapper;
use OpenLoyalty\Analytics\Application\Response\CampaignExecutionsStats;
use OpenLoyalty\Analytics\Application\UseCase\GetCampaignsExecutionsStatsUseCase;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\CampaignExecutionsStatsRepositoryReadContextInterface;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Search\Criteria\StoreCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollection\CriteriaCollection;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponder;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponse;
use OpenLoyalty\Core\Domain\Store;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class GetCampaignsExecutionsStatsUseCaseTest extends TestCase
{
    /**
     * @test
     */
    public function it_gets_campaign_executions_stats(): void
    {
        $campaign1 = $this->createMock(Campaign::class);
        $campaign2 = $this->createMock(Campaign::class);
        $campaign3 = $this->createMock(Campaign::class);
        $campaign1->method('getCampaignId')->willReturn(new CampaignId('5b60e95c-d548-44a4-a51f-f7212aa4c837'));
        $campaign1->method('getName')->willReturn('Campaign 1 name');
        $campaign2->method('getCampaignId')->willReturn(new CampaignId('e4a74e82-085f-4026-9b31-ee3721518dc8'));
        $campaign2->method('getName')->willReturn('Campaign 2 name');
        $campaign3->method('getCampaignId')->willReturn(new CampaignId('895adff7-7832-412c-938a-9ec5a70e21e9'));
        $campaign3->method('getName')->willReturn('Campaign 3 name');
        $data = [
            [
                0 => $campaign1,
                'executions' => 2,
            ],
            [
                0 => $campaign2,
                'executions' => 1,
            ],
            [
                0 => $campaign3,
                'executions' => 5,
            ],
        ];
        $expectedData = [
            new CampaignExecutionsStats('5b60e95c-d548-44a4-a51f-f7212aa4c837', 'Campaign 1 name', 2),
            new CampaignExecutionsStats('e4a74e82-085f-4026-9b31-ee3721518dc8', 'Campaign 2 name', 1),
            new CampaignExecutionsStats('895adff7-7832-412c-938a-9ec5a70e21e9', 'Campaign 3 name', 5),
        ];

        /** @var CampaignExecutionsStatsRepositoryReadContextInterface&MockObject $campaignExecutionsStatsRepository */
        $campaignExecutionsStatsRepository = $this->createMock(CampaignExecutionsStatsRepositoryReadContextInterface::class);
        $campaignExecutionsStatsRepository->method('findByCriteria')->willReturn($data);
        $campaignExecutionsStatsRepository->method('countByCriteria')->willReturnOnConsecutiveCalls(3, 3);

        $useCase = new GetCampaignsExecutionsStatsUseCase(
            $campaignExecutionsStatsRepository,
            new SearchableResponder(),
            new CampaignExecutionsStatsDataMapper()
        );
        $criteria = new CriteriaCollection();
        $criteria->add(new StoreCriteria('store', new Store(new StoreId('484635af-cc11-48ae-bf19-8afbe5f31fc7'), 'DEFAULT', 'EUR', 'Store name')));

        $result = $useCase->execute($criteria);
        $expected = new SearchableResponse($expectedData, 3, 3, true);
        $this->assertEquals($expected, $result);
    }
}
