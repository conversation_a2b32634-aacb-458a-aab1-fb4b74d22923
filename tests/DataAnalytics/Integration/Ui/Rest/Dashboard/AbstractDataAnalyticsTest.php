<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\DataAnalytics\Integration\Ui\Rest\Dashboard;

use Carbon\Carbon;
use DateTimeImmutable;
use OpenLoyalty\Account\Application\Command\CreateWalletType;
use OpenLoyalty\Account\Application\UseCase\CreateWalletTypeUseCase;
use OpenLoyalty\Account\Domain\WalletTypeTranslation;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\Id\WalletTypeId;
use OpenLoyalty\Points\Application\UseCase\ActivatePointsUseCase;
use OpenLoyalty\Points\Application\UseCase\AddPointsTransferUseCase;
use OpenLoyalty\Points\Application\UseCase\BlockPointsTransferUseCase;
use OpenLoyalty\Points\Application\UseCase\CancelPointsUseCase;
use OpenLoyalty\Points\Application\UseCase\ExpirePointsUseCase;
use OpenLoyalty\Points\Application\UseCase\SpendPointsTransferUseCase;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

abstract class AbstractDataAnalyticsTest extends AbstractApiTest
{
    protected HttpKernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    protected function createCustomer(
        string $email,
        string $storeCode,
        string $activatedAt = null
    ): string {
        $customerData = [
            'customer' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'email' => $email,
                'gender' => 'male',
                'birthDate' => '1991-06-16',
                'agreement1' => true,
            ],
        ];

        Carbon::setTestNow($activatedAt);

        $this->client->jsonRequest('POST', '/api/'.$storeCode.'/member', $customerData);
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        Carbon::setTestNow();

        return $data['customerId'];
    }

    protected function deactivateCustomer(string $customerId, string $storeCode): void
    {
        $this->client->request('POST', '/api/'.$storeCode.'/member/'.$customerId.'/deactivate', []);
        $this->assertNoContentResponseStatus($this->client->getResponse());
    }

    protected function activateCustomer(
        string $customerId,
        string $storeCode,
        string $activatedAt = null
    ): void {
        Carbon::setTestNow($activatedAt);
        $this->client->request('POST', '/api/'.$storeCode.'/member/'.$customerId.'/activate', []);
        $this->assertNoContentResponseStatus($this->client->getResponse());
        Carbon::setTestNow();
    }

    protected function createTransaction(
        string $documentNumber,
        string $customerEmail,
        string $purchasedAt,
        int $grossValue,
        string $storeCode
    ): void {
        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => $documentNumber,
                        'purchasedAt' => $purchasedAt,
                        'purchasePlace' => 'Warsaw',
                    ],
                    'items' => [
                        [
                            'sku' => '111222',
                            'name' => 'sku',
                            'quantity' => 1,
                            'grossValue' => $grossValue,
                            'category' => 'dataAnalytics',
                        ],
                    ],
                    'customerData' => [
                        'email' => $customerEmail,
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($this->client->getResponse());
    }

    protected function createTier(string $storeCode): void
    {
        $this->createTierSet($storeCode);
    }

    protected function createWalletType(
        StoreId $tenantId,
        WalletTypeId $walletTypeId,
        string $code,
        string $unitSingularName,
        string $unitPluralName,
        string $walletTypeName,
        string $unitsExpiryAfter = 'all_time_active',
        ?int $unitDaysActiveCount = null,
        ?int $unitYearsActiveCount = null
    ): void {
        $container = self::getContainer();

        $walletTypeTranslation = new WalletTypeTranslation();
        $walletTypeTranslation->setName($walletTypeName);
        $walletTypeTranslation->setLocale('en');

        /* @var CreateWalletTypeUseCase $createWalletTypeUseCase */
        $createWalletTypeUseCase = $container->get(CreateWalletTypeUseCase::class);
        $createWalletTypeUseCase->execute(
            new CreateWalletType(
                $walletTypeId,
                $code,
                $tenantId,
                $unitSingularName,
                $unitPluralName,
                true,
                [
                    'en' => $walletTypeTranslation,
                ],
                $unitsExpiryAfter,
                $unitDaysActiveCount,
                $unitYearsActiveCount,
                null,
                true
            )
        );
    }

    protected function createAddingTransfer(
        StoreId $tenantId,
        CustomerId $customerId,
        float $units,
        DateTimeImmutable $createdAt,
        ?string $walletCode = null,
        ?int $expiresInDays = null
    ): TransferId {
        $container = self::getContainer();

        return $container->get(AddPointsTransferUseCase::class)->execute(
            'user',
            $tenantId,
            $customerId,
            $units,
            null,
            null,
            $expiresInDays,
            $walletCode,
            $createdAt,
        );
    }

    protected function createSpendingTransfer(
        StoreId $tenantId,
        CustomerId $customerId,
        float $units,
        DateTimeImmutable $createdAt,
        ?string $walletCode = null
    ): TransferId {
        $container = self::getContainer();

        return $container->get(SpendPointsTransferUseCase::class)->execute(
            'user',
            $tenantId,
            $customerId,
            $units,
            null,
            $walletCode,
            $createdAt
        );
    }

    protected function activateTransfer(
        StoreId $tenantId,
        TransferId $transferId,
        DateTimeImmutable $unlockedAt
    ): void {
        /* @var ActivatePointsUseCase $activateUnitsUseCase */
        $container = self::getContainer();
        $activateUnitsUseCase = $container->get(ActivatePointsUseCase::class);
        $activateUnitsUseCase->execute(
            $tenantId,
            $transferId,
            'user',
            $unlockedAt,
        );
    }

    protected function cancelTransfer(
        StoreId $tenantId,
        TransferId $transferId,
        DateTimeImmutable $cancelledAt
    ): void {
        /* @var CancelPointsUseCase $cancelUnitsUseCase */
        $container = self::getContainer();
        $cancelUnitsUseCase = $container->get(CancelPointsUseCase::class);
        $cancelUnitsUseCase->execute(
            $tenantId,
            $transferId,
            $cancelledAt,
        );
    }

    protected function expireTransfer(
        StoreId $tenantId,
        TransferId $transferId,
        DateTimeImmutable $expiredAt
    ): void {
        /* @var ExpirePointsUseCase $expirePointsUseCase */
        $container = self::getContainer();
        $expirePointsUseCase = $container->get(ExpirePointsUseCase::class);
        $expirePointsUseCase->execute(
            $tenantId,
            $transferId,
            $expiredAt,
        );
    }

    protected function createBlockingTransfer(
        StoreId $tenantId,
        CustomerId $customerId,
        float $units,
        DateTimeImmutable $createdAt,
        ?string $walletCode = null
    ): TransferId {
        $container = self::getContainer();

        return $container->get(BlockPointsTransferUseCase::class)->execute(
            'user',
            $tenantId,
            $customerId,
            $units,
            null,
            $walletCode,
            $createdAt,
        );
    }
}
