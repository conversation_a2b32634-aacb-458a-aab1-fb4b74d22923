<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\DataAnalytics\Integration\Ui\Rest\Dashboard;

use Carbon\CarbonImmutable;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use function sprintf;

final class DataAnalyticsTest extends AbstractDataAnalyticsTest
{
    use TenantApiTrait;

    /**
     * @test
     * @group dataAnalytics
     */
    public function it_returns_general_overview(): void
    {
        self::markTestSkipped('will be fixed in OLOY-12738');
        CarbonImmutable::setTestNow('2020-01-01 00:00:00');
        $storeCode = 'data_analytics';
        $this->postTenant($this->client, $storeCode);
        $this->createTier($storeCode);

        $this->createCustomer('<EMAIL>', $storeCode);
        $secondCustomerId = $this->createCustomer('<EMAIL>', $storeCode);
        $this->deactivateCustomer($secondCustomerId, $storeCode);
        $thirdCustomerId = $this->createCustomer('<EMAIL>', $storeCode);
        $this->deactivateCustomer($thirdCustomerId, $storeCode);
        $this->activateCustomer($thirdCustomerId, $storeCode);

        /**
         * general overview per day.
         */
        $today = new CarbonImmutable();
        $yesterday = $today->modify('-1 day');
        $tomorrow = $today->modify('+1 day');

        $this->createTransaction(
            '1a2b3c',
            '<EMAIL>',
            $today->format('Y-m-d H:i:s'),
            60,
            $storeCode
        );
        $this->createTransaction(
            'a1b2c3',
            '<EMAIL>',
            $today->format('Y-m-d H:i:s'),
            120,
            $storeCode
        );
        $this->createTransaction(
            '4d5e6f',
            '<EMAIL>',
            $tomorrow->format('Y-m-d H:i:s'),
            40,
            $storeCode
        );
        $this->createTransaction(
            'd4e5f6',
            '<EMAIL>',
            $tomorrow->format('Y-m-d H:i:s'),
            20,
            $storeCode
        );

        $intervalStartDate = $yesterday->format('Y-m-d');
        $intervalEndDate = $today->format('Y-m-d');

        $expectedData = [
            'aggregationType' => null,
            'dataType' => null,
            'intervalStartDate' => $intervalStartDate,
            'intervalEndDate' => $intervalEndDate,
            'header' => [
                'registeredMembers' => 2,
                'activeMembers' => 2,
                'revenue' => 180.0,
                'avgSpending' => 90.0,
                'transactions' => 2,
                'avgTransactionValue' => 90.0,
                'avgNumberOfTransactions' => 1.0,
            ],
            'data' => [],
            'comparativePeriodData' => [],
        ];

        /**
         * general overview per month.
         */
        $aggregationType = 'day';
        $dataType = 'revenue';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$yesterday->format('Y-m-d')] = 0;
        $expectedData['data'][$today->format('Y-m-d')] = 180.0;
        $this->assertSame($expectedData, $data);

        $dataType = 'avgSpending';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$yesterday->format('Y-m-d')] = 0;
        $expectedData['data'][$today->format('Y-m-d')] = 90.0;
        $this->assertSame($expectedData, $data);

        $dataType = 'transactions';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$yesterday->format('Y-m-d')] = 0;
        $expectedData['data'][$today->format('Y-m-d')] = 2;
        $this->assertSame($expectedData, $data);

        $dataType = 'avgTransactionValue';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$yesterday->format('Y-m-d')] = 0;
        $expectedData['data'][$today->format('Y-m-d')] = 90.0;
        $this->assertSame($expectedData, $data);

        $dataType = 'avgNumberOfTransactions';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$yesterday->format('Y-m-d')] = 0;
        $expectedData['data'][$today->format('Y-m-d')] = 1;
        $this->assertSame($expectedData, $data);

        /**
         * general overview per month.
         */
        $twoMonthsBack = $today->modify('-2 month');
        $oneMonthBack = $today->modify('-1 month');

        $this->createTransaction(
            '1a2b3c1',
            '<EMAIL>',
            $twoMonthsBack->format('Y-m-d H:i:s'),
            1,
            $storeCode
        );
        $this->createTransaction(
            'a1b2c31',
            '<EMAIL>',
            $oneMonthBack->format('Y-m-d H:i:s'),
            5,
            $storeCode
        );

        $intervalStartDate = $twoMonthsBack->format('Y-m-d');
        $intervalEndDate = $today->format('Y-m-d');
        $expectedData = [
            'aggregationType' => null,
            'dataType' => null,
            'intervalStartDate' => $intervalStartDate,
            'intervalEndDate' => $intervalEndDate,
            'header' => [
                'registeredMembers' => 2,
                'activeMembers' => 2,
                'revenue' => 186.0,
                'avgSpending' => 93.0,
                'transactions' => 4,
                'avgTransactionValue' => 46.5,
                'avgNumberOfTransactions' => 2.0,
            ],
            'data' => [],
            'comparativePeriodData' => [],
        ];

        $aggregationType = 'month';
        $dataType = 'revenue';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$twoMonthsBack->format('Y-m')] = 1.0;
        $expectedData['data'][$oneMonthBack->format('Y-m')] = 5.0;
        $expectedData['data'][$today->format('Y-m')] = 180.0;
        $this->assertSame($expectedData, $data);

        $dataType = 'avgSpending';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$twoMonthsBack->format('Y-m')] = 1.0;
        $expectedData['data'][$oneMonthBack->format('Y-m')] = 5.0;
        $expectedData['data'][$today->format('Y-m')] = 90.0;
        $this->assertSame($expectedData, $data);

        $dataType = 'transactions';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$twoMonthsBack->format('Y-m')] = 1;
        $expectedData['data'][$oneMonthBack->format('Y-m')] = 1;
        $expectedData['data'][$today->format('Y-m')] = 2;
        $this->assertSame($expectedData, $data);

        $dataType = 'avgTransactionValue';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$twoMonthsBack->format('Y-m')] = 1.0;
        $expectedData['data'][$oneMonthBack->format('Y-m')] = 5.0;
        $expectedData['data'][$today->format('Y-m')] = 90.0;
        $this->assertSame($expectedData, $data);

        $dataType = 'avgNumberOfTransactions';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$twoMonthsBack->format('Y-m')] = 1;
        $expectedData['data'][$oneMonthBack->format('Y-m')] = 1;
        $expectedData['data'][$today->format('Y-m')] = 1;
        $this->assertSame($expectedData, $data);

        /**
         * general overview per year.
         */
        $twoYearsBack = $today->modify('-2 year');
        $oneYearBack = $today->modify('-1 year');

        $this->createTransaction(
            '1a2b3c1a',
            '<EMAIL>',
            $twoYearsBack->format('Y-m-d H:i:s'),
            1,
            $storeCode
        );
        $this->createTransaction(
            'a1b2c31a',
            '<EMAIL>',
            $oneYearBack->format('Y-m-d H:i:s'),
            5,
            $storeCode
        );

        $intervalStartDate = $twoYearsBack->format('Y-m-d');
        $intervalEndDate = $today->format('Y-m-d');
        $expectedData = [
            'aggregationType' => null,
            'dataType' => null,
            'intervalStartDate' => $intervalStartDate,
            'intervalEndDate' => $intervalEndDate,
            'header' => [
                'registeredMembers' => 2,
                'activeMembers' => 2,
                'revenue' => 192.0,
                'avgSpending' => 96.0,
                'transactions' => 6,
                'avgTransactionValue' => 32.0,
                'avgNumberOfTransactions' => 3.0,
            ],
            'data' => [],
            'comparativePeriodData' => [],
        ];

        $aggregationType = 'year';
        $dataType = 'revenue';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$twoYearsBack->format('Y')] = 1.0;
        $expectedData['data'][$oneYearBack->format('Y')] = 6.0;
        $expectedData['data'][$today->format('Y')] = 185.0;
        $this->assertSame($expectedData, $data);

        $dataType = 'avgSpending';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$twoYearsBack->format('Y')] = 1.0;
        $expectedData['data'][$oneYearBack->format('Y')] = 3.0;
        $expectedData['data'][$today->format('Y')] = 92.5;
        $this->assertSame($expectedData, $data);

        $dataType = 'transactions';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$twoYearsBack->format('Y')] = 1;
        $expectedData['data'][$oneYearBack->format('Y')] = 2;
        $expectedData['data'][$today->format('Y')] = 3;
        $this->assertSame($expectedData, $data);

        $dataType = 'avgTransactionValue';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$twoYearsBack->format('Y')] = 1.0;
        $expectedData['data'][$oneYearBack->format('Y')] = 3.0;
        $expectedData['data'][$today->format('Y')] = 61.666666666666664;
        $this->assertSame($expectedData, $data);

        $dataType = 'avgNumberOfTransactions';
        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/analytics/dashboard/general-overview?aggregationType=%s&intervalStartDate=%s&intervalEndDate=%s&dataType=%s',
                $storeCode,
                $aggregationType,
                $intervalStartDate,
                $intervalEndDate,
                $dataType
            )
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $expectedData['aggregationType'] = $aggregationType;
        $expectedData['dataType'] = $dataType;
        $expectedData['data'][$twoYearsBack->format('Y')] = 1;
        $expectedData['data'][$oneYearBack->format('Y')] = 1;
        $expectedData['data'][$today->format('Y')] = 1.5;
        $this->assertSame($expectedData, $data);
    }
}
