<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\DataAnalytics\Integration\Ui\Rest;

use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class GetCampaignsTest extends AbstractApiTest
{
    /**
     * @test
     *
     * @dataProvider queryParamsProvider
     */
    public function it_executes(string $field, string $orderDirection): void
    {
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/analytics/campaigns?_orderBy[%s]=%s', $field, $orderDirection)
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('total', $data);
        $this->assertArrayHasKey('items', $data);
        $this->assertNotEmpty($data['items']);
        $this->assertArrayHasKey('campaignId', $data['items'][0]);
        $this->assertArrayHasKey('name', $data['items'][0]);
        $this->assertArrayHasKey('executions', $data['items'][0]);
    }

    public function queryParamsProvider(): array
    {
        return [
            ['executions', 'asc'],
            ['executions', 'desc'],
        ];
    }
}
