<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\DataAnalytics\Integration\Ui\Rest;

use Doctrine\ORM\Query\ResultSetMapping;
use OpenLoyalty\DataAnalytics\Domain\Wallet\Entity\WalletType;
use OpenLoyalty\DataAnalytics\Infrastructure\Wallet\Persistence\Doctrine\Repository\WalletTypeRepository;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;

final class SavingAnalyticsItemWalletTypeAfterStoreCreatedTest extends AbstractApiTest
{
    use TenantApiTrait;

    /**
     * @test
     */
    public function it_creates_new_store_and_analytics_record(): void
    {
        $this->deleteAllWalletRepositoryDataAnalytics();

        $client = self::createAuthenticatedClient();

        $this->postTenant($client, 'ANALYTICS_TENANT', 'analytics tenant');

        /** @var WalletTypeRepository $repository */
        $repository = self::getContainer()->get(WalletTypeRepository::class);

        $result = $repository->findAll();

        /** @var WalletType $item */
        $item = $result[0];
        $this->assertCount(1, $result);
        $this->assertSame('Default Wallet', $item->getName());
        $this->assertSame('default', $item->getCode());
    }

    protected function deleteAllWalletRepositoryDataAnalytics(): void
    {
        $entityManager = self::getContainer()->get('doctrine.orm.default_entity_manager');
        $query = $entityManager->createNativeQuery('TRUNCATE openloyalty.public.data_analytics_wallet_type', new ResultSetMapping());
        $query->execute();
        self::ensureKernelShutdown();
    }
}
