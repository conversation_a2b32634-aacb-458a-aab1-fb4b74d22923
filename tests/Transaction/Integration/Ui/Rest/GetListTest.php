<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Transaction\Integration\Ui\Rest;

use OpenLoyalty\Integration\Helpers\ResponseChecker;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\Transaction\Integration\Traits\TransactionApiTrait;
use OpenLoyalty\Test\Utils\Provider\StringProvider;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class GetListTest extends AbstractApiTest
{
    use TransactionApiTrait;
    use TenantApiTrait;

    private HttpKernelBrowser $client;

    private string $tenantCode = 'transactionFiltersTest';
    private string $transaction1Id;
    private string $transaction2Id;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();

        $this->postTenant($this->client, $this->tenantCode);
    }

    /**
     * @test
     */
    public function it_filter_transaction_with_special_chars(): void
    {
        $items = [
            0 => [
                'sku' => '123',
                'name' => 'sku',
                'quantity' => 1,
                'grossValue' => 1,
                'category' => 'test',
                'maker' => 'company',
                'labels' => [
                    [
                        'key' => 'brandId',
                        'value' => 'Schmuckstück by Alpenherz',
                    ],
                ],
            ],
            1 => [
                'sku' => '1123',
                'name' => 'sku',
                'quantity' => 1,
                'grossValue' => 11,
                'category' => 'test',
                'maker' => 'company',
                'labels' => [
                    [
                        'key' => 'myKey',
                        'value' => 'Mały klucz',
                    ],
                ],
            ],
        ];

        $response = $this->postTransaction(httpClient: $this->client, storeCode: $this->tenantCode, items: $items);
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'GET',
            '/api/'.$this->tenantCode.'/transaction?items:labels=(brandId;Schmuckstück by Alpenherz)',
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('items', $data);
        $this->assertCount(1, $data['items']);
    }

    /**
     * @test
     */
    public function it_filter_transaction_with_special_polish_chars(): void
    {
        $items = [
            0 => [
                'sku' => '123',
                'name' => 'sku',
                'quantity' => 1,
                'grossValue' => 1,
                'category' => 'test',
                'maker' => 'company',
                'labels' => [
                    [
                        'key' => 'brandId',
                        'value' => 'Schmuckstuck by Alpenherz',
                    ],
                ],
            ],
            1 => [
                'sku' => '1123',
                'name' => 'sku',
                'quantity' => 1,
                'grossValue' => 11,
                'category' => 'test',
                'maker' => 'company',
                'labels' => [
                    [
                        'key' => 'myKey',
                        'value' => 'Mały klucz',
                    ],
                ],
            ],
        ];

        $response = $this->postTransaction(httpClient: $this->client, storeCode: $this->tenantCode, items: $items);
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'GET',
            '/api/'.$this->tenantCode.'/transaction?items:labels=(myKey;Mały klucz)',
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('items', $data);
        $this->assertCount(1, $data['items']);
    }

    /**
     * @test
     */
    public function it_filter_transaction_with_special_chars_for_two_labels(): void
    {
        $items = [
            0 => [
                'sku' => '123',
                'name' => 'sku',
                'quantity' => 1,
                'grossValue' => 1,
                'category' => 'test',
                'maker' => 'company',
                'labels' => [
                    [
                        'key' => 'brandId',
                        'value' => 'Schmuckstuck by Alpenherz',
                    ],
                ],
            ],
        ];

        $response = $this->postTransaction(httpClient: $this->client, storeCode: $this->tenantCode, items: $items);
        $this->assertOkResponseStatus($response);

        $items = [
            0 => [
                'sku' => '1123',
                'name' => 'sku',
                'quantity' => 1,
                'grossValue' => 11,
                'category' => 'test',
                'maker' => 'company',
                'labels' => [
                    [
                        'key' => 'myKey',
                        'value' => 'Mały klucz',
                    ],
                ],
            ],
        ];

        $response = $this->postTransaction(httpClient: $this->client, storeCode: $this->tenantCode, items: $items);
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'GET',
            '/api/'.$this->tenantCode.'/transaction?items:labels=(myKey;Mały klucz),(brandId;Schmuckstuck by Alpenherz)',
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('items', $data);
        $this->assertCount(2, $data['items']);
    }

    /**
     * @test
     */
    public function it_filter_transaction_with_special_chars_for_label_key(): void
    {
        $items = [
            0 => [
                'sku' => '123',
                'name' => 'sku',
                'quantity' => 1,
                'grossValue' => 1,
                'category' => 'test',
                'maker' => 'company',
                'labels' => [
                    [
                        'key' => 'How to write Umlaut ä ü ö',
                        'value' => 'Schmuckstück by Alpenherz',
                    ],
                ],
            ],
            1 => [
                'sku' => '1123',
                'name' => 'sku',
                'quantity' => 1,
                'grossValue' => 11,
                'category' => 'test',
                'maker' => 'company',
                'labels' => [
                    [
                        'key' => 'myKey',
                        'value' => 'How to write Umlaut ä ü ö',
                    ],
                ],
            ],
        ];

        $response = $this->postTransaction(httpClient: $this->client, storeCode: $this->tenantCode, items: $items);
        $this->assertOkResponseStatus($response);

        $this->client->request(
            'GET',
            '/api/'.$this->tenantCode.'/transaction?items:labels=(How to write Umlaut ä ü ö;Schmuckstück by Alpenherz)',
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('items', $data);
        $this->assertCount(1, $data['items']);
    }

    /**
     * @test
     */
    public function it_returns_transactions_filtered_by_id(): void
    {
        $this->createTwoTransactions();

        $this->client->request(
            'GET',
            sprintf('/api/%s/transaction?transactionId=%s', $this->tenantCode, $this->transaction1Id)
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $this->assertCount(1, json_decode($response->getContent(), true)['items']);
        $this->assertSame($this->transaction1Id, json_decode($response->getContent(), true)['items'][0]['transactionId']);
    }

    /**
     * @test
     */
    public function it_returns_transactions_filtered_by_more_that_one_ids(): void
    {
        $this->createTwoTransactions();

        $this->client->request(
            'GET',
            sprintf(
                '/api/%s/transaction?transactionId[in]=%s,%s',
                $this->tenantCode,
                $this->transaction1Id,
                $this->transaction2Id
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $this->assertCount(2, json_decode($response->getContent(), true)['items']);

        $transactionIds = array_map(fn ($item) => $item['transactionId'], json_decode($response->getContent(), true)['items']);
        $this->assertContains($this->transaction1Id, $transactionIds);
        $this->assertContains($this->transaction2Id, $transactionIds);
    }

    /**
     * @test
     */
    public function it_validates_too_many_ids_in_one_request(): void
    {
            $this->client->request(
            'GET',
            sprintf(
                '/api/%s/transaction?transactionId[in]=%s',
                $this->tenantCode,
                StringProvider::getStringWithUuids(55)
            )
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        ResponseChecker::assertHasError(
            json_decode($response->getContent(), true),
            'transactionId',
            'You can get max 50 of ids in single request'
        );
    }

    private function createTwoTransactions(): void
    {
        $transaction1Response = $this->postTransaction(
            $this->client,
            $this->tenantCode,
            items: [
                0 => [
                    'sku' => '123',
                    'name' => 'sku',
                    'quantity' => 1,
                    'grossValue' => 100,
                    'category' => 'test',
                    'maker' => 'company',
                    'labels' => [
                        [
                            'key' => 'test',
                            'value' => 'label',
                        ],
                    ],
                ],
            ],
        );

        $this->transaction1Id = json_decode($transaction1Response->getContent(), true)['transactionId'];

       $transaction2Response = $this->postTransaction(
            $this->client,
            $this->tenantCode,
            items: [
                0 => [
                    'sku' => '123',
                    'name' => 'sku',
                    'quantity' => 1,
                    'grossValue' => 200,
                    'category' => 'test',
                    'maker' => 'company',
                    'labels' => [
                        [
                            'key' => 'test',
                            'value' => 'label',
                        ],
                    ],
                ],
            ],
        );

        $this->transaction2Id = json_decode($transaction2Response->getContent(), true)['transactionId'];
    }
}
