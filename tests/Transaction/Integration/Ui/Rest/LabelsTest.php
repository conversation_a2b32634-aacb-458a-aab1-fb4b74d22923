<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Transaction\Integration\Ui\Rest;

use OpenLoyalty\Channel\Infrastructure\DataFixtures\ORM\LoadChannelData;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class LabelsTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_creates_transaction_with_the_labels(): void
    {
        $uniqueDocNumber = 'dn_tr_9888_1';

        $client = self::createAuthenticatedClient();
        $newTransactionData = [
            'header' => [
                'documentNumber' => $uniqueDocNumber,
                'documentType' => 'sell',
                'purchasedAt' => '2010-01-01T01:20:11',
                'purchasePlace' => 'New York',
                'labels' => [
                    [
                        'key' => 'keyA',
                        'value' => 'ValueA',
                    ],
                ],
            ],
            'channelId' => LoadChannelData::CHANNEL_ID,
            'items' => [
                0 => [
                    'sku' => '123',
                    'name' => 'sku',
                    'quantity' => 1,
                    'grossValue' => 1,
                    'category' => 'test',
                    'maker' => 'company',
                ],
            ],
            'customerData' => [
                'name' => 'John Doe 2',
                'email' => '<EMAIL>',
                'nip' => '000-00-00-001',
                'phone' => '+48111222927',
                'loyaltyCardNumber' => 'not-present-in-system-123',
            ],
        ];

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => $newTransactionData,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $transactionId = $data['transactionId'];

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction/'.$transactionId,
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertSame(
            [
                'grossValue' => 1.0,
                'transactionId' => $transactionId,
                'channelId' => LoadChannelData::CHANNEL_ID,
                'customerData' => [
                    'email' => '<EMAIL>',
                    'name' => 'John Doe 2',
                    'nip' => '000-00-00-001',
                    'phone' => '+48111222927',
                    'loyaltyCardNumber' => 'not-present-in-system-123',
                    'address' => [],
                ],
                'items' => [
                    0 => [
                        'name' => 'sku',
                        'quantity' => 1,
                        'highPrecisionQuantity' => 1.000,
                        'grossValue' => 1.0,
                        'category' => 'test',
                        'labels' => [],
                        'maker' => 'company',
                        'sku' => '123',
                    ],
                ],
                'header' => [
                    'documentNumber' => $uniqueDocNumber,
                    'documentType' => 'sell',
                    'purchasedAt' => '2010-01-01T01:20:11+01:00',
                    'purchasePlace' => 'New York',
                    'excludedDeliverySKUs' => [],
                    'excludedSKUs' => [],
                    'excludedCategories' => [
                        'category_excluded_from_level',
                    ],
                    'labels' => [
                        [
                            'key' => 'keyA',
                            'value' => 'ValueA',
                        ],
                    ],
                ],
                'matched' => false,
                'currency' => 'EUR',
                'pointsEarned' => 0.0,
                'unitsDeducted' => 0.0,
                'channelName' => 'test2',
            ],
            $data
        );
    }

    /**
     * @test
     */
    public function it_added_labels_to_transaction(): void
    {
        $uniqueDocNumber = 'dn_tr_9888_2';

        $client = self::createAuthenticatedClient();
        $newTransactionData = [
            'header' => [
                'documentNumber' => $uniqueDocNumber,
                'documentType' => 'sell',
                'purchasedAt' => '2010-01-01T01:20:11',
                'purchasePlace' => 'New York',
                'labels' => [
                    [
                        'key' => 'keyA',
                        'value' => 'ValueA',
                    ],
                ],
            ],
            'channelId' => LoadChannelData::CHANNEL_ID,
            'items' => [
                0 => [
                    'sku' => '123',
                    'name' => 'sku',
                    'quantity' => 1,
                    'grossValue' => 1,
                    'category' => 'test',
                    'maker' => 'company',
                ],
            ],
            'customerData' => [
                'name' => 'John Doe 2',
                'email' => '<EMAIL>',
                'nip' => '000-00-00-001',
                'phone' => '+48111222927',
                'loyaltyCardNumber' => 'not-present-in-system-123',
            ],
        ];

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => $newTransactionData,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $transactionId = $data['transactionId'];

        $client->request(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction/'.$transactionId.'/labels',
            [
                'labels' => [
                    [
                        'key' => 'KeyAdded',
                        'value' => 'ValueAdded',
                    ],
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction/'.$transactionId,
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertSame(
            [
                'grossValue' => 1.0,
                'transactionId' => $transactionId,
                'channelId' => LoadChannelData::CHANNEL_ID,
                'customerData' => [
                    'email' => '<EMAIL>',
                    'name' => 'John Doe 2',
                    'nip' => '000-00-00-001',
                    'phone' => '+48111222927',
                    'loyaltyCardNumber' => 'not-present-in-system-123',
                    'address' => [],
                ],
                'items' => [
                    0 => [
                        'name' => 'sku',
                        'quantity' => 1,
                        'highPrecisionQuantity' => 1.000,
                        'grossValue' => 1.0,
                        'category' => 'test',
                        'labels' => [],
                        'maker' => 'company',
                        'sku' => '123',
                    ],
                ],
                'header' => [
                    'documentNumber' => $uniqueDocNumber,
                    'documentType' => 'sell',
                    'purchasedAt' => '2010-01-01T01:20:11+01:00',
                    'purchasePlace' => 'New York',
                    'excludedDeliverySKUs' => [],
                    'excludedSKUs' => [],
                    'excludedCategories' => [
                        'category_excluded_from_level',
                    ],
                    'labels' => [
                        [
                            'key' => 'keyA',
                            'value' => 'ValueA',
                        ],
                        [
                            'key' => 'KeyAdded',
                            'value' => 'ValueAdded',
                        ],
                    ],
                ],
                'matched' => false,
                'currency' => 'EUR',
                'pointsEarned' => 0.0,
                'unitsDeducted' => 0.0,
                'channelName' => 'test2',
            ],
            $data
        );
    }

    /**
     * @test
     */
    public function it_removes_labels_from_transaction(): void
    {
        $uniqueDocNumber = 'dn_tr_9888_3';

        $client = self::createAuthenticatedClient();
        $newTransactionData = [
            'header' => [
                'documentNumber' => $uniqueDocNumber,
                'documentType' => 'sell',
                'purchasedAt' => '2010-01-01T01:20:11',
                'purchasePlace' => 'New York',
                'labels' => [
                    [
                        'key' => 'keyA',
                        'value' => 'valueA',
                    ],
                    [
                        'key' => 'keyB',
                        'value' => 'valueB',
                    ],
                ],
            ],
            'channelId' => LoadChannelData::CHANNEL_ID,
            'items' => [
                0 => [
                    'sku' => '123',
                    'name' => 'sku',
                    'quantity' => 1,
                    'grossValue' => 1,
                    'category' => 'test',
                    'maker' => 'company',
                ],
            ],
            'customerData' => [
                'name' => 'John Doe 2',
                'email' => '<EMAIL>',
                'nip' => '000-00-00-001',
                'phone' => '+48111222927',
                'loyaltyCardNumber' => 'not-present-in-system-123',
            ],
        ];

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => $newTransactionData,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $transactionId = $data['transactionId'];

        $client->request(
            'DELETE',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction/'.$transactionId.'/labels',
            [
                'labels' => [
                    [
                        'key' => 'keyA',
                        'value' => 'valueA',
                    ],
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction/'.$transactionId,
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertSame(
            [
                'grossValue' => 1.0,
                'transactionId' => $transactionId,
                'channelId' => LoadChannelData::CHANNEL_ID,
                'customerData' => [
                    'email' => '<EMAIL>',
                    'name' => 'John Doe 2',
                    'nip' => '000-00-00-001',
                    'phone' => '+48111222927',
                    'loyaltyCardNumber' => 'not-present-in-system-123',
                    'address' => [],
                ],
                'items' => [
                    0 => [
                        'name' => 'sku',
                        'quantity' => 1,
                        'highPrecisionQuantity' => 1.000,
                        'grossValue' => 1.0,
                        'category' => 'test',
                        'labels' => [],
                        'maker' => 'company',
                        'sku' => '123',
                    ],
                ],
                'header' => [
                    'documentNumber' => $uniqueDocNumber,
                    'documentType' => 'sell',
                    'purchasedAt' => '2010-01-01T01:20:11+01:00',
                    'purchasePlace' => 'New York',
                    'excludedDeliverySKUs' => [],
                    'excludedSKUs' => [],
                    'excludedCategories' => [
                        'category_excluded_from_level',
                    ],
                    'labels' => [
                        [
                            'key' => 'keyB',
                            'value' => 'valueB',
                        ],
                    ],
                ],
                'matched' => false,
                'currency' => 'EUR',
                'pointsEarned' => 0.0,
                'unitsDeducted' => 0.0,
                'channelName' => 'test2',
            ],
            $data
        );
    }
}
