<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Test\Transaction\Integration\Ui\Rest\Security;

use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseAccessControlTest;
use OpenLoyalty\Transaction\Infrastructure\DataFixtures\ORM\LoadTransactionData;

final class TransactionControllerAccessTest extends BaseAccessControlTest
{
    /**
     * @test
     */
    public function customer_admin_should_have_access_to_list(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'not_status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/transaction');
    }

    /**
     * @test
     */
    public function only_admin_and_customer_should_have_access_to_transaction_assigned_to_this_customer(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'not_status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/transaction/'.LoadTransactionData::TRANSACTION3_ID);
    }

    /**
     * @test
     */
    public function only_admin_can_register_transaction(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction', [], 'POST');
    }
}
