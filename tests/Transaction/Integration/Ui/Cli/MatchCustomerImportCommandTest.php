<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Transaction\Integration\Ui\Cli;

use OpenLoyalty\Application;
use OpenLoyalty\Core\Infrastructure\Provider\StoreContextProvider;
use OpenLoyalty\Import\Infrastructure\Service\ConsoleImportFileManager;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractKernelTest;
use OpenLoyalty\Transaction\Infrastructure\Import\MatchCustomerXmlImporter;
use OpenLoyalty\Transaction\Ui\Console\Command\MatchCustomerImportCommand;
use Symfony\Component\Console\Tester\CommandTester;

final class MatchCustomerImportCommandTest extends AbstractKernelTest
{
    /**
     * @test
     */
    public function it_imports_using_command(): void
    {
        $kernel = self::bootKernel();

        /** @var MatchCustomerXmlImporter $importer */
        $importer = self::getContainer()->get(MatchCustomerXmlImporter::class);

        /** @var ConsoleImportFileManager $importFileManager */
        $importFileManager = self::getContainer()->get(ConsoleImportFileManager::class);

        /** @var StoreContextProvider $storeContextProvider */
        $storeContextProvider = self::getContainer()->get(StoreContextProvider::class);
        $storeContextProvider->setRequestStoreCode(LoadSettingsData::DEFAULT_STORE_CODE);

        $application = new Application($kernel);
        $application->add(new MatchCustomerImportCommand($importer, $importFileManager, $storeContextProvider));

        $command = $application->find('oloy:transaction:customer-match:import');
        $commandTester = new CommandTester($command);
        $commandTester->execute(
            [
                'command' => $command->getName(),
                'file' => __DIR__.'/../../../Resources/fixtures/transaction/import-match-customer.xml',
            ],
            [
                'store-code' => LoadSettingsData::DEFAULT_STORE_CODE,
            ]
        );

        $output = $commandTester->getDisplay();

        $this->assertStringContainsString('Import has been finished: Processed 1, Success: 1, Failed: 0', $output);
    }
}
