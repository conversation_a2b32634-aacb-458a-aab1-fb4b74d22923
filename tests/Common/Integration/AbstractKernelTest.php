<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Common\Integration;

use DAMA\DoctrineTestBundle\Doctrine\DBAL\StaticDriver;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

abstract class AbstractKernelTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        StaticDriver::setKeepStaticConnections(true);
    }

    protected function setUp(): void
    {
        parent::setUp();
        StaticDriver::beginTransaction();
    }

    protected function tearDown(): void
    {
        StaticDriver::rollBack();
        parent::tearDown();
    }

    public static function tearDownAfterClass(): void
    {
        StaticDriver::setKeepStaticConnections(false);
    }
}
