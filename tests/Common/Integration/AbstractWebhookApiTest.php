<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Test\Common\Integration;

use OpenLoyalty\Core\Domain\Id\WebhookSubscriptionId;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Messaging\Domain\CachedWebhookSubscriptionRepositoryInterface;
use OpenLoyalty\Messaging\Domain\Entity\WebhookSubscription;
use OpenLoyalty\Messaging\Domain\Webhook\WebhookEventsProviderInterface;
use OpenLoyalty\Messaging\Infrastructure\Webhook\Client\WebhookClientInterface;
use OpenLoyalty\Test\Utils\Service\WebhookClientMock;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

abstract class AbstractWebhookApiTest extends AbstractApiTest
{
    protected HttpKernelBrowser $client;

    protected WebhookClientMock|WebhookClientInterface $webhookClientMock;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();

        $this->webhookClientMock = self::getContainer()->get(WebhookClientInterface::class);
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        $this->webhookClientMock->clearWebhooks();
    }

    protected function createWebhook(
        string $tenantCode,
        string $eventName,
        string $url = 'https://example.com'
    ): void {
        $this->client->request(
            'POST',
            '/api/'.$tenantCode.'/webhook/subscription',
            [
                'webhookSubscription' => [
                    'eventName' => $eventName,
                    'url' => $url,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
    }

    protected function addAllWebhooks(string $tenantCode): void
    {
        $store = self::getContainer()->get(StoreRepository::class)->byCode($tenantCode);
        $webhookSubscriptionRepository = self::getContainer()->get(CachedWebhookSubscriptionRepositoryInterface::class);
        $webhookEvents = self::getContainer()->get(WebhookEventsProviderInterface::class)->getEvents();

        foreach ($webhookEvents as $webhookEvent) {
            $webhookSubscription = WebhookSubscription::create(
                new WebhookSubscriptionId(Uuid::uuid4()),
                $store,
                $webhookEvent,
                'https://example.com',
                null,
                null,
                []
            );
            $webhookSubscriptionRepository->save($webhookSubscription);
        }
    }

    protected function assertWebhookHeaders($headers): void
    {
        $this->assertSameArrays([
            'Content-Type' => 'application/json',
            'User-Agent' => 'OpenLoyalty',
        ], $headers
        );
    }
}
