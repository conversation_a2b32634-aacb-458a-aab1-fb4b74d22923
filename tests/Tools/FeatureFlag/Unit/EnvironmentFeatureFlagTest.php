<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Tools\FeatureFlag\Unit;

use OpenLoyalty\Core\Application\Exception\FeatureFlagLogicException;
use OpenLoyalty\Tools\FeatureFlag\EnvironmentFeatureFlag;
use OpenLoyalty\Tools\FeatureFlag\FeatureFlag;
use PHPUnit\Framework\TestCase;

final class EnvironmentFeatureFlagTest extends TestCase
{
    /**
     * @test
     */
    public function it_returns_flag(): void
    {
        $featureFlags = new EnvironmentFeatureFlag('FF_TEST_1,MANUALLY_EDITED_ACHIEVEMENT_LEGACY_MODE');
        $this->assertTrue($featureFlags->isEnabled(FeatureFlag::MANUALLY_EDITED_ACHIEVEMENT_LEGACY_MODE));
    }

    /**
     * @test
     */
    public function it_returns_false_when_empty(): void
    {
        $featureFlags = new EnvironmentFeatureFlag('');
        $this->assertFalse($featureFlags->isEnabled(FeatureFlag::MANUALLY_EDITED_ACHIEVEMENT_LEGACY_MODE));
    }

    /**
     * @test
     */
    public function it_throws_exception_if_feature_flag_is_not_set_while_validate(): void
    {
        $featureFlags = new EnvironmentFeatureFlag('');

        $this->expectException(FeatureFlagLogicException::class);
        $featureFlags->throwExceptionWhenDisabled(FeatureFlag::MANUALLY_EDITED_ACHIEVEMENT_LEGACY_MODE, 'className');
    }
}
