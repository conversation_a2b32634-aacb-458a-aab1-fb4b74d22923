<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Test\Segment\Integration\Async;

use OpenLoyalty\Segment\Application\Job\RecreateSegmentJob;
use OpenLoyalty\Segment\Domain\Model\Criterion;
use OpenLoyalty\Test\Core\Integration\Infrastructure\AsyncTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\Segment\Integration\Trait\SegmentApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class RecreateSegmentTest extends AsyncTest
{
    use TenantApiTrait;
    use SegmentApiTrait;
    use MemberApiTrait;

    private HttpKernelBrowser $client;
    private string $namespace;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAsyncClient();

        $this->namespace = $this->getNamespace();
        $this->postTenant($this->client, $this->namespace);
        $this->clearQueue('segment_recreate_async');
    }

    /**
     * @test
     * @group async
     */
    public function it_consumes_recreate_segment_message(): void
    {
        // create segment
        $response = $this->postSegment(
            $this->client,
            $this->namespace,
            parts: [
                [
                    'criteria' => [
                        [
                            'type' => Criterion::TYPE_GENDER,
                            'options' => ['male'],
                        ],
                    ],
                ],
            ]
        );
        $this->assertOkResponseStatus($response);
        $this->consumeMessage(RecreateSegmentJob::class, 'segment_recreate_async');

        $segmentId = json_decode($response->getContent(), true)['segmentId'];

        $response = $this->postMember($this->client, $this->namespace);
        $this->assertOkResponseStatus($response);

        $this->assertSegmentMembersCount($this->namespace, 0, $segmentId);

        $this->executeCommand('oloy:segment:recreate', ['--segmentId' => $segmentId]);

        $this->consumeMessage(RecreateSegmentJob::class, 'segment_recreate_async');
        $this->assertSegmentMembersCount($this->namespace, 1, $segmentId);
    }

    /**
     * @test
     * @group async
     */
    public function it_consumes_recreate_segment_message_for_store_code(): void
    {
        //Create segment for 1st store
        $storeCode1 = $this->getNamespace();
        $this->postTenant($this->client, $storeCode1);
        $response = $this->postSegment(
            $this->client,
            $storeCode1,
            parts: [
                [
                    'criteria' => [
                        [
                            'type' => Criterion::TYPE_GENDER,
                            'options' => ['male'],
                        ],
                    ],
                ],
            ]
        );
        $this->assertOkResponseStatus($response);
        $segmentIdStore1 = json_decode($response->getContent(), true)['segmentId'];

        //Create segment for 2nd store
        $storeCode2 = $this->getNamespace();
        $this->postTenant($this->client, $storeCode2);
        $response = $this->postSegment(
            $this->client,
            $storeCode2,
            parts: [
                [
                    'criteria' => [
                        [
                            'type' => Criterion::TYPE_GENDER,
                            'options' => ['male'],
                        ],
                    ],
                ],
            ]
        );
        $this->assertOkResponseStatus($response);
        $segmentIdStore2 = json_decode($response->getContent(), true)['segmentId'];

        $this->consumeMessage(RecreateSegmentJob::class, 'segment_recreate_async', limit: 2);

        //Create member
        $response = $this->postMember($this->client, $storeCode1);
        $this->assertOkResponseStatus($response);
        //Create member
        $response = $this->postMember($this->client, $storeCode1, email: '<EMAIL>');
        $this->assertOkResponseStatus($response);
        //Create member
        $response = $this->postMember($this->client, $storeCode2);
        $this->assertOkResponseStatus($response);

        $this->assertSegmentMembersCount($storeCode1, 0, $segmentIdStore1);
        $this->assertSegmentMembersCount($storeCode2, 0, $segmentIdStore2);

        $this->executeCommand('oloy:segment:recreate', ['--store-code' => $storeCode1]);

        $this->consumeMessage(RecreateSegmentJob::class, 'segment_recreate_async');
        $this->assertSegmentMembersCount($storeCode1, 2, $segmentIdStore1);
        $this->assertSegmentMembersCount($storeCode2, 0, $segmentIdStore2);
    }

    private function assertSegmentMembersCount(string $storeCode, int $count, string $segmentId): void
    {
        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/segment/'.$segmentId.'/members',
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        self::assertSame($count, $data['total']['all']);
    }
}
