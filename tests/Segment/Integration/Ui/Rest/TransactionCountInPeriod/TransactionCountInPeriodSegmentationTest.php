<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Integration\Ui\Rest\TransactionCountInPeriod;

use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\Segment\Integration\Trait\SegmentApiTrait;
use OpenLoyalty\Test\Transaction\Integration\Traits\TransactionApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;

final class TransactionCountInPeriodSegmentationTest extends AbstractApiTest
{
    use MemberApiTrait;
    use TenantApiTrait;
    use TransactionApiTrait;
    use SegmentApiTrait;

    /**
     * @test
     */
    public function it_remove_member_from_segment_when_member_was_deleted(): void
    {
        $tenantCode = 'remove_member_from_segment_when_member_was_deleted';
        $client = self::createAuthenticatedClient();

        $this->postTenant($client, $tenantCode);
        $postMemberResponse = $this->postMember($client, $tenantCode, '<EMAIL>');
        $memberId = json_decode($postMemberResponse->getContent(), true)['customerId'];

        $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'TEST/CA/0121s',
                'documentType' => 'sell',
                'purchasedAt' => '2022-12-08T15:00:00+01:00',
            ],
            items: [
                0 => [
                    'sku' => '123',
                    'name' => 'sku',
                    'quantity' => 1,
                    'grossValue' => 100,
                    'category' => 'test',
                    'maker' => 'company',
                    'labels' => [
                        [
                            'key' => 'test',
                            'value' => 'label',
                        ],
                    ],
                ],
            ],
            customerData: [
                'email' => '<EMAIL>',
            ]
        );

        $response = $this->postSegment(
            $client,
            $tenantCode,
            name: 'transaction count in period',
            parts: [
                [
                    'criteria' => [
                        [
                            'type' => 'transaction_count_in_period',
                            'fromDate' => '2022-12-08 00:00',
                            'toDate' => '2022-12-08 23:59',
                            'operator' => [
                                'code' => 'is_equal',
                            ],
                            'count' => 1,
                        ],
                    ],
                ],
            ],
        );

        $segmentId = json_decode($response->getContent(), true)['segmentId'];

        $client->request(
            'GET',
            '/api/'.$tenantCode.'/segment/'.$segmentId,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $segmentDataBeforeDeleteMember = json_decode($response->getContent(), true);

        $this->assertSame(1, $segmentDataBeforeDeleteMember['customersCount']);
        $this->assertSame(100.0, $segmentDataBeforeDeleteMember['averageTransactionAmount']);
        $this->assertSame(1.0, $segmentDataBeforeDeleteMember['averageTransactions']);
        $this->assertSame(100.0, $segmentDataBeforeDeleteMember['averageClv']);

        // remove member and recalculate segment
        $this->deleteMember($client, $memberId, $tenantCode);
        $putSegmentResponse = $this->putSegment(
            $client,
            $segmentId,
            $tenantCode,
            name: 'transaction count in period',
            parts: [
                [
                    'criteria' => [
                        [
                            'type' => 'transaction_count_in_period',
                            'fromDate' => '2022-12-08 00:00',
                            'toDate' => '2022-12-08 23:59',
                            'operator' => [
                                'code' => 'is_equal',
                            ],
                            'count' => 1,
                        ],
                    ],
                ],
            ],
        );

        $this->assertOkResponseStatus($putSegmentResponse);

        $client->request(
            'GET',
            '/api/'.$tenantCode.'/segment/'.$segmentId,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $segmentDataAfterDeleteMember = json_decode($response->getContent(), true);

        $this->assertSame(0, $segmentDataAfterDeleteMember['customersCount']);
        $this->assertSame(0.0, $segmentDataAfterDeleteMember['averageTransactionAmount']);
        $this->assertSame(0.0, $segmentDataAfterDeleteMember['averageTransactions']);
        $this->assertSame(0.0, $segmentDataAfterDeleteMember['averageClv']);
    }
}
