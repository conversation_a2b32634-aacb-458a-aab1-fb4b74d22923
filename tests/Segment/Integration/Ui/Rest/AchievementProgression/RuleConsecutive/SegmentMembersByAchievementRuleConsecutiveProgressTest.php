<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Integration\Ui\Rest\AchievementProgression\RuleConsecutive;

use DateTimeImmutable;
use OpenLoyalty\Application;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use Symfony\Component\Console\Tester\CommandTester;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class SegmentMembersByAchievementRuleConsecutiveProgressTest extends AbstractApiTest
{
    private HttpKernelBrowser $client;
    private string $achievement1Id;
    private string $achievement2Id;
    private string $achievement1RuleId;
    private string $achievement2RuleId;
    private string $tenantCode;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();

        $this->tenantCode = 'tenant_for_segmentation';
        $this->createTenant($this->tenantCode);

        $this->createCustomEventSchema('test_schema_number_1');

        $response = $this->createAchievementWithRuleConsecutiveAndPeriodGoal(4, 2);
        $data = json_decode($response->getContent(), true);
        $this->achievement1Id = $data['achievementId'];

        $response = $this->createAchievementWithRuleConsecutiveAndPeriodGoal(1, 4);
        $data = json_decode($response->getContent(), true);
        $this->achievement2Id = $data['achievementId'];

        $this->createMember('<EMAIL>');

        $today = new DateTimeImmutable();
        $tomorrow = $today->modify('+1 day');
        $this->createMember('<EMAIL>');
        $this->createCustomEvents('<EMAIL>', 'test_schema_number_1', 1, $today);

        $this->createMember('<EMAIL>');
        $this->createCustomEvents('<EMAIL>', 'test_schema_number_1', 2, $today);

        $this->createMember('<EMAIL>');
        $this->createCustomEvents('<EMAIL>', 'test_schema_number_1', 3, $today);

        $this->createMember('<EMAIL>');
        $this->createCustomEvents('<EMAIL>', 'test_schema_number_1', 4, $today);

        $this->createMember('<EMAIL>');
        $this->createCustomEvents('<EMAIL>', 'test_schema_number_1', 5, $today);

        $this->createMember('<EMAIL>');
        $this->createCustomEvents('<EMAIL>', 'test_schema_number_1', 5, $today);
        $this->createCustomEvents('<EMAIL>', 'test_schema_number_1', 1, $tomorrow);

        $this->createMember('<EMAIL>');
        $this->createCustomEvents('<EMAIL>', 'test_schema_number_1', 4, $today);
        $this->createCustomEvents('<EMAIL>', 'test_schema_number_1', 4, $tomorrow);

        $this->achievement1RuleId = $this->getIdOfFirstRuleFromAchievement($this->achievement1Id);
        $this->achievement2RuleId = $this->getIdOfFirstRuleFromAchievement($this->achievement2Id);
    }

    /**
     * @test
     */
    public function it_creates_segment_with_achievement_rule_consecutive_progress_criteria_is_equal(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement consecutive progress',
                    'active' => true,
                    'description' => 'Desc - achievement consecutive progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievement1Id,
                                    'achievementRuleId' => $this->achievement1RuleId,
                                    'achievementGoal' => 'current_progress',
                                    'operator' => [
                                        'code' => 'is_equal',
                                    ],
                                    'progress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();

        $this->assertOkResponseStatus($response);

        $segment = json_decode($response->getContent(), true);

        $this->assertSegmentMembersCount(1, $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
    }

    /**
     * @test
     */
    public function it_creates_segment_with_achievement_rule_consecutive_progress_criteria_is_equal_0(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement consecutive progress',
                    'active' => true,
                    'description' => 'Desc - achievement consecutive progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievement1Id,
                                    'achievementRuleId' => $this->achievement1RuleId,
                                    'achievementGoal' => 'current_progress',
                                    'operator' => [
                                        'code' => 'is_equal',
                                    ],
                                    'progress' => 0,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();

        $this->assertOkResponseStatus($response);

        $segment = json_decode($response->getContent(), true);

        $this->assertSegmentMembersCount(4, $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
    }

    /**
     * @test
     */
    public function it_creates_segment_with_achievement_rule_consecutive_progress_criteria_is_greater(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement consecutive progress',
                    'active' => true,
                    'description' => 'Desc - achievement consecutive progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievement1Id,
                                    'achievementRuleId' => $this->achievement1RuleId,
                                    'achievementGoal' => 'current_progress',
                                    'operator' => [
                                        'code' => 'is_greater',
                                    ],
                                    'progress' => 1,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $segment = json_decode($response->getContent(), true);

        $this->assertSegmentMembersCount(2, $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
    }

    /**
     * @test
     */
    public function it_creates_segment_with_achievement_rule_consecutive_progress_criteria_is_greater_or_equal(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement consecutive progress',
                    'active' => true,
                    'description' => 'Desc - achievement consecutive progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievement1Id,
                                    'achievementRuleId' => $this->achievement1RuleId,
                                    'achievementGoal' => 'current_progress',
                                    'operator' => [
                                        'code' => 'is_greater_or_equal',
                                    ],
                                    'progress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $segment = json_decode($response->getContent(), true);

        $this->assertSegmentMembersCount(2, $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
    }

    /**
     * @test
     */
    public function it_creates_segment_with_achievement_rule_consecutive_progress_criteria_is_less(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement consecutive progress',
                    'active' => true,
                    'description' => 'Desc - achievement consecutive progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievement1Id,
                                    'achievementRuleId' => $this->achievement1RuleId,
                                    'achievementGoal' => 'current_progress',
                                    'operator' => [
                                        'code' => 'is_less',
                                    ],
                                    'progress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $segment = json_decode($response->getContent(), true);

        $this->assertSegmentMembersCount(6, $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
    }

    /**
     * @test
     */
    public function it_creates_segment_with_achievement_rule_consecutive_progress_criteria_is_less_or_equal(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement consecutive progress',
                    'active' => true,
                    'description' => 'Desc - achievement consecutive progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievement1Id,
                                    'achievementRuleId' => $this->achievement1RuleId,
                                    'achievementGoal' => 'current_progress',
                                    'operator' => [
                                        'code' => 'is_less_or_equal',
                                    ],
                                    'progress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $segment = json_decode($response->getContent(), true);

        $this->assertSegmentMembersCount(7, $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
    }

    /**
     * @test
     */
    public function it_creates_segment_with_achievement_rule_consecutive_progress_criteria_is_number_between(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement consecutive progress',
                    'active' => true,
                    'description' => 'Desc - achievement consecutive progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievement1Id,
                                    'achievementRuleId' => $this->achievement1RuleId,
                                    'achievementGoal' => 'current_progress',
                                    'operator' => [
                                        'code' => 'is_number_between',
                                    ],
                                    'fromProgress' => 1,
                                    'toProgress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $segment = json_decode($response->getContent(), true);
        $this->assertSegmentMembersCount(3, $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
    }

    /**
     * @test
     */
    public function it_updates_segment_member_to_0_if_achievement_rule_is_removed(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement consecutive progress',
                    'active' => true,
                    'description' => 'Desc - achievement consecutive progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievement1Id,
                                    'achievementRuleId' => $this->achievement1RuleId,
                                    'achievementGoal' => 'current_progress',
                                    'operator' => [
                                        'code' => 'is_less_or_equal',
                                    ],
                                    'progress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $segmentId = json_decode($response->getContent(), true)['segmentId'];

        // updated achievement get rules with new IDs
        $this->updateAchievement(4);

        $this->recreateSegment($segmentId);

        $this->assertSegmentMembersCount(0, $segmentId);
    }

    /**
     * @test
     */
    public function it_creates_segment_with_achievement_rule_consecutive_completed_criteria_is_equal(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement consecutive progress',
                    'active' => true,
                    'description' => 'Desc - achievement consecutive progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievement1Id,
                                    'achievementRuleId' => $this->achievement1RuleId,
                                    'achievementGoal' => 'completed_periods',
                                    'operator' => [
                                        'code' => 'is_equal',
                                    ],
                                    'progress' => 1,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $segment = json_decode($response->getContent(), true);

        $this->assertSegmentMembersCount(3, $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
    }

    /**
     * @test
     */
    public function it_creates_segment_with_achievement_rule_consecutive_completed_criteria_is_equal_0(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement consecutive progress',
                    'active' => true,
                    'description' => 'Desc - achievement consecutive progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievement1Id,
                                    'achievementRuleId' => $this->achievement1RuleId,
                                    'achievementGoal' => 'completed_periods',
                                    'operator' => [
                                        'code' => 'is_equal',
                                    ],
                                    'progress' => 0,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();

        $this->assertOkResponseStatus($response);

        $segment = json_decode($response->getContent(), true);

        $this->assertSegmentMembersCount(5, $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
        $this->assertCustomerIsInSegmentMembers('<EMAIL>', $segment['segmentId']);
    }

    private function assertCustomerIsInSegmentMembers(string $email, string $segmentId): void
    {
        $this->client->request(
            'GET',
            '/api/'.$this->tenantCode.'/segment/'.$segmentId.'/members',
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $customersFromSegmentEmails = array_map(fn (array $item): string => $item['email'], $data['items']);
        $this->assertContains($email, $customersFromSegmentEmails, sprintf('there is no customer with email %s in segment', $email));
    }

    private function assertSegmentMembersCount(int $count, string $segmentId): void
    {
        $this->client->request(
            'GET',
            '/api/'.$this->tenantCode.'/segment/'.$segmentId.'/members',
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertSame($count, $data['total']['all']);
    }

    private function createMember(string $email): Response
    {
        $this->client->jsonRequest(
            'POST',
            '/api/'.$this->tenantCode.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Stone',
                    'email' => $email,
                    'agreement1' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();

        $this->assertOkResponseStatus($response);

        return $response;
    }

    private function createCustomEventSchema(string $eventType): Response
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/customEvent/schema',
            [
                'customEventSchema' => [
                    'eventType' => $eventType,
                    'name' => 'Test schema number 1',
                    'schema' => [
                        'fields' => [
                            [
                                'type' => 'number',
                                'name' => 'numberProperty',
                            ],
                        ],
                    ],
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        return $response;
    }

    private function createAchievementWithRuleConsecutiveAndPeriodGoal(int $periodGoal, int $consecutive = 2): Response
    {
        $achievementData = [
            'achievement' => [
                'active' => true,
                'translations' => [
                    'en' => [
                        'name' => 'Progress custom event achievement',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'custom_event',
                        'event' => 'test_schema_number_1',
                        'completeRule' => [
                            'period' => [
                                'type' => 'day',
                                'consecutive' => $consecutive,
                            ],
                            'periodGoal' => $periodGoal,
                        ],
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'conditions' => [
                            [
                                'attribute' => 'event.body.numberProperty',
                                'operator' => 'is_greater_or_equal',
                                'data' => '10',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/achievement',
            $achievementData
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        return $response;
    }

    private function createCustomEvents(
        string $memberEmail,
        string $eventSchemaType,
        int $count,
        DateTimeImmutable $date
    ): void {
        for ($i = 0; $i < $count; ++$i) {
            $this->client->request(
                'POST',
                '/api/'.$this->tenantCode.'/customEvent',
                [
                    'event' => [
                        'type' => $eventSchemaType,
                        'eventDate' => $date->format('Y-m-d H:i:s'),
                        'customerData' => [
                            'email' => $memberEmail,
                        ],
                        'body' => [
                            'numberProperty' => 10,
                        ],
                    ],
                ]
            );
        }
    }

    private function getIdOfFirstRuleFromAchievement(string $achievementId): string
    {
        $this->client->request(
            'GET',
            sprintf('/api/%s/achievement/%s', $this->tenantCode, $achievementId)
        );

        $response = $this->client->getResponse();
        $achievement = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        return $achievement['rules'][0]['achievementRuleId'];
    }

    private function updateAchievement(int $periodGoal = 4, int $consecutive = 2): Response
    {
        $achievementData = [
            'achievement' => [
                'active' => true,
                'translations' => [
                    'en' => [
                        'name' => 'Progress custom event achievement',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'custom_event',
                        'event' => 'test_schema_number_1',
                        'completeRule' => [
                            'period' => [
                                'type' => 'year',
                                'consecutive' => $consecutive,
                            ],
                            'periodGoal' => $periodGoal,
                        ],
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'conditions' => [
                            [
                                'attribute' => 'event.body.numberProperty',
                                'operator' => 'is_greater_or_equal',
                                'data' => '10',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->client->request(
            'PUT',
            '/api/'.$this->tenantCode.'/achievement/'.$this->achievement1Id,
            $achievementData
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        return $response;
    }

    private function recreateSegment(string $segmentId): void
    {
        $application = new Application(self::$kernel);
        $command = $application->find('oloy:segment:recreate');
        $commandTester = new CommandTester($command);
        $commandTester->execute(
            [
                'command' => $command->getName(),
            ],
            [
                'segmentId' => $segmentId,
            ]
        );
    }
}
