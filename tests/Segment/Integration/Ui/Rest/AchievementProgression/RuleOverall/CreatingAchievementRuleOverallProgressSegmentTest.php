<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Integration\Ui\Rest\AchievementProgression\RuleOverall;

use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class CreatingAchievementRuleOverallProgressSegmentTest extends AbstractApiTest
{
    private string $achievementRuleId;
    private string $achievementId;
    private HttpKernelBrowser $client;
    private string $tenantCode;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = $this->createAuthenticatedClient();

        $this->tenantCode = 'tenant_for_segmentation';
        $this->createTenant($this->tenantCode);

        $this->createCustomEventSchema('test_schema_number_1');

        $data = json_decode($this->createAchievementWithRuleOverallAndPeriodGoal(3)->getContent(), true);
        $this->achievementId = $data['achievementId'];

        $this->achievementRuleId = $this->getIdOfFirstRuleFromAchievement($this->achievementId);
    }

    /**
     * @test
     *
     * @dataProvider simpleOperatorCodesProvider
     */
    public function it_creates_segment_with_achievement_rule_overall_progress_criteria(string $operatorCode): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievementId,
                                    'achievementRuleId' => $this->achievementRuleId,
                                    'operator' => [
                                        'code' => $operatorCode,
                                    ],
                                    'progress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $segment = json_decode($response->getContent(), true);

        $this->client->request(
            'GET',
            '/api/'.$this->tenantCode.'/segment/'.$segment['segmentId']
        );

        $response = $this->client->getResponse();
        $segmentDetails = json_decode($response->getContent(), true);
        $this->assertSame('achievement_rule_progress', $segmentDetails['parts'][0]['criteria'][0]['type']);
        $this->assertSame($operatorCode, $segmentDetails['parts'][0]['criteria'][0]['operator']['code']);
        $this->assertSame(2.0, $segmentDetails['parts'][0]['criteria'][0]['progress']);
    }

    public function simpleOperatorCodesProvider(): array
    {
        return [
            ['is_equal'],
            ['is_greater'],
            ['is_greater_or_equal'],
            ['is_less'],
            ['is_less_or_equal'],
        ];
    }

    /**
     * @test
     */
    public function it_creates_segment_with_achievement_rule_overall_progress_criteria_is_number_between(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievementId,
                                    'achievementRuleId' => $this->achievementRuleId,
                                    'operator' => [
                                        'code' => 'is_number_between',
                                    ],
                                    'fromProgress' => 1,
                                    'toProgress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $segment = json_decode($response->getContent(), true);

        $this->client->request(
            'GET',
            '/api/'.$this->tenantCode.'/segment/'.$segment['segmentId']
        );

        $response = $this->client->getResponse();
        $segmentDetails = json_decode($response->getContent(), true);
        $this->assertSame('achievement_rule_progress', $segmentDetails['parts'][0]['criteria'][0]['type']);
        $this->assertSame('is_number_between', $segmentDetails['parts'][0]['criteria'][0]['operator']['code']);
        $this->assertSame(1.0, $segmentDetails['parts'][0]['criteria'][0]['fromProgress']);
        $this->assertSame(2.0, $segmentDetails['parts'][0]['criteria'][0]['toProgress']);
    }

    /**
     * @test
     */
    public function it_validates_operator_segment_with_achievement_rule_overall_progress_criteria(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievementId,
                                    'achievementRuleId' => $this->achievementRuleId,
                                    'operator' => [
                                        'code' => 'random-string',
                                    ],
                                    'progress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertSame(
            'It is not a valid operator, choose one of: is_equal, is_greater, is_greater_or_equal, is_less, is_less_or_equal, is_number_between',
            $data['errors'][0]['message']
        );
    }

    /**
     * @test
     */
    public function it_validates_segment_with_achievement_rule_overall_progress_criteria_in_between_with_same_values(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievementId,
                                    'achievementRuleId' => $this->achievementRuleId,
                                    'operator' => [
                                        'code' => 'is_number_between',
                                    ],
                                    'fromProgress' => 2,
                                    'toProgress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $this->assertSame('This value should be greater than "2".', $data['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_validates_segment_with_achievement_rule_overall_progress_criteria_in_between_with_from_values_greater_than_to(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievementId,
                                    'achievementRuleId' => $this->achievementRuleId,
                                    'operator' => [
                                        'code' => 'is_number_between',
                                    ],
                                    'fromProgress' => 3,
                                    'toProgress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $this->assertSame('This value should be greater than "3".', $data['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_validates_achievement_rule_id_in_segment_with_achievement_rule_overall_progress_criteria(): void
    {
        $someRandomUuid = '16ee978c-c269-4701-b633-853b78649960';

        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievementId,
                                    'achievementRuleId' => $someRandomUuid,
                                    'operator' => [
                                        'code' => 'is_equal',
                                    ],
                                    'progress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();

        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame('Provided achievement rule is not from provided achievement.', $data['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_validates_progress_segment_with_achievement_rule_overall_progress_criteria_with_progress_greater_than_max_progress_from_achievement(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievementId,
                                    'achievementRuleId' => $this->achievementRuleId,
                                    'operator' => [
                                        'code' => 'is_equal',
                                    ],
                                    'progress' => 5,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertSame(
            'This value should be less than or equal to 3',
            $data['errors'][0]['message']
        );
    }

    /**
     * @test
     */
    public function it_validates_progress_segment_with_achievement_rule_overall_progress_criteria_with_is_number_between_from_greater_than_to(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievementId,
                                    'achievementRuleId' => $this->achievementRuleId,
                                    'operator' => [
                                        'code' => 'is_number_between',
                                    ],
                                    'fromProgress' => 2,
                                    'toProgress' => 1,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertSame(
            'This value should be greater than "2".',
            $data['errors'][0]['message']
        );
    }

    /**
     * @test
     */
    public function it_validates_progress_segment_with_achievement_rule_overall_progress_criteria_with_is_number_between(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievementId,
                                    'achievementRuleId' => $this->achievementRuleId,
                                    'operator' => [
                                        'code' => 'is_number_between',
                                    ],
                                    'fromProgress' => 1,
                                    'toProgress' => 5,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertSame(
            'This value should be less than or equal to 3',
            $data['errors'][0]['message']
        );
    }

    /**
     * @test
     */
    public function it_validates_progress_segment_with_achievement_rule_overall_progress_criteria_with_is_number_between_with_from_progress_is_not_set(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievementId,
                                    'achievementRuleId' => $this->achievementRuleId,
                                    'operator' => [
                                        'code' => 'is_number_between',
                                    ],
                                    'toProgress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertSame(
            'This value should not be blank.',
            $data['errors'][0]['message']
        );
    }

    /**
     * @test
     */
    public function it_validates_progress_segment_with_achievement_rule_overall_progress_if_achievement_rule_is_not_from_achievement(): void
    {
        $differentAchievementId = json_decode($this->createAchievementWithRuleOverallAndPeriodGoal(3)->getContent(), true)['achievementId'];

        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $differentAchievementId,
                                    'achievementRuleId' => $this->achievementRuleId,
                                    'operator' => [
                                        'code' => 'is_number_between',
                                    ],
                                    'fromProgress' => 1,
                                    'toProgress' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertSame(
            'Provided achievement rule is not from provided achievement.',
            $data['errors'][0]['message']
        );
    }

    /**
     * @test
     */
    public function it_validates_progress_segment_with_achievement_rule_equal_null(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievementId,
                                    'achievementRuleId' => null,
                                    'operator' => [
                                        'code' => 'is_equal',
                                    ],
                                    'progress' => 5,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertSame(
            'This value should not be blank.',
            $data['errors'][0]['message']
        );
    }

    /**
     * @test
     */
    public function it_validates_progress_segment_with_achievement_rule_equal_empty_string(): void
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Achievement overall progress',
                    'active' => true,
                    'description' => 'Desc - achievement overall progress',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'achievement_rule_progress',
                                    'achievementId' => $this->achievementId,
                                    'achievementRuleId' => '',
                                    'operator' => [
                                        'code' => 'is_equal',
                                    ],
                                    'progress' => 5,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertSame(
            'This value should not be blank.',
            $data['errors'][0]['message']
        );
    }

    private function createAchievementWithRuleOverallAndPeriodGoal(int $periodGoal = 1): Response
    {
        $achievementData = [
            'achievement' => [
                'active' => true,
                'translations' => [
                    'en' => [
                        'name' => 'Progress custom event achievement',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'custom_event',
                        'event' => 'test_schema_number_1',
                        'completeRule' => [
                            'period' => [
                                'type' => 'overall',
                            ],
                            'periodGoal' => $periodGoal,
                        ],
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'conditions' => [
                            [
                                'attribute' => 'event.body.numberProperty',
                                'operator' => 'is_greater_or_equal',
                                'data' => '10',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/achievement',
            $achievementData
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        return $response;
    }

    private function createAchievementWithRuleLastXDaysAndPeriodGoal(int $periodGoal = 1): Response
    {
        $achievementData = [
            'achievement' => [
                'active' => true,
                'translations' => [
                    'en' => [
                        'name' => 'Progress custom event achievement',
                    ],
                ],
                'rules' => [
                    [
                        'type' => 'direct',
                        'trigger' => 'custom_event',
                        'event' => 'test_schema_number_1',
                        'completeRule' => [
                            'period' => [
                                'type' => 'last_day',
                                'value' => $periodGoal,
                            ],
                            'periodGoal' => $periodGoal,
                        ],
                        'aggregation' => [
                            'type' => 'quantity',
                        ],
                        'conditions' => [
                            [
                                'attribute' => 'event.body.numberProperty',
                                'operator' => 'is_greater_or_equal',
                                'data' => '10',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/achievement',
            $achievementData
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        return $response;
    }

    private function createCustomEventSchema(string $eventType): Response
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/customEvent/schema',
            [
                'customEventSchema' => [
                    'eventType' => $eventType,
                    'name' => 'Test schema number 1',
                    'schema' => [
                        'fields' => [
                            [
                                'type' => 'number',
                                'name' => 'numberProperty',
                            ],
                        ],
                    ],
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        return $response;
    }

    private function getIdOfFirstRuleFromAchievement(string $achievementId): string
    {
        $this->client->request(
            'GET',
            sprintf('/api/%s/achievement/%s', $this->tenantCode, $achievementId)
        );

        $response = $this->client->getResponse();
        $achievement = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        return $achievement['rules'][0]['achievementRuleId'];
    }

    private function createAchievementWithConsecutiveRule(): string
    {
        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/achievement',
            [
                'achievement' => [
                    'active' => true,
                    'translations' => [
                        'en' => [
                            'name' => 'Consecutive 3 transactions achievement',
                            'description' => 'achievement description',
                        ],
                    ],
                    'activity' => [],
                    'rules' => [
                        [
                            'type' => 'direct',
                            'trigger' => 'transaction',
                            'completeRule' => [
                                'period' => [
                                    'type' => 'day',
                                    'consecutive' => 3,
                                ],
                                'periodGoal' => 7,
                            ],
                            'aggregation' => [
                                'type' => 'quantity',
                            ],
                            'conditions' => [
                                [
                                    'attribute' => 'transaction.grossValue',
                                    'operator' => 'is_greater_or_equal',
                                    'data' => '50',
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        return $data['achievementId'];
    }
}
