<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Integration\Ui\Rest;

use OpenLoyalty\Segment\Domain\Model\Criterion;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class BoughtSkuSegmentationTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_segments_member_by_bought_sku(): void
    {
        $client = self::createAuthenticatedClient();

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'firstName' => 'Johns',
                    'lastName' => 'Doe',
                    'gender' => 'male',
                    'birthDate' => '2000-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => 'TEST/CA/0121',
                        'documentType' => 'sell',
                        'purchasedAt' => '2022-12-06T15:00:00+01:00',
                    ],
                    'items' => [
                        [
                            'sku' => '123',
                            'name' => 'SKU123',
                            'quantity' => 1,
                            'grossValue' => 150,
                            'category' => 'Christmas gift',
                        ],
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'firstName' => 'Johns',
                    'lastName' => 'Doe',
                    'gender' => 'male',
                    'birthDate' => '2000-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '11-800',
                        'city' => 'Białystok',
                        'country' => 'PL',
                        'province' => 'podlaskie',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => 'TEST/CA/0232',
                        'documentType' => 'sell',
                        'purchasedAt' => '2022-12-06T15:00:00+01:00',
                    ],
                    'items' => [
                        [
                            'sku' => '1233',
                            'name' => 'SKU1233',
                            'quantity' => 1,
                            'grossValue' => 15,
                            'category' => 'Christmas gift',
                            'maker' => 'companyB',
                        ],
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'firstName' => 'Johns',
                    'lastName' => 'Doe',
                    'gender' => 'male',
                    'birthDate' => '2000-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '11-800',
                        'city' => 'Białystok',
                        'country' => 'PL',
                        'province' => 'podlaskie',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => 'TEST/CA/02134',
                        'documentType' => 'sell',
                        'purchasedAt' => '2022-12-06T15:00:00+01:00',
                    ],
                    'items' => [
                        [
                            'sku' => '124',
                            'name' => 'SKU1233',
                            'quantity' => 1,
                            'grossValue' => 15,
                            'category' => 'Christmas gift',
                        ],
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/segment',
            [
                'segment' => [
                    'name' => 'bought sku',
                    'description' => 'desc',
                    'active' => true,
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => Criterion::TYPE_BOUGHT_SKUS,
                                    'skuIds' => [
                                        '1233',
                                        '44',
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $client->getResponse();

        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $segmentId = $data['segmentId'];

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/segment/'.$segmentId.'/members',
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        self::assertSame(1, $data['total']['all']);
        self::assertSame('<EMAIL>', $data['items'][0]['email']);

        //update segment and see if it recreate correctly

        $client->request(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/segment/'.$segmentId,
            [
                'segment' => [
                    'name' => 'update bought sku',
                    'description' => 'desc',
                    'active' => true,
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => Criterion::TYPE_BOUGHT_SKUS,
                                    'skuIds' => [
                                        '124',
                                        '44',
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $client->getResponse();

        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $segmentId = $data['segmentId'];

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/segment/'.$segmentId.'/members',
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        self::assertSame(1, $data['total']['all']);
        self::assertSame('<EMAIL>', $data['items'][0]['email']);
    }
}
