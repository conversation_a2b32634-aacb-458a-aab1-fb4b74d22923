<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Integration\Ui\Rest\MemberRegistrationDate;

use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Segment\Integration\Trait\SegmentApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class CreatingMemberRegistrationDateSegmentTest extends AbstractApiTest
{
    use SegmentApiTrait;

    private HttpKernelBrowser $client;
    private string $tenantCode;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();

        $this->tenantCode = 'tenant_for_segmentation';
        $this->createTenant($this->tenantCode);
    }

    /**
     * @test
     * @dataProvider segmentValidDataProvider
     */
    public function it_creates_segment_with_member_registration_date_criterion(string $operatorCode, array $daysArray): void
    {
        $criterion = array_merge([
            'type' => 'member_registration_date',
            'operator' => [
                'code' => $operatorCode,
            ],
        ], $daysArray);

        $segmentResponse = $this->postSegment(
            $this->client,
            $this->tenantCode,
                parts: [
                    [
                        'criteria' => [
                            $criterion,
                        ],
                    ],
                ],
        );

        $this->assertOkResponseStatus($segmentResponse);

        $segment = json_decode($segmentResponse->getContent(), true);

        $this->client->request(
            'GET',
            '/api/'.$this->tenantCode.'/segment/'.$segment['segmentId']
);
        $response = $this->client->getResponse();
        $segmentDetails = json_decode($response->getContent(), true);

        $this->assertSame('member_registration_date', $segmentDetails['parts'][0]['criteria'][0]['type']);
        $this->assertSame($operatorCode, $segmentDetails['parts'][0]['criteria'][0]['operator']['code']);
    }

    public function segmentValidDataProvider(): array
    {
       return [
           'segment_with_member_registration_date_is_equal' => ['is_equal', ['days' => 2]],
           'segment_with_member_registration_date_is_greater' => ['is_greater', ['days' => 2]],
           'segment_with_member_registration_date_is_greater_or_equal' => ['is_greater_or_equal', ['days' => 2]],
           'segment_with_member_registration_date_is_less' => ['is_less', ['days' => 2]],
           'segment_with_member_registration_date_is_less_or_equal' => ['is_less_or_equal', ['days' => 2]],
           'segment_with_member_registration_date_is_between' => ['is_number_between', ['fromDays' => 2, 'toDays' => 5]],
           'segment_with_member_registration_date_is_between_from_0' => ['is_number_between', ['fromDays' => 0, 'toDays' => 5]],
       ];
    }

    /**
     * @test
     * @dataProvider segmentInValidDataProvider
     */
    public function it_validates_segment_with_member_registration_date_criterion(string $operatorCode, array $daysArray, string $errorMessage): void
    {
        $criterion = array_merge([
            'type' => 'member_registration_date',
            'operator' => [
                'code' => $operatorCode,
            ],
        ], $daysArray);

        $segmentResponse = $this->postSegment(
            $this->client,
            $this->tenantCode,
            parts: [
                [
                    'criteria' => [
                        $criterion,
                    ],
                ],
            ],
        );

        $this->assertBadRequestResponseStatus($segmentResponse);
        $data = json_decode($segmentResponse->getContent(), true);
        $this->assertStringContainsString($errorMessage, $data['errors'][0]['message']);
    }

    public function segmentInValidDataProvider(): array
    {
        return [
            'invalid_operator_code' => ['random', ['days' => 2], 'It is not a valid operator, choose one of:'],
            'invalid_same_days' => ['is_number_between', ['fromDays' => 2, 'toDays' => 2], 'This value should be greater than 2.'],
            'invalid_days_out_of_range' => ['is_number_between', ['fromDays' => 12, 'toDays' => 7399], 'This value should be less than 7305.'],
            'invalid_days_from_greater_that_to' => ['is_number_between', ['fromDays' => 12, 'toDays' => 10], 'This value should be greater than 12.'],
            'invalid_days_missing_field' => ['is_number_between', ['toDays' => 10], 'This value should not be blank.'],
        ];
    }
}
