<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Integration\Ui\Rest;

use OpenLoyalty\Segment\Domain\Model\Criterion;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class CustomerHasLabelSegmentationTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_segments_member_with_label(): void
    {
        $client = self::createAuthenticatedClient();

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'firstName' => 'Johns',
                    'lastName' => 'Doe',
                    'gender' => 'male',
                    'birthDate' => '2000-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'labels' => [
                        [
                            'key' => 'label1',
                            'value' => 'label1',
                        ],
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'firstName' => 'Johns',
                    'lastName' => 'Doe',
                    'gender' => 'male',
                    'birthDate' => '2000-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Białystok',
                        'country' => 'PL',
                        'province' => 'Podlaskie',
                    ],
                    'labels' => [
                        [
                            'key' => 'label2',
                            'value' => 'label2',
                        ],
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/segment',
            [
                'segment' => [
                    'name' => 'customer has label',
                    'description' => 'desc',
                    'active' => true,
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => Criterion::TYPE_CUSTOMER_HAS_LABELS,
                                    'labels' => [
                                        ['key' => 'label1'],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $client->getResponse();

        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $segmentId = $data['segmentId'];

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/segment/'.$segmentId.'/members',
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        self::assertSame(1, $data['total']['all']);
        self::assertSame('<EMAIL>', $data['items'][0]['email']);

        //update segment and see if it recreate correctly
        $client->request(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/segment/'.$segmentId,
            [
                'segment' => [
                    'name' => 'updated customer has label',
                    'description' => 'desc',
                    'active' => true,
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => Criterion::TYPE_CUSTOMER_HAS_LABELS,
                                    'labels' => [
                                        ['key' => 'label2'],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $client->getResponse();

        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $segmentId = $data['segmentId'];

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/segment/'.$segmentId.'/members',
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        self::assertSame(1, $data['total']['all']);
        self::assertSame('<EMAIL>', $data['items'][0]['email']);
    }
}
