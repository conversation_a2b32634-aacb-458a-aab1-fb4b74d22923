<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Integration\Ui\Rest;

use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Segment\Domain\SegmentPartRepository;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class DoctrineSegmentPartRepositoryTest extends AbstractApiTest
{
    private HttpKernelBrowser $client;
    private SegmentPartRepository $segmentPartRepository;

    protected function setUp(): void
    {
        parent::setUp();
        self::ensureKernelShutdown();
        $this->client = self::createAuthenticatedClient();
        $this->segmentPartRepository = self::getContainer()->get(SegmentPartRepository::class);
    }

    /**
     * @test
     */
    public function it_find_that_campaign_exists_in_segment_part(): void
    {
        $tenantCode = 'CampaignCompletion';
        $storeId = $this->createTenant($tenantCode);

        $this->client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Campaign name',
                        ],
                    ],
                    'active' => false,
                    'type' => 'direct',
                    'trigger' => 'transaction',
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'rules' => [
                        [
                            'effects' => [
                                [
                                    'effect' => 'give_points',
                                    'pointsRule' => 15,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $campaignId = $data['campaignId'];

        $this->client->request(
            'POST',
            '/api/'.$tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Campaign completion',
                    'description' => 'Campaign completion segment',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'campaign_completion',
                                    'campaignId' => $campaignId,
                                    'timeframe' => [
                                        'type' => 'last_x_days',
                                        'number' => 30,
                                    ],
                                    'completionNumber' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $segmentId = $data['segmentId'];

        $this->client->request('GET', '/api/'.$tenantCode.'/segment/'.$segmentId);

        $response = $this->client->getResponse();
        $segmentData = json_decode($response->getContent(), true);

        $this->assertSame('Campaign completion', $segmentData['name']);
        $this->assertSame('Campaign completion segment', $segmentData['description']);
        $this->assertSame('campaign_completion', $segmentData['parts'][0]['criteria'][0]['type']);
        $this->assertSame($campaignId, $segmentData['parts'][0]['criteria'][0]['campaignId']);

        $result = $this->segmentPartRepository->isCampaignExistsInSegmentPartCriteria(
            new CampaignId($campaignId),
            new StoreId($storeId)
        );

        $this->assertSame(true, $result);
    }

    /**
     * @test
     */
    public function it_does_not_find_that_campaign_exists_in_segment_part(): void
    {
        $tenantCode = 'CampaignCompletion';
        $storeId = $this->createTenant($tenantCode);

        $this->client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Campaign name',
                        ],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => 'transaction',
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'rules' => [
                        [
                            'effects' => [
                                [
                                    'effect' => 'give_points',
                                    'pointsRule' => 15,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $campaignId = $data['campaignId'];

        $this->client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Campaign name',
                        ],
                    ],
                    'active' => false,
                    'type' => 'direct',
                    'trigger' => 'transaction',
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'rules' => [
                        [
                            'effects' => [
                                [
                                    'effect' => 'give_points',
                                    'pointsRule' => 15,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $campaignId1 = $data['campaignId'];

        $this->client->request(
            'POST',
            '/api/'.$tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Campaign completion',
                    'description' => 'Campaign completion segment',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'campaign_completion',
                                    'campaignId' => $campaignId,
                                    'timeframe' => [
                                        'type' => 'last_x_days',
                                        'number' => 30,
                                    ],
                                    'completionNumber' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $result = $this->segmentPartRepository->isCampaignExistsInSegmentPartCriteria(
            new CampaignId($campaignId1),
            new StoreId($storeId)
        );

        $this->assertSame(false, $result);
    }
}
