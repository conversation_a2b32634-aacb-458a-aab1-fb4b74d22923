<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Integration\Ui\Rest;

use OpenLoyalty\Segment\Domain\Model\Criterion;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class PurchasePeriodSegmentationTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_segments_member_by_purchase_period(): void
    {
        $client = self::createAuthenticatedClient();
        $storeCode = 'purchasePeriodCodeTenant';
        $this->createTenant($storeCode);
        $client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'firstName' => 'Johns',
                    'lastName' => 'Doe',
                    'gender' => 'male',
                    'birthDate' => '2000-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => 'TEST/CA/0121',
                        'documentType' => 'sell',
                        'purchasedAt' => '2022-12-06T15:00:00+01:00',
                    ],
                    'items' => [
                        [
                            'sku' => '123',
                            'name' => 'SKU123',
                            'quantity' => 1,
                            'grossValue' => 150,
                            'category' => 'Christmas gift',
                            'labels' => [
                                ['key' => 'k3', 'value' => 'v3'],
                                ['key' => 'k4', 'value' => 'v4'],
                            ],
                        ],
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        $client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'firstName' => 'Johns',
                    'lastName' => 'Doe',
                    'gender' => 'male',
                    'birthDate' => '2000-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '11-800',
                        'city' => 'Białystok',
                        'country' => 'PL',
                        'province' => 'podlaskie',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        //bought with correct labels
        $client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => 'TEST/CA/0232',
                        'documentType' => 'sell',
                        'purchasedAt' => '2022-12-10T15:00:00+01:00',
                    ],
                    'items' => [
                        [
                            'sku' => '1233',
                            'name' => 'SKU1233',
                            'quantity' => 1,
                            'grossValue' => 15,
                            'category' => 'Christmas gift',
                            'labels' => [
                                ['key' => 'k1', 'value' => 'v1'],
                            ],
                        ],
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        $client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'firstName' => 'Johns',
                    'lastName' => 'Doe',
                    'gender' => 'male',
                    'birthDate' => '2000-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '11-800',
                        'city' => 'Białystok',
                        'country' => 'PL',
                        'province' => 'podlaskie',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        //bought without labels
        $client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => 'TEST/CA/02134',
                        'documentType' => 'sell',
                        'purchasedAt' => '2022-12-12T15:00:00+01:00',
                    ],
                    'items' => [
                        [
                            'sku' => '1233',
                            'name' => 'SKU1233',
                            'quantity' => 1,
                            'grossValue' => 15,
                            'category' => 'Christmas gift',
                        ],
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($client->getResponse());

        $client->request(
            'POST',
            '/api/'.$storeCode.'/segment',
            [
                'segment' => [
                    'name' => 'purchase in period',
                    'description' => 'desc',
                    'active' => true,
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => Criterion::TYPE_PURCHASE_PERIOD,
                                    'fromDate' => '2022-12-05T15:00:00+01:00',
                                    'toDate' => '2022-12-07T15:00:00+01:00',
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $client->getResponse();

        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $segmentId = $data['segmentId'];

        $client->request(
            'GET',
            '/api/'.$storeCode.'/segment/'.$segmentId.'/members',
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        self::assertSame(1, $data['total']['all']);
        self::assertSame('<EMAIL>', $data['items'][0]['email']);

        //update segment and see if it recreate correctly
        $client->request(
            'PUT',
            '/api/'.$storeCode.'/segment/'.$segmentId,
            [
                'segment' => [
                    'name' => 'update purchase in period',
                    'description' => 'desc',
                    'active' => true,
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => Criterion::TYPE_PURCHASE_PERIOD,
                                    'fromDate' => '2022-12-11T15:00:00+01:00',
                                    'toDate' => '2022-12-13T15:00:00+01:00',
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $client->getResponse();

        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $segmentId = $data['segmentId'];

        $client->request(
            'GET',
            '/api/'.$storeCode.'/segment/'.$segmentId.'/members',
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        self::assertSame(1, $data['total']['all']);
        self::assertSame('<EMAIL>', $data['items'][0]['email']);
    }
}
