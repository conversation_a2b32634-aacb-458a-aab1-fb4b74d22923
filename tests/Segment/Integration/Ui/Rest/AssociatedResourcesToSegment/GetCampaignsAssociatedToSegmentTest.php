<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Integration\Ui\Rest\AssociatedResourcesToSegment;

use League\OpenAPIValidation\PSR7\Exception\Validation\AddressValidationFailed;
use OpenLoyalty\Segment\Domain\Model\Criterion;
use OpenLoyalty\Test\Campaign\Integration\Traits\CampaignApiTrait;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\Segment\Integration\Trait\SegmentApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class GetCampaignsAssociatedToSegmentTest extends AbstractApiTest
{
    use CampaignApiTrait;
    use TenantApiTrait;
    use SegmentApiTrait;

    private HttpKernelBrowser $client;
    private string $tenantCode = 'segmentsAssociatedToCampaign';

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
        $this->postTenant($this->client, $this->tenantCode);
    }

    /**
     * @test
     */
    public function it_returns_a_paginated_list_of_campaigns_associated_with_segment(): void
    {
        $segment1Id = $this->createSegment('segment 1');
        $segment2Id = $this->createSegment('segment 2');
        $segment3Id = $this->createSegment('segment 3');

        $this->createCampaignAssociatedToSegmentAndReturnId('campaign 1', $segment1Id);
        $this->createCampaignAssociatedToSegmentAndReturnId('campaign 2', $segment1Id, $segment2Id);
        $this->createCampaignAssociatedToSegmentAndReturnId('campaign 3', $segment3Id);
        $this->createCampaignAssociatedToSegmentAndReturnId('campaign 4', $segment1Id);

        $campaignsNamesFromAllPages = [];
        $page1 = $this->getCampaignsAssociatedToSegment($segment1Id, page: 1, itemsOnPage: 2);
        $this->assertCount(2, $page1['items']);

        $campaignsNamesFromAllPages[] = $page1['items'][0]['name'];
        $campaignsNamesFromAllPages[] = $page1['items'][1]['name'];

        $page2 = $this->getCampaignsAssociatedToSegment($segment1Id, page: 2, itemsOnPage: 2);
        $this->assertCount(1, $page2['items']);
        $campaignsNamesFromAllPages[] = $page2['items'][0]['name'];

        $this->assertCount(3, $campaignsNamesFromAllPages);
        $this->assertArrayHasUniqueSubset(['campaign 1', 'campaign 2', 'campaign 4'], $campaignsNamesFromAllPages);
    }

    /**
     * @test
     */
    public function it_returns_a_list_of_campaigns_associated_with_segment_with_additional_data_from_campaign(): void
    {
        $segment1Id = $this->createSegment('segment 1');

        $this->createCampaignAssociatedToSegmentAndReturnId('campaign 1', $segment1Id);

        $page = $this->getCampaignsAssociatedToSegment($segment1Id, page: 1, itemsOnPage: 2);
        $this->assertArrayHasKey('campaign_type', $page['items']['0']['additionalData']);
        $this->assertSame($page['items']['0']['additionalData']['campaign_type'], 'direct');
        $this->assertArrayHasKey('campaign_trigger', $page['items']['0']['additionalData']);
        $this->assertSame($page['items']['0']['additionalData']['campaign_trigger'], 'transaction');
    }

    /**
     * @test
     */
    public function it_returns_a_paginated_list_of_campaigns_associated_to_segment_with_es_names(): void
    {
        $segment1Id = $this->createSegment('segment 1');
        $segment2Id = $this->createSegment('segment 2');
        $segment3Id = $this->createSegment('segment 3');

        $this->createCampaignAssociatedToSegmentAndReturnId('campaign 1', $segment1Id);
        $this->createCampaignAssociatedToSegmentAndReturnId('campaign 2', $segment1Id, $segment2Id);
        $this->createCampaignAssociatedToSegmentAndReturnId('campaign 3', $segment3Id);
        $this->createCampaignAssociatedToSegmentAndReturnId('campaign 4', $segment1Id);

        $this->client->jsonRequest(
            'PUT',
            '/api/language/es',
            [
                'code' => 'es',
                'name' => 'Spanish',
                'adminDefault' => false,
                'apiDefault' => true,
                'order' => 1,
            ]
        );

        $campaignsNamesFromAllPages = [];
        $page1 = $this->getCampaignsAssociatedToSegment($segment1Id, page: 1, itemsOnPage: 2);
        $this->assertCount(2, $page1['items']);

        $campaignsNamesFromAllPages[] = $page1['items'][0]['name'];
        $campaignsNamesFromAllPages[] = $page1['items'][1]['name'];

        $page2 = $this->getCampaignsAssociatedToSegment($segment1Id, page: 2, itemsOnPage: 2);
        $this->assertCount(1, $page2['items']);
        $campaignsNamesFromAllPages[] = $page2['items'][0]['name'];

        $this->assertCount(3, $campaignsNamesFromAllPages);
        $this->assertArrayHasUniqueSubset(['campaign 1_es', 'campaign 2_es', 'campaign 4_es'], $campaignsNamesFromAllPages);
    }

    /**
     * @test
     */
    public function it_returns_empty_array_with_there_no_valid_associations(): void
    {
        $segmentId = $this->createSegment('segment');
        $this->client->request(
            'GET',
            "/api/$this->tenantCode/segment/$segmentId/association/campaign",
        );

        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->assertCount(0, $data['items']);
    }

    /**
     * @test
     */
    public function it_returns_error_if_associated_resource_type_is_not_supported(): void
    {
        $segmentId = $this->createSegment('name');
        $this->createCampaignAssociatedToSegmentAndReturnId('campaign 1', $segmentId);

        try {
            $this->client->request(
                'GET',
                "/api/$this->tenantCode/segment/$segmentId/association/random-type",
            );
        } catch (AddressValidationFailed $e) {
            $this->addWarning('Suppressed contract validation exception with message: '.$e->getMessage());
        }

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertSame('Invalid resource type, choose supported type.', $data['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_returns_all_type_of_associations_with_campaign(): void
    {
        $segmentId = $this->createSegment('segment1');

        $this->createCampaignWithVisibilityToSegment('campaign1', $segmentId);
        $this->createCampaignWithAudienceToSegment('campaign2', $segmentId);
        $this->createCampaignAssociatedToSegmentAndReturnId('campaign3', $segmentId);

        $page1 = $this->getCampaignsAssociatedToSegment($segmentId, 1, 5);
        $this->assertContains($page1['items'][0]['name'], ['campaign1', 'campaign2', 'campaign3']);
    }

    private function createCampaignWithVisibilityToSegment(string $name, string $segmentId): string
    {
        $this->postCampaign(
            $this->client,
            $this->tenantCode,
            translations: ['en' => ['name' => $name]],
            visibility: [
                'target' => 'segment',
                'segments' => [$segmentId],
            ]
        );

        $data = json_decode($this->client->getResponse()->getContent(), true);

        return $data['campaignId'];
    }

    private function createCampaignWithAudienceToSegment(string $name, string $segmentId): string
    {
        $this->postCampaign(
            $this->client,
            $this->tenantCode,
            translations: ['en' => ['name' => $name]],
            audience: [
                'target' => 'segment',
                'segments' => [$segmentId],
            ]
        );

        $data = json_decode($this->client->getResponse()->getContent(), true);

        return $data['campaignId'];
    }

    private function createCampaignAssociatedToSegmentAndReturnId(string $name, string ...$segmentIds): string
    {
        $conditions = [];

        foreach ($segmentIds as $segmentId) {
            $conditions[] =
                [
                    'attribute' => 'customer.segments',
                    'operator' => 'contains',
                    'data' => $segmentId,
                ];
        }

        $this->postCampaign(
            $this->client,
            $this->tenantCode,
            translations: ['en' => ['name' => $name], 'es' => ['name' => $name.'_es']],
            active: false,
            rules: [
                [
                    'conditions' => $conditions,
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '10',
                        ],
                    ],
                ],
            ]
        );

        $data = json_decode($this->client->getResponse()->getContent(), true);

        return $data['campaignId'];
    }

    private function getCampaignsAssociatedToSegment(string $segmentId, int $page, int $itemsOnPage): array
    {
        $this->client->request(
            'GET',
            "/api/$this->tenantCode/segment/$segmentId/association/campaign",
            [
                '_page' => $page,
                '_itemsOnPage' => $itemsOnPage,
            ],
        );

        $response = $this->client->getResponse();

        return json_decode($response->getContent(), true);
    }

    private function createSegment(string $name): string
    {
        $this->postSegment(
            $this->client,
            $this->tenantCode,
            name: $name,
            parts: [
                [
                    'criteria' => [
                        [
                            'anniversaryType' => 'birthday',
                            'type' => Criterion::TYPE_ANNIVERSARY,
                            'days' => 2,
                        ],
                    ],
                ],
            ],
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        return $data['segmentId'];
    }
}
