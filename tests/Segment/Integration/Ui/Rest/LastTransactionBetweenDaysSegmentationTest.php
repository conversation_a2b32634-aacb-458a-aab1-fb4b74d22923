<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Integration\Ui\Rest;

use Carbon\CarbonImmutable;
use DateTimeImmutable;
use OpenLoyalty\Segment\Domain\Model\Criterion;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\Segment\Integration\Trait\SegmentApiTrait;
use OpenLoyalty\Test\Transaction\Integration\Traits\TransactionApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;

final class LastTransactionBetweenDaysSegmentationTest extends AbstractApiTest
{
    use TenantApiTrait;
    use MemberApiTrait;
    use TransactionApiTrait;
    use SegmentApiTrait;

    private $client;

    public function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_segments_member_by_last_transaction_between_days(): void
    {
        $tenantCode = 'segments_member_by_last_transaction_between_days';
        $this->postTenant($this->client, $tenantCode);

        CarbonImmutable::setTestNow('2025-02-13');
        $fiveDaysAgo = CarbonImmutable::now()->modify('-5 days');
        $sixDaysAgo = CarbonImmutable::now()->modify('-6 days');
        $sevenDaysAgo = CarbonImmutable::now()->modify('-7 days');

        $email1 = '<EMAIL>';
        $this->postMember($this->client, $tenantCode, $email1);
        $this->createTransaction($tenantCode, $fiveDaysAgo, $email1);

        $email2 = '<EMAIL>';
        $this->postMember($this->client, $tenantCode, $email2);
        $this->createTransaction($tenantCode, $sixDaysAgo, $email2);

        $email3 = '<EMAIL>';
        $this->postMember($this->client, $tenantCode, $email3);
        $this->createTransaction($tenantCode, $sevenDaysAgo, $email3);

        $this->postSegment($this->client, $tenantCode, parts: [
            [
                'criteria' => [
                    [
                        'type' => Criterion::TYPE_LAST_TRANSACTION_BETWEEN_DAYS,
                        'fromDays' => 1,
                        'toDays' => 5,
                    ],
                ],
            ],
        ]);

        $response = $this->client->getResponse();

        $this->assertOkResponseStatus($response);

        $segmentId = json_decode($response->getContent(), true)['segmentId'];

        $this->assertSegmentContainsOnlyUsers($segmentId, $tenantCode, '<EMAIL>');

        //update segment and see if it recreate correctly
        $this->putSegment($this->client, $segmentId, $tenantCode, parts: [
            [
                'criteria' => [
                    [
                        'type' => Criterion::TYPE_LAST_TRANSACTION_BETWEEN_DAYS,
                        'fromDays' => 6,
                        'toDays' => 10,
                    ],
                ],
            ],
        ]);

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->assertSegmentContainsOnlyUsers($segmentId, $tenantCode, '<EMAIL>', '<EMAIL>');
    }

    /**
     * @test
     */
    public function it_try_to_create_invalid_segments_type_last_transaction_between_days(): void
    {
        $tenantCode = 'try_to_create_invalid_segments_type_last_transaction_between_days';
        $this->postTenant($this->client, $tenantCode);

        $this->postSegment($this->client, $tenantCode, parts: [
            [
                'criteria' => [
                    [
                        'type' => Criterion::TYPE_LAST_TRANSACTION_BETWEEN_DAYS,
                        'fromDays' => 4,
                        'toDays' => 2,
                    ],
                ],
            ],
        ]);

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
    }

    private function createTransaction(string $tenantCode, DateTimeImmutable $daysAgo, string $email): void
    {
        $this->postTransaction($this->client,
            $tenantCode,
            header: [
                'documentNumber' => 'TEST/CA/0121'.$daysAgo->format('Y-m-d H:i:s'),
                'documentType' => 'sell',
                'purchasedAt' => $daysAgo->format('Y-m-d H:i:s'),
            ], customerData: [
                'email' => $email,
            ]
        );

        $this->assertOkResponseStatus($this->client->getResponse());
    }

    private function assertSegmentContainsOnlyUsers(string $segmentId, string $tenantCode, ...$userEmails): void
    {
        $response = $this->getSegmentMembers($this->client, $tenantCode, $segmentId);
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        self::assertSame(count($userEmails), $data['total']['all']);

        $userEmailsFromSegment = array_map(fn (array $item) => $item['email'], $data['items']);

        foreach ($userEmails as $userEmail) {
            $this->assertContains($userEmail, $userEmailsFromSegment);
        }
    }
}
