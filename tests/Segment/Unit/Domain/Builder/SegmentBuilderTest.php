<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Unit\Domain\Builder;

use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Segment\Domain\Builder\SegmentBuilder;
use OpenLoyalty\Segment\Domain\Segment;
use OpenLoyalty\Segment\Domain\Segmentation\SegmentationProvider;
use OpenLoyalty\Segment\Domain\SegmentCustomerProjector;
use OpenLoyalty\Segment\Domain\SegmentCustomerRepositoryInterface;
use OpenLoyalty\Segment\Domain\SegmentRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class SegmentBuilderTest extends TestCase
{
    private SegmentCustomerProjector $segmentedCustomersProjector;
    private MockObject&SegmentationProvider $segmentationProvider;
    private MockObject&SegmentRepository $segmentsRepository;
    private MockObject&LoggerInterface $segmentLogger;

    protected function setUp(): void
    {
        $this->segmentationProvider = $this->createMock(SegmentationProvider::class);
        $this->segmentsRepository = $this->createMock(SegmentRepository::class);
        $this->segmentLogger = $this->createMock(LoggerInterface::class);
        $this->segmentedCustomersProjector = new SegmentCustomerProjector(
            $this->segmentsRepository,
            $this->segmentLogger,
            $this->createMock(UuidGeneratorInterface::class),
            $this->createMock(SegmentCustomerRepositoryInterface::class),
            100
        );
    }

    /**
     * @test
     */
    public function it_recreates_segment(): void
    {
        $this->segmentsRepository->expects(self::once())
            ->method('byId')
            ->with(new SegmentId('6ce8110a-82d7-4f98-8e14-fd010b27231a'))
            ->willReturn($this->createMock(Segment::class));

        $this->segmentationProvider->expects(self::once())
            ->method('evaluateSegment');

        $this->segmentLogger->expects(self::exactly(4))
            ->method('info');

        $segmentBuilder = new SegmentBuilder(
            $this->segmentedCustomersProjector,
            $this->segmentationProvider,
            $this->segmentsRepository,
            $this->segmentLogger
        );
        $segmentBuilder->recreate(new SegmentId('6ce8110a-82d7-4f98-8e14-fd010b27231a'));
    }

    /**
     * @test
     */
    public function it_does_not_recreate_not_found_segment(): void
    {
        $this->segmentsRepository->expects(self::once())
            ->method('byId')
            ->with(new SegmentId('6ce8110a-82d7-4f98-8e14-fd010b27231a'))
            ->willReturn(null);

        $this->segmentationProvider->expects(self::never())->method('evaluateSegment');
        $this->segmentLogger->expects(self::once())
            ->method('warning')
            ->with('Segment with ID 6ce8110a-82d7-4f98-8e14-fd010b27231a not found.');

        $segmentBuilder = new SegmentBuilder(
            $this->segmentedCustomersProjector,
            $this->segmentationProvider,
            $this->segmentsRepository,
            $this->segmentLogger
        );
        $segmentBuilder->recreate(new SegmentId('6ce8110a-82d7-4f98-8e14-fd010b27231a'));
    }
}
