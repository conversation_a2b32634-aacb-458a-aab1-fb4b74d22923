<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Unit\Infrastucture\Form\Type;

use Nelmio\ApiDocBundle\Form\Extension\DocumentationExtension;
use OpenLoyalty\Channel\Domain\Channel;
use OpenLoyalty\Channel\Domain\ChannelRepository;
use OpenLoyalty\Core\Domain\Id\ChannelId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Domain\LevelRepository;
use OpenLoyalty\Segment\Domain\CustomEventSchemaFacadeInterface;
use OpenLoyalty\Segment\Domain\Model\Criterion;
use OpenLoyalty\Segment\Domain\TierFacadeInterface;
use OpenLoyalty\Segment\Infrastructure\Form\EventSubscriber\AdditionalFieldsForCriterionType;
use OpenLoyalty\Segment\Infrastructure\Form\Type\CreateSegmentFormType;
use OpenLoyalty\Segment\Infrastructure\Form\Type\CriterionFormType;
use OpenLoyalty\Segment\Infrastructure\Form\Type\SegmentPartFormType;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Form\Extension\Validator\ValidatorExtension;
use Symfony\Component\Form\PreloadedExtension;
use Symfony\Component\Form\Test\TypeTestCase;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Mapping\ClassMetadata;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

final class SegmentFormTypeTest extends TypeTestCase
{
    private MockObject&ValidatorInterface $validator;
    private MockObject&UuidGeneratorInterface $uuidGenerator;
    private MockObject&ChannelRepository $channelRepository;
    private MockObject&LevelRepository $levelRepository;
    private StoreContextProviderInterface&MockObject $storeContextProvider;

    protected function setUp(): void
    {
        $this->validator = $this->getMockBuilder(ValidatorInterface::class)->getMock();
        $this->validator->method('validate')->willReturn(new ConstraintViolationList());

        $metadata = $this->getMockBuilder(ClassMetadata::class)
            ->disableOriginalConstructor()->getMock();
        $metadata->method('addConstraint')->willReturnSelf();
        $metadata->method('addPropertyConstraint')->willReturnSelf();

        $this->validator->method('getMetadataFor')->willReturn($metadata);

        $this->uuidGenerator = $this->getMockBuilder(UuidGeneratorInterface::class)->getMock();
        $this->uuidGenerator->method('generate')->willReturn('00000000-0000-0000-0000-**********'.random_int(10, 99));

        $store = new Store(
            new StoreId('00000000-0000-0000-1111-000000001111'),
            'ST_CODE',
            'EUR',
            'Test store'
        );

        $this->channelRepository = $this->getMockBuilder(ChannelRepository::class)->getMock();
        $this->channelRepository->method('findBy')->will($this->returnCallback(function () use ($store) {
            return [
                new Channel(new ChannelId('00000000-0000-0000-0000-**********00'), $store),
                new Channel(new ChannelId('00000000-0000-0000-0000-**********01'), $store),
            ];
        }));

        $this->levelRepository = $this->getMockBuilder(LevelRepository::class)->getMock();
        $this->levelRepository->method('findByStore')->will($this->returnCallback(function () use ($store) {
            return [
                new Level(new LevelId('00000000-0000-0000-0000-**********01'), $store, 1),
                new Level(new LevelId('00000000-0000-0000-0000-**********02'), $store, 2),
            ];
        }));

        $this->storeContextProvider = $this->createMock(StoreContextProviderInterface::class);
        $this->storeContextProvider->method('getStore')->willReturn($store);

        parent::setUp();
    }

    protected function getExtensions(): array
    {
        $type = new CreateSegmentFormType();

        $additionalFieldsForCriterionType = new AdditionalFieldsForCriterionType(
            $this->channelRepository,
            $this->levelRepository,
            $this->storeContextProvider,
            $this->createMock(TranslatorInterface::class),
            $this->createMock(CustomEventSchemaFacadeInterface::class),
            $this->createMock(TierFacadeInterface::class)
        );

        return [
            new PreloadedExtension([
                $type,
                new SegmentPartFormType($this->uuidGenerator),
                new CriterionFormType(
                    $this->uuidGenerator,
                    $additionalFieldsForCriterionType
                ),
            ], []),
            new ValidatorExtension($this->validator),
        ];
    }

    protected function getTypeExtensions(): array
    {
        return [
            new DocumentationExtension(),
        ];
    }

    /**
     * @test
     */
    public function it_has_valid_data_when_creating_new_pos(): void
    {
        $formData = [
            'name' => 'test',
            'description' => 'desc',
            'parts' => [
                [
                    'criteria' => [
                        [
                            'type' => Criterion::TYPE_BOUGHT_THROUGH_CHANNEL,
                            'channelIds' => [
                                '00000000-0000-0000-0000-**********00',
                                '00000000-0000-0000-0000-**********01',
                            ],
                        ],
                        [
                            'type' => Criterion::TYPE_AVERAGE_TRANSACTION_AMOUNT,
                            'fromAmount' => 1,
                            'toAmount' => 10000,
                        ],
                        [
                            'type' => Criterion::TYPE_TRANSACTION_COUNT,
                            'min' => 10,
                            'max' => 20,
                        ],
                    ],
                ],
            ],
        ];

        $form = $this->factory->create(CreateSegmentFormType::class);

        $form->submit($formData);

        $this->assertTrue($form->isSynchronized());
        $this->assertTrue($form->isValid());

        $view = $form->createView();
        $children = $view->children;

        foreach (array_keys($formData) as $key) {
            $this->assertArrayHasKey($key, $children);
        }
    }
}
