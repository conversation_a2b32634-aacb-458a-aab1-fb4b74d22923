<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Unit\Infrastucture\Provider;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Segment\Domain\Exception\CustomerNotFoundException;
use OpenLoyalty\Segment\Infrastructure\Provider\CustomerIdProvider;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractKernelTest;
use OpenLoyalty\User\Domain\UserRepositoryInterface;
use OpenLoyalty\User\Infrastructure\Entity\Customer;

final class CustomerIdProviderTest extends AbstractKernelTest
{
    private const USER_ID = '22222222-0000-174c-b092-b0dd880c03e5';
    private const USER_PASSWORD = 'loyalty';
    private const USER_LOYALTY_CARD_NUMBER = '1231123';
    private const USER_PHONE_NUMBER = '+600200334';
    private const USER_EMAIL = '<EMAIL>';

    private UserRepositoryInterface $userRepository;
    private CustomerIdProvider $customerIdProvider;
    private StoreRepository $storeRepository;

    /**
     * @test
     *
     * @dataProvider getData
     *
     * @throws CustomerNotFoundException
     */
    public function it_get_customer_id_by_data(
        string $value,
        StoreId $storeId,
        string $expectation,
        ?Customer $customer
    ): void {
        $store = $this->storeRepository->find($storeId);
        $customer->setStore($store);
        $this->userRepository->save($customer);

        $customerId = $this->customerIdProvider->getCustomerId(
            $value,
            $storeId
        );

        $this->assertEquals($expectation, $customerId);
    }

    /**
     * @test
     */
    public function it_throws_exception_when_customer_not_found(): void
    {
        $this->expectException(CustomerNotFoundException::class);

        $this->customerIdProvider->getCustomerId(
            'not-found',
            new StoreId(LoadSettingsData::DEFAULT_STORE_ID)
        );
    }

    /**
     * @dataProvider
     */
    public function getData(): iterable
    {
        $customer = new Customer(new CustomerId(self::USER_ID));
        $customer->setEmail(self::USER_EMAIL);
        $customer->setPassword(self::USER_PASSWORD);
        $customer->setLoyaltyCardNumber(self::USER_LOYALTY_CARD_NUMBER);
        $customer->setPhone(self::USER_PHONE_NUMBER);
        $customer->setIsActive(true);

        $storeId = new StoreId(LoadSettingsData::DEFAULT_STORE_ID);

        yield 'It return customer id by email' => [
            self::USER_EMAIL,
            $storeId,
            self::USER_ID,
            $customer,
        ];

        yield 'It return customer id by phone' => [
            self::USER_PHONE_NUMBER,
            $storeId,
            self::USER_ID,
            $customer,
        ];

        yield 'It return customer id by loyalty card number' => [
            self::USER_LOYALTY_CARD_NUMBER,
            $storeId,
            self::USER_ID,
            $customer,
        ];

        yield 'It return customer id by customer id' => [
            self::USER_ID,
            $storeId,
            self::USER_ID,
            $customer,
        ];

        $emailInUppercase = strtoupper($customer->getEmail());

        yield 'It return customer id by uppercase email' => [
            $emailInUppercase,
            $storeId,
            self::USER_ID,
            $customer,
        ];

        $phoneWithOutPrefix = str_replace('+', '', $customer->getPhone());

        yield 'It return customer id by phone with out prefix' => [
            $phoneWithOutPrefix,
            $storeId,
            self::USER_ID,
            $customer,
        ];
    }

    protected function setUp(): void
    {
        parent::setUp();
        $container = self::getContainer();

        $this->userRepository = $container->get(UserRepositoryInterface::class);
        $this->storeRepository = $container->get(StoreRepository::class);
        $this->customerIdProvider = new CustomerIdProvider($this->userRepository);
    }
}
