<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Webhook\Integration\Ui\Rest;

use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class GetTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_returns_webhook_subscription_details(): void
    {
        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/webhook/subscription',
            [
                'webhookSubscription' => [
                    'eventName' => 'CustomerRegistered',
                    'url' => 'https://example.com/6',
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $webhookSubscriptionId = $data['webhookSubscriptionId'];

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/webhook/subscription/'.$webhookSubscriptionId,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $webhookSubscription = json_decode($response->getContent(), true);
        $this->assertSame('CustomerRegistered', $webhookSubscription['eventName']);
        $this->assertSame('https://example.com/6', $webhookSubscription['url']);
        $this->assertFalse($webhookSubscription['legacy']);
        $this->assertSame([], $webhookSubscription['headers']);
        $this->assertArrayNotHasKey('headerName', $webhookSubscription);
        $this->assertArrayNotHasKey('headerValue', $webhookSubscription);
    }

    /**
     * @test
     */
    public function it_returns_webhook_subscription_details_with_legacy_headers(): void
    {
        $client = self::createAuthenticatedClient();

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/webhook/subscription',
            [
                'webhookSubscription' => [
                    'eventName' => 'CustomerRegistered',
                    'url' => 'https://example.com/webhook-subscription-details',
                    'headerName' => 'headerName1',
                    'headerValue' => 'headerValue1',
                    'headers' => [],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $webhookSubscriptionId = $data['webhookSubscriptionId'];

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/webhook/subscription/'.$webhookSubscriptionId,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $webhookSubscription = json_decode($response->getContent(), true);
        $this->assertSame('CustomerRegistered', $webhookSubscription['eventName']);
        $this->assertSame('https://example.com/webhook-subscription-details', $webhookSubscription['url']);
        $this->assertFalse($webhookSubscription['legacy']);
        $this->assertSame([], $webhookSubscription['headers']);
        $this->assertSame('headerName1', $webhookSubscription['headerName']);
        $this->assertSame('headerValue1', $webhookSubscription['headerValue']);
    }

    /**
     * @test
     */
    public function it_returns_webhook_subscription_details_with_header(): void
    {
        $client = self::createAuthenticatedClient();

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/webhook/subscription',
            [
                'webhookSubscription' => [
                    'eventName' => 'CustomerRegistered',
                    'url' => 'https://example.com/webhook-subscription-details',
                    'headers' => [
                        [
                            'headerName' => 'headerName1',
                            'headerValue' => 'headerValue1',
                        ],
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $webhookSubscriptionId = $data['webhookSubscriptionId'];

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/webhook/subscription/'.$webhookSubscriptionId,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $webhookSubscription = json_decode($response->getContent(), true);
        $this->assertSame('CustomerRegistered', $webhookSubscription['eventName']);
        $this->assertSame('https://example.com/webhook-subscription-details', $webhookSubscription['url']);
        $this->assertFalse($webhookSubscription['legacy']);
        $this->assertSame(
            [
                [
                    'headerName' => 'headerName1',
                    'headerValue' => 'headerValue1',
                ],
            ],
            $webhookSubscription['headers']
        );
        $this->assertArrayNotHasKey('headerName', $webhookSubscription);
        $this->assertArrayNotHasKey('headerValue', $webhookSubscription);
    }

    /**
     * @test
     */
    public function it_returns_webhook_subscription_details_with_headers(): void
    {
        $client = self::createAuthenticatedClient();

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/webhook/subscription',
            [
                'webhookSubscription' => [
                    'eventName' => 'CustomerRegistered',
                    'url' => 'https://example.com/webhook-subscription-details',
                    'headers' => [
                        [
                            'headerName' => 'headerName1',
                            'headerValue' => 'headerValue1',
                        ],
                        [
                            'headerName' => 'headerName2',
                            'headerValue' => 'headerValue2',
                        ],
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $webhookSubscriptionId = $data['webhookSubscriptionId'];

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/webhook/subscription/'.$webhookSubscriptionId,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $webhookSubscription = json_decode($response->getContent(), true);
        $this->assertSame('CustomerRegistered', $webhookSubscription['eventName']);
        $this->assertSame('https://example.com/webhook-subscription-details', $webhookSubscription['url']);
        $this->assertFalse($webhookSubscription['legacy']);
        $this->assertSame(
            [
                [
                    'headerName' => 'headerName1',
                    'headerValue' => 'headerValue1',
                ],
                [
                    'headerName' => 'headerName2',
                    'headerValue' => 'headerValue2',
                ],
            ],
            $webhookSubscription['headers']
        );
        $this->assertArrayNotHasKey('headerName', $webhookSubscription);
        $this->assertArrayNotHasKey('headerValue', $webhookSubscription);
    }
}
