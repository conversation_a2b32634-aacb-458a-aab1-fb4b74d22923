<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Webhook\Integration\Ui\Rest;

use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use function sprintf;

final class PutLegacyTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_updates_webhook_subscription(): void
    {
        $client = self::createAuthenticatedClient();

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/webhook/subscription',
            [
                'webhookSubscription' => [
                    'eventName' => 'TransactionRegistered',
                    'url' => 'https://example.com/1',
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $webhookSubscriptionId = $data['webhookSubscriptionId'];

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/webhook/subscription/'.$webhookSubscriptionId,
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $webhookSubscription = json_decode($response->getContent(), true);
        $this->assertSame('TransactionRegistered', $webhookSubscription['eventName']);
        $this->assertSame('https://example.com/1', $webhookSubscription['url']);
        $this->assertFalse($webhookSubscription['legacy']);

        $client->request(
            'PUT',
            sprintf(
                '/api/%s/webhook/subscription/%s/legacy',
                LoadSettingsData::DEFAULT_STORE_CODE,
                $webhookSubscriptionId
            ),
            [
                'webhookSubscription' => [
                    'legacy' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);
    }
}
