<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Webhook\Integration\Ui\Rest;

use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\Webhook\Integration\Trait\WebhookApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class PostTest extends AbstractApiTest
{
    use WebhookApiTrait;
    use TenantApiTrait;

    private HttpKernelBrowser $client;

    private string $firstTenantCode = 'FIRST_TENANT_CODE';
    private string $secondTenantCode = 'SECOND_TENANT_CODE';

    private string $defaultGlobalActiveWebhookSubscriptionLimit;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
        $this->postTenant($this->client, $this->firstTenantCode);
        $this->postTenant($this->client, $this->secondTenantCode);

        $this->defaultGlobalActiveWebhookSubscriptionLimit = $_ENV['GLOBAL_ACTIVE_WEBHOOK_SUBSCRIPTION_LIMIT'];
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $_ENV['GLOBAL_ACTIVE_WEBHOOK_SUBSCRIPTION_LIMIT'] = $this->defaultGlobalActiveWebhookSubscriptionLimit;
    }

    /**
     * @test
     */
    public function it_creates_webhook_subscription(): void
    {
        $response = $this->postWebhookSubscription($this->client, storeCode: $this->firstTenantCode);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $webhookSubscriptionId = $data['webhookSubscriptionId'];

        $this->client->request(
            'GET',
            '/api/'.$this->firstTenantCode.'/webhook/subscription/'.$webhookSubscriptionId,
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $webhookSubscription = json_decode($response->getContent(), true);
        $this->assertSame('CustomerRegistered', $webhookSubscription['eventName']);
        $this->assertSame('https://example.com/1', $webhookSubscription['url']);
        $this->assertFalse($webhookSubscription['legacy']);
    }

    /**
     * @test
     */
    public function it_does_not_creates_webhook_subscription_when_event_is_invalid(): void
    {
        $response = $this->postWebhookSubscription(
            $this->client,
            storeCode: $this->firstTenantCode,
            eventName: 'someWrongEvent'
        );
        $this->assertBadRequestResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $errors = $data['errors'];
        $this->assertSame('eventName', $errors[0]['path']);
        $this->assertSame('Event name is invalid', $errors[0]['message']);
    }

    /**
     * @test
     */
    public function it_does_not_creates_webhook_subscription_for_this_same_event_and_url(): void
    {
        $response = $this->postWebhookSubscription($this->client, storeCode: $this->firstTenantCode);
        $this->assertOkResponseStatus($response);

        $response = $this->postWebhookSubscription($this->client, storeCode: $this->firstTenantCode);
        $this->assertBadRequestResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $errors = $data['errors'];
        $this->assertSame('url', $errors[0]['path']);
        $this->assertSame('Event name and url combination must be unique', $errors[0]['message']);
    }

    /**
     * @test
     */
    public function it_returns_validation_error_for_header_without_name_or_value(): void
    {
        $response = $this->postWebhookSubscription(
            $this->client,
            storeCode: $this->firstTenantCode,
            headers: [
                [
                    'headerValue' => 'headerValue1',
                ],
                [
                    'headerName' => 'headerName2',
                ],
            ]);

        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame('Validation failed', $data['message']);
        $this->assertSame('This value should not be blank.', $data['errors'][0]['message']);
        $this->assertSame('This value should not be blank.', $data['errors'][0]['code']);
        $this->assertSame('headers.0.headerName', $data['errors'][0]['path']);
        $this->assertSame('This value should not be blank.', $data['errors'][1]['message']);
        $this->assertSame('This value should not be blank.', $data['errors'][1]['code']);
        $this->assertSame('headers.1.headerValue', $data['errors'][1]['path']);
    }

    /**
     * @test
     */
    public function it_returns_validation_error_for_header_with_wrong_name(): void
    {
        $response = $this->postWebhookSubscription(
            $this->client,
            storeCode: $this->firstTenantCode,
            headers: [
                [
                    'headerName' => 'header,Name',
                    'headerValue' => 'headerValue2',
                ],
                [
                    'headerName' => 'headerName@',
                    'headerValue' => 'headerValue3',
                ],
                [
                    'headerName' => '(headerName',
                    'headerValue' => 'headerValue3',
                ],
                [
                    'headerName' => 'headerName)',
                    'headerValue' => 'headerValue3',
                ],
            ],
            headerName: 'header Name',
            headerValue: 'headerValue1',
        );
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame('Validation failed', $data['message']);
        $this->assertSame('The header name is invalid. Allowed special characters are: ~ ` ! # $ % ^ & * - _ + \' . |', $data['errors'][0]['message']);
        $this->assertSame('The header name is invalid. Allowed special characters are: ~ ` ! # $ % ^ & * - _ + \' . |', $data['errors'][0]['code']);
        $this->assertSame('headerName', $data['errors'][0]['path']);
        $this->assertSame('The header name is invalid. Allowed special characters are: ~ ` ! # $ % ^ & * - _ + \' . |', $data['errors'][1]['message']);
        $this->assertSame('The header name is invalid. Allowed special characters are: ~ ` ! # $ % ^ & * - _ + \' . |', $data['errors'][1]['code']);
        $this->assertSame('headers.0.headerName', $data['errors'][1]['path']);
        $this->assertSame('The header name is invalid. Allowed special characters are: ~ ` ! # $ % ^ & * - _ + \' . |', $data['errors'][2]['message']);
        $this->assertSame('The header name is invalid. Allowed special characters are: ~ ` ! # $ % ^ & * - _ + \' . |', $data['errors'][2]['code']);
        $this->assertSame('headers.1.headerName', $data['errors'][2]['path']);
        $this->assertSame('The header name is invalid. Allowed special characters are: ~ ` ! # $ % ^ & * - _ + \' . |', $data['errors'][3]['message']);
        $this->assertSame('The header name is invalid. Allowed special characters are: ~ ` ! # $ % ^ & * - _ + \' . |', $data['errors'][3]['code']);
        $this->assertSame('headers.2.headerName', $data['errors'][3]['path']);
        $this->assertSame('The header name is invalid. Allowed special characters are: ~ ` ! # $ % ^ & * - _ + \' . |', $data['errors'][4]['message']);
        $this->assertSame('The header name is invalid. Allowed special characters are: ~ ` ! # $ % ^ & * - _ + \' . |', $data['errors'][4]['code']);
        $this->assertSame('headers.3.headerName', $data['errors'][4]['path']);
    }

    /**
     * @test
     * @dataProvider getWrongSubscriptionUrls
     */
    public function it_returns_validation_error_for_wrong_url(string $wrongSubscriptionUrl): void
    {
        $response = $this->postWebhookSubscription($this->client, storeCode: $this->firstTenantCode, url: $wrongSubscriptionUrl);
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame('Validation failed', $data['message']);
        $this->assertSame('This value is not a valid URL.', $data['errors'][0]['message']);
        $this->assertSame('This value is not a valid URL.', $data['errors'][0]['code']);
        $this->assertSame('url', $data['errors'][0]['path']);
    }

    public function getWrongSubscriptionUrls(): iterable
    {
        return [
            ['11https://example.com/webhook-subscription-details'],
            ['https11://example.com/webhook-subscription-details'],
            ['11https11://example.com/webhook-subscription-details'],
            ['123http://example.com/webhook-subscription-details'],
            ['http123://example.com/webhook-subscription-details'],
            ['123http123://example.com/webhook-subscription-details'],
            ['ftp://example.com/webhook-subscription-details'],
            ['2ftp://example.com/webhook-subscription-details'],
            ['ftp2://example.com/webhook-subscription-details'],
            ['2ftp2://example.com/webhook-subscription-details'],
            ['//example.com/webhook-subscription-details'],
            ['example.com/webhook-subscription-details'],
            ['h65-8e59-b63baa57dd4e'],
        ];
    }

    /**
     * @test
     */
    public function it_creates_webhook_subscription_with_long_url(): void
    {
        $tenantCode = 'it_creates_webhook_subscription_with_long_url';
        $this->createTenant($tenantCode);
        $longUrl = 'http://plantuml.com/plantuml/png/ZPJ1Rjim38RlUWhkjcB82-HGj4cnGxUWMJrWbm41RR6sK5lI9Af1tdwq4pTLwnhnZVf_afyeMJUHCN3gMlM56-e8oXQhvyMpDNN32XCxcxgIWb9OiGkmIWSAkrLm-oYBNd9DPJnQXZzKVW_EShQueL2XThlxOlVJb9dqi2co-77pAulNssAorDfPIu8oAQu4dgnMAkDSN8zmIxYjAeeHF1uwaaIFDIaroelh77Xv2k6-06CDmryBNn-JZJ0AnTsFgxVvMTybt9deMpoyjdjoePk1svvibRDF9dJ8njaPElZjPBSGw5-YoA1Khd_chybIG2Qbh6C2zq81pWmZwbXrRxW1rDhqCDYA7h2BmoHuAFSDKfJjK8UcbU2Kl-k86wUlu0s-7FbocvTNIXONrtBOI_WWdV46tMEC2sw2ItK3blOWBL0ZusKWGenMl-vFazYYaihzdFAxDS7otY99-PsRoiaTdwRCM2UGqFiWGz5JJBARAUDva1_BHdmnjWPk22eCMiRKtz8XZgcC_LuiZrCxddjvW58uw-2IrU8CXFfAzQ5_Nqu1N32vjLHWIKYHNKTXJ9gVn-ZUcGkxtCReAeESayqtwPrp3NBtMjpz12ZpPuTJYAgF0CD3TRo0ylYTpoqoSegdFsDIQPtpeA3_ihzBdAvjmUVsmITgzjBa5a6PUSKUWgO0liMA-YHr8_RKjVy1';

        $response = $this->postWebhookSubscription(
            $this->client,
            storeCode: $tenantCode,
            eventName: 'AvailablePointsAmountChanged',
            url: $longUrl
        );
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $webhookSubscriptionId = $data['webhookSubscriptionId'];

        $this->client->request(
            'GET',
            '/api/'.$tenantCode.'/webhook/subscription/'.$webhookSubscriptionId,
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $webhookSubscription = json_decode($response->getContent(), true);
        $this->assertSame('AvailablePointsAmountChanged', $webhookSubscription['eventName']);
        $this->assertSame($longUrl, $webhookSubscription['url']);
        $this->assertFalse($webhookSubscription['legacy']);

        $response = $this->postWebhookSubscription(
            $this->client,
            storeCode: $tenantCode,
            eventName: 'AvailablePointsAmountChanged',
            url: 'https://'.$this->generateLongString()
        );
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame('Validation failed', $data['message']);
        $this->assertSame('This value is too long. It should have 1000 characters or less.', $data['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_limits_webhook_creation_to_global_limit(): void
    {
        $_ENV['GLOBAL_ACTIVE_WEBHOOK_SUBSCRIPTION_LIMIT'] = 3;
        $response = $this->postWebhookSubscription($this->client, storeCode: $this->firstTenantCode, url: 'https://example.com/2');
        $this->assertOkResponseStatus($response);
        $response = $this->postWebhookSubscription($this->client, storeCode: $this->firstTenantCode, url: 'https://example.com/3');
        $this->assertOkResponseStatus($response);
        $response = $this->postWebhookSubscription($this->client, storeCode: $this->secondTenantCode, url: 'https://example.com/4');
        $this->assertOkResponseStatus($response);
        $response = $this->postWebhookSubscription($this->client, storeCode: $this->secondTenantCode, url: 'https://example.com/5');
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(
            'You have reached the global limit of 3 webhook subscriptions. To add a new webhook, please delete an existing one or contact your Customer Success Manager or the support <NAME_EMAIL>.',
            $data['errors'][0]['message']
        );
    }

    private function generateLongString($length = 1001)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';

        for ($i = 0; $i < $length; ++$i) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }

        return $randomString;
    }
}
