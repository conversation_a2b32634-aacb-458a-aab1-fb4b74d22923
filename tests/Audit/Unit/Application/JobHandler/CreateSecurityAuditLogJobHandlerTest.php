<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Audit\Unit\Application\JobHandler;

use OpenLoyalty\Audit\Application\Job\CreateSecurityAuditLog;
use OpenLoyalty\Audit\Application\JobHandler\CreateSecurityAuditLogJobHandler;
use OpenLoyalty\Audit\Domain\AuditLogRepository;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class CreateSecurityAuditLogJobHandlerTest extends TestCase
{
    /**
     * @var MockObject&\OpenLoyalty\Audit\Domain\AuditLogRepository
     */
    private AuditLogRepository $auditLogRepository;

    /**
     * @var MockObject&UuidGeneratorInterface
     */
    private UuidGeneratorInterface $uuidGenerator;

    /**
     * @var MockObject&StoreRepository
     */
    private StoreRepository $storeRepository;

    protected function setUp(): void
    {
        $this->auditLogRepository = $this->getMockBuilder(AuditLogRepository::class)->getMock();
        $this->uuidGenerator = $this->getMockBuilder(UuidGeneratorInterface::class)->getMock();
        $this->storeRepository = $this->getMockBuilder(StoreRepository::class)->getMock();
    }

    /**
     * @test
     */
    public function it_handles_job(): void
    {
        $handler = new CreateSecurityAuditLogJobHandler($this->auditLogRepository, $this->uuidGenerator, $this->storeRepository);

        $this->storeRepository->method('byId')->willReturn($this->createMock(Store::class));
        $this->uuidGenerator->method('generate')->willReturn('00000000-0000-0000-0000-000000000000');

        $this->auditLogRepository->expects($this->once())->method('save');

        $job = new CreateSecurityAuditLog(
            new StoreId('00000000-0000-0000-0000-000000000122'),
            [
                'eventType' => 'v',
                'entityType' => 'v',
                'entityId' => 'v',
                'createdAt' => new \DateTime(),
                'username' => 'v',
                'userId' => 'v',
                'userType' => 'v',
                'data' => [],
                'ip' => 'v',
            ]
        );

        $handler->__invoke($job);
    }
}
