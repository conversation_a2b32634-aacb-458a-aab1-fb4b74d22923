<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Export\Integration\Ui\Rest;

use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Segment\Domain\Model\Criterion;
use OpenLoyalty\Segment\Domain\SegmentBuilderInterface;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class GetDownloadMemberSegmentTest extends AbstractApiTest
{
    protected HttpKernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_returns_members_segment_data(): void
    {
        $storeCode = 'exportMembersSegmentTest';
        $this->client->request(
            'POST',
            '/api/store',
            [
                'store' => [
                    'code' => $storeCode,
                    'name' => 'Test export members segment',
                    'currency' => 'EUR',
                    'active' => 1,
                ],
            ]
        );
        $this->assertOkResponseStatus($this->client->getResponse());

        $customer1Email = '<EMAIL>';
        $customer1FirstName = 'customer first name 1';
        $customer1LastName = 'customer last name 1';
        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/member/register',
            [
                'customer' => [
                    'email' => $customer1Email,
                    'firstName' => $customer1FirstName,
                    'lastName' => $customer1LastName,
                    'plainPassword' => 'someSecretPass1#',
                    'agreement1' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $customer1Data = json_decode($response->getContent(), true);
        $customer1Id = $customer1Data['customerId'];

        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/member/'.$customer1Id.'/activate'
        );

        $this->assertNoContentResponseStatus($this->client->getResponse());

        $customer2Email = '<EMAIL>';
        $customer2FirstName = 'customer first name 2';
        $customer2LastName = 'customer last name 2';
        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/member/register',
            [
                'customer' => [
                    'email' => $customer2Email,
                    'firstName' => $customer2FirstName,
                    'lastName' => $customer2LastName,
                    'plainPassword' => 'someSecretPass1#',
                    'agreement1' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $customer2Data = json_decode($response->getContent(), true);
        $customer2Id = $customer2Data['customerId'];

        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/member/'.$customer2Id.'/activate'
        );

        $this->assertNoContentResponseStatus($this->client->getResponse());

        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/segment',
            [
                'segment' => [
                    'name' => 'First tier segment',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => Criterion::TYPE_CUSTOMER_LIST,
                                    'customers' => [$customer1Id, $customer2Id],
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $segmentData = json_decode($response->getContent(), true);
        $segmentId = $segmentData['segmentId'];

        $segmentBuilder = self::getContainer()->get(SegmentBuilderInterface::class);
        $segmentBuilder->recreate(new SegmentId($segmentId));

        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/export/memberSegment',
            [
                'memberSegment' => [
                    'segmentId' => $segmentId,
                ],
            ]
        );
        $response = $this->client->getResponse();
        $exportData = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $filePath = 'var/storage/export_async/memberSegment/'.$exportData['exportId'].'.csv';

        $expectedHeaders = [
            'memberId',
            'tenantId',
            'active',
            'channelId',
            'firstName',
            'lastName',
            'gender',
            'email',
            'phone',
            'birthDate',
            'lastLevelRecalculation',
            'address',
            'loyaltyCardNumber',
            'createdAt',
            'updatedAt',
            'levelId',
            'manuallyAssignedLevelId',
            'agreement1',
            'agreement2',
            'agreement3',
            'company',
            'transactionsCount',
            'transactionsAmount',
            'transactionsAmountWithoutDeliveryCosts',
            'transactionsAmountExcludedForLevel',
            'averageTransactionAmount',
            'lastTransactionDate',
            'firstTransactionDate',
            'levelAchievementDate',
            'referrerMemberId',
            'labels',
            'anonymized',
            'referralToken',
            'currency',
            'storeCode',
        ];

        $this->assertFileExists($filePath);

        $filePointer = fopen($filePath, 'rb');
        $headers = fgetcsv($filePointer);
        $this->assertSame($expectedHeaders, $headers);

        $rows = [];
        while (!feof($filePointer)) {
            $row = fgetcsv($filePointer);

            if (!$row) {
                break;
            }

            $rows[] = $row;
        }
        fclose($filePointer);

        $this->assertCount(2, $rows);
        $expectedValues = [
            $customer1Email.$customer1FirstName.$customer1LastName,
            $customer2Email.$customer2FirstName.$customer2LastName,
        ];
        $values = array_map(function ($row) {
            return $row[7].$row[4].$row[5];
        }, $rows);

        $this->assertArrayHasUniqueSubset($values, $expectedValues);

        unlink($filePath);
    }

    /**
     * @test
     */
    public function it_returns_only_headers_for_empty_members_segment_export(): void
    {
        $storeCode = 'exportEmptyMembersSegmentTest';
        $this->client->request(
            'POST',
            '/api/store',
            [
                'store' => [
                    'code' => $storeCode,
                    'name' => 'Test empty export members segment',
                    'currency' => 'EUR',
                    'active' => 1,
                ],
            ]
        );
        $this->assertOkResponseStatus($this->client->getResponse());

        $customer1Email = '<EMAIL>';
        $customer1FirstName = 'customer first name 1';
        $customer1LastName = 'customer last name 1';
        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/member/register',
            [
                'customer' => [
                    'email' => $customer1Email,
                    'firstName' => $customer1FirstName,
                    'lastName' => $customer1LastName,
                    'plainPassword' => 'someSecretPass1#',
                    'agreement1' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $customer1Data = json_decode($response->getContent(), true);
        $customer1Id = $customer1Data['customerId'];

        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/segment',
            [
                'segment' => [
                    'name' => 'First tier segment',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => Criterion::TYPE_CUSTOMER_LIST,
                                    'customers' => [$customer1Id], //this customer is inactive so will not be included in segment
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $segmentData = json_decode($response->getContent(), true);
        $segmentId = $segmentData['segmentId'];

        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/export/memberSegment',
            [
                'memberSegment' => [
                    'segmentId' => $segmentId,
                ],
            ]
        );
        $response = $this->client->getResponse();
        $exportData = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $filePath = 'var/storage/export_async/memberSegment/'.$exportData['exportId'].'.csv';

        $expectedHeaders = [
            'memberId',
            'tenantId',
            'active',
            'channelId',
            'firstName',
            'lastName',
            'gender',
            'email',
            'phone',
            'birthDate',
            'lastLevelRecalculation',
            'address',
            'loyaltyCardNumber',
            'createdAt',
            'updatedAt',
            'levelId',
            'manuallyAssignedLevelId',
            'agreement1',
            'agreement2',
            'agreement3',
            'company',
            'transactionsCount',
            'transactionsAmount',
            'transactionsAmountWithoutDeliveryCosts',
            'transactionsAmountExcludedForLevel',
            'averageTransactionAmount',
            'lastTransactionDate',
            'firstTransactionDate',
            'levelAchievementDate',
            'referrerMemberId',
            'labels',
            'anonymized',
            'referralToken',
            'currency',
            'storeCode',
        ];

        $this->assertFileExists($filePath);

        $filePointer = fopen($filePath, 'rb');
        $headers = fgetcsv($filePointer);
        $this->assertSame($expectedHeaders, $headers);

        $rows = [];
        while (!feof($filePointer)) {
            $row = fgetcsv($filePointer);

            if (!$row) {
                break;
            }

            $rows[] = $row;
        }
        fclose($filePointer);

        $this->assertEmpty($rows);

        unlink($filePath);
    }
}
