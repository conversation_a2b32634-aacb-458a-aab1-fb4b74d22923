<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Member\Integration\Ui\Cli;

use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

/**
 * @covers \OpenLoyalty\User\Ui\Rest\Controller\Member\Get
 */
final class GetMemberTest extends AbstractApiTest
{
    use MemberApiTrait;
    use TenantApiTrait;
    private HttpKernelBrowser $client;

    /**
     * @test
     */
    public function it_get_member(): void
    {
        $tenantCode = 'getMemberTenantCode';
        $response = $this->postTenant($this->client, $tenantCode);
        $this->assertOkResponseStatus($response);

        $response = $this->postMember($this->client, $tenantCode, '<EMAIL>');
        $this->assertOkResponseStatus($response);
        $customerId = $this->getResponseData($response)['customerId'];

        $this->client->jsonRequest(
            'GET',
            '/api/'.$tenantCode.'/member/email=<EMAIL>',
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        self::assertSame($customerId, $data['customerId']);
        self::assertSame('<EMAIL>', $data['email']);
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }
}
