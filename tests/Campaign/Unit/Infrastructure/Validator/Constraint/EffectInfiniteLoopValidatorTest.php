<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Infrastructure\Validator\Constraint;

use OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\EffectInfiniteLoop;
use OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\EffectInfiniteLoopValidator;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Violation\ConstraintViolationBuilderInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

final class EffectInfiniteLoopValidatorTest extends TestCase
{
    private MockObject&ExecutionContextInterface $context;
    private MockObject&TranslatorInterface $translator;
    private EffectInfiniteLoopValidator $validator;

    protected function setUp(): void
    {
        $this->context = $this->createMock(ExecutionContextInterface::class);
        $this->translator = $this->createMock(TranslatorInterface::class);
        $this->translator->method('trans')->willReturnCallback(fn (string $code): string => $code);
        $this->validator = new EffectInfiniteLoopValidator($this->translator);
        $this->validator->initialize($this->context);
    }

    /**
     * @test
     */
    public function it_adds_violation_if_campaign_infinite_loop_possible(): void
    {
        $constraint = new EffectInfiniteLoop([
            'trigger' => 'internal_event',
            'event' => 'MemberDetailsChanged',
        ]);
        $builder = $this->createMock(ConstraintViolationBuilderInterface::class);

        $this->translator->expects(self::once())->method('trans')->willReturn('Some error.');
        $this->context->expects(self::once())->method('buildViolation')->willReturn($builder);
        $builder->expects(self::once())->method('addViolation');

        $this->validator->validate('customAttributeKey value', $constraint);
    }

    /**
     * @test
     */
    public function it_does_not_add_violation_if_campaign_infinite_loop_is_not_possible(): void
    {
        $constraint = new EffectInfiniteLoop([
            'trigger' => 'internal_event',
            'event' => 'MemberWasActivated',
        ]);

        $this->translator->expects(self::never())->method('trans');
        $this->context->expects(self::never())->method('buildViolation');

        $this->validator->validate('customAttributeKey value', $constraint);
    }
}
