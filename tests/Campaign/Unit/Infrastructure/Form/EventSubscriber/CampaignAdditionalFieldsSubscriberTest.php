<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Infrastructure\Form\EventSubscriber;

use OpenLoyalty\Campaign\Infrastructure\Form\EventSubscriber\CampaignAdditionalFieldsSubscriber;
use OpenLoyalty\Campaign\Infrastructure\Form\Type\AchievementFormType;
use OpenLoyalty\Campaign\Infrastructure\Form\Type\CampaignLimitFormType;
use OpenLoyalty\Campaign\Infrastructure\Form\Type\MemberFilterFormType;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Form\Event\PreSubmitEvent;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormInterface;

final class CampaignAdditionalFieldsSubscriberTest extends TestCase
{
    private CampaignAdditionalFieldsSubscriber $subscriber;

    protected function setUp(): void
    {
        $this->subscriber = new CampaignAdditionalFieldsSubscriber(2);
    }

    /**
     * @test
     */
    public function it_not_adds_additional_fields_for_empty_data(): void
    {
        $form = $this->getMockBuilder(FormInterface::class)->getMock();
        $event = new PreSubmitEvent($form, []);

        $form->expects(self::never())->method('add');

        $this->subscriber->onPreSubmit($event);
    }

    /**
     * @test
     */
    public function it_adds_only_rules_field(): void
    {
        $form = $this->getMockBuilder(FormInterface::class)->getMock();
        $event = new PreSubmitEvent($form, ['trigger' => Trigger::TRANSACTION]);

        $form->expects(self::exactly(2))->method('add')->withConsecutive(
            ['transactionItemsFilters', CollectionType::class, $this->anything()],
            ['rules', CollectionType::class, $this->anything()],
        );

        $this->subscriber->onPreSubmit($event);
    }

    /**
     * @test
     */
    public function it_adds_additional_fields_for_internal_event_trigger(): void
    {
        $form = $this->getMockBuilder(FormInterface::class)->getMock();
        $event = new PreSubmitEvent($form, ['trigger' => Trigger::INTERNAL_EVENT]);

        $form->expects(self::exactly(2))->method('add')->withConsecutive(
            ['event', TextType::class, $this->anything()],
            ['rules', CollectionType::class, $this->anything()]
        );

        $this->subscriber->onPreSubmit($event);
    }

    /**
     * @test
     */
    public function it_adds_additional_fields_for_custom_event_trigger(): void
    {
        $form = $this->getMockBuilder(FormInterface::class)->getMock();
        $event = new PreSubmitEvent($form, ['trigger' => Trigger::CUSTOM_EVENT]);

        $form->expects(self::exactly(2))->method('add')->withConsecutive(
            ['event', TextType::class, $this->anything()],
            ['rules', CollectionType::class, $this->anything()]
        );

        $this->subscriber->onPreSubmit($event);
    }

    /**
     * @test
     */
    public function it_adds_additional_fields_for_time_trigger(): void
    {
        $form = $this->getMockBuilder(FormInterface::class)->getMock();
        $event = new PreSubmitEvent($form, ['trigger' => Trigger::TIME]);

        $form->expects(self::exactly(2))->method('add')->withConsecutive(
            ['memberFilter', MemberFilterFormType::class],
            ['rules', CollectionType::class, $this->anything()]
        );

        $this->subscriber->onPreSubmit($event);
    }

    /**
     * @test
     */
    public function it_adds_additional_fields_for_limits(): void
    {
        $form = $this->getMockBuilder(FormInterface::class)->getMock();
        $event = new PreSubmitEvent($form, [
            'trigger' => Trigger::TRANSACTION,
            'limits' => [
                'points' => [
                    'value' => 10.0,
                ],
            ],
        ]);

        $form->expects(self::exactly(3))->method('add')->withConsecutive(
            ['transactionItemsFilters', CollectionType::class, $this->anything()],
            ['rules', CollectionType::class, $this->anything()],
            ['limits', CampaignLimitFormType::class, $this->anything()]
        );

        $this->subscriber->onPreSubmit($event);
    }

    /**
     * @test
     */
    public function it_adds_additional_fields_for_achievement_trigger(): void
    {
        $form = $this->getMockBuilder(FormInterface::class)->getMock();
        $event = new PreSubmitEvent($form, ['trigger' => Trigger::ACHIEVEMENT]);

        $form->expects(self::exactly(2))->method('add')->withConsecutive(
            ['achievementId', AchievementFormType::class],
            ['rules', CollectionType::class, $this->anything()]
        );

        $this->subscriber->onPreSubmit($event);
    }
}
