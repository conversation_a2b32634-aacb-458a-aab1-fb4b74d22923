<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Infrastructure\ExpressionLanguage;

use OpenLoyalty\Core\Domain\Condition\Schema\ConditionExpressionBuilder;
use OpenLoyalty\Core\Domain\Condition\Schema\ConditionValueExpressionProvider;
use OpenLoyalty\Core\Domain\ValueFacadeInterface;
use OpenLoyalty\Tools\ExpressionLanguage\InvalidExpressionException;
use OpenLoyalty\Tools\ExpressionLanguage\Processor\WildcardProcessor;
use OpenLoyalty\Tools\ExpressionLanguage\ValueExpressionLanguage;
use PHPUnit\Framework\TestCase;

final class ValueExpressionLanguageTest extends TestCase
{
    /**
     * @test
     * @dataProvider getExpressions
     */
    public function it_parses_expression(string $expression, $result): void
    {
        $valueFacade = $this->createMock(ValueFacadeInterface::class);
        $expressionLanguage = new ValueExpressionLanguage(
            new WildcardProcessor(),
            $valueFacade,
            new ConditionExpressionBuilder(
                new ConditionValueExpressionProvider([])
            ),
            100,
            100,
            100
        );
        $evaluationResult = $expressionLanguage->evaluate($expression);

        $this->assertEquals($result, $evaluationResult);
    }

    public function getExpressions(): array
    {
        return [
            ['round_down(120.4)', 120],
            ['round_up(120.4)', 121],
            ['round_up(\'120.4\')', '121'],
            ['round_down(\'120.4\')', '120'],
            ['<#>round_down(\'120.4\')</#>', '120'],
            ['<#>120.0</#> + <#>150.0</#>', 270.0],
            ['<#>3>2</#>', true],
        ];
    }

    /**
     * @test
     * @dataProvider getPercentValueDistributionExpressions
     */
    public function it_parses_percent_value_distribution_expressions(string $expression, $result): void
    {
        if ($result instanceof \Exception) {
            $this->expectException($result::class);
            $this->expectExceptionMessage($result->getMessage());
        }
        $valueFacade = $this->createMock(ValueFacadeInterface::class);
        $expressionLanguage = new ValueExpressionLanguage(
            new WildcardProcessor(),
            $valueFacade,
            new ConditionExpressionBuilder(
                new ConditionValueExpressionProvider([])
            ),
            100,
            100,
            100
        );
        $evaluationResult = $expressionLanguage->evaluate($expression);

        $this->assertEquals($result, $evaluationResult);
    }

    public function getPercentValueDistributionExpressions(): array
    {
        return [
            ['percent_value_distribution(100, [], [])', new InvalidExpressionException('Thresholds array should have at least one item')],
            ['percent_value_distribution(100, [0], [0, 0, 0])', new InvalidExpressionException('ThresholdPercentValues array should have 2 items')],
            ['percent_value_distribution(100, [0], [])', new InvalidExpressionException('ThresholdPercentValues array should have 2 items')],
            ['percent_value_distribution(100, [60], [0.05, 0.2])', 11],
            ['percent_value_distribution(60, [60], [0.05, 1.0])', 3],
            ['percent_value_distribution(1000, [1500.0], [0.05, 0])', 50.0],
            ['percent_value_distribution(100, [0], [0.2, 0.4])', 40], // 20% for all before 0 (means 0) and 40% for all after 0 (means 100)
            ['percent_value_distribution(100, [0, 0, 0], [0.2, 0.4, 0.6, 0.8])', 80],
            ['percent_value_distribution(60, [40], [0.05, 1.0], 30)', 50.5],
            ['percent_value_distribution(1000, [100, 500], [0.05, 0.4, 0.1])', 215],
            ['percent_value_distribution(1000, [100, 500], [0.05, 0.4, 0.1], 200)', 190],
            // For a monthly spent of up to $10K members get: $5K x 1% Cashback + next $2.5K x 1.2% + next $2.5K x 1.5%
            ['percent_value_distribution(1000, [5000, 7500, 10000], [0.01, 0.012, 0.015, 0], 0)', 10],
            ['percent_value_distribution(1000, [5000, 7500, 10000], [0.01, 0.012, 0.015, 0], 4500)', 11],
            ['percent_value_distribution(1000, [5000, 7500, 10000], [0.01, 0.012, 0.015, 0], 10000)', 0],
            ['percent_value_distribution(1000, [5000, 7500, 10000], [0.01, 0.012, 0.015, 0], 9999)', 0.015],
            ['percent_value_distribution(10100, [5000, 7500, 10000], [0.01, 0.012, 0.015, 0], 0)', 117.5],
            ['percent_value_distribution(3000, [5000, 7500, 10000], [0.01, 0.012, 0.015, 0], 5001)', 37.503],
        ];
    }

    /**
     * @test
     */
    public function it_throws_exception_for_too_much_values(): void
    {
        $valueFacade = $this->createMock(ValueFacadeInterface::class);
        $this->expectExceptionMessage('Too much expression value: 100000000000 (expression 100000000000)');
        $expressionLanguage = new ValueExpressionLanguage(
            new WildcardProcessor(),
            $valueFacade,
            new ConditionExpressionBuilder(
                new ConditionValueExpressionProvider([])
            ),
            100,
            100,
            100
        );
        $expressionLanguage->evaluate('100000000000');
    }
}
