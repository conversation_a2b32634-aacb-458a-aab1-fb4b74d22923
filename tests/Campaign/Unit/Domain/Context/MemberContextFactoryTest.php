<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Domain\Context;

use Carbon\CarbonImmutable;
use DateTime;
use DateTimeImmutable;
use InvalidArgumentException;
use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\Context\MemberContextFactory;
use OpenLoyalty\Campaign\Domain\Context\MemberContextFactoryInterface;
use OpenLoyalty\Core\Domain\Context\InternalEvent;
use OpenLoyalty\Core\Domain\Context\LazyCustomerItem;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\CustomEventId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransactionId;
use OpenLoyalty\Core\Domain\Message\InternalEventInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use OpenLoyalty\CustomEvent\Domain\CustomEvent;
use OpenLoyalty\CustomEvent\Domain\ValueObject\CustomerData;
use OpenLoyalty\Transaction\Domain\Transaction;
use OpenLoyalty\Transaction\Domain\ValueObject\CustomerBasicData;
use OpenLoyalty\Transaction\Domain\ValueObject\TransactionHeader;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class MemberContextFactoryTest extends TestCase
{
    private CarbonImmutable $currentTime;
    private MemberContextFactory $memberContextFactory;

    protected function setUp(): void
    {
        CarbonImmutable::setTestNow('2023-03-28 00:00:00');
        $this->currentTime = new CarbonImmutable();
        $this->memberContextFactory = new MemberContextFactory();
    }

    protected function tearDown(): void
    {
        CarbonImmutable::setTestNow(); // Reset the test date
        parent::tearDown();
    }

    /**
     * @test
     */
    public function it_creates_context_for_custom_event_strategy(): void
    {
        // Create a real CustomEvent instance
        $storeId = new StoreId('00000000-0000-0000-0001-000000000000');
        $customerId = new CustomerId('00000000-0000-0000-4444-000000000000');
        $customEventId = new CustomEventId('00000000-0000-0000-2222-000000000000');
        $store = new Store($storeId, 'test-store', 'USD', 'Test Store');

        $customerData = new CustomerData('<EMAIL>', '555-555-5555', '12345');

        $customEvent = new CustomEvent(
            $customEventId,
            'test_event',
            'Test Event',
            $store,
            $customerData,
            ['key' => 'value'],
            new DateTimeImmutable('2023-03-28 00:00:00'),
            new DateTimeImmutable('2023-03-28 00:00:00')
        );

        // Create context from the factory
        $context = $this->memberContextFactory->create(
            $storeId,
            $customerId,
            MemberContextFactoryInterface::CUSTOM_EVENT,
            $customEvent
        );

        $this->assertSame(Trigger::create(Trigger::CUSTOM_EVENT)->getCode(), $context->getTrigger()->getCode());
        $this->assertSame($customerId->__toString(), $context->getCustomerId()->__toString());

        $this->assertInstanceOf(LazyCustomerItem::class, $context->getItem(Context::CUSTOMER_ITEM));

        // Check that the custom event item is present
        $this->assertInstanceOf(\OpenLoyalty\Core\Domain\Context\CustomEvent::class, $context->getItem(Context::EVENT_ITEM));

        // Verify the custom event details match
        $eventItem = $context->getItem(Context::EVENT_ITEM);

        // Verify basic properties
        $this->assertSame('00000000-0000-0000-2222-000000000000', $eventItem->customEventId->__toString());
        $this->assertSame('test_event', $eventItem->type);
        $this->assertSame('2023-03-28 00:00:00', $eventItem->eventDate->format('Y-m-d H:i:s'));
        $this->assertNotNull($eventItem->body->data);
        $this->assertTrue(isset($eventItem->body->data['key']));
        $this->assertSame('value', $eventItem->body->data['key']);
    }

    /**
     * @test
     */
    public function it_creates_context_for_transaction_strategy(): void
    {
        // Create a real Transaction instance
        $storeId = new StoreId('00000000-0000-0000-0001-000000000000');
        $customerId = new CustomerId('00000000-0000-0000-4444-000000000000');
        $transactionId = new TransactionId('00000000-0000-0000-3333-000000000000');

        $header = new TransactionHeader(
            'DOC-123',
            Transaction::TYPE_SELL,
            new DateTime('2023-03-28 00:00:00'),
            'Test Store',
            []
        );

        $customerData = new CustomerBasicData(
            '<EMAIL>',
            'Test Customer',
            null,
            '555-555-5555',
            '12345'
        );

        $transaction = new Transaction(
            $transactionId,
            $storeId,
            $header,
            $customerData
        );

        // Set the required timestamp properties using setter methods
        $transaction->setCreatedAt(new DateTime('2023-03-28 00:00:00'));
        $transaction->setUpdatedAt(new DateTime('2023-03-28 00:00:00'));

        // Create context from the factory
        $context = $this->memberContextFactory->create(
            $storeId,
            $customerId,
            MemberContextFactoryInterface::TRANSACTION,
            $transaction
        );

        $this->assertSame(Trigger::create(Trigger::TRANSACTION)->getCode(), $context->getTrigger()->getCode());
        $this->assertSame($this->currentTime->format('Y-m-d H:i:s'), $context->getCurrentTime()->format('Y-m-d H:i:s'));
        $this->assertSame($customerId->__toString(), $context->getCustomerId()->__toString());

        $this->assertInstanceOf(LazyCustomerItem::class, $context->getItem(Context::CUSTOMER_ITEM));

        // Check that a transaction item is present of the Core domain type
        $this->assertInstanceOf(\OpenLoyalty\Core\Domain\Context\Transaction::class, $context->getItem(Context::TRANSACTION_ITEM));

        // Verify the transaction ID matches
        $transactionItem = $context->getItem(Context::TRANSACTION_ITEM);
        $this->assertSame('00000000-0000-0000-3333-000000000000', $transactionItem->id);

        // Verify additional transaction properties
        $this->assertSame('DOC-123', $transactionItem->documentNumber);
        $this->assertSame('sell', $transactionItem->documentType);
        $this->assertSame('Test Store', $transactionItem->purchasePlace);
        $this->assertSame(0.0, $transactionItem->grossValue);

        // Verify date properties
        $this->assertInstanceOf(\DateTime::class, $transactionItem->purchasedAt);
        $this->assertSame('2023-03-28 00:00:00', $transactionItem->purchasedAt->format('Y-m-d H:i:s'));

        $this->assertInstanceOf(\DateTimeImmutable::class, $transactionItem->createdAt);
        $this->assertSame('2023-03-28 00:00:00', $transactionItem->createdAt->format('Y-m-d H:i:s'));

        // Verify collections
        $this->assertIsArray($transactionItem->items);
        $this->assertCount(0, $transactionItem->items);

        $this->assertIsArray($transactionItem->labels);
        $this->assertCount(0, $transactionItem->labels);
    }

    /**
     * @test
     */
    public function it_creates_context_for_internal_event_strategy(): void
    {
        /** @var InternalEventInterface&MockObject $internalEvent */
        $internalEvent = $this->createMock(InternalEventInterface::class);
        $internalEvent->method('getEventDate')->willReturn($this->currentTime);
        $internalEvent->method('getName')->willReturn('test_event');
        $internalEvent->method('getCustomerId')
            ->willReturn(new CustomerId('00000000-0000-0000-5555-000000000000'));
        $internalEvent->method('getStoreId')
            ->willReturn(new StoreId('00000000-0000-0000-0002-000000000000'));
        $internalEvent->method('isTriggerCampaign')->willReturn(true);
        $internalEvent->method('getEventBody')->willReturn([]);

        $storeId = new StoreId('00000000-0000-0000-0001-000000000000');
        $customerId = new CustomerId('00000000-0000-0000-4444-000000000000');

        $context = $this->memberContextFactory->create(
            $storeId,
            $customerId,
            MemberContextFactoryInterface::INTERNAL_EVENT,
            $internalEvent
        );

        $this->assertSame(Trigger::create(Trigger::INTERNAL_EVENT)->getCode(), $context->getTrigger()->getCode());
        $this->assertSame($this->currentTime->format('Y-m-d H:i:s'), $context->getCurrentTime()->format('Y-m-d H:i:s'));
        $this->assertSame($customerId->__toString(), $context->getCustomerId()->__toString());

        $this->assertInstanceOf(LazyCustomerItem::class, $context->getItem(Context::CUSTOMER_ITEM));

        // Check that the internal event item is present
        $this->assertInstanceOf(InternalEvent::class, $context->getItem(Context::EVENT_ITEM));
    }

    /**
     * @test
     */
    public function it_throws_exception_for_invalid_custom_event_payload(): void
    {
        $storeId = new StoreId('00000000-0000-0000-0001-000000000000');
        $customerId = new CustomerId('00000000-0000-0000-4444-000000000000');
        $invalidPayload = new \stdClass();

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Expected CustomEvent, got stdClass');

        $this->memberContextFactory->create(
            $storeId,
            $customerId,
            MemberContextFactoryInterface::CUSTOM_EVENT,
            $invalidPayload
        );
    }

    /**
     * @test
     */
    public function it_throws_exception_for_invalid_transaction_payload(): void
    {
        $storeId = new StoreId('00000000-0000-0000-0001-000000000000');
        $customerId = new CustomerId('00000000-0000-0000-4444-000000000000');
        $invalidPayload = new \stdClass();

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Expected Transaction, got stdClass');

        $this->memberContextFactory->create(
            $storeId,
            $customerId,
            MemberContextFactoryInterface::TRANSACTION,
            $invalidPayload
        );
    }

    /**
     * @test
     */
    public function it_throws_exception_for_invalid_internal_event_payload(): void
    {
        $storeId = new StoreId('00000000-0000-0000-0001-000000000000');
        $customerId = new CustomerId('00000000-0000-0000-4444-000000000000');
        $invalidPayload = new \stdClass();

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Expected InternalEventInterface, got stdClass');

        $this->memberContextFactory->create(
            $storeId,
            $customerId,
            MemberContextFactoryInterface::INTERNAL_EVENT,
            $invalidPayload
        );
    }

    /**
     * @test
     */
    public function it_throws_exception_for_unsupported_event_type(): void
    {
        $storeId = new StoreId('00000000-0000-0000-0001-000000000000');
        $customerId = new CustomerId('00000000-0000-0000-4444-000000000000');
        $payload = new \stdClass();

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Unsupported event type: unknown');

        $this->memberContextFactory->create(
            $storeId,
            $customerId,
            'unknown',
            $payload
        );
    }
}
