<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Domain\Effect\Points;

use OpenLoyalty\Account\Domain\WalletType;
use OpenLoyalty\Campaign\Domain\AccountFacadeInterface;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\CalculatedEffect;
use OpenLoyalty\Campaign\Domain\Effect\Points\DeductEffect;
use OpenLoyalty\Campaign\Domain\Entity\CampaignExecution\CalculatedEffectResult;
use OpenLoyalty\Campaign\Domain\Entity\Effect\DeductEffect as DeductEffectEntity;
use OpenLoyalty\Campaign\Domain\Entity\Effect\PointsEffect;
use OpenLoyalty\Campaign\Domain\Factory\CalculatedEffectResultFactoryInterface;
use OpenLoyalty\Campaign\Domain\PointsFacadeInterface;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectStatus;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Model\Wallet;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\ValueObject\ActionCause;
use OpenLoyalty\Core\Domain\ValueObject\Wallet\Account;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class DeductEffectTest extends TestCase
{
    private DeductEffect $deductUnitEffect;
    private PointsFacadeInterface $pointsFacade;
    private CalculatedEffectResultFactoryInterface $calculatedEffectResultFactory;
    private LoggerInterface $campaignLogger;
    private AccountFacadeInterface $accountFacade;

    protected function setUp(): void
    {
        $this->pointsFacade = $this->createMock(PointsFacadeInterface::class);
        $this->calculatedEffectResultFactory = $this->createMock(CalculatedEffectResultFactoryInterface::class);
        $this->campaignLogger = $this->createMock(LoggerInterface::class);
        $this->accountFacade = $this->createMock(AccountFacadeInterface::class);

        $this->deductUnitEffect = new DeductEffect(
            $this->pointsFacade,
            $this->calculatedEffectResultFactory,
            $this->campaignLogger,
            $this->accountFacade
        );
    }

    /**
     * @test
     */
    public function it_applies_deduct_effect(): void
    {
        $context = $this->createMock(Context::class);
        $context->method('getCustomerId')->willReturn(new CustomerId('********-0000-0000-0000-************'));

        $campaignId = new CampaignId('********-0000-0000-0000-************');
        $campaignName = 'Campaign name';
        $campaign = $this->createMock(Campaign::class);

        $deductEffect = $this->createMock(DeductEffectEntity::class);
        $deductEffect->method('getPointsRule')->willReturn('100');

        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->method('getEffect')
            ->willReturn($deductEffect);

        $walletCode = 'stars';

        $deductEffect->expects(self::once())
            ->method('getWalletCode')
            ->willReturn($walletCode);

        $calculatedEffect->method('getPoints')
            ->willReturn(100.0);

        $storeId = new StoreId('********-0000-0000-0000-************');
        $store = $this->createMock(Store::class);

        $campaign->method('getStoreId')->willReturn($storeId);

        $store->method('getStoreId')->willReturn($storeId);

        $effectTarget = $this->createMock(EffectTarget::class);

        $calculatedEffect->method('getTarget')
            ->willReturn($effectTarget);

        $customerId = new CustomerId('********-0000-0000-0000-************');

        $effectTarget->method('getCustomerId')
            ->willReturn($customerId);

        $campaign->expects(self::once())
            ->method('getName')
            ->willReturn($campaignName);

        $campaign->expects(self::once())
            ->method('getCampaignId')
            ->willReturn($campaignId);

        $wallet = $this->createMock(Wallet::class);

        $this->accountFacade->expects(self::once())
            ->method('getWallet')
            ->with($storeId, $customerId, $walletCode)
            ->willReturn($wallet);

        $account = $this->createMock(Account::class);

        $wallet->expects(self::once())
            ->method('getAccount')
            ->willReturn($account);

        $account->method('getActiveUnits')
            ->willReturn(100.0);

        $this->pointsFacade->expects(self::once())
            ->method('spendUnit')
            ->with(
                $storeId,
                $customerId,
                100.0,
                $wallet,
                $campaignName,
                new ActionCause(
                    $campaignId,
                    $customerId
                )
            );

        $this->campaignLogger->expects(self::never())
            ->method('error');

        $this->calculatedEffectResultFactory->expects(self::once())
            ->method('createDeduct')
            ->with(
                EffectStatus::createSuccess(),
                $customerId,
                $walletCode,
                100.0
            );

        $result = $this->deductUnitEffect->apply(
            $context,
            $campaign,
            $calculatedEffect
        );

        self::assertInstanceOf(CalculatedEffectResult::class, $result);
    }

    /**
     * @test
     */
    public function it_applies_deduct_effect_when_customer_has_less_unit(): void
    {
        $context = $this->createMock(Context::class);
        $context->method('getCustomerId')->willReturn(new CustomerId('********-0000-0000-0000-************'));

        $campaignId = new CampaignId('********-0000-0000-0000-************');
        $campaignName = 'Campaign name';
        $campaign = $this->createMock(Campaign::class);

        $deductEffect = $this->createMock(DeductEffectEntity::class);
        $deductEffect->method('getPointsRule')->willReturn('100');

        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->method('getEffect')
            ->willReturn($deductEffect);

        $walletCode = 'stars';

        $deductEffect->expects(self::once())
            ->method('getWalletCode')
            ->willReturn($walletCode);

        $calculatedEffect->method('getPoints')
            ->willReturn(100.0);

        $storeId = new StoreId('********-0000-0000-0000-************');
        $campaign->method('getStoreId')->willReturn($storeId);

        $effectTarget = $this->createMock(EffectTarget::class);

        $calculatedEffect->method('getTarget')
            ->willReturn($effectTarget);

        $customerId = new CustomerId('********-0000-0000-0000-************');

        $effectTarget->method('getCustomerId')
            ->willReturn($customerId);

        $campaign->expects(self::once())
            ->method('getName')
            ->willReturn($campaignName);

        $campaign->expects(self::once())
            ->method('getCampaignId')
            ->willReturn($campaignId);

        $wallet = $this->createMock(Wallet::class);

        $this->accountFacade->expects(self::once())
            ->method('getWallet')
            ->with($storeId, $customerId, $walletCode)
            ->willReturn($wallet);

        $account = $this->createMock(Account::class);

        $wallet->expects(self::once())
            ->method('getAccount')
            ->willReturn($account);

        $account->method('getActiveUnits')->willReturn(50.0);

        $this->pointsFacade->expects(self::once())
            ->method('spendUnit')
            ->with(
                $storeId,
                $customerId,
                50.0,
                $wallet,
                $campaignName,
                new ActionCause(
                    $campaignId,
                    $customerId
                )
            );

        $this->campaignLogger->expects(self::never())
            ->method('error');

        $this->calculatedEffectResultFactory->expects(self::once())
            ->method('createDeduct')
            ->with(
                EffectStatus::createSuccess(),
                $customerId,
                $walletCode,
                50.0
            );

        $result = $this->deductUnitEffect->apply(
            $context,
            $campaign,
            $calculatedEffect
        );

        self::assertInstanceOf(CalculatedEffectResult::class, $result);
    }

    /**
     * @test
     */
    public function it_skips_applies_deduct_effect_when_member_does_not_have_unit(): void
    {
        $context = $this->createMock(Context::class);
        $context->method('getCustomerId')->willReturn(new CustomerId('********-0000-0000-0000-************'));

        $campaign = $this->createMock(Campaign::class);

        $deductEffect = $this->createMock(DeductEffectEntity::class);
        $deductEffect->method('getPointsRule')->willReturn('100');

        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->method('getEffect')
            ->willReturn($deductEffect);

        $walletCode = 'stars';

        $deductEffect->expects(self::once())
            ->method('getWalletCode')
            ->willReturn($walletCode);

        $calculatedEffect->method('getPoints')
            ->willReturn(100.0);

        $storeId = new StoreId('********-0000-0000-0000-************');
        $campaign->expects(self::once())->method('getStoreId')->willReturn($storeId);

        $effectTarget = $this->createMock(EffectTarget::class);

        $calculatedEffect->method('getTarget')
            ->willReturn($effectTarget);

        $customerId = new CustomerId('********-0000-0000-0000-************');

        $effectTarget->method('getCustomerId')
            ->willReturn($customerId);

        $wallet = $this->createMock(Wallet::class);

        $this->accountFacade->expects(self::once())
            ->method('getWallet')
            ->with($storeId, $customerId, $walletCode)
            ->willReturn($wallet);

        $account = $this->createMock(Account::class);

        $wallet->expects(self::once())
            ->method('getAccount')
            ->willReturn($account);

        $account->method('getActiveUnits')->willReturn(0.0);

        $this->pointsFacade->expects(self::never())
            ->method('spendUnit');

        $this->campaignLogger->expects(self::never())
            ->method('error');

        $this->calculatedEffectResultFactory->expects(self::once())
            ->method('createDeduct')
            ->with(
                EffectStatus::createSkipped(),
                $customerId,
                null,
                100.0
            );

        $result = $this->deductUnitEffect->apply(
            $context,
            $campaign,
            $calculatedEffect
        );

        self::assertInstanceOf(CalculatedEffectResult::class, $result);
    }

    /**
     * @test
     */
    public function it_does_not_skip_applies_deduct_effect_when_member_does_not_have_unit_for_negative_balance_wallet(): void
    {
        $context = $this->createMock(Context::class);
        $context->method('getCustomerId')->willReturn(new CustomerId('********-0000-0000-0000-************'));

        $campaignId = new CampaignId('********-0000-0000-0000-************');
        $campaignName = 'Campaign name';
        $campaign = $this->createMock(Campaign::class);

        $deductEffect = $this->createMock(DeductEffectEntity::class);
        $deductEffect->method('getPointsRule')->willReturn('100');

        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->method('getEffect')
            ->willReturn($deductEffect);

        $walletCode = 'stars';

        $deductEffect->expects(self::once())
            ->method('getWalletCode')
            ->willReturn($walletCode);

        $calculatedEffect->method('getPoints')
            ->willReturn(100.0);

        $storeId = new StoreId('********-0000-0000-0000-************');
        $store = $this->createMock(Store::class);

        $campaign->method('getStoreId')->willReturn($storeId);

        $store->method('getStoreId')->willReturn($storeId);

        $effectTarget = $this->createMock(EffectTarget::class);

        $calculatedEffect->method('getTarget')
            ->willReturn($effectTarget);

        $customerId = new CustomerId('********-0000-0000-0000-************');

        $effectTarget->method('getCustomerId')
            ->willReturn($customerId);

        $campaign->expects(self::once())
            ->method('getName')
            ->willReturn($campaignName);

        $campaign->expects(self::once())
            ->method('getCampaignId')
            ->willReturn($campaignId);

        $walletType = $this->createMock(WalletType::class);
        $walletType->expects($this->exactly(1))->method('isNegativeBalance')->willReturn(true);

        $wallet = $this->createMock(Wallet::class);
        $wallet->expects($this->exactly(1))->method('getWalletType')->willReturn($walletType);

        $this->accountFacade->expects(self::once())
            ->method('getWallet')
            ->with($storeId, $customerId, $walletCode)
            ->willReturn($wallet);

        $account = $this->createMock(Account::class);

        $wallet->expects(self::once())
            ->method('getAccount')
            ->willReturn($account);

        $account->method('getActiveUnits')
            ->willReturn(-100.0);

        $this->pointsFacade->expects(self::once())
            ->method('spendUnit')
            ->with(
                $storeId,
                $customerId,
                100.0,
                $wallet,
                $campaignName,
                new ActionCause(
                    $campaignId,
                    $customerId
                )
            );

        $this->campaignLogger->expects(self::never())
            ->method('error');

        $this->calculatedEffectResultFactory->expects(self::once())
            ->method('createDeduct')
            ->with(
                EffectStatus::createSuccess(),
                $customerId,
                $walletCode,
                100.0
            );

        $result = $this->deductUnitEffect->apply(
            $context,
            $campaign,
            $calculatedEffect
        );

        self::assertInstanceOf(CalculatedEffectResult::class, $result);
    }

    /**
     * @test
     */
    public function it_applies_deduct_effect_with_skipped_status(): void
    {
        $context = $this->createMock(Context::class);
        $context->method('getCustomerId')->willReturn(new CustomerId('********-0000-0000-0000-************'));

        $campaign = $this->createMock(Campaign::class);

        $deductEffect = $this->createMock(DeductEffectEntity::class);
        $deductEffect->method('getPointsRule')->willReturn('100');

        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->method('getEffect')
            ->willReturn($deductEffect);

        $walletCode = 'stars';

        $deductEffect->expects(self::once())
            ->method('getWalletCode')
            ->willReturn($walletCode);

        $calculatedEffect->method('getPoints')
            ->willReturn(-1.0);

        $effectTarget = $this->createMock(EffectTarget::class);

        $calculatedEffect->method('getTarget')
            ->willReturn($effectTarget);

        $customerId = new CustomerId('********-0000-0000-0000-************');

        $effectTarget->method('getCustomerId')
            ->willReturn($customerId);

        $this->calculatedEffectResultFactory->expects(self::once())
            ->method('createDeduct')
            ->with(
                EffectStatus::createSkipped(),
                $customerId,
                $walletCode,
                -1.0
            );

        $this->pointsFacade->expects(self::never())
            ->method('spendUnit');

        $this->campaignLogger->expects(self::never())
            ->method('error');

        $result = $this->deductUnitEffect->apply(
            $context,
            $campaign,
            $calculatedEffect
        );

        self::assertInstanceOf(CalculatedEffectResult::class, $result);
    }

    /**
     * @test
     */
    public function it_throws_exception_when_effect_is_not_deduct(): void
    {
        $this->expectException(\InvalidArgumentException::class);

        $context = $this->createMock(Context::class);
        $context->method('getCustomerId')->willReturn(new CustomerId('********-0000-0000-0000-************'));

        $campaign = $this->createMock(Campaign::class);

        $deductEffect = $this->createMock(PointsEffect::class);
        $deductEffect->method('getPointsRule')->willReturn('100');

        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->method('getEffect')
            ->willReturn($deductEffect);

        $this->deductUnitEffect->apply(
            $context,
            $campaign,
            $calculatedEffect
        );
    }

    /**
     * @test
     */
    public function it_test_supports_method(): void
    {
        $effect = $this->createMock(DeductEffectEntity::class);
        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->expects(self::once())
            ->method('getEffect')
            ->willReturn($effect);

        $effect->expects(self::once())
            ->method('getType')
            ->willReturn('deduct_unit');

        self::assertTrue($this->deductUnitEffect->supports($calculatedEffect));
    }
}
