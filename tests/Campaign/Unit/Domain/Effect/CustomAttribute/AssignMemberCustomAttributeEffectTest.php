<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Domain\Effect\CustomAttribute;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\CustomerFacadeInterface;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\CalculatedEffect;
use OpenLoyalty\Campaign\Domain\Effect\CustomAttribute\AssignMemberCustomAttributeEffect;
use OpenLoyalty\Campaign\Domain\Entity\CampaignExecution\CalculatedEffectResult;
use OpenLoyalty\Campaign\Domain\Entity\Effect\AssignMemberCustomAttributeEffect as AssignMemberCustomAttributeEffectEntity;
use OpenLoyalty\Campaign\Domain\Entity\Effect\EffectInterface;
use OpenLoyalty\Campaign\Domain\Factory\CalculatedEffectResultFactoryInterface;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectStatus;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;
use OpenLoyalty\Campaign\Domain\ValueObject\Target;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class AssignMemberCustomAttributeEffectTest extends TestCase
{
    private AssignMemberCustomAttributeEffect $assignMemberCustomAttributeEffect;
    private CalculatedEffectResultFactoryInterface $calculatedEffectResultFactory;
    private LoggerInterface $campaignLogger;
    private CustomerFacadeInterface $customerFacade;

    protected function setUp(): void
    {
        $this->calculatedEffectResultFactory = $this->createMock(CalculatedEffectResultFactoryInterface::class);
        $this->campaignLogger = $this->createMock(LoggerInterface::class);
        $this->customerFacade = $this->createMock(CustomerFacadeInterface::class);

        $this->assignMemberCustomAttributeEffect = new AssignMemberCustomAttributeEffect(
            $this->customerFacade,
            $this->calculatedEffectResultFactory,
            $this->campaignLogger,
        );
    }

    /**
     * @test
     */
    public function it_applies_assign_member_custom_attribute_effect(): void
    {
        $context = $this->createMock(Context::class);
        $context->method('getCustomerId')->willReturn(new CustomerId('00000000-0000-0000-0000-000000000000'));

        $assignMemberCustomAttributeEffectEntity = $this->createMock(AssignMemberCustomAttributeEffectEntity::class);
        $assignMemberCustomAttributeEffectEntity->method('getCustomAttributeKey')->willReturn('customAttributeKey');
        $assignMemberCustomAttributeEffectEntity->method('getCustomAttributeValueRule')
                                                ->willReturn('customAttributeValue');

        $customerId = new CustomerId('00000000-0000-0000-0000-000000000000');

        $effectTarget = $this->createMock(EffectTarget::class);
        $effectTarget->method('getCustomerId')->willReturn($customerId);

        $calculatedEffect = $this->createMock(CalculatedEffect::class);
        $calculatedEffect->method('getTarget')->willReturn($effectTarget);
        $calculatedEffect->method('getEffect')->willReturn($assignMemberCustomAttributeEffectEntity);
        $calculatedEffect->method('getCustomAttributeValue')->willReturn('customAttributeValue');

        $storeId = new StoreId('00000000-0000-0000-0000-000000000000');
        $campaign = $this->createMock(Campaign::class);
        $campaign->method('getStoreId')->willReturn($storeId);

        $this->customerFacade->expects(self::once())
                           ->method('addOrUpdateMemberCustomAttribute')
                           ->with($storeId, $customerId, 'customAttributeKey', 'customAttributeValue');

        $this->campaignLogger->expects(self::never())
                             ->method('error');

        $this->calculatedEffectResultFactory->expects(self::once())
                                            ->method('createCustomAttribute')
                                            ->with(EffectStatus::createSuccess(), $customerId);

        $result = $this->assignMemberCustomAttributeEffect->apply($context, $campaign, $calculatedEffect);

        self::assertInstanceOf(CalculatedEffectResult::class, $result);
    }

    /**
     * @test
     */
    public function it_throws_exception_when_effect_is_not_assign_member_custom_attribute(): void
    {
        $this->expectException(\InvalidArgumentException::class);

        $context = $this->createMock(Context::class);
        $context->method('getCustomerId')->willReturn(new CustomerId('00000000-0000-0000-0000-000000000000'));

        $campaign = $this->createMock(Campaign::class);

        $assignMemberCustomAttributeEffectEntity = $this->createMock(EffectInterface::class);

        $calculatedEffect = $this->createMock(CalculatedEffect::class);
        $calculatedEffect->method('getEffect')->willReturn($assignMemberCustomAttributeEffectEntity);

        $this->assignMemberCustomAttributeEffect->apply(
            $context,
            $campaign,
            $calculatedEffect
        );
    }

    /**
     * @test
     */
    public function it_test_supports_method(): void
    {
        $effect = $this->createMock(AssignMemberCustomAttributeEffectEntity::class);
        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->expects(self::once())
                         ->method('getEffect')
                         ->willReturn($effect);

        $effect->expects(self::once())
               ->method('getType')
               ->willReturn(AssignMemberCustomAttributeEffectEntity::EFFECT);

        self::assertTrue($this->assignMemberCustomAttributeEffect->supports($calculatedEffect));
    }

    /**
     * @test
     */
    public function it_throws_exception_when_effect_is_not_applied_and_failed_state_is_set(): void
    {
        $context = $this->createMock(Context::class);
        $context->method('getCustomerId')->willReturn(new CustomerId('00000000-0000-0000-0000-000000000000'));

        $campaign = $this->createMock(Campaign::class);
        $campaign->method('getCampaignId')->willReturn(new CampaignId('07c52f10-bb24-4253-ba9f-956337fd2a30'));

        $assignMemberCustomAttributeEffectEntity = $this->createMock(AssignMemberCustomAttributeEffectEntity::class);

        $calculatedEffect = $this->createMock(CalculatedEffect::class);
        $calculatedEffect->method('getEffect')->willReturn($assignMemberCustomAttributeEffectEntity);
        $calculatedEffect->method('getTarget')->willReturn(new EffectTarget(
            new CustomerId('00000000-0000-0000-0000-000000000000'),
            Target::create(Target::SELF)
        ));

        $this->customerFacade->method('addOrUpdateMemberCustomAttribute')->willThrowException(new \Exception('some exception'));

        $this->calculatedEffectResultFactory->expects($this->once())->method('createCustomAttribute')
            ->with(EffectStatus::createFailed(), new CustomerId('00000000-0000-0000-0000-000000000000'));

        $this->assignMemberCustomAttributeEffect->apply(
            $context,
            $campaign,
            $calculatedEffect
        );
    }
}
