<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Domain\Effect\Factory;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\CalculatedEffect;
use OpenLoyalty\Campaign\Domain\Effect\CustomAttribute\AssignMemberCustomAttributeEffectResult;
use OpenLoyalty\Campaign\Domain\Effect\Factory\AssignMemberCustomAttributeEffectResultFactory;
use OpenLoyalty\Campaign\Domain\Effect\Factory\EffectResultFactoryInterface;
use OpenLoyalty\Campaign\Domain\Entity\Effect\AssignMemberCustomAttributeEffect;
use OpenLoyalty\Campaign\Domain\Entity\Effect\EffectInterface;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use PHPUnit\Framework\TestCase;

final class AssignMemberCustomAttributeEffectResultFactoryTest extends TestCase
{
    private EffectResultFactoryInterface $resultFactory;

    protected function setUp(): void
    {
        $this->resultFactory = new AssignMemberCustomAttributeEffectResultFactory();
    }

    /**
     * @test
     */
    public function it_supports_for_assign_member_custom_attribute_effect_type(): void
    {
        $effect = $this->createMock(EffectInterface::class);
        $effect->expects(self::once())
            ->method('getType')
            ->willReturn(AssignMemberCustomAttributeEffect::EFFECT);

        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->expects(self::once())
            ->method('getEffect')
            ->willReturn($effect);

        self::assertTrue($this->resultFactory->supports($calculatedEffect));
    }

    /**
     * @test
     */
    public function it_does_no_support_for_unsupported_effect_type(): void
    {
        $effect = $this->createMock(EffectInterface::class);
        $effect->expects(self::once())
            ->method('getType')
            ->willReturn('NON_SUPPORTED_EFFECT');

        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->expects(self::once())
            ->method('getEffect')
            ->willReturn($effect);

        self::assertFalse($this->resultFactory->supports($calculatedEffect));
    }

    /**
     * @test
     */
    public function it_creates_assign_member_custom_attribute_effect_result(): void
    {
        $target = $this->createMock(EffectTarget::class);

        $campaignId = new CampaignId('00000000-0000-0000-0000-000000000002');
        $campaign = $this->createMock(Campaign::class);
        $campaign->expects(self::once())->method('getCampaignId')->willReturn($campaignId);

        $effect = $this->createMock(AssignMemberCustomAttributeEffect::class);
        $effect->expects(self::once())->method('getCustomAttributeKey')->willReturn('customAttributeKey');
        $effect->expects(self::once())->method('getCustomAttributeValueRule')->willReturn('customAttributeValueRule');

        $calculatedEffect = $this->createMock(CalculatedEffect::class);
        $calculatedEffect->expects(self::once())->method('getCustomAttributeValue')->willReturn('customAttributeValue');
        $calculatedEffect->expects(self::exactly(3))->method('getEffect')->willReturn($effect);
        $calculatedEffect->expects(self::once())->method('getTarget')->willReturn($target);

        self::assertInstanceOf(
            AssignMemberCustomAttributeEffectResult::class,
            $this->resultFactory->create($campaign, $calculatedEffect)
        );
    }
}
