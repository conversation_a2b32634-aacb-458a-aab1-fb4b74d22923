<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Domain\Effect\Factory;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\CalculatedEffect;
use OpenLoyalty\Campaign\Domain\Effect\Factory\DeductEffectResultFactory;
use OpenLoyalty\Campaign\Domain\Effect\Factory\EffectResultFactoryInterface;
use OpenLoyalty\Campaign\Domain\Effect\Points\DeductEffectResult;
use OpenLoyalty\Campaign\Domain\Entity\Effect\DeductEffect;
use OpenLoyalty\Campaign\Domain\Entity\Effect\EffectInterface;
use OpenLoyalty\Campaign\Domain\Entity\Effect\PointsEffect;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use PHPUnit\Framework\TestCase;

final class DeductEffectResultFactoryTest extends TestCase
{
    private EffectResultFactoryInterface $resultFactory;

    protected function setUp(): void
    {
        $this->resultFactory = new DeductEffectResultFactory();
    }

    /**
     * @test
     */
    public function it_supports_for_deduct_effect_type(): void
    {
        $effect = $this->createMock(EffectInterface::class);
        $effect->expects(self::once())
            ->method('getType')
            ->willReturn(DeductEffect::EFFECT);

        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->expects(self::once())
            ->method('getEffect')
            ->willReturn($effect);

        self::assertTrue($this->resultFactory->supports($calculatedEffect));
    }

    /**
     * @test
     */
    public function it_does_no_support_for_non_deduct_effect_type(): void
    {
        $effect = $this->createMock(EffectInterface::class);
        $effect->expects(self::once())
            ->method('getType')
            ->willReturn(PointsEffect::EFFECT);

        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->expects(self::once())
            ->method('getEffect')
            ->willReturn($effect);

        self::assertFalse($this->resultFactory->supports($calculatedEffect));
    }

    /**
     * @test
     */
    public function it_create_deduct_effect_result(): void
    {
        $campaignId = new CampaignId('00000000-0000-0000-0000-000000000002');

        $campaign = $this->createMock(Campaign::class);
        $campaign->expects(self::once())
            ->method('getCampaignId')
            ->willReturn($campaignId);

        $effect = $this->createMock(DeductEffect::class);

        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $calculatedEffect->expects(self::once())
            ->method('getPoints')
            ->willReturn(100.0);

        $calculatedEffect->expects(self::exactly(3))
            ->method('getEffect')
            ->willReturn($effect);

        $effect->expects(self::once())
            ->method('getPointsRule')
            ->willReturn('100');

        $target = $this->createMock(EffectTarget::class);

        $calculatedEffect->expects(self::once())
            ->method('getTarget')
            ->willReturn($target);

        self::assertInstanceOf(
            DeductEffectResult::class,
            $this->resultFactory->create($campaign, $calculatedEffect)
        );
    }
}
