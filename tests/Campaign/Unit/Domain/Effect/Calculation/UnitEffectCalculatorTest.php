<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Domain\Effect\Calculation;

use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\CalculatedEffect;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\EffectCalculatorInterface;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\UnitEffectCalculator;
use OpenLoyalty\Campaign\Domain\Entity\Effect\DeductEffect;
use OpenLoyalty\Campaign\Domain\Entity\Effect\EffectInterface;
use OpenLoyalty\Campaign\Domain\Entity\Effect\PointsEffect;
use OpenLoyalty\Campaign\Domain\Entity\Effect\RewardEffect;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;
use OpenLoyalty\Tools\ExpressionLanguage\InvalidExpressionException;
use OpenLoyalty\Tools\ExpressionLanguage\ValueExpressionLanguageInterface;
use PHPUnit\Framework\TestCase;

final class UnitEffectCalculatorTest extends TestCase
{
    private ValueExpressionLanguageInterface $expressionLanguage;
    private EffectCalculatorInterface $unitEffectCalculator;

    protected function setUp(): void
    {
        $this->expressionLanguage = $this->createMock(ValueExpressionLanguageInterface::class);

        $this->unitEffectCalculator = new UnitEffectCalculator(
            $this->expressionLanguage
        );
    }

    /**
     * @test,
     */
    public function it_supports_for_points_effect_type(): void
    {
        $effect = $this->createMock(EffectInterface::class);
        $effect->method('getType')->willReturn(PointsEffect::EFFECT);

        $result = $this->unitEffectCalculator->supports($effect);

        self::assertTrue($result);
    }

    /**
     * @test
     */
    public function it_supports_for_deduct_effect_type(): void
    {
        $effect = $this->createMock(EffectInterface::class);
        $effect->method('getType')->willReturn(DeductEffect::EFFECT);

        $result = $this->unitEffectCalculator->supports($effect);

        self::assertTrue($result);
    }

    /**
     * @test
     */
    public function it_does_no_support_for_non_points_effect_type(): void
    {
        $effect = $this->createMock(EffectInterface::class);
        $effect->method('getType')->willReturn(RewardEffect::EFFECT);

        $result = $this->unitEffectCalculator->supports($effect);

        self::assertFalse($result);
    }

    /**
     * @test
     */
    public function it_returns_null_while_the_effect_has_wrong_instance(): void
    {
        $context = $this->createMock(Context::class);
        $effect = $this->createMock(RewardEffect::class);
        $target = $this->createMock(EffectTarget::class);

        $this->expressionLanguage->expects(self::never())->method('evaluate');

        $result = $this->unitEffectCalculator->calculateEffect($context, $effect, $target);

        self::assertNull($result);
    }

    /**
     * @test
     */
    public function it_returns_null_while_points_are_null(): void
    {
        $context = $this->createMock(Context::class);
        $effect = $this->createMock(PointsEffect::class);
        $target = $this->createMock(EffectTarget::class);

        $this->expressionLanguage->expects(self::once())->method('evaluate')->willReturn(null);

        $result = $this->unitEffectCalculator->calculateEffect($context, $effect, $target);

        self::assertNull($result);
    }

    /**
     * @test
     */
    public function it_returns_calculated_effect_for_points_effect(): void
    {
        $context = $this->createMock(Context::class);
        $effect = $this->createMock(PointsEffect::class);
        $target = $this->createMock(EffectTarget::class);

        $this->expressionLanguage->expects(self::once())->method('evaluate')->willReturn(120);

        $result = $this->unitEffectCalculator->calculateEffect($context, $effect, $target);

        self::assertInstanceOf(CalculatedEffect::class, $result);
        self::assertSame(120.0, $result->getPoints());
    }

    /**
     * @test
     */
    public function it_returns_calculated_effect_for_deduct_effect(): void
    {
        $context = $this->createMock(Context::class);
        $effect = $this->createMock(DeductEffect::class);
        $target = $this->createMock(EffectTarget::class);

        $this->expressionLanguage->expects(self::once())->method('evaluate')->willReturn(120);

        $result = $this->unitEffectCalculator->calculateEffect($context, $effect, $target);

        self::assertInstanceOf(CalculatedEffect::class, $result);
        self::assertSame(120.0, $result->getPoints());
    }

    /**
     * @test
     * @dataProvider invalidExpressionValueProvider
     */
    public function it_throws_invalid_expression_exception_for_non_numeric_units_value(bool|string $invalidValue): void
    {
        $context = $this->createMock(Context::class);
        $effect = $this->createMock(PointsEffect::class);
        $target = $this->createMock(EffectTarget::class);

        $this->expressionLanguage->expects(self::once())
            ->method('evaluate')
            ->willReturn($invalidValue);

        $this->expectException(InvalidExpressionException::class);
        $this->unitEffectCalculator->calculateEffect($context, $effect, $target);
    }

    public function invalidExpressionValueProvider(): array
    {
        return [
            ['string'],
            ['5.4'],
            [''],
            [true],
            [false],
        ];
    }
}
