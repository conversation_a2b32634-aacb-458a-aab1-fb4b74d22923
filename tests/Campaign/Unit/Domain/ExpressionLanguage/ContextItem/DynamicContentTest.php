<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Domain\ExpressionLanguage\ContextItem;

use OpenLoyalty\Tools\ExpressionLanguage\ContextItem\DynamicContent;
use PHPUnit\Framework\TestCase;

final class DynamicContentTest extends TestCase
{
    /**
     * @test
     */
    public function it_gets_simple_value(): void
    {
        $object = DynamicContent::create(['fieldA' => 'valueA']);
        $this->assertSame('valueA', $object->fieldA);
    }

    /**
     * @test
     */
    public function it_gets_not_existing_value(): void
    {
        $object = DynamicContent::create(['fieldA' => 'valueA']);
        $this->assertNull($object->fieldB);
    }

    /**
     * @test
     */
    public function it_gets_dynamic_content(): void
    {
        $subObject = DynamicContent::create(['fieldB' => 'valueB']);
        $object = DynamicContent::create(['fieldA' => $subObject]);
        $this->assertSame('valueB', $object->fieldA->fieldB);
    }

    /**
     * @test
     */
    public function it_gets_raw_array(): void
    {
        $object = DynamicContent::create([
            ['fieldB' => 'valueB'],
            ['fieldB' => 'valueB'],
        ]);
        $this->assertIsArray($object);
        $this->assertSame($object[0]->fieldB, 'valueB');
    }
}
