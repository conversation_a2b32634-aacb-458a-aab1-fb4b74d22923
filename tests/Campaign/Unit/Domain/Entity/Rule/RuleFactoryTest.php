<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Domain\Entity\Rule;

use DateTime;
use DateTimeInterface;
use OpenLoyalty\Campaign\Domain\Entity\Condition\ConditionBuilder;
use OpenLoyalty\Campaign\Domain\Entity\Condition\ConditionInterface;
use OpenLoyalty\Campaign\Domain\Entity\Effect\EffectBuilder;
use OpenLoyalty\Campaign\Domain\Entity\Effect\EffectInterface;
use OpenLoyalty\Campaign\Domain\Entity\Rule\RuleFactory;
use PHPUnit\Framework\TestCase;

final class RuleFactoryTest extends TestCase
{
    /**
     * @test
     * @dataProvider getRules
     */
    public function it_creates_a_rule(array $data): void
    {
        $ruleFactory = new RuleFactory(new ConditionBuilder(), new EffectBuilder());
        $rule = $ruleFactory->fromArray($data);

        $this->assertInstanceOf(ConditionInterface::class, $rule->getConditions()[0]);
        $this->assertInstanceOf(EffectInterface::class, $rule->getEffects()[0]);
        $this->assertEquals($data['target'], $rule->getTarget());
    }

    public function getRules(): array
    {
        return [
            [
                [
                    'target' => 'self',
                    'conditions' => [
                        [
                            'attribute' => 'currentTime',
                            'operator' => 'is_between',
                            'data' => [
                                'from' => DateTime::createFromFormat(DateTimeInterface::ATOM, '2022-01-01T20:10:11P'),
                                'to' => DateTime::createFromFormat(DateTimeInterface::ATOM, '2022-02-01T20:10:11P'),
                            ],
                        ],
                    ],
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '300',
                            'unitsExpirationRule' => ['expirationStrategy' => 'from_wallet'],
                            'unitsLockRule' => ['lockStrategy' => 'from_wallet'],
                        ],
                    ],
                ],
            ],
            [
                [
                    'target' => 'referrer',
                    'conditions' => [
                        [
                            'attribute' => 'currentTime',
                            'operator' => 'is_between',
                            'data' => [
                                'from' => DateTime::createFromFormat(DateTimeInterface::ATOM, '2022-01-01T20:10:11P'),
                                'to' => DateTime::createFromFormat(DateTimeInterface::ATOM, '2022-02-01T20:10:11P'),
                            ],
                        ],
                    ],
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '300',
                            'unitsExpirationRule' => ['expirationStrategy' => 'from_wallet'],
                            'unitsLockRule' => ['lockStrategy' => 'from_wallet'],
                        ],
                    ],
                ],
            ],
        ];
    }
}
