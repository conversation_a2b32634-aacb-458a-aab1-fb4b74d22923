<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Domain\Limit\Validator;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\CampaignExecutionRepositoryInterface;
use OpenLoyalty\Campaign\Domain\Context;
use OpenLoyalty\Campaign\Domain\Effect\Calculation\CalculatedEffect;
use OpenLoyalty\Campaign\Domain\Entity\Effect\EffectInterface;
use OpenLoyalty\Campaign\Domain\Entity\Effect\PointsEffect;
use OpenLoyalty\Campaign\Domain\Limit\LimitPeriodCalculatorInterface;
use OpenLoyalty\Campaign\Domain\Limit\Validator\LimitValidatorInterface;
use OpenLoyalty\Campaign\Domain\Limit\Validator\PointsPerMemberLimitValidator;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectTarget;
use OpenLoyalty\Campaign\Domain\ValueObject\Limits;
use OpenLoyalty\Campaign\Domain\ValueObject\Target;
use OpenLoyalty\Campaign\Domain\ValueObject\UnitsExpirationRule;
use OpenLoyalty\Campaign\Domain\ValueObject\UnitsLockRule;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\ValueObject\UnitsLimit\UnitsPerMemberLimit;
use PHPUnit\Framework\TestCase;

final class PointsPerMemberLimitValidatorTest extends TestCase
{
    private CampaignExecutionRepositoryInterface $campaignExecutionRepository;
    private LimitValidatorInterface $validator;

    protected function setUp(): void
    {
        $this->campaignExecutionRepository = $this->createMock(CampaignExecutionRepositoryInterface::class);
        $this->validator = new PointsPerMemberLimitValidator(
            $this->campaignExecutionRepository,
            $this->createMock(LimitPeriodCalculatorInterface::class)
        );
    }

    /**
     * @test
     */
    public function it_return_false_while_customer_not_exists(): void
    {
        $storeId = $this->createMock(StoreId::class);
        $context = $this->createMock(Context::class);
        $campaign = $this->createMock(Campaign::class);

        $context->method('getCustomerId')->willReturn(null);
        $campaign->expects(self::never())->method('getLimits');

        $result = $this->validator->isValid($storeId, $context, $campaign, []);

        self::assertFalse($result);
    }

    /**
     * @test
     */
    public function it_return_true_while_limits_not_exists(): void
    {
        $storeId = $this->createMock(StoreId::class);
        $context = $this->createMock(Context::class);
        $campaign = $this->createMock(Campaign::class);
        $customerId = $this->createMock(CustomerId::class);

        $context->method('getCustomerId')->willReturn($customerId);
        $campaign->method('getLimits')->willReturn(null);

        $result = $this->validator->isValid($storeId, $context, $campaign, []);

        self::assertTrue($result);
    }

    /**
     * @test
     */
    public function it_return_true_while_points_per_member_limit_not_exists(): void
    {
        $storeId = $this->createMock(StoreId::class);
        $context = $this->createMock(Context::class);
        $campaign = $this->createMock(Campaign::class);
        $customerId = $this->createMock(CustomerId::class);
        $limits = Limits::create(null, null, null);

        $context->method('getCustomerId')->willReturn($customerId);
        $campaign->method('getLimits')->willReturn($limits);

        $result = $this->validator->isValid($storeId, $context, $campaign, []);

        self::assertTrue($result);
    }

    /**
     * @test
     */
    public function it_return_true_while_points_to_add_are_within_the_limit(): void
    {
        $storeId = $this->createMock(StoreId::class);
        $context = $this->createMock(Context::class);
        $campaign = $this->createMock(Campaign::class);
        $campaign->method('getCampaignId')->willReturn(new CampaignId('43b41976-05db-485f-9b37-f39c8592fa1e'));
        $customerId = new CustomerId('a2c828fd-9c63-493c-80d7-279ec9bea058');
        $pointsPerMemberLimit = UnitsPerMemberLimit::create(50.0);
        $limits = Limits::create(null, $pointsPerMemberLimit, null);
        $effect = $this->createMock(EffectInterface::class);
        $target = new EffectTarget($customerId, Target::create(Target::SELF));
        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $context->method('getCustomerId')->willReturn($customerId);
        $campaign->method('getLimits')->willReturn($limits);
        $calculatedEffect->method('getEffect')->willReturn($effect);
        $calculatedEffect->method('getPoints')->willReturn(10.0);
        $calculatedEffect->method('getTarget')->willReturn($target);
        $effect->method('getType')->willReturn(PointsEffect::EFFECT);

        $result = $this->validator->isValid($storeId, $context, $campaign, [$calculatedEffect]);

        self::assertTrue($result);
    }

    /**
     * @test
     */
    public function it_return_false_while_points_to_add_are_not_within_the_limit(): void
    {
        $storeId = $this->createMock(StoreId::class);
        $context = $this->createMock(Context::class);
        $campaign = $this->createMock(Campaign::class);
        $campaign->method('getCampaignId')->willReturn(new CampaignId('695de556-675c-4a05-a6f4-31ef75df48c3'));
        $customerId = new CustomerId('a2c828fd-9c63-493c-80d7-279ec9bea058');
        $pointsPerMemberLimit = UnitsPerMemberLimit::create(50.0);
        $limits = Limits::create(null, $pointsPerMemberLimit, null);
        $effect = $this->createMock(EffectInterface::class);
        $target = new EffectTarget($customerId, Target::create(Target::SELF));
        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $context->method('getCustomerId')->willReturn($customerId);
        $campaign->method('getLimits')->willReturn($limits);
        $calculatedEffect->method('getEffect')->willReturn($effect);
        $calculatedEffect->method('getPoints')->willReturn(60.0);
        $calculatedEffect->method('getTarget')->willReturn($target);
        $effect->method('getType')->willReturn(PointsEffect::EFFECT);

        $result = $this->validator->isValid($storeId, $context, $campaign, [$calculatedEffect]);

        self::assertFalse($result);
    }

    /**
     * @test
     */
    public function it_return_false_while_used_points_are_not_within_the_limit(): void
    {
        $storeId = $this->createMock(StoreId::class);
        $context = $this->createMock(Context::class);
        $campaign = $this->createMock(Campaign::class);
        $campaign->method('getCampaignId')->willReturn(new CampaignId('4e543d28-6a46-4551-94ae-1baba5db1638'));
        $customerId = new CustomerId('a2c828fd-9c63-493c-80d7-279ec9bea058');
        $pointsPerMemberLimit = UnitsPerMemberLimit::create(50.0);
        $limits = Limits::create(null, $pointsPerMemberLimit, null);
        $effect = $this->createMock(EffectInterface::class);
        $target = new EffectTarget($customerId, Target::create(Target::SELF));
        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $context->method('getCustomerId')->willReturn($customerId);
        $campaign->method('getLimits')->willReturn($limits);
        $calculatedEffect->method('getEffect')->willReturn($effect);
        $calculatedEffect->method('getPoints')->willReturn(0.0);
        $calculatedEffect->method('getTarget')->willReturn($target);
        $effect->method('getType')->willReturn(PointsEffect::EFFECT);

        $this->campaignExecutionRepository->method('getUsedPointsByLimitPeriod')->willReturn(51.0);

        $result = $this->validator->isValid($storeId, $context, $campaign, [$calculatedEffect]);

        self::assertFalse($result);
    }

    /**
     * @test
     */
    public function it_return_true_while_used_points_plus_points_to_add_are_within_the_limit(): void
    {
        $storeId = $this->createMock(StoreId::class);
        $context = $this->createMock(Context::class);
        $campaign = $this->createMock(Campaign::class);
        $campaign->method('getCampaignId')->willReturn(new CampaignId('a804b5c3-832e-4f01-b62e-dd6539c195f1'));
        $customerId = new CustomerId('a2c828fd-9c63-493c-80d7-279ec9bea058');
        $pointsPerMemberLimit = UnitsPerMemberLimit::create(50.0);
        $limits = Limits::create(null, $pointsPerMemberLimit, null);
        $effect = $this->createMock(EffectInterface::class);
        $target = new EffectTarget($customerId, Target::create(Target::SELF));
        $calculatedEffect = $this->createMock(CalculatedEffect::class);

        $context->method('getCustomerId')->willReturn($customerId);
        $campaign->method('getLimits')->willReturn($limits);
        $calculatedEffect->method('getEffect')->willReturn($effect);
        $calculatedEffect->method('getPoints')->willReturn(30.0);
        $calculatedEffect->method('getTarget')->willReturn($target);
        $effect->method('getType')->willReturn(PointsEffect::EFFECT);

        $this->campaignExecutionRepository->method('getUsedPointsByLimitPeriod')->willReturn(20.0);

        $result = $this->validator->isValid($storeId, $context, $campaign, [$calculatedEffect]);

        self::assertTrue($result);
    }

    /**
     * @test
     */
    public function it_return_false_while_used_points_plus_points_to_add_are_not_within_the_limit(): void
    {
        $storeId = $this->createMock(StoreId::class);
        $context = $this->createMock(Context::class);
        $campaign = $this->createMock(Campaign::class);
        $campaign->method('getCampaignId')->willReturn(new CampaignId('73bffabe-c5c7-481f-919a-c4cd04a774cf'));
        $customerId = new CustomerId('a2c828fd-9c63-493c-80d7-279ec9bea058');
        $pointsPerMemberLimit = UnitsPerMemberLimit::create(50.0);
        $limits = Limits::create(null, $pointsPerMemberLimit, null);
        $effect = $this->createMock(EffectInterface::class);
        $calculatedEffect = $this->createMock(CalculatedEffect::class);
        $target = new EffectTarget($customerId, Target::create(Target::SELF));
        $context->method('getCustomerId')->willReturn($customerId);
        $campaign->method('getLimits')->willReturn($limits);
        $calculatedEffect->method('getEffect')->willReturn($effect);
        $calculatedEffect->method('getPoints')->willReturn(30.0);
        $calculatedEffect->method('getTarget')->willReturn($target);
        $effect->method('getType')->willReturn(PointsEffect::EFFECT);

        $this->campaignExecutionRepository->method('getUsedPointsByLimitPeriod')->willReturn(21.0);

        $result = $this->validator->isValid($storeId, $context, $campaign, [$calculatedEffect]);

        self::assertFalse($result);
    }

    /**
     * @test
     */
    public function it_return_false_while_referrer_used_points_plus_points_to_add_are_not_within_the_limit(): void
    {
        $storeId = $this->createMock(StoreId::class);
        $context = $this->createMock(Context::class);
        $campaign = $this->createMock(Campaign::class);
        $campaign->method('getCampaignId')->willReturn(new CampaignId('73bffabe-c5c7-481f-919a-c4cd04a774cf'));
        $customerId = new CustomerId('a2c828fd-9c63-493c-80d7-279ec9bea058');
        $pointsPerMemberLimit = UnitsPerMemberLimit::create(20.0);
        $limits = Limits::create(null, $pointsPerMemberLimit, null);
        $effect = $this->createMock(EffectInterface::class);

        $referrerCalculatedEffect = new CalculatedEffect(
            new PointsEffect('20', UnitsExpirationRule::create('from_wallet'), UnitsLockRule::create('from_wallet')),
            new EffectTarget(new CustomerId('a2c828fd-9c63-493c-80d7-279ec9bea038'), Target::create(Target::REFERRER)),
            20
        );
        $selfCalculatedEffect = new CalculatedEffect(
            new PointsEffect('10', UnitsExpirationRule::create('from_wallet'), UnitsLockRule::create('from_wallet')),
            new EffectTarget($customerId, Target::create(Target::SELF)),
            10
        );

        $context->method('getCustomerId')->willReturn($customerId);
        $campaign->method('getLimits')->willReturn($limits);
        $effect->method('getType')->willReturn(PointsEffect::EFFECT);

        $this->campaignExecutionRepository->method('getUsedPointsByLimitPeriod')->willReturn(1.0);
        $result = $this->validator->isValid($storeId, $context, $campaign, [$selfCalculatedEffect, $referrerCalculatedEffect]);

        self::assertFalse($result);
    }
}
