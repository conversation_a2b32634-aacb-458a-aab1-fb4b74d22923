<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Domain\Limit;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\ValueObject\LimitInterval;
use OpenLoyalty\Core\Domain\ValueObject\LimitPeriod;
use PHPUnit\Framework\TestCase;

final class LimitIntervalCalculatorTest extends TestCase
{
    /**
     * @test
     * @dataProvider getIntervals
     */
    public function it_calculates_limit_period(
        array $interval,
        string $start,
        string $currentTime,
        string $end
    ): void {
        $calculator = new \OpenLoyalty\Core\Domain\Limit\LimitIntervalCalculator();
        $currentTime = new DateTimeImmutable($currentTime);
        $limitPeriod = $calculator->calculate(LimitInterval::create($interval[0], $interval[1]), $currentTime);
        $expected = new LimitPeriod(
            new DateTimeImmutable($start),
            new DateTimeImmutable($end)
        );
        $this->assertEqualsWithDelta($expected, $limitPeriod, 10);
    }

    public function getIntervals(): array
    {
        return [
            [['calendarHours', 1], '2022-04-21 12:00:00', '2022-04-21 12:31:45', '2022-04-21 12:59:59'],
            [['calendarHours', 2], '2022-04-21 11:00:00', '2022-04-21 12:31:45', '2022-04-21 12:59:59'],
            [['calendarHours', 2], '2022-04-21 05:00:00', '2022-04-21 06:01:00', '2022-04-21 06:59:59'],
            [['calendarHours', 48], '2022-04-19 11:00:00', '2022-04-21 10:15:00', '2022-04-21 10:59:59'],
            [['calendarDays', 1], '2022-04-21 00:00:00', '2022-04-21 12:31:45', '2022-04-21 23:59:59'],
            [['calendarDays', 2], '2022-04-20 00:00:00', '2022-04-21 10:15:00', '2022-04-21 23:59:59'],
            [['calendarDays', 21], '2022-04-01 00:00:00', '2022-04-21 15:59:59', '2022-04-21 23:59:59'],
            [['calendarWeeks', 1], '2022-04-18 00:00:00', '2022-04-21 12:31:45', '2022-04-24 23:59:59'],
            [['calendarWeeks', 2], '2022-04-11 00:00:00', '2022-04-21 10:15:00', '2022-04-24 23:59:59'],
            [['calendarWeeks', 4], '2022-04-04 00:00:00', '2022-04-25 12:30:00', '2022-05-01 23:59:59'],
            [['calendarWeeks', 22], '2021-11-22 00:00:00', '2022-04-21 15:59:59', '2022-04-24 23:59:59'],
            [['calendarMonths', 1], '2022-04-01 00:00:00', '2022-04-21 12:31:45', '2022-04-30 23:59:59'],
            [['calendarMonths', 2], '2022-03-01 00:00:00', '2022-04-21 10:15:00', '2022-04-30 23:59:59'],
            [['calendarMonths', 14], '2021-03-01 00:00:00', '2022-04-21 15:59:59', '2022-04-30 23:59:59'],
            [['calendarYears', 1], '2022-01-01 00:00:00', '2022-04-21 12:31:45', '2022-12-31 23:59:59'],
            [['calendarYears', 2], '2021-01-01 00:00:00', '2022-04-21 10:15:00', '2022-12-31 23:59:59'],
            [['calendarYears', 6], '2017-01-01 00:00:00', '2022-04-21 15:59:59', '2022-12-31 23:59:59'],
        ];
    }
}
