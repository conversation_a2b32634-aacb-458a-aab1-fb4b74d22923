<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Application\JobHandler;

use OpenLoyalty\Campaign\Application\Job\PrepareTimeCampaign;
use OpenLoyalty\Campaign\Application\JobHandler\PrepareTimeCampaignHandler;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\CampaignRepositoryInterface;
use OpenLoyalty\Campaign\Domain\CampaignTimeRequestRepositoryInterface;
use OpenLoyalty\Campaign\Domain\Exception\CampaignNotFoundException;
use OpenLoyalty\Campaign\Domain\TriggerStrategy\TriggerStrategyExecutorInterface;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\JobBusInterface;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use PHPUnit\Framework\TestCase;

final class PrepareTimeCampaignHandlerTest extends TestCase
{
    private CampaignRepositoryInterface $campaignRepository;
    private TriggerStrategyExecutorInterface $triggerStrategyExecutor;
    private CampaignTimeRequestRepositoryInterface $campaignTimeRequestRepository;
    private UuidGeneratorInterface $uuidGenerator;
    private JobBusInterface $jobBus;
    private PrepareTimeCampaignHandler $handler;

    protected function setUp(): void
    {
        $this->campaignRepository = $this->createMock(CampaignRepositoryInterface::class);
        $this->triggerStrategyExecutor = $this->createMock(TriggerStrategyExecutorInterface::class);
        $this->campaignTimeRequestRepository = $this->createMock(CampaignTimeRequestRepositoryInterface::class);
        $this->uuidGenerator = $this->createMock(UuidGeneratorInterface::class);
        $this->jobBus = $this->createMock(JobBusInterface::class);

        $this->handler = new PrepareTimeCampaignHandler(
            $this->campaignRepository,
            $this->triggerStrategyExecutor,
            $this->campaignTimeRequestRepository,
            $this->uuidGenerator,
            $this->jobBus
        );
    }

    /**
     * @test
     */
    public function it_handles_job(): void
    {
        $campaign = $this->createMock(Campaign::class);
        $this->campaignRepository->expects(self::once())->method('findOneBy')->willReturn($campaign);
        $this->triggerStrategyExecutor->expects(self::once())->method('getFilteredMembers');
        $this->campaignTimeRequestRepository->expects(self::once())->method('saveBulk');
        $this->jobBus->expects(self::once())->method('dispatch');

        $this->handler->__invoke(new PrepareTimeCampaign(
            new StoreId('00000000-0000-0000-0000-000000000007'),
            new CampaignId('00000000-0000-0000-0000-000000000007'),
            new \DateTimeImmutable()
        ));
    }

    /**
     * @test
     */
    public function it_throw_exception(): void
    {
        $this->campaignRepository->expects(self::once())->method('findOneBy')->willReturn(null);

        $this->expectException(CampaignNotFoundException::class);

        $this->handler->__invoke(new PrepareTimeCampaign(
            new StoreId('00000000-0000-0000-0000-000000000007'),
            new CampaignId('00000000-0000-0000-0000-000000000007'),
            new \DateTimeImmutable()
        ));
    }
}
