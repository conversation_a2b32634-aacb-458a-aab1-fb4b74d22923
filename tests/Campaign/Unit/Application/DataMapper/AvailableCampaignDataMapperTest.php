<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Application\DataMapper;

use Doctrine\Common\Collections\Collection;
use OpenLoyalty\Campaign\Application\DataMapper\AvailableCampaignDataMapper;
use OpenLoyalty\Campaign\Application\DataMapper\CampaignRuleDataMapper;
use OpenLoyalty\Campaign\Application\DataMapper\TranslationsDataMapper;
use OpenLoyalty\Campaign\Application\Response\AvailableCampaign;
use OpenLoyalty\Campaign\Application\Response\LimitUsages;
use OpenLoyalty\Campaign\Domain\AchievementFacadeInterface;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\ReadModel\Entity\Campaign as CampaignReadModel;
use OpenLoyalty\Campaign\Domain\ReadModel\Entity\MemberCampaignUsages;
use OpenLoyalty\Campaign\Domain\ReadModel\Projector\MassCampaignRequestProjection;
use OpenLoyalty\Campaign\Domain\ReadModel\Projector\MassMemberCampaignUsagesRequestProjection;
use OpenLoyalty\Campaign\Domain\ReadModel\Projector\SingleCampaignRequestProjection;
use OpenLoyalty\Campaign\Domain\ReadModel\Projector\SingleMemberCampaignUsagesRequestProjection;
use OpenLoyalty\Campaign\Domain\ValueObject\Activity;
use OpenLoyalty\Campaign\Domain\ValueObject\CodeGenerator;
use OpenLoyalty\Campaign\Domain\ValueObject\MemberFilter;
use OpenLoyalty\Campaign\Domain\ValueObject\Type;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\ReadModel\ProjectionProviderInterface;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use PHPUnit\Framework\TestCase;

final class AvailableCampaignDataMapperTest extends TestCase
{
    private ProjectionProviderInterface $projectionProvider;
    private AchievementFacadeInterface $achievementFacade;
    private TranslationsDataMapper $translationsDataMapper;
    private CampaignRuleDataMapper $campaignRuleDataMapper;
    private AvailableCampaignDataMapper $availableCampaignDataMapper;

    /**
     * @test
     */
    public function it_map_single_object(): void
    {
        $campaign = $this->createMock(Campaign::class);
        $customerId = new CustomerId('00000000-0000-aaaa-0000-000000000000');
        $memberCampaignUsagesProjection = $this->createMock(MemberCampaignUsages::class);
        $campaignReadModel = $this->createMock(CampaignReadModel::class);

        $trigger = Trigger::create('transaction');
        $translations = $this->createMock(Collection::class);

        $campaign->method('getStoreId')->willReturn(new StoreId('00000000-0000-aaaa-0000-000000000000'));
        $campaign->method('getCampaignId')->willReturn(new CampaignId('00000000-0000-aaaa-0000-000000000000'));
        $campaign->method('getLimits')->willReturn(null);
        $campaign->method('isTriggeredBy')->willReturn(false);
        $campaign->method('isActive')->willReturn(true);
        $campaign->method('getTrigger')->willReturn($trigger);
        $campaign->method('getName')->willReturn('Sample Campaign');
        $campaign->method('getDescription')->willReturn('Sample description');
        $campaign->method('getActivity')->willReturn($this->createMock(Activity::class));
        $campaign->method('getLabels')->willReturn(['label1', 'label2']);
        $campaign->method('getDisplayOrder')->willReturn(1);
        $campaign->method('getMultiLevel')->willReturn(2);
        $campaign->method('getCodeGenerator')->willReturn($this->createMock(CodeGenerator::class));
        $campaign->method('getEventCodeAttribute')->willReturn('event-code');
        $campaign->method('getType')->willReturn($this->createMock(Type::class));
        $campaign->method('getCreatedAt')->willReturn(new \DateTimeImmutable());
        $campaign->method('getMemberFilter')->willReturn($this->createMock(MemberFilter::class));
        $campaign->method('getAchievementId')->willReturn($this->createMock(AchievementId::class));
        $campaign->method('getEvent')->willReturn('event-sample');
        $campaign->method('getTranslations')->willReturn($translations);

        $memberCampaignUsagesProjection->method('isLimitReached')->willReturn(false);
        $memberCampaignUsagesProjection->method('getUsedLimitExecutionPerMember')->willReturn(0.0);
        $memberCampaignUsagesProjection->method('getUsedLimitUnitsPerMember')->willReturn(0.0);

        $campaignReadModel->method('getUsedUnits')->willReturn(0.0);

        $this->projectionProvider
            ->method('getProjection')
            ->withConsecutive(
                [new SingleMemberCampaignUsagesRequestProjection($campaign->getStoreId(), $campaign->getCampaignId(), $customerId)],
                [new SingleCampaignRequestProjection($campaign->getStoreId(), $campaign->getCampaignId())]
            )
            ->willReturnOnConsecutiveCalls(
                $memberCampaignUsagesProjection,
                $campaignReadModel
            );

        $this->translationsDataMapper
            ->method('map')
            ->willReturn(['en' => 'Campaign Name']);

        $this->campaignRuleDataMapper
            ->expects($this->atLeast(1))
            ->method('map')
            ->willReturn(['rule1', 'rule2']);

        $this->campaignRuleDataMapper
            ->expects($this->never())
            ->method('mapRaw');

        $availableCampaign = $this->availableCampaignDataMapper->map($campaign, $customerId);

        $this->assertInstanceOf(AvailableCampaign::class, $availableCampaign);
        $reflection = new \ReflectionClass($availableCampaign);

        $this->assertEquals('Sample Campaign', $this->getPrivateProperty($reflection, $availableCampaign, 'name'));
        $this->assertEquals('Sample description', $this->getPrivateProperty($reflection, $availableCampaign, 'description'));
        $this->assertEquals(new CampaignId('00000000-0000-aaaa-0000-000000000000'), $this->getPrivateProperty($reflection, $availableCampaign, 'campaignId'));
        $this->assertTrue($this->getPrivateProperty($reflection, $availableCampaign, 'active'));
        $this->assertEquals(['en' => 'Campaign Name'], $this->getPrivateProperty($reflection, $availableCampaign, 'translations'));
        $this->assertInstanceOf(Activity::class, $this->getPrivateProperty($reflection, $availableCampaign, 'activity'));
        $this->assertEquals(['rule1', 'rule2'], $this->getPrivateProperty($reflection, $availableCampaign, 'rules'));
        $this->assertNull($this->getPrivateProperty($reflection, $availableCampaign, 'limits'));
        $this->assertEquals($trigger, $this->getPrivateProperty($reflection, $availableCampaign, 'trigger'));
        $this->assertInstanceOf(AchievementId::class, $this->getPrivateProperty($reflection, $availableCampaign, 'achievementId'));
        $this->assertNull($this->getPrivateProperty($reflection, $availableCampaign, 'relatedAchievement'));
        $this->assertFalse($this->getPrivateProperty($reflection, $availableCampaign, 'limitReached'));

        $limitUsages = $this->getPrivateProperty($reflection, $availableCampaign, 'limitUsages');
        $this->assertInstanceOf(LimitUsages::class, $limitUsages);

        $limitUsagesReflection = new \ReflectionClass($limitUsages);
        $this->assertNull($this->getPrivateProperty($limitUsagesReflection, $limitUsages, 'points'));
        $this->assertNull($this->getPrivateProperty($limitUsagesReflection, $limitUsages, 'pointsPerMember'));
        $this->assertNull($this->getPrivateProperty($limitUsagesReflection, $limitUsages, 'executionsPerMember'));

        $this->assertEquals('event-sample', $this->getPrivateProperty($reflection, $availableCampaign, 'event'));
        $this->assertInstanceOf(MemberFilter::class, $this->getPrivateProperty($reflection, $availableCampaign, 'memberFilter'));
        $this->assertEquals(1, $this->getPrivateProperty($reflection, $availableCampaign, 'displayOrder'));
        $this->assertEquals(['label1', 'label2'], $this->getPrivateProperty($reflection, $availableCampaign, 'labels'));
        $this->assertEquals(2, $this->getPrivateProperty($reflection, $availableCampaign, 'multiLevel'));
        $this->assertInstanceOf(CodeGenerator::class, $this->getPrivateProperty($reflection, $availableCampaign, 'codeGenerator'));
        $this->assertEquals('event-code', $this->getPrivateProperty($reflection, $availableCampaign, 'eventCodeAttribute'));
    }

    /**
     * @test
     */
    public function it_map_for_list(): void
    {
        $campaign1 = $this->createMock(Campaign::class);
        $campaign2 = $this->createMock(Campaign::class);
        $customerId = new CustomerId('00000000-0000-aaaa-0000-000000000000');

        $campaignId1 = new CampaignId('00000000-0000-aaaa-0000-000000000001');
        $campaignId2 = new CampaignId('00000000-0000-aaaa-0000-000000000002');

        $storeId = new StoreId('00000000-0000-aaaa-0000-000000000000');
        $trigger = Trigger::create('transaction');
        $translations = $this->createMock(Collection::class);

        $campaign1->method('getStoreId')->willReturn($storeId);
        $campaign1->method('getCampaignId')->willReturn($campaignId1);
        $campaign1->method('getLimits')->willReturn(null);
        $campaign1->method('isTriggeredBy')->willReturn(false);
        $campaign1->method('isActive')->willReturn(true);
        $campaign1->method('getTrigger')->willReturn($trigger);
        $campaign1->method('getName')->willReturn('Campaign 1');
        $campaign1->method('getDescription')->willReturn('Description 1');
        $campaign1->method('getActivity')->willReturn($this->createMock(Activity::class));
        $campaign1->method('getLabels')->willReturn(['label1', 'label2']);
        $campaign1->method('getDisplayOrder')->willReturn(1);
        $campaign1->method('getMultiLevel')->willReturn(2);
        $campaign1->method('getCodeGenerator')->willReturn($this->createMock(CodeGenerator::class));
        $campaign1->method('getEventCodeAttribute')->willReturn('event-code');
        $campaign1->method('getType')->willReturn($this->createMock(Type::class));
        $campaign1->method('getCreatedAt')->willReturn(new \DateTimeImmutable());
        $campaign1->method('getMemberFilter')->willReturn($this->createMock(MemberFilter::class));
        $campaign1->method('getAchievementId')->willReturn($this->createMock(AchievementId::class));
        $campaign1->method('getEvent')->willReturn('event-sample-1');
        $campaign1->method('getTranslations')->willReturn($translations);

        $campaign2->method('getStoreId')->willReturn($storeId);
        $campaign2->method('getCampaignId')->willReturn($campaignId2);
        $campaign2->method('getLimits')->willReturn(null);
        $campaign2->method('isTriggeredBy')->willReturn(false);
        $campaign2->method('isActive')->willReturn(true);
        $campaign2->method('getTrigger')->willReturn($trigger);
        $campaign2->method('getName')->willReturn('Campaign 2');
        $campaign2->method('getDescription')->willReturn('Description 2');
        $campaign2->method('getActivity')->willReturn($this->createMock(Activity::class));
        $campaign2->method('getLabels')->willReturn(['label3', 'label4']);
        $campaign2->method('getDisplayOrder')->willReturn(2);
        $campaign2->method('getMultiLevel')->willReturn(3);
        $campaign2->method('getCodeGenerator')->willReturn($this->createMock(CodeGenerator::class));
        $campaign2->method('getEventCodeAttribute')->willReturn('event-code-2');
        $campaign2->method('getType')->willReturn($this->createMock(Type::class));
        $campaign2->method('getCreatedAt')->willReturn(new \DateTimeImmutable());
        $campaign2->method('getMemberFilter')->willReturn($this->createMock(MemberFilter::class));
        $campaign2->method('getAchievementId')->willReturn($this->createMock(AchievementId::class));
        $campaign2->method('getEvent')->willReturn('event-sample-2');
        $campaign2->method('getTranslations')->willReturn($translations);

        $memberCampaignUsagesProjection1 = $this->createMock(MemberCampaignUsages::class);
        $memberCampaignUsagesProjection1->method('isLimitReached')->willReturn(false);
        $memberCampaignUsagesProjection1->method('getUsedLimitExecutionPerMember')->willReturn(0.0);
        $memberCampaignUsagesProjection1->method('getUsedLimitUnitsPerMember')->willReturn(0.0);

        $memberCampaignUsagesProjection2 = $this->createMock(MemberCampaignUsages::class);
        $memberCampaignUsagesProjection2->method('isLimitReached')->willReturn(false);
        $memberCampaignUsagesProjection2->method('getUsedLimitExecutionPerMember')->willReturn(0.0);
        $memberCampaignUsagesProjection2->method('getUsedLimitUnitsPerMember')->willReturn(0.0);

        $campaignReadModel1 = $this->createMock(CampaignReadModel::class);
        $campaignReadModel1->method('getUsedUnits')->willReturn(0.0);

        $campaignReadModel2 = $this->createMock(CampaignReadModel::class);
        $campaignReadModel2->method('getUsedUnits')->willReturn(0.0);

        $this->projectionProvider
            ->method('getProjections')
            ->withConsecutive(
                [new MassMemberCampaignUsagesRequestProjection($customerId, $storeId, [$campaignId1, $campaignId2])],
                [new MassCampaignRequestProjection($storeId, [$campaignId1, $campaignId2])]
            )
            ->willReturnOnConsecutiveCalls(
                [
                    (string) $campaignId1 => $memberCampaignUsagesProjection1,
                    (string) $campaignId2 => $memberCampaignUsagesProjection2,
                ],
                [
                    (string) $campaignId1 => $campaignReadModel1,
                    (string) $campaignId2 => $campaignReadModel2,
                ]
            );

        $this->translationsDataMapper
            ->method('map')
            ->willReturn(['en' => 'Campaign Name']);

        $this->campaignRuleDataMapper
            ->expects($this->atLeast(1))
            ->method('map')
            ->willReturn(['rule1', 'rule1']);

        $this->campaignRuleDataMapper
            ->expects($this->never())
            ->method('mapRaw');

        $availableCampaigns = $this->availableCampaignDataMapper->mapList([$campaign1, $campaign2], $customerId);
        $this->assertCount(2, $availableCampaigns);

        $availableCampaign1 = $availableCampaigns[0];
        $reflection1 = new \ReflectionClass($availableCampaign1);
        $this->assertEquals('Campaign 1', $this->getPrivateProperty($reflection1, $availableCampaign1, 'name'));
        $this->assertEquals('Description 1', $this->getPrivateProperty($reflection1, $availableCampaign1, 'description'));

        $availableCampaign2 = $availableCampaigns[1];
        $reflection2 = new \ReflectionClass($availableCampaign2);
        $this->assertEquals('Campaign 2', $this->getPrivateProperty($reflection2, $availableCampaign2, 'name'));
        $this->assertEquals('Description 2', $this->getPrivateProperty($reflection2, $availableCampaign2, 'description'));
    }

    /**
     * @test
     */
    public function it_map_for_list_with_rules_raw(): void
    {
        $campaign1 = $this->createMock(Campaign::class);
        $campaign2 = $this->createMock(Campaign::class);
        $customerId = new CustomerId('00000000-0000-aaaa-0000-000000000000');

        $campaignId1 = new CampaignId('00000000-0000-aaaa-0000-000000000001');
        $campaignId2 = new CampaignId('00000000-0000-aaaa-0000-000000000002');

        $storeId = new StoreId('00000000-0000-aaaa-0000-000000000000');
        $trigger = Trigger::create('transaction');
        $translations = $this->createMock(Collection::class);

        $campaign1->method('getStoreId')->willReturn($storeId);
        $campaign1->method('getCampaignId')->willReturn($campaignId1);
        $campaign1->method('getLimits')->willReturn(null);
        $campaign1->method('isTriggeredBy')->willReturn(false);
        $campaign1->method('isActive')->willReturn(true);
        $campaign1->method('getTrigger')->willReturn($trigger);
        $campaign1->method('getName')->willReturn('Campaign 1');
        $campaign1->method('getDescription')->willReturn('Description 1');
        $campaign1->method('getActivity')->willReturn($this->createMock(Activity::class));
        $campaign1->method('getLabels')->willReturn(['label1', 'label2']);
        $campaign1->method('getDisplayOrder')->willReturn(1);
        $campaign1->method('getMultiLevel')->willReturn(2);
        $campaign1->method('getCodeGenerator')->willReturn($this->createMock(CodeGenerator::class));
        $campaign1->method('getEventCodeAttribute')->willReturn('event-code');
        $campaign1->method('getType')->willReturn($this->createMock(Type::class));
        $campaign1->method('getCreatedAt')->willReturn(new \DateTimeImmutable());
        $campaign1->method('getMemberFilter')->willReturn($this->createMock(MemberFilter::class));
        $campaign1->method('getAchievementId')->willReturn($this->createMock(AchievementId::class));
        $campaign1->method('getEvent')->willReturn('event-sample-1');
        $campaign1->method('getTranslations')->willReturn($translations);

        $campaign2->method('getStoreId')->willReturn($storeId);
        $campaign2->method('getCampaignId')->willReturn($campaignId2);
        $campaign2->method('getLimits')->willReturn(null);
        $campaign2->method('isTriggeredBy')->willReturn(false);
        $campaign2->method('isActive')->willReturn(true);
        $campaign2->method('getTrigger')->willReturn($trigger);
        $campaign2->method('getName')->willReturn('Campaign 2');
        $campaign2->method('getDescription')->willReturn('Description 2');
        $campaign2->method('getActivity')->willReturn($this->createMock(Activity::class));
        $campaign2->method('getLabels')->willReturn(['label3', 'label4']);
        $campaign2->method('getDisplayOrder')->willReturn(2);
        $campaign2->method('getMultiLevel')->willReturn(3);
        $campaign2->method('getCodeGenerator')->willReturn($this->createMock(CodeGenerator::class));
        $campaign2->method('getEventCodeAttribute')->willReturn('event-code-2');
        $campaign2->method('getType')->willReturn($this->createMock(Type::class));
        $campaign2->method('getCreatedAt')->willReturn(new \DateTimeImmutable());
        $campaign2->method('getMemberFilter')->willReturn($this->createMock(MemberFilter::class));
        $campaign2->method('getAchievementId')->willReturn($this->createMock(AchievementId::class));
        $campaign2->method('getEvent')->willReturn('event-sample-2');
        $campaign2->method('getTranslations')->willReturn($translations);

        $memberCampaignUsagesProjection1 = $this->createMock(MemberCampaignUsages::class);
        $memberCampaignUsagesProjection1->method('isLimitReached')->willReturn(false);
        $memberCampaignUsagesProjection1->method('getUsedLimitExecutionPerMember')->willReturn(0.0);
        $memberCampaignUsagesProjection1->method('getUsedLimitUnitsPerMember')->willReturn(0.0);

        $memberCampaignUsagesProjection2 = $this->createMock(MemberCampaignUsages::class);
        $memberCampaignUsagesProjection2->method('isLimitReached')->willReturn(false);
        $memberCampaignUsagesProjection2->method('getUsedLimitExecutionPerMember')->willReturn(0.0);
        $memberCampaignUsagesProjection2->method('getUsedLimitUnitsPerMember')->willReturn(0.0);

        $campaignReadModel1 = $this->createMock(CampaignReadModel::class);
        $campaignReadModel1->method('getUsedUnits')->willReturn(0.0);
        $campaignReadModel1->method('getRulesRaw')->willReturn(['rules1', 'rules2']);

        $campaignReadModel2 = $this->createMock(CampaignReadModel::class);
        $campaignReadModel2->method('getUsedUnits')->willReturn(0.0);
        $campaignReadModel2->method('getRulesRaw')->willReturn(['rules1', 'rules2']);

        $this->projectionProvider
            ->method('getProjections')
            ->withConsecutive(
                [new MassMemberCampaignUsagesRequestProjection($customerId, $storeId, [$campaignId1, $campaignId2])],
                [new MassCampaignRequestProjection($storeId, [$campaignId1, $campaignId2])]
            )
            ->willReturnOnConsecutiveCalls(
                [
                    (string) $campaignId1 => $memberCampaignUsagesProjection1,
                    (string) $campaignId2 => $memberCampaignUsagesProjection2,
                ],
                [
                    (string) $campaignId1 => $campaignReadModel1,
                    (string) $campaignId2 => $campaignReadModel2,
                ]
            );

        $this->translationsDataMapper
            ->method('map')
            ->willReturn(['en' => 'Campaign Name']);

        $this->campaignRuleDataMapper
            ->expects($this->atLeast(1))
            ->method('mapRaw');

        $this->campaignRuleDataMapper
            ->expects($this->never())
            ->method('map');

        $availableCampaigns = $this->availableCampaignDataMapper->mapList([$campaign1, $campaign2], $customerId);
        $this->assertCount(2, $availableCampaigns);

        $availableCampaign1 = $availableCampaigns[0];
        $reflection1 = new \ReflectionClass($availableCampaign1);
        $this->assertEquals('Campaign 1', $this->getPrivateProperty($reflection1, $availableCampaign1, 'name'));
        $this->assertEquals('Description 1', $this->getPrivateProperty($reflection1, $availableCampaign1, 'description'));

        $availableCampaign2 = $availableCampaigns[1];
        $reflection2 = new \ReflectionClass($availableCampaign2);
        $this->assertEquals('Campaign 2', $this->getPrivateProperty($reflection2, $availableCampaign2, 'name'));
        $this->assertEquals('Description 2', $this->getPrivateProperty($reflection2, $availableCampaign2, 'description'));
    }

    protected function setUp(): void
    {
        $this->projectionProvider = $this->createMock(ProjectionProviderInterface::class);
        $this->achievementFacade = $this->createMock(AchievementFacadeInterface::class);
        $this->translationsDataMapper = $this->createMock(TranslationsDataMapper::class);
        $this->campaignRuleDataMapper = $this->createMock(CampaignRuleDataMapper::class);

        $this->availableCampaignDataMapper = new AvailableCampaignDataMapper(
            $this->projectionProvider,
            $this->translationsDataMapper,
            $this->achievementFacade,
            $this->campaignRuleDataMapper,
        );
    }

    private function getPrivateProperty(\ReflectionClass $reflection, $object, string $property)
    {
        $property = $reflection->getProperty($property);

        return $property->getValue($object);
    }
}
