<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Application\Export;

use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use OpenLoyalty\Campaign\Application\Export\CalculatedEffectResultDataProvider;
use OpenLoyalty\Campaign\Domain\CalculatedEffectResultRepositoryInterface;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\Entity\CampaignExecution\CalculatedEffectResult as CalculatedEffectResultEntity;
use OpenLoyalty\Campaign\Domain\Entity\CampaignExecution\CampaignExecution;
use OpenLoyalty\Campaign\Domain\ValueObject\EffectStatus;
use OpenLoyalty\Campaign\Ui\Console\Command\Export\DataMapper\CalculatedEffectResultDataMapper;
use OpenLoyalty\Campaign\Ui\Console\Command\Export\Response\CalculatedEffectResult;
use OpenLoyalty\Core\Application\Export\DateRangeCriteriaBuilder;
use OpenLoyalty\Core\Domain\Id\CalculatedEffectResultId;
use OpenLoyalty\Core\Domain\Id\CampaignExecutionId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class CalculatedEffectResultDataProviderTest extends TestCase
{
    private SearchableResponderInterface|MockObject $searchableResponder;
    private CalculatedEffectResultDataProvider $dataProvider;

    protected function setUp(): void
    {
        $this->searchableResponder = $this->createMock(SearchableResponderInterface::class);

        $this->dataProvider = new CalculatedEffectResultDataProvider(
            $this->searchableResponder,
            $this->createMock(CalculatedEffectResultRepositoryInterface::class),
            new DateRangeCriteriaBuilder(),
            $this->createMock(EntityManagerInterface::class),
            new CalculatedEffectResultDataMapper()
        );
    }

    /**
     * @test
     */
    public function it_returns_items_count(): void
    {
        $this->searchableResponder->method('countByCriteria')->willReturn(5);

        self::assertSame(5, $this->dataProvider->getItemsCountByDate(null));
    }

    /**
     * @test
     * @dataProvider mappedItemsTestDataProvider
     */
    public function it_returns_mapped_items(Generator $foundItems, array $expectation): void
    {
        $this->searchableResponder->method('fromCriteriaIterable')->willReturn($foundItems);

        self::assertEquals($expectation, iterator_to_array($this->dataProvider->getItemsByDate(null)));
    }

    public function mappedItemsTestDataProvider(): Generator
    {
        yield 'It returns empty results for empty entry data' => [
            $this->array_to_iterator([]),
            [],
        ];

        $entity = new CalculatedEffectResultEntity(
            new CalculatedEffectResultId('52c34632-46eb-4cd1-8271-7c663a555a9c'),
            EffectStatus::createSuccess(),
            new CustomerId('75e25a89-6aae-4088-bc4c-59d04b250849'),
            'give_points',
            15.0,
            null,
            null,
            'default',
            null
        );
        $campaign = $this->createMock(Campaign::class);
        $storeId = new StoreId('89a3c5c1-5c46-4d03-bb30-15384c8cf00f');
        $campaign->method('getStoreId')->willReturn($storeId);
        $campaignExecution = $this->createMock(CampaignExecution::class);
        $campaignExecution->method('getCampaignExecutionId')->willReturn(
            new CampaignExecutionId('700dbee5-24c8-4077-8d29-0297b4d8911a')
        );
        $campaignExecution->method('getCampaign')->willReturn($campaign);
        $entity->setCampaignExecution($campaignExecution);
        $entity->setCreatedAt(new DateTimeImmutable('2022-12-23'));
        $entity->setUpdatedAt(new DateTimeImmutable('2022-12-23'));

        $response = new CalculatedEffectResult(
            new CalculatedEffectResultId('52c34632-46eb-4cd1-8271-7c663a555a9c'),
            new StoreId('89a3c5c1-5c46-4d03-bb30-15384c8cf00f'),
            new CampaignExecutionId('700dbee5-24c8-4077-8d29-0297b4d8911a'),
            EffectStatus::createSuccess(),
            new CustomerId('75e25a89-6aae-4088-bc4c-59d04b250849'),
            'give_points',
            new DateTimeImmutable('2022-12-23'),
            new DateTimeImmutable('2022-12-23'),
            15.0,
            null,
            null,
            'default',
            null
        );

        yield 'It returns mapped response' => [
            $this->array_to_iterator([$entity]),
            [$response],
        ];
    }

    private function array_to_iterator(array $array): Generator
    {
        foreach ($array as $item) {
            yield $item;
        }
    }
}
