<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Unit\Application\CommandHandler;

use OpenLoyalty\Campaign\Application\Command\UpdateCampaign;
use OpenLoyalty\Campaign\Application\CommandHandler\UpdateCampaignCommandHandler;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\CampaignAudienceRepositoryInterface;
use OpenLoyalty\Campaign\Domain\CampaignRepositoryInterface;
use OpenLoyalty\Campaign\Domain\CampaignVisibilityRepositoryInterface;
use OpenLoyalty\Campaign\Domain\Entity\Audience\CampaignAudienceFactoryInterface;
use OpenLoyalty\Campaign\Domain\Entity\Rule\RuleFactoryInterface;
use OpenLoyalty\Campaign\Domain\Entity\Visibility\CampaignVisibilityFactoryInterface;
use OpenLoyalty\Campaign\Domain\Exception\CannotChangeCampaignTypeException;
use OpenLoyalty\Campaign\Domain\SystemEvent\CampaignWasUpdated;
use OpenLoyalty\Campaign\Domain\ValueObject\MemberFilter;
use OpenLoyalty\Campaign\Domain\ValueObject\Type;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepository;
use PHPUnit\Framework\TestCase;

final class UpdateCampaignCommandHandlerTest extends TestCase
{
    private CampaignRepositoryInterface $campaignRepository;
    private UpdateCampaignCommandHandler $commandHandler;
    private EventBusInterface $eventBus;

    protected function setUp(): void
    {
        $this->campaignRepository = $this->createMock(CampaignRepositoryInterface::class);
        $this->eventBus = $this->createMock(EventBusInterface::class);

        $this->commandHandler = new UpdateCampaignCommandHandler(
            $this->campaignRepository,
            $this->createMock(RuleFactoryInterface::class),
            $this->createMock(CampaignVisibilityFactoryInterface::class),
            $this->createMock(CampaignAudienceFactoryInterface::class),
            $this->createMock(CampaignVisibilityRepositoryInterface::class),
            $this->createMock(CampaignAudienceRepositoryInterface::class),
            $this->eventBus
        );
    }

    /**
     * @test
     */
    public function it_throws_an_exception_when_campaign_is_triggered_by_wrong_trigger(): void
    {
        $campaignId = new CampaignId('00000000-0000-0000-0000-000000000001');
        $campaignCommand = new UpdateCampaign(
            $campaignId,
            $this->createMock(StoreId::class),
            false,
            ['en' => 'Transaction campaign'],
            Type::DIRECT,
            Trigger::TRANSACTION,
            new \DateTime('2022-01-01'),
            null,
            []
        );
        $campaign = $this->createMock(Campaign::class);

        $this->campaignRepository->expects(self::once())->method('byId')->willReturn($campaign);
        $campaign->expects(self::once())->method('isTriggeredBy')->willReturn(false);
        $this->campaignRepository->expects(self::never())->method('save');
        $this->expectException(CannotChangeCampaignTypeException::class);

        $this->eventBus->expects(self::never())->method('dispatch');

        $commandHandler = $this->commandHandler;
        $commandHandler($campaignCommand);
    }

    /**
     * @test
     */
    public function it_updates_inactive_internal_event_campaign(): void
    {
        $campaignId = new CampaignId('00000000-0000-0000-0000-000000000001');
        $campaignCommand = new UpdateCampaign(
            $campaignId,
            $this->createMock(StoreId::class),
            false,
            ['en' => 'Internal event campaign'],
            Type::DIRECT,
            Trigger::INTERNAL_EVENT,
            new \DateTime('2022-01-01'),
            null,
            []
        );
        $campaignCommand->setEvent(InternalEventSchemaRepository::MEMBER_COMPANY_DETAILS_CHANGED);
        $campaign = $this->createMock(Campaign::class);

        $this->campaignRepository->method('byId')->willReturn($campaign);
        $campaign->method('isTriggeredBy')->willReturn(true);
        $campaign->method('isActive')->willReturn(false);

        $campaign->expects(self::once())->method('assignTranslations');
        $campaign->expects(self::never())->method('activate');
        $campaign->expects(self::never())->method('deactivate');
        $campaign->expects(self::once())->method('changeActivity');
        $campaign->expects(self::once())->method('replaceRules');
        $campaign->expects(self::once())->method('assignEvent');
        $campaign->expects(self::never())->method('assignMemberFilter');
        $this->campaignRepository->expects(self::once())->method('save');

        $this->eventBus->expects(self::once())->method('dispatch')->with(new CampaignWasUpdated(
            $campaign,
            false,
            false
        ));

        $commandHandler = $this->commandHandler;
        $commandHandler($campaignCommand);
    }

    /**
     * @test
     */
    public function it_updates_active_birthday_campaign(): void
    {
        $campaignId = new CampaignId('00000000-0000-0000-0000-000000000001');
        $campaignCommand = new UpdateCampaign(
            $campaignId,
            $this->createMock(StoreId::class),
            true,
            ['en' => 'Birthday campaign'],
            Type::DIRECT,
            Trigger::TIME,
            new \DateTime('2022-01-01'),
            null,
            []
        );
        $campaignCommand->setMemberFilter(MemberFilter::create('birthday'));
        $campaign = $this->createMock(Campaign::class);

        $this->campaignRepository->method('byId')->willReturn($campaign);
        $campaign->method('isTriggeredBy')->willReturn(true);
        $campaign->method('isActive')->willReturn(false);

        $campaign->expects(self::once())->method('assignTranslations');
        $campaign->expects(self::once())->method('activate');
        $campaign->expects(self::never())->method('deactivate');
        $campaign->expects(self::once())->method('changeActivity');
        $campaign->expects(self::once())->method('replaceRules');
        $campaign->expects(self::never())->method('assignEvent');
        $campaign->expects(self::once())->method('assignMemberFilter');
        $this->campaignRepository->expects(self::once())->method('save');

        $this->eventBus->expects(self::once())->method('dispatch')->with(new CampaignWasUpdated(
            $campaign,
            false,
            false
        ));

        $commandHandler = $this->commandHandler;
        $commandHandler($campaignCommand);
    }

    /**
     * @test
     */
    public function it_updates_achievement_campaign(): void
    {
        $campaignId = new CampaignId('00000000-0000-0000-0000-000000000001');
        $achievementId = new AchievementId('00000000-0000-0000-0000-000000000002');
        $campaignCommand = new UpdateCampaign(
            $campaignId,
            $this->createMock(StoreId::class),
            false,
            ['en' => 'Achievement campaign'],
            Type::DIRECT,
            Trigger::ACHIEVEMENT,
            new \DateTime('2022-06-01'),
            null,
            []
        );
        $campaignCommand->setAchievementId($achievementId);

        $campaign = $this->createMock(Campaign::class);

        $this->campaignRepository->method('byId')->willReturn($campaign);
        $campaign->method('isTriggeredBy')->willReturn(true);
        $campaign->method('isActive')->willReturn(false);

        $campaign->expects(self::once())->method('assignTranslations');
        $campaign->expects(self::never())->method('activate');
        $campaign->expects(self::never())->method('deactivate');
        $campaign->expects(self::once())->method('changeActivity');
        $campaign->expects(self::once())->method('replaceRules');
        $campaign->expects(self::never())->method('assignEvent');
        $campaign->expects(self::never())->method('assignMemberFilter');
        $campaign->expects(self::once())->method('assignAchievementId');

        $this->campaignRepository->expects(self::once())->method('save');

        $this->eventBus->expects(self::once())->method('dispatch')->with(new CampaignWasUpdated(
            $campaign,
            false,
            false
        ));

        $commandHandler = $this->commandHandler;
        $commandHandler($campaignCommand);
    }
}
