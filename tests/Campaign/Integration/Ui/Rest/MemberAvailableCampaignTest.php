<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Integration\Ui\Rest;

use OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepository;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Campaign\Integration\Traits\CampaignApiTrait;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use OpenLoyalty\User\Infrastructure\DataFixtures\ORM\LoadUserData;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class MemberAvailableCampaignTest extends AbstractApiTest
{
    use TenantApiTrait;
    use MemberApiTrait;
    use CampaignApiTrait;

    private HttpKernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_test_response_for_logged_member(): void
    {
        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/login_check',
            [
                'username' => LoadUserData::USER2_USERNAME,
                'password' => LoadUserData::USER2_PASSWORD,
            ]
        );

        $data = json_decode($this->client->getResponse()->getContent(), true);
        $token = $data['token'];
        $this->assertTrue(isset($token), 'Response should have field "token". '.$this->client->getResponse()->getContent());

        $this->client->setServerParameter('HTTP_Authorization', sprintf('Bearer %s', $token));
        $this->client->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER2_USER_ID.'/campaign');

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $campaigns = $data['items'];
        $campaign = $campaigns[0];

        $this->assertArrayHasKey('campaignId', $campaign);
        $this->assertArrayHasKey('translations', $campaign);
        $this->assertArrayHasKey('limitReached', $campaign);
        $this->assertArrayHasKey('name', $campaign);
        $this->assertArrayHasKey('description', $campaign);
        $this->assertArrayNotHasKey('type', $campaign);
        $this->assertArrayNotHasKey('trigger', $campaign);
        $this->assertArrayNotHasKey('activity', $campaign);
        $this->assertArrayNotHasKey('active', $campaign);
        $this->assertArrayNotHasKey('createdAt', $campaign);
        $this->assertArrayNotHasKey('memberFilter', $campaign);
        $this->assertArrayNotHasKey('limits', $campaign);
        $this->assertArrayNotHasKey('rules', $campaign);
        $this->assertIsString($campaign['campaignId']);
        $this->assertIsArray($campaign['translations']);
        $this->assertIsBool($campaign['limitReached']);
        $this->assertIsString($campaign['name']);
        $this->assertIsString($campaign['description']);
    }

    /**
     * @test
     */
    public function it_filters_available_campaign_by_labels(): void
    {
        $storeCode = 'tenant1';
        $this->createTenant($storeCode);

        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'firstName' => 'Kelly',
                    'lastName' => 'Jonns',
                    'agreement1' => true,
                    'agreement2' => true,
                    'agreement3' => true,
                    'labels' => [],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $memberId = $data['customerId'];

        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Campaign name',
                        ],
                    ],
                    'labels' => [
                        ['key' => 'k1', 'value' => 'v1'],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => 'transaction',
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'rules' => [
                        [
                            'effects' => [
                                [
                                    'effect' => 'give_points',
                                    'pointsRule' => 15,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId1 = $data['campaignId'];

        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Campaign name',
                        ],
                    ],
                    'labels' => [
                        ['key' => 'k2', 'value' => 'v2'],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => 'transaction',
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'rules' => [
                        [
                            'effects' => [
                                [
                                    'effect' => 'give_points',
                                    'pointsRule' => 15,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId2 = $data['campaignId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/member/'.$memberId.'/campaign?labels=(k2;v2)');

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertCount(1, $data['items']);
        $campaign = $data['items'][0];
        $this->assertSame($campaignId2, $campaign['campaignId']);
    }

    /**
     * @test
     */
    public function it_test_campaign_availability_with_executions_per_member_limit(): void
    {
        $campaignData = [
            'campaign' => [
                'translations' => [
                    'en' => [
                        'name' => 'Executions per member limits campaign',
                    ],
                ],
                'active' => true,
                'type' => 'direct',
                'trigger' => 'internal_event',
                'event' => InternalEventSchemaRepository::MEMBER_WAS_ACTIVATED,
                'activity' => [
                    'startsAt' => '2020-01-01 00:00:00',
                ],
                'rules' => [
                    [
                        'effects' => [
                            [
                                'effect' => 'give_points',
                                'pointsRule' => '1',
                            ],
                        ],
                    ],
                ],
                'limits' => [
                    'executionsPerMember' => [
                        'value' => 1,
                        'interval' => [
                            'type' => 'calendarWeeks',
                            'value' => 2,
                        ],
                    ],
                ],
            ],
        ];

        $this->client->jsonRequest('POST', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign', $campaignData);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        $customerId = $this->createCustomer(
            '<EMAIL>',
            'Archer',
            'Cooke'
        );
        $availableCampaigns = $this->countAvailableCampaigns($customerId);

        $this->checkCampaignAvailability($customerId, $campaignId, false);
        $this->activateCustomer($customerId);

        $this->checkCampaignAvailability($customerId, $campaignId, true);
        $this->assertSame($availableCampaigns - 1, $this->countAvailableCampaigns($customerId));

        $this->deactivateCampaign($campaignData, $campaignId);
    }

    /**
     * @test
     */
    public function it_test_campaign_availability_with_points_per_member_limit(): void
    {
        $campaignData = [
            'campaign' => [
                'translations' => [
                    'en' => [
                        'name' => 'Points per member limits campaign',
                    ],
                ],
                'active' => true,
                'type' => 'direct',
                'trigger' => 'transaction',
                'activity' => [
                    'startsAt' => '2020-01-01 00:00:00',
                ],
                'rules' => [
                    [
                        'effects' => [
                            [
                                'effect' => 'give_points',
                                'pointsRule' => '10',
                            ],
                        ],
                    ],
                ],
                'limits' => [
                    'pointsPerMember' => [
                        'value' => 20.0,
                    ],
                ],
            ],
        ];

        $this->client->jsonRequest('POST', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign', $campaignData);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        $customerId = $this->createCustomer(
            '<EMAIL>',
            'Rocco',
            'Holmes'
        );

        $this->activateCustomer($customerId);

        $availableCampaigns = $this->countAvailableCampaigns($customerId);
        $this->checkCampaignAvailability($customerId, $campaignId, false);

        $this->addTransaction(
            'document1',
            '2022-04-20 08:00:00',
            'Holmes',
            '<EMAIL>'
        );

        $this->checkCampaignAvailability($customerId, $campaignId, false);
        $this->assertSame($availableCampaigns, $this->countAvailableCampaigns($customerId));

        $this->addTransaction(
            'document2',
            '2022-04-21 08:00:00',
            'Holmes',
            '<EMAIL>'
        );

        $this->checkCampaignAvailability($customerId, $campaignId, true);
        $this->assertSame($availableCampaigns - 1, $this->countAvailableCampaigns($customerId));

        $this->deactivateCampaign($campaignData, $campaignId);
    }

    /**
     * @test
     */
    public function it_test_campaign_availability_with_units_limit(): void
    {
        $tenantCode = 'units_limit_campaign_tenant';

        $this->createTenant($tenantCode);

        $campaignData = [
            'campaign' => [
                'translations' => [
                    'en' => [
                        'name' => 'Units limit campaign',
                    ],
                ],
                'active' => true,
                'type' => 'direct',
                'trigger' => 'transaction',
                'activity' => [
                    'startsAt' => '2020-01-01 00:00:00',
                ],
                'rules' => [
                    [
                        'effects' => [
                            [
                                'effect' => 'give_points',
                                'pointsRule' => '10',
                            ],
                        ],
                    ],
                ],
                'limits' => [
                    'points' => [
                        'value' => 20.0,
                    ],
                ],
            ],
        ];

        $this->client->jsonRequest('POST', '/api/'.$tenantCode.'/campaign', $campaignData);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $customerId = $this->createCustomer(
            '<EMAIL>',
            'Rocco',
            'Holmes',
            $tenantCode
        );

        $this->activateCustomer($customerId, $tenantCode);

        $this->addTransaction(
            'document1',
            '2022-04-20 08:00:00',
            'Holmes',
            '<EMAIL>',
            $tenantCode
        );

        $this->client->request('GET', '/api/'.$tenantCode.'/member/'.$customerId.'/campaign');

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        self::assertSame(
            [
                'points' => [
                    'currentValue' => 10.0,
                    'limitValue' => 20.0,
                    'remaining' => 10.0,
                    'interval' => [],
                ],
            ],
            $data['items'][0]['limitUsages']
        );
    }

    /**
     * @test
     */
    public function it_test_campaign_availability_for_member_by_email(): void
    {
        $tenantCode = 'campaign_tenant_availability_for_member_by_email';
        $this->postTenant($this->client, $tenantCode);

        $response = $this->postCampaign(
            $this->client,
            $tenantCode,
            activity: [
                'startsAt' => '2020-01-01 00:00:00',
            ],
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '10',
                        ],
                    ],
                ],
            ],
            limits: [
                'points' => [
                    'value' => 20.0,
                ],
            ]
        );
        $this->assertOkResponseStatus($response);

        $response = $this->postMember(
            $this->client,
            $tenantCode,
            '<EMAIL>',
            'Rocco',
            'Holmes',
        );
        $this->assertOkResponseStatus($response);

        $this->client->request('GET', '/api/'.$tenantCode.'/member/email=<EMAIL>/campaign');

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        self::assertSame(
            [
                'points' => [
                    'currentValue' => 0.0,
                    'limitValue' => 20.0,
                    'remaining' => 20.0,
                    'interval' => [],
                ],
            ],
            $data['items'][0]['limitUsages']
        );
    }

    /**
     * @test
     */
    public function it_use_effect_with_no_points_when_one_of_effects_is_dynamic(): void
    {
        $campaignData = [
            'campaign' => [
                'translations' => [
                    'en' => [
                        'name' => 'Points per member limits campaign',
                    ],
                ],
                'active' => true,
                'type' => 'direct',
                'trigger' => 'transaction',
                'activity' => [
                    'startsAt' => '2020-01-01 00:00:00',
                ],
                'rules' => [
                    [
                        'effects' => [
                            [
                                'effect' => 'give_points',
                                'pointsRule' => 'transaction.grossValue + 30',
                            ],
                        ],
                    ],
                    [
                        'effects' => [
                            [
                                'effect' => 'give_points',
                                'pointsRule' => '30',
                            ],
                        ],
                    ],
                ],
                'limits' => [
                    'pointsPerMember' => [
                        'value' => 20.0,
                    ],
                ],
            ],
        ];

        $this->client->jsonRequest('POST', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign', $campaignData);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        $customerId = $this->createCustomer(
            '<EMAIL>',
            'Barnaby',
            'Cole'
        );

        $this->activateCustomer($customerId);

        $availableCampaigns = $this->countAvailableCampaigns($customerId);
        $this->checkCampaignAvailability($customerId, $campaignId, false);

        $this->addTransaction(
            'document22',
            '2022-04-20 08:00:00',
            'Barnaby Cole',
            '<EMAIL>'
        );

        $this->checkCampaignAvailability($customerId, $campaignId, false);
        $this->assertSame($availableCampaigns, $this->countAvailableCampaigns($customerId));

        $this->deactivateCampaign($campaignData, $campaignId);
    }

    /**
     * @test
     */
    public function it_test_campaign_is_not_available_when_used_points_with_effect_is_grater_then_limit(): void
    {
        $campaignData = [
            'campaign' => [
                'translations' => [
                    'en' => [
                        'name' => 'Points limits campaign',
                    ],
                ],
                'active' => true,
                'type' => 'direct',
                'trigger' => 'transaction',
                'activity' => [
                    'startsAt' => '2020-01-01 00:00:00',
                ],
                'rules' => [
                    [
                        'effects' => [
                            [
                                'effect' => 'give_points',
                                'pointsRule' => '15',
                            ],
                        ],
                    ],
                ],
                'limits' => [
                    'points' => [
                        'value' => 20.0,
                    ],
                ],
            ],
        ];

        $this->client->jsonRequest('POST', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign', $campaignData);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        $customerId = $this->createCustomer(
            '<EMAIL>',
            'Clark',
            'Hawkins'
        );

        $this->activateCustomer($customerId);

        $this->checkCampaignAvailability($customerId, $campaignId, false);
        $availableCampaigns = $this->countAvailableCampaigns($customerId);

        $this->addTransaction(
            'document3',
            '2022-04-21 08:00:00',
            'Clark Hawkins',
            '<EMAIL>'
        );

        $this->checkCampaignAvailability($customerId, $campaignId, true);
        $this->assertSame($availableCampaigns - 1, $this->countAvailableCampaigns($customerId));

        $this->deactivateCampaign($campaignData, $campaignId);
    }

    /**
     * @test
     */
    public function it_test_campaign_with_dynamic_effects_is_available_when_used_points_is_equal_to_limit(): void
    {
        $campaignData = [
            'campaign' => [
                'translations' => [
                    'en' => [
                        'name' => 'Points limits campaign',
                    ],
                ],
                'active' => true,
                'type' => 'direct',
                'trigger' => 'transaction',
                'activity' => [
                    'startsAt' => '2020-01-01 00:00:00',
                ],
                'rules' => [
                    [
                        'effects' => [
                            [
                                'effect' => 'give_points',
                                'pointsRule' => 'transaction.grossValue',
                            ],
                        ],
                    ],
                ],
                'limits' => [
                    'points' => [
                        'value' => 20.0,
                    ],
                ],
            ],
        ];

        $this->client->jsonRequest('POST', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign', $campaignData);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        $customerId = $this->createCustomer(
            '<EMAIL>',
            'Mateo',
            'Matthews'
        );

        $this->activateCustomer($customerId);
        $availableCampaigns = $this->countAvailableCampaigns($customerId);
        $this->checkCampaignAvailability($customerId, $campaignId, false);

        $this->addTransaction(
            'document4',
            '2022-04-21 08:00:00',
            'Mateo Matthews',
            '<EMAIL>'
        );

        $this->checkCampaignAvailability($customerId, $campaignId, false);
        $this->assertSame($availableCampaigns, $this->countAvailableCampaigns($customerId));
        $this->addTransaction(
            'document5',
            '2022-04-21 08:00:00',
            'Mateo Matthews',
            '<EMAIL>'
        );

        $this->checkCampaignAvailability($customerId, $campaignId, false);
        $this->assertSame($availableCampaigns, $this->countAvailableCampaigns($customerId));

        $this->deactivateCampaign($campaignData, $campaignId);
    }

    protected function checkCampaignAvailability(string $memberId, string $campaignId, bool $expectedLimitReached): void
    {
        self::ensureKernelShutdown();
        $this->client->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$memberId.'/campaign?_itemsOnPage=50');

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaigns = $data['items'];
        $isAvailable = false;

        foreach ($campaigns as $campaign) {
            if ($campaign['campaignId'] === $campaignId && true === $campaign['limitReached']) {
                $isAvailable = true;
            }
        }

        $this->assertSame($expectedLimitReached, $isAvailable);
    }

    protected function countAvailableCampaigns(string $memberId): int
    {
        self::ensureKernelShutdown();
        $this->client->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$memberId.'/campaign?_itemsOnPage=50');
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaigns = $data['items'];
        $numberOfAvailableCampaigns = 0;
        foreach ($campaigns as $campaign) {
            if (false === $campaign['limitReached']) {
                ++$numberOfAvailableCampaigns;
            }
        }

        return $numberOfAvailableCampaigns;
    }

    protected function createCustomer(
        string $email,
        string $firstName,
        string $lastName,
        string $tenantCode = LoadSettingsData::DEFAULT_STORE_CODE
    ): string {
        self::ensureKernelShutdown();

        $this->client->request(
            'POST',
            '/api/'.$tenantCode.'/member/register',
            [
                'customer' => [
                    'email' => $email,
                    'firstName' => $firstName,
                    'lastName' => $lastName,
                    'plainPassword' => 'someSecretPass1#',
                    'agreement1' => true,
                    'agreement2' => true,
                    'agreement3' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        return $data['customerId'];
    }

    protected function activateCustomer(
        string $customerId,
        string $tenantCode = LoadSettingsData::DEFAULT_STORE_CODE
    ): void {
        self::ensureKernelShutdown();

        $this->client->request(
            'POST',
            '/api/'.$tenantCode.'/member/'.$customerId.'/activate'
        );

        $this->assertNoContentResponseStatus($this->client->getResponse());
    }

    protected function deactivateCampaign(array $campaignData, string $campaignId): void
    {
        self::ensureKernelShutdown();
        $campaignData['campaign']['active'] = false;
        $this->client->jsonRequest(
            'PUT',
            sprintf('/api/%s/campaign/%s', LoadSettingsData::DEFAULT_STORE_CODE, $campaignId),
            $campaignData
        );
        $this->assertNoContentResponseStatus($this->client->getResponse());
    }

    protected function addTransaction(
        string $documentNumber,
        string $purchasedAt,
        string $name,
        string $email,
        string $tenantCode = LoadSettingsData::DEFAULT_STORE_CODE
    ): void {
        self::ensureKernelShutdown();

        $this->client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => $documentNumber,
                        'documentType' => 'sell',
                        'purchasedAt' => $purchasedAt,
                        'purchasePlace' => 'Warsaw',
                    ],
                    'items' => [
                        0 => [
                            'sku' => '123',
                            'name' => 'SKU123',
                            'quantity' => 1,
                            'grossValue' => 10,
                            'category' => 'test',
                            'maker' => 'company',
                        ],
                    ],
                    'customerData' => [
                        'name' => $name,
                        'email' => $email,
                    ],
                ],
            ]
        );

        $this->assertOkResponseStatus($this->client->getResponse());
    }

    /**
     * @test
     */
    public function it_test_campaign_available_with_show_all_parameter(): void
    {
        $activeCampaignData = [
            'campaign' => [
                'translations' => [
                    'en' => [
                        'name' => 'active campaign',
                    ],
                ],
                'active' => true,
                'type' => 'direct',
                'trigger' => 'internal_event',
                'event' => InternalEventSchemaRepository::MEMBER_WAS_ACTIVATED,
                'activity' => [
                    'startsAt' => '2020-01-01 00:00:00',
                ],
                'rules' => [
                    [
                        'effects' => [
                            [
                                'effect' => 'give_points',
                                'pointsRule' => '1',
                            ],
                        ],
                    ],
                ],
                'limits' => [
                    'executionsPerMember' => [
                        'value' => 1,
                        'interval' => [
                            'type' => 'calendarWeeks',
                            'value' => 2,
                        ],
                    ],
                ],
            ],
        ];

        $notActiveCampaignData = [
            'campaign' => [
                'translations' => [
                    'en' => [
                        'name' => 'not active campaign',
                    ],
                ],
                'active' => false,
                'type' => 'direct',
                'trigger' => 'internal_event',
                'event' => InternalEventSchemaRepository::MEMBER_WAS_ACTIVATED,
                'activity' => [
                    'startsAt' => '2020-01-01 00:00:00',
                ],
                'rules' => [
                    [
                        'effects' => [
                            [
                                'effect' => 'give_points',
                                'pointsRule' => '1',
                            ],
                        ],
                    ],
                ],
                'limits' => [
                    'executionsPerMember' => [
                        'value' => 1,
                        'interval' => [
                            'type' => 'calendarWeeks',
                            'value' => 2,
                        ],
                    ],
                ],
            ],
        ];

        $this->client->jsonRequest('POST', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign', $activeCampaignData);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $this->client->jsonRequest('POST', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign', $notActiveCampaignData);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $customerId = $this->createCustomer(
            '<EMAIL>',
            'Archer',
            'Cooke'
        );

        $this->client->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId.'/campaign?_itemsOnPage=50');
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaigns = $data['items'];

        $activeCampaigns = count($campaigns);

        //use parameter showAll
        $this->client->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId.'/campaign?_itemsOnPage=50&showAll=true');
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $allCampaigns = $data['items'];

        self::assertSame($activeCampaigns + 1, count($allCampaigns));
    }
}
