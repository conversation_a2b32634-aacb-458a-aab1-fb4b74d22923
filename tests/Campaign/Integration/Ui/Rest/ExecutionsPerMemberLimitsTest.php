<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Integration\Ui\Rest;

use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;

final class ExecutionsPerMemberLimitsTest extends AbstractLimitsTest
{
    /**
     * @test
     */
    public function it_test_executions_per_member_limit_for_direct_transaction_campaign(): void
    {
        $campaignData = [
            'campaign' => [
                'translations' => [
                    'en' => [
                        'name' => 'Direct transaction executions per member limits campaign',
                    ],
                ],
                'active' => true,
                'type' => 'direct',
                'trigger' => 'transaction',
                'activity' => [
                    'startsAt' => '2020-01-01 00:00:00',
                ],
                'rules' => [
                    [
                        'effects' => [
                            [
                                'effect' => 'give_points',
                                'pointsRule' => '3',
                            ],
                        ],
                    ],
                ],
                'limits' => [
                    'executionsPerMember' => [
                        'value' => 2,
                        'interval' => [
                            'type' => 'calendarWeeks',
                            'value' => 2,
                        ],
                    ],
                ],
            ],
        ];

        $client = self::createAuthenticatedClient();

        $client->jsonRequest('POST', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign', $campaignData);
        $this->assertOkResponseStatus($client->getResponse());

        $customerId1 = $this->createCustomer(
            '<EMAIL>',
            'Jean',
            'Valley'
        );
        $this->activateCustomer($customerId1);

        $this->checkCustomerStatus(
            $customerId1,
            [
                'activePoints' => 0.0,
                'earnedPoints' => 0.0,
            ]
        );

        $customerId2 = $this->createCustomer(
            '<EMAIL>',
            'Hiro',
            'Okumura'
        );
        $this->activateCustomer($customerId2);

        $this->checkCustomerStatus(
            $customerId2,
            [
                'activePoints' => 0.0,
                'earnedPoints' => 0.0,
            ]
        );

        $this->addTransaction(
            'DTEPMLC/000/000001',
            '2021-03-20 08:00:00',
            'Jean Valley',
            '<EMAIL>'
        );

        $this->checkCustomerStatus(
            $customerId1,
            [
                'activePoints' => 3.0,
                'earnedPoints' => 3.0,
            ]
        );

        $this->addTransaction(
            'DTEPMLC/000/000002',
            '2021-03-22 08:00:00',
            'Jean Valley',
            '<EMAIL>'
        );

        $this->checkCustomerStatus(
            $customerId1,
            [
                'activePoints' => 6.0,
                'earnedPoints' => 6.0,
            ]
        );

        $this->addTransaction(
            'DTEPMLC/000/000003',
            '2021-03-25 08:00:00',
            'Jean Valley',
            '<EMAIL>'
        );

        $this->checkCustomerStatus(
            $customerId1,
            [
                'activePoints' => 6.0,
                'earnedPoints' => 6.0,
            ]
        );

        $this->addTransaction(
            'DTEPMLC/000/000004',
            '2021-03-25 08:00:00',
            'Hiro Okumura',
            '<EMAIL>',
        );

        $this->checkCustomerStatus(
            $customerId2,
            [
                'activePoints' => 3.0,
                'earnedPoints' => 3.0,
            ]
        );

        $this->addTransaction(
            'DTEPMLC/000/000005',
            '2021-03-29 08:00:00',
            'Jean Valley',
            '<EMAIL>'
        );

        $this->checkCustomerStatus(
            $customerId1,
            [
                'activePoints' => 9.0,
                'earnedPoints' => 9.0,
            ]
        );
    }
}
