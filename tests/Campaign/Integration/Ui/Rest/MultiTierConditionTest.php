<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Integration\Ui\Rest;

use OpenLoyalty\Points\Domain\Expiring\ExpiringTransferMode;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Integration\Traits\TierSetApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class MultiTierConditionTest extends AbstractApiTest
{
    use TierSetApiTrait;

    protected HttpKernelBrowser $client;

    private int $defaultTierSetLimit;
    private int $defaultActiveTierSetLimit;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();

        $this->defaultTierSetLimit = (int) $_ENV['ACTIVE_TIER_SET_LIMIT'];
        $this->defaultActiveTierSetLimit = (int) $_ENV['TOTAL_TIER_SET_LIMIT'];
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $_ENV['ACTIVE_TIER_SET_LIMIT'] = $this->defaultActiveTierSetLimit;
        $_ENV['TOTAL_TIER_SET_LIMIT'] = $this->defaultTierSetLimit;
    }

    /**
     * @test
     */
    public function it_check_multi_tiers_from_member_correctly_in_conditions(): void
    {
        $_ENV['TOTAL_TIER_SET_LIMIT'] = 2;
        $_ENV['ACTIVE_TIER_SET_LIMIT'] = 2;

        $storeCode = 'sendCodeTenant';
        $this->createTenant($storeCode);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/walletType'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $walletType = $data['items'][0]['walletTypeId'];

        $this->client->jsonRequest(
            'PUT',
            '/api/'.$storeCode.'/walletType/'.$walletType,
            [
                'walletType' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Default Stars',
                            'description' => 'Default Stars',
                        ],
                    ],
                    'unitSingularName' => 'Point',
                    'unitPluralName' => 'Points',
                    'active' => true,
                    'unitDaysExpiryAfter' => ExpiringTransferMode::EXPIRING_AFTER_X_DAYS,
                    'unitDaysActiveCount' => 30,
                    'allTimeNotLocked' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $response = $this->postTierSet(
            $this->client,
            $storeCode,
            'first tier set',
        );
        $data = json_decode($response->getContent(), true);
        $firstTierSetId = $data['tierSetId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$firstTierSetId
        );

        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);
        $conditionId = $data['conditions'][0]['id'];

        $response = $this->putTiersToTierSet(
            $this->client,
            $storeCode,
            $firstTierSetId,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'Bronze',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 100,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'Silver',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
            ]
        );

        $this->assertNoContentResponseStatus($response);

        $response = $this->postTierSet(
            $this->client,
            $storeCode,
            'second tier set',
            conditions: [['attribute' => 'totalEarnedUnits', 'walletType' => 'default']]
        );

        $data = json_decode($response->getContent(), true);
        $secondTierSetId = $data['tierSetId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$secondTierSetId
        );

        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);
        $conditionId = $data['conditions'][0]['id'];

        $response = $this->putTiersToTierSet(
            $this->client,
            $storeCode,
            $secondTierSetId,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'Normal',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 100,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'Advanced',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
            ]
        );

        $this->assertNoContentResponseStatus($response);

        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/member',
            [
                'customer' => [
                    'firstName' => 'Jonny',
                    'lastName' => 'Wall',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'birthDate' => '1991-01-01',
                    'agreement1' => true,
                    'registeredAt' => '2024-02-03T14:15:22Z',
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $customerId = $data['customerId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$firstTierSetId.'/tiers?name=Bronze'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $bronzeTierId = $data['items'][0]['levelId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$secondTierSetId.'/tiers?name=Normal'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $normalTierId = $data['items'][0]['levelId'];

        //created 2 campaign with different tiers as condition
        $this->createCampaign($storeCode, 'contains_one_of', [$bronzeTierId]);
        $this->createCampaign($storeCode, 'contains_one_of', [$normalTierId]);

        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/points/add',
            [
                'transfer' => [
                    'customer' => $customerId,
                    'points' => 100,
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $activeUnits = $this->getCustomerActivePoints($customerId, $storeCode);
        $this->assertSame(100.0, $activeUnits);

        //trigger campaign
        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => 'unique-number-1',
                        'documentType' => 'sell',
                        'purchasedAt' => (new \DateTimeImmutable())->format('Y-m-d H:i:s'),
                        'purchasePlace' => 'Warsaw',
                    ],
                    'items' => [
                        0 => [
                            'sku' => '123',
                            'name' => 'SKU123',
                            'quantity' => 1,
                            'grossValue' => 100,
                            'category' => 'test',
                            'maker' => 'company',
                        ],
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $activeUnits = $this->getCustomerActivePoints($customerId, $storeCode);
        $this->assertSame(120.0, $activeUnits);
    }

    /**
     * @test
     */
    public function it_check_multi_tiers_condition(): void
    {
        $_ENV['TOTAL_TIER_SET_LIMIT'] = 2;
        $_ENV['ACTIVE_TIER_SET_LIMIT'] = 2;

        $storeCode = 'sendCodeTenant';
        $this->createTenant($storeCode);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/walletType'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $walletType = $data['items'][0]['walletTypeId'];

        $this->client->jsonRequest(
            'PUT',
            '/api/'.$storeCode.'/walletType/'.$walletType,
            [
                'walletType' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Default Stars',
                            'description' => 'Default Stars',
                        ],
                    ],
                    'unitSingularName' => 'Point',
                    'unitPluralName' => 'Points',
                    'active' => true,
                    'unitDaysExpiryAfter' => ExpiringTransferMode::EXPIRING_AFTER_X_DAYS,
                    'unitDaysActiveCount' => 30,
                    'allTimeNotLocked' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $response = $this->postTierSet(
            $this->client,
            $storeCode,
            'first tier set',
        );
        $data = json_decode($response->getContent(), true);
        $firstTierSetId = $data['tierSetId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$firstTierSetId
        );

        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);
        $conditionId = $data['conditions'][0]['id'];

        $response = $this->putTiersToTierSet(
            $this->client,
            $storeCode,
            $firstTierSetId,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'Bronze',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 100,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'Silver',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
            ]
        );

        $this->assertNoContentResponseStatus($response);

        $response = $this->postTierSet(
            $this->client,
            $storeCode,
            'second tier set',
            conditions: [['attribute' => 'totalEarnedUnits', 'walletType' => 'default']]
        );

        $data = json_decode($response->getContent(), true);
        $secondTierSetId = $data['tierSetId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$secondTierSetId
        );

        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);
        $conditionId = $data['conditions'][0]['id'];

        $response = $this->putTiersToTierSet(
            $this->client,
            $storeCode,
            $secondTierSetId,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'Normal',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'Advanced',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 300,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
            ]
        );

        $this->assertNoContentResponseStatus($response);

        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/member',
            [
                'customer' => [
                    'firstName' => 'Jonny',
                    'lastName' => 'Wall',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'birthDate' => '1991-01-01',
                    'agreement1' => true,
                    'registeredAt' => '2024-02-03T14:15:22Z',
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $customerId = $data['customerId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$firstTierSetId.'/tiers?name=Bronze'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $bronzeTierId = $data['items'][0]['levelId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$secondTierSetId.'/tiers?name=Normal'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $normalTierId = $data['items'][0]['levelId'];

        $this->createCampaign($storeCode, 'contains_one_of', [$bronzeTierId, $normalTierId]);

        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/points/add',
            [
                'transfer' => [
                    'customer' => $customerId,
                    'points' => 100,
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $activeUnits = $this->getCustomerActivePoints($customerId, $storeCode);
        $this->assertSame(100.0, $activeUnits);

        //trigger campaign
        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => 'unique-number-1',
                        'documentType' => 'sell',
                        'purchasedAt' => (new \DateTimeImmutable())->format('Y-m-d H:i:s'),
                        'purchasePlace' => 'Warsaw',
                    ],
                    'items' => [
                        0 => [
                            'sku' => '123',
                            'name' => 'SKU123',
                            'quantity' => 1,
                            'grossValue' => 100,
                            'category' => 'test',
                            'maker' => 'company',
                        ],
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $activeUnits = $this->getCustomerActivePoints($customerId, $storeCode);
        $this->assertSame(110.0, $activeUnits);
    }

    /**
     * @test
     */
    public function it_does_not_give_units_if_one_tier_is_in_customer_tiers_for_not_contains_condition(): void
    {
        $_ENV['TOTAL_TIER_SET_LIMIT'] = 2;
        $_ENV['ACTIVE_TIER_SET_LIMIT'] = 2;

        $storeCode = 'sendCodeTenant';
        $this->createTenant($storeCode);

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/walletType'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $walletType = $data['items'][0]['walletTypeId'];

        $this->client->jsonRequest(
            'PUT',
            '/api/'.$storeCode.'/walletType/'.$walletType,
            [
                'walletType' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Default Stars',
                            'description' => 'Default Stars',
                        ],
                    ],
                    'unitSingularName' => 'Point',
                    'unitPluralName' => 'Points',
                    'active' => true,
                    'unitDaysExpiryAfter' => ExpiringTransferMode::EXPIRING_AFTER_X_DAYS,
                    'unitDaysActiveCount' => 30,
                    'allTimeNotLocked' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $response = $this->postTierSet(
            $this->client,
            $storeCode,
            'first tier set',
        );
        $data = json_decode($response->getContent(), true);
        $firstTierSetId = $data['tierSetId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$firstTierSetId
        );

        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);
        $conditionId = $data['conditions'][0]['id'];

        $response = $this->putTiersToTierSet(
            $this->client,
            $storeCode,
            $firstTierSetId,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'Bronze',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 100,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'Silver',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
            ]
        );

        $this->assertNoContentResponseStatus($response);

        $response = $this->postTierSet(
            $this->client,
            $storeCode,
            'second tier set',
            conditions: [['attribute' => 'totalEarnedUnits', 'walletType' => 'default']]
        );

        $data = json_decode($response->getContent(), true);
        $secondTierSetId = $data['tierSetId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$secondTierSetId
        );

        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);
        $conditionId = $data['conditions'][0]['id'];

        $response = $this->putTiersToTierSet(
            $this->client,
            $storeCode,
            $secondTierSetId,
            [
                [
                    'translations' => [
                        'en' => [
                            'name' => 'Normal',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 200,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
                [
                    'translations' => [
                        'en' => [
                            'name' => 'Advanced',
                            'description' => '',
                        ],
                    ],
                    'active' => true,
                    'rewards' => [
                    ],
                    'conditions' => [
                        [
                            'value' => 300,
                            'conditionId' => $conditionId,
                        ],
                    ],
                ],
            ]
        );

        $this->assertNoContentResponseStatus($response);

        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/member',
            [
                'customer' => [
                    'firstName' => 'Jonny',
                    'lastName' => 'Wall',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'birthDate' => '1991-01-01',
                    'agreement1' => true,
                    'registeredAt' => '2024-02-03T14:15:22Z',
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $customerId = $data['customerId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$firstTierSetId.'/tiers?name=Bronze'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $bronzeTierId = $data['items'][0]['levelId'];

        $this->client->request(
            'GET',
            '/api/'.$storeCode.'/tierSet/'.$secondTierSetId.'/tiers?name=Normal'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $normalTierId = $data['items'][0]['levelId'];

        $this->postMemberToTier($this->client, $storeCode, $customerId, $normalTierId);

        $this->createCampaign($storeCode, 'not_contains_one_of', [$bronzeTierId, $normalTierId]);

        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/points/add',
            [
                'transfer' => [
                    'customer' => $customerId,
                    'points' => 300,
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $activeUnits = $this->getCustomerActivePoints($customerId, $storeCode);
        $this->assertSame(300.0, $activeUnits);

        //trigger campaign
        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/transaction',
            [
                'transaction' => [
                    'header' => [
                        'documentNumber' => 'unique-number-1',
                        'documentType' => 'sell',
                        'purchasedAt' => (new \DateTimeImmutable())->format('Y-m-d H:i:s'),
                        'purchasePlace' => 'Warsaw',
                    ],
                    'items' => [
                        0 => [
                            'sku' => '123',
                            'name' => 'SKU123',
                            'quantity' => 1,
                            'grossValue' => 100,
                            'category' => 'test',
                            'maker' => 'company',
                        ],
                    ],
                    'customerData' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $activeUnits = $this->getCustomerActivePoints($customerId, $storeCode);
        $this->assertSame(310.0, $activeUnits);
    }

    private function createCampaign(string $storeCode, string $operator, array $tiers): string
    {
        $this->client->jsonRequest(
            'POST',
            '/api/'.$storeCode.'/campaign',
            [
                'campaign' => [
                    'activity' => [
                        'startsAt' => '2022-09-01 00:00',
                    ],
                    'rules' => [
                        [
                            'conditions' => [
                                [
                                    'operator' => $operator,
                                    'data' => $tiers,
                                    'attribute' => 'customer.tiers',
                                ],
                            ],
                            'effects' => [
                                [
                                    'effect' => 'give_points',
                                    'pointsRule' => '10',
                                ],
                            ],
                        ],
                    ],
                    'translations' => [
                        'en' => [
                            'name' => 'Campaign for first Tier set',
                            'description' => 'Campaign for first Tier set',
                        ],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => 'transaction',
                ],
            ]
        );
        $response = $this->client->getResponse();
        $campaignData = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('campaignId', $campaignData);

        return $campaignData['campaignId'];
    }
}
