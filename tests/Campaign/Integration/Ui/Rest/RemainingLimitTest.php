<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace Campaign\Integration\Ui\Rest;

use Carbon\Carbon;
use OpenLoyalty\Test\Campaign\Integration\Traits\CampaignApiTrait;
use OpenLoyalty\Test\Campaign\Integration\Ui\Rest\AbstractLimitsTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\Transaction\Integration\Traits\TransactionApiTrait;
use OpenLoyalty\Test\UnitTransfer\Integration\Traits\UnitTransferApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;

final class RemainingLimitTest extends AbstractLimitsTest
{
    use CampaignApiTrait;
    use MemberApiTrait;
    use TenantApiTrait;
    use TransactionApiTrait;
    use UnitTransferApiTrait;

    /**
     * @test
     */
    public function it_adding_remaining_units_to_the_limit_that_left(): void
    {
        $client = self::createAuthenticatedClient();

        $tenantCode = 'code';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '66',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                    ],
                ],
            ],
            limits: [
                'pointsPerMember' => [
                    'value' => 100,
                ],
            ]
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('campaignId', $data);
        $campaignId = $data['campaignId'];

        $memberEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberEmail);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC123',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(66.0, $activeUnits);

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC125',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(100.0, $activeUnits);
    }

    /**
     * @test
     */
    public function it_adding_remaining_units_to_the_limit_that_left_to_referral_user(): void
    {
        $client = self::createAuthenticatedClient();

        $tenantCode = 'code';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            type: 'referral',
            rules: [
                [
                    'target' => 'referrer',
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '66',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                    ],
                ],
                [
                    'target' => 'self',
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '66',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                    ],
                ],
            ],
            limits: [
                'pointsPerMember' => [
                    'value' => 100,
                ],
            ]
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('campaignId', $data);

        $memberAEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberAEmail);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerIdA = $data['customerId'];

        $client->request(
            'GET',
            '/api/'.$tenantCode.'/member/'.$customerIdA
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $memberAReferralToken = $data['referralToken'];

        $memberBEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberBEmail, referrerToken: $memberAReferralToken);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerIdB = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC1',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberBEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerIdB, $tenantCode);
        $this->assertSame(66.0, $activeUnits);

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC125',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberBEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerIdB, $tenantCode);
        $this->assertSame(100.0, $activeUnits);

        $activeUnits = $this->getCustomerActivePoints($customerIdA, $tenantCode);
        $this->assertSame(100.0, $activeUnits);
    }

    /**
     * @test
     */
    public function it_adds_remaining_units_for_more_then_one_effect(): void
    {
        $client = self::createAuthenticatedClient();

        $tenantCode = 'code';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '100',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '100',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                    ],
                ],
            ],
            limits: [
                'points' => [
                    'value' => 100,
                ],
                'pointsPerMember' => [
                    'value' => 80,
                ],
            ]
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        $memberEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberEmail);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC123',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(80.0, $activeUnits);

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC125',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(80.0, $activeUnits);
    }

    /**
     * @test
     */
    public function it_check_if_deduct_units_work_with_limits(): void
    {
        $client = self::createAuthenticatedClient();

        $tenantCode = 'code';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'deduct_unit',
                            'pointsRule' => '100',
                        ],
                    ],
                ],
            ],
            limits: [
                'points' => [
                    'value' => 50,
                ],
                'pointsPerMember' => [
                    'value' => 20,
                ],
            ]
        );

        $this->assertOkResponseStatus($response);

        $memberEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberEmail);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $response = $this->postUnitTransferAdd($client, $tenantCode, $customerId, 150);
        $data = json_decode($response->getContent(), true);
        $transferId = $data['transferId'];
        $this->postActivateUnitTransfer($client, $transferId, $tenantCode);

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC123',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(50.0, $activeUnits);
    }

    /**
     * @test
     */
    public function it_check_if_deduct_units_work_with_limits_while_adding_points_effects(): void
    {
        $client = self::createAuthenticatedClient();

        $tenantCode = 'code';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '100',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '100',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                        [
                            'effect' => 'deduct_unit',
                            'pointsRule' => '50',
                        ],
                    ],
                ],
            ],
            limits: [
                'points' => [
                    'value' => 100,
                ],
                'pointsPerMember' => [
                    'value' => 80,
                ],
            ]
        );

        $this->assertOkResponseStatus($response);

        $memberEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberEmail);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC123',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(30.0, $activeUnits);
    }

    /**
     * @test
     */
    public function it_adds_remaining_units_referrer_campaign(): void
    {
        $client = self::createAuthenticatedClient();

        $tenantCode = 'code';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            type: 'referral',
            rules: [
                [
                    'target' => 'referrer',
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '100',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                    ],
                ],
                [
                    'target' => 'self',
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '100',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                    ],
                ],
            ],
            limits: [
                'points' => [
                    'value' => 50,
                ],
            ]
        );

        $this->assertOkResponseStatus($response);

        $memberAEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberAEmail);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerIdA = $data['customerId'];

        $client->request(
            'GET',
            '/api/'.$tenantCode.'/member/'.$customerIdA
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $memberAReferralToken = $data['referralToken'];

        $memberBEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberBEmail, referrerToken: $memberAReferralToken);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerIdB = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC123',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberBEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerIdA, $tenantCode);
        $this->assertSame(50.0, $activeUnits);
        $activeUnits1 = $this->getCustomerActivePoints($customerIdB, $tenantCode);
        $this->assertSame(0.0, $activeUnits1);
    }

    /**
     * @test
     */
    public function it_adds_remaining_units_for_more_then_one_effect_with_referrer(): void
    {
        $client = self::createAuthenticatedClient();

        $tenantCode = 'code';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            type: 'referral',
            rules: [
                [
                    'target' => 'referrer',
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '100',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '50',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                    ],
                ],
                [
                    'target' => 'self',
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '100',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '50',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                    ],
                ],
            ],
            limits: [
                'points' => [
                    'value' => 150,
                ],
                'pointsPerMember' => [
                    'value' => 100,
                ],
            ]
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $memberAEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberAEmail);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerIdA = $data['customerId'];

        $client->request(
            'GET',
            '/api/'.$tenantCode.'/member/'.$customerIdA
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $memberAReferralToken = $data['referralToken'];

        $memberBEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberBEmail, referrerToken: $memberAReferralToken);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerIdB = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC123',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberBEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerIdA, $tenantCode);
        $this->assertSame(100.0, $activeUnits);
        $activeUnits1 = $this->getCustomerActivePoints($customerIdB, $tenantCode);
        $this->assertSame(50.0, $activeUnits1);
    }

    /**
     * @test
     */
    public function it_adding_remaining_units_to_the_global_campaign_limit_that_left(): void
    {
        $client = self::createAuthenticatedClient();

        $tenantCode = 'code';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '66',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                    ],
                ],
            ],
            limits: [
                'points' => [
                    'value' => 100,
                ],
            ]
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('campaignId', $data);
        $campaignId = $data['campaignId'];

        $memberEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberEmail);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC123',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(66.0, $activeUnits);

        $memberEmail1 = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberEmail1);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId1 = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC125',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail1,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId1, $tenantCode);
        $this->assertSame(34.0, $activeUnits);
    }

    /**
     * @test
     */
    public function it_adds_points_based_on_the_stricter_limit(): void
    {
        $client = self::createAuthenticatedClient();

        $tenantCode = 'code';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '60',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                    ],
                ],
            ],
            limits: [
                'points' => [
                    'value' => 100,
                ],
                'pointsPerMember' => [
                    'value' => 80,
                ],
            ]
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        $memberEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberEmail);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC123',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(60.0, $activeUnits);

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC125',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(80.0, $activeUnits);
    }

    /**
     * @test
     */
    public function it_does_not_add_points_beyond_per_member_limit_even_if_global_limit_allows(): void
    {
        $client = self::createAuthenticatedClient();

        $tenantCode = 'code';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '80',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                    ],
                ],
            ],
            limits: [
                'points' => [
                    'value' => 100,
                ],
                'pointsPerMember' => [
                    'value' => 80,
                ],
            ]
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        $memberEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberEmail);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC123',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(80.0, $activeUnits);

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC125',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(80.0, $activeUnits);
    }

    /**
     * @test
     */
    public function it_does_not_add_remaining_units_to_limit_if_feature_flag_is_not_enable(): void
    {
        $_ENV['FEATURE_FLAGS'] = '';
        $client = self::createAuthenticatedClient();

        $tenantCode = 'code';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '60',
                            'unitsLockRule' => [
                                'lockStrategy' => 'no_pending',
                            ],
                        ],
                    ],
                ],
            ],
            limits: [
                'pointsPerMember' => [
                    'value' => 80,
                ],
            ]
        );

        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        $memberEmail = '<EMAIL>';
        $response = $this->postMember($client, $tenantCode, $memberEmail);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC123',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(60.0, $activeUnits);

        $transactionResponse = $this->postTransaction(
            $client,
            $tenantCode,
            header: [
                'documentNumber' => 'DOC125',
                'documentType' => 'sell',
                'purchasedAt' => Carbon::now()->format('Y-m-d H:i'),
                'purchasePlace' => 'New York',
            ],
            items: [
                [
                    'sku' => 'A/1',
                    'name' => 'Item 1',
                    'quantity' => 1,
                    'grossValue' => 50,
                    'category' => 'Test',
                    'maker' => 'brand1',
                    'labels' => [
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                ],
            ],
            customerData: [
                'email' => $memberEmail,
            ]
        );

        $this->assertOkResponseStatus($transactionResponse);

        $activeUnits = $this->getCustomerActivePoints($customerId, $tenantCode);
        $this->assertSame(60.0, $activeUnits);
    }
}
