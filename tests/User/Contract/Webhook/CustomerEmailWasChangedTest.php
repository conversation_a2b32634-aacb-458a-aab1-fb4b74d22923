<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Contract\Webhook;

use OpenLoyalty\Test\Common\Integration\AbstractWebhookApiTest;
use OpenLoyalty\Test\Utils\Service\WebhookDetails;

final class CustomerEmailWasChangedTest extends AbstractWebhookApiTest
{
    /**
     * @test
     */
    public function it_checks_webhook_customer_email_was_changed(): void
    {
        $this->markTestSkipped('TODO OLOY-10372');
        $this->createTenant('it_checks_webhook_customer_email_was_changed');
        $customerId = $this->createCustomer('it_checks_webhook_customer_email_was_changed');
        $this->addAllWebhooks('it_checks_webhook_customer_email_was_changed');
        //trigger
        $this->updateCustomer('it_checks_webhook_customer_email_was_changed', $customerId);

        /** @var WebhookDetails[] $webhooks */
        $webhooks = $this->webhookClientMock->getSentWebhooks();
        $this->assertSame(1, $this->webhookClientMock->getWebhooksCount());
        $this->assertSame('CustomerEmailWasChanged', $webhooks[0]->getWebhookName());
        $this->assertSameJson($this->getWebhookJsonBody($customerId), $webhooks[0]->getBodyJson());
        $this->assertWebhookHeaders($webhooks[0]->getHeaders());
    }

    protected function updateCustomer(string $storeCode, string $customerId): void
    {
        //todo https://openloyalty.atlassian.net/browse/OLOY-10372
    }

    protected function createCustomer(
        string $storeCode
    ): string {
        $customerData = [
            'customer' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'loyaltyCardNumber' => '123',
                'email' => '<EMAIL>',
                'gender' => 'male',
                'birthDate' => '1991-06-16',
                'agreement1' => true,
                'phone' => '*********',
                'address' => [
                    'street' => 'street',
                    'address1' => 'building',
                    'address2' => '1',
                    'city' => 'city',
                    'country' => 'ZW',
                    'postal' => '12345',
                    'province' => 'state',
                ],
                'company' => [
                    'name' => 'company',
                    'nip' => '123',
                ],
                'labels' => [
                    [
                        'key' => 'test',
                        'value' => 'true',
                    ],
                ],
            ],
        ];

        $this->client->jsonRequest('POST', '/api/'.$storeCode.'/member', $customerData);
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        return $data['customerId'];
    }

    private function getWebhookJsonBody(string $customerId): string
    {
        return <<<EOT
            {
              "eventName": "CustomerEmailWasChanged",
              "messageId": "@uuid@",
              "storeCode": "it_checks_webhook_customer_email_was_changed",
              "createdAt":"@datetime@",
              "data": {
                "customer": {
                  "customerId": "$customerId",
                  "email": "<EMAIL>",
                  "phone": "*********",
                  "loyaltyCardNumber": "123"
                },
                "code": "string",
                "codeNumber": 0
              },
              "requestId": "@uuid@"
            }
        EOT;
    }
}
