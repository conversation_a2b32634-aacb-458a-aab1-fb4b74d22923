<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Unit\Infrastructure\Form\Type;

use Nelmio\ApiDocBundle\Form\Extension\DocumentationExtension;
use OpenLoyalty\Channel\Domain\ChannelRepository;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Infrastructure\Form\Type\StoreChoicesType;
use OpenLoyalty\Level\Domain\LevelRepository;
use OpenLoyalty\Settings\Infrastructure\Entity\BooleanSettingEntry;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;
use OpenLoyalty\User\Infrastructure\Form\Listener\AllowUserToEditProfileSubscriber;
use OpenLoyalty\User\Infrastructure\Form\Type\CustomerEditFormType;
use OpenLoyalty\User\Infrastructure\Form\Type\CustomerRegistrationFormType;
use OpenLoyalty\User\Infrastructure\Form\Type\LabelFormType;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Form\Extension\Validator\ValidatorExtension;
use Symfony\Component\Form\PreloadedExtension;
use Symfony\Component\Form\Test\TypeTestCase;
use Symfony\Component\Security\Core\Authentication\Token\NullToken;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Mapping\ClassMetadata;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

abstract class CustomerEditFormTypeTestCase extends TypeTestCase
{
    abstract protected function isAllowCustomersProfileEdits(): bool;

    /**
     * {@inheritdoc}
     */
    protected function getExtensions(): iterable
    {
        /** @var SettingsManager&MockObject $settings */
        $settings = $this->createMock(SettingsManager::class);
        $settings
            ->method('getSettingByKey')
            ->willReturn(new BooleanSettingEntry('allowCustomersProfileEdits', $this->isAllowCustomersProfileEdits()));

        /** @var StoreContextProviderInterface&MockObject $storeContextProvider */
        $storeContextProvider = $this->createMock(StoreContextProviderInterface::class);

        /** @var TokenStorageInterface&MockObject $tokenStorage */
        $tokenStorage = $this->createMock(TokenStorageInterface::class);
        $tokenStorage->method('getToken')->willReturn(new NullToken());

        /** @var TranslatorInterface&MockObject $translator */
        $translator = $this->createMock(TranslatorInterface::class);
        $translator
            ->method('trans')
            ->willReturn('You don\'t have permission to edit this data.')
        ;

        $subscriber = new AllowUserToEditProfileSubscriber($settings, $tokenStorage, $translator, $storeContextProvider);

        $customerEditFormType = new CustomerEditFormType(
            $subscriber
        );

        /** @var LevelRepository&MockObject $levelRepository */
        $levelRepository = $this->createMock(LevelRepository::class);
        /** @var \OpenLoyalty\Channel\Domain\ChannelRepository&MockObject $channelRepository */
        $channelRepository = $this->createMock(ChannelRepository::class);

        $storeContextProvider = $this->createMock(StoreContextProviderInterface::class);

        $customerRegistrationFormType = new CustomerRegistrationFormType(
            $levelRepository,
            $channelRepository,
            $translator,
            $storeContextProvider,
            25,
            255
        );

        $validator = $this->createMock(ValidatorInterface::class);
        $validator
            ->method('validate')
            ->willReturn(new ConstraintViolationList())
        ;

        $metadata = $this
            ->getMockBuilder(ClassMetadata::class)
            ->disableOriginalConstructor()
            ->getMock()
        ;
        $metadata->method('addConstraint')->willReturnSelf();
        $metadata->method('addPropertyConstraint')->willReturnSelf();
        $validator->method('getMetadataFor')->willReturn($metadata);

        $storeRepository = $this->getMockBuilder(StoreRepository::class)->getMock();
        $storeFormType = new StoreChoicesType($storeRepository);

        return [
            new PreloadedExtension(
                [
                    $customerEditFormType,
                    $customerRegistrationFormType,
                    $storeFormType,
                    new LabelFormType(255, 255),
                ],
                [],
            ),
            new ValidatorExtension($validator),
        ];
    }

    protected function getTypeExtensions(): array
    {
        return [
            new DocumentationExtension(),
        ];
    }
}
