<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Unit\Infrastructure\Form\Type;

use Nelmio\ApiDocBundle\Form\Extension\DocumentationExtension;
use OpenLoyalty\Channel\Domain\ChannelRepository;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Infrastructure\Form\Type\StoreChoicesType;
use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Domain\LevelRepository;
use OpenLoyalty\User\Infrastructure\Form\Type\CustomerRegistrationFormType;
use OpenLoyalty\User\Infrastructure\Form\Type\LabelFormType;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Form\Extension\Validator\ValidatorExtension;
use Symfony\Component\Form\PreloadedExtension;
use Symfony\Component\Form\Test\TypeTestCase;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Mapping\ClassMetadata;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

final class CustomerRegistrationFormTypeTest extends TypeTestCase
{
    private LevelRepository&MockObject $levelRepository;
    private ChannelRepository&MockObject $channelRepository;
    private MockObject&ValidatorInterface $validator;
    private MockObject&TranslatorInterface $translator;
    private StoreRepository&MockObject $storeRepository;
    private StoreContextProviderInterface&MockObject $storeContextProvider;

    protected function setUp(): void
    {
        $this->translator = $this->createMock(TranslatorInterface::class);
        $this->translator->method('trans')->willReturn('Incorrect phone number format, use 00000000000.');

        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->validator
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        $metadata = $this->createMock(ClassMetadata::class);
        $metadata->method('addConstraint')->willReturnSelf();
        $metadata->method('addPropertyConstraint')->willReturnSelf();

        $this->validator->method('getMetadataFor')->willReturn($metadata);

        $this->levelRepository = $this->createMock(LevelRepository::class);
        $this->levelRepository->method('findAllActive')->willReturnCallback(
            function () {
                $store = new Store(new StoreId('00000000-0000-1111-0000-000000000003'), 'ST_CODE', 'EUR', 'Test store');

                return [
                    new Level(
                        new LevelId('f99748f2-bf86-11e6-a4a6-cec0c932ce01'),
                        $store,
                        0
                    ),
                ];
            }
        );

        $this->channelRepository = $this->createMock(ChannelRepository::class);
        $this->storeRepository = $this->createMock(StoreRepository::class);
        $this->storeContextProvider = $this->createMock(StoreContextProviderInterface::class);

        parent::setUp();
    }

    /**
     * @test
     */
    public function it_contains_valid_form_fields_when_register_customer(): void
    {
        $formData = [
            'firstName' => 'Jone',
            'lastName' => 'Doe',
            'gender' => 'male',
            'email' => '<EMAIL>',
            'phone' => '123456',
            'birthDate' => '2018-12-12',
            'createdAt' => '2018-08-06 00:00:00',
            'loyaltyCardNumber' => '1234567',
            'agreement1' => 1,
            'agreement2' => 1,
            'agreement3' => 1,
            'address' => [
                'street' => 'street 1',
                'address1' => 'address 1',
                'address2' => 'address 2',
                'postal' => '777-888',
                'city' => 'Wroclaw',
                'province' => 'dolnoslaskie',
            ],
        ];

        $form = $this->factory->create(CustomerRegistrationFormType::class);
        $form->submit($formData);

        $this->assertTrue($form->isSynchronized());
        $this->assertTrue($form->isValid());
        $this->assertSame('Jone', $form->get('firstName')->getData());
        $this->assertSame('Doe', $form->get('lastName')->getData());
        $this->assertSame('male', $form->get('gender')->getData());
        $this->assertSame('<EMAIL>', $form->get('email')->getData());
        $this->assertSame('2018-12-12', $form->get('birthDate')->getData()->format('Y-m-d'));
        $this->assertSame('2018-08-06', $form->get('createdAt')->getData()->format('Y-m-d'));
        $this->assertSame('1234567', $form->get('loyaltyCardNumber')->getData());
        $this->assertTrue($form->get('agreement1')->getData());
        $this->assertTrue($form->get('agreement2')->getData());
        $this->assertTrue($form->get('agreement3')->getData());

        $this->assertSame('address 1', $form->get('address')->get('address1')->getData());
        $this->assertSame('address 2', $form->get('address')->get('address2')->getData());
        $this->assertSame('777-888', $form->get('address')->get('postal')->getData());
        $this->assertSame('Wroclaw', $form->get('address')->get('city')->getData());
        $this->assertSame('dolnoslaskie', $form->get('address')->get('province')->getData());
    }

    protected function getExtensions(): array
    {
        $formType = new CustomerRegistrationFormType(
            $this->levelRepository,
            $this->channelRepository,
            $this->translator,
            $this->storeContextProvider,
            25,
            255
        );

        return [
            new PreloadedExtension(
                [
                    $formType,
                    new StoreChoicesType($this->storeRepository),
                    new LabelFormType(255, 255),
                ], []),
            new ValidatorExtension($this->validator),
        ];
    }

    protected function getTypeExtensions(): array
    {
        return [
            new DocumentationExtension(),
        ];
    }
}
