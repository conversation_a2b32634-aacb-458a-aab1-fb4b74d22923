<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Unit\Infrastructure\Service;

use OpenLoyalty\Core\Domain\Id\CodeId;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\User\Domain\Code;
use OpenLoyalty\User\Domain\CodeRepositoryInterface;
use OpenLoyalty\User\Domain\Service\CodeManagerInterface;
use OpenLoyalty\User\Infrastructure\Entity\Customer;
use OpenLoyalty\User\Infrastructure\Generator\AlphaNumericCodeGenerator;
use OpenLoyalty\User\Infrastructure\Generator\CodeGenerator;
use OpenLoyalty\User\Infrastructure\Persistence\Doctrine\Repository\DoctrineCodeRepository;
use OpenLoyalty\User\Infrastructure\Service\CodeManager;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Contracts\Translation\TranslatorInterface;

final class CodeManagerTest extends TestCase
{
    private MockObject&DoctrineCodeRepository $repository;
    private MockObject&UuidGeneratorInterface $uuidGenerator;
    private MockObject&CodeGenerator $codeGenerator;
    private MockObject&AlphaNumericCodeGenerator $internalCodeGenerator;

    /**
     * {@inheritdoc}
     */
    public function setUp(): void
    {
        $this->repository = $this->getCodeRepositoryMock();
        $this->repository
            ->method('countByObjectAndAction')
            ->willReturn(1);

        $this->uuidGenerator = $this->getUuidGeneratorMock();

        $translator = $this->getMockBuilder(TranslatorInterface::class)->getMock();
        $translator->method('trans')->willReturn('content');

        $this->codeGenerator = $this->getMockBuilder(CodeGenerator::class)->getMock();
        $this->internalCodeGenerator = $this->createMock(AlphaNumericCodeGenerator::class);
    }

    public function objectTypeObjectIdProvider(): array
    {
        return [
            ['c85bef3a-1549-11e8-b642-0ed5f89f718b', Customer::class, '542ecfc0-1543-11e8-b642-0ed5f89f718b'],
            ['c85bef3a-1549-11e8-b642-0ed5f89f718b', Customer::class, '542ed61e-1543-11e8-b642-0ed5f89f718b'],
        ];
    }

    public function codePhoneProvider(): array
    {
        return [
            [
                new CodeId('542ecfc0-1543-11e8-b642-0ed5f89f718b'),
                Customer::class,
                '1b62631e-1548-11e8-b642-0ed5f89f718b',
                'ABC123',
                '123456789',
            ],
            [
                new CodeId('1b6266a2-1548-11e8-b642-0ed5f89f718b'),
                Customer::class,
                '70cba220-1548-11e8-b642-0ed5f89f718b',
                'ABC123',
                '123456789',
            ],
        ];
    }

    /**
     * @test
     * @dataProvider objectTypeObjectIdProvider
     */
    public function it_creates_code(string $id, string $objectType, string $objectId): void
    {
        $this->uuidGenerator->method('generate')->willReturn($id);
        $this->repository
            ->method('findOneBy')
            ->willReturn(null);

        $this->codeGenerator->method('generate')->willReturn('1234567');

        $activationCode = $this->getCodeManager(
            $this->uuidGenerator,
            $this->repository,
            $this->codeGenerator,
            $this->internalCodeGenerator
        )->createCode($objectType, $objectId, CodeManagerInterface::CUSTOMER_ACTIVATION);

        $this->assertSame($id, (string) $activationCode->getCodeId());
        $this->assertSame($objectId, $activationCode->getObjectId());
        $this->assertNotEmpty($activationCode->getCode());
        $this->assertSame('1234567', $activationCode->getCode());
    }

    /**
     * @test
     * @dataProvider objectTypeObjectIdProvider
     */
    public function it_creates_code_for_reset_password(string $id, string $objectType, string $objectId): void
    {
        $this->uuidGenerator->method('generate')->willReturn($id);
        $this->repository
            ->method('findOneBy')
            ->willReturn(null);

        $this->internalCodeGenerator->method('generate')->willReturn(
            'JBZFXUSJHHWSW7EESDZSTTVH8EGCQ7Z62G6BGZ2AD4F9Y2GHF88M7Y6NLZFXL6TBMD44Q3QDTULC9GE2FPWDEG4PC5Y2ZPZFBMXF2ZV5W9LF889QPGW393CWK2KSDBX4'
        );

        $activationCode = $this->getCodeManager(
            $this->uuidGenerator,
            $this->repository,
            $this->codeGenerator,
            $this->internalCodeGenerator
        )->createCode($objectType, $objectId, CodeManagerInterface::USER_PASSWORD_RESET);

        $this->assertSame($id, (string) $activationCode->getCodeId());
        $this->assertSame($objectId, $activationCode->getObjectId());
        $this->assertNotEmpty($activationCode->getCode());
        $this->assertSame('JBZFXUSJHHWSW7EESDZSTTVH8EGCQ7Z62G6BGZ2AD4F9Y2GHF88M7Y6NLZFXL6TBMD44Q3QDTULC9GE2FPWDEG4PC5Y2ZPZFBMXF2ZV5W9LF889QPGW393CWK2KSDBX4', $activationCode->getCode());
    }

    protected function getActivationCodeMock(
        CodeId $codeId,
        string $objectType,
        string $objectId,
        string $code
    ): MockObject&Code {
        return $this->getMockBuilder(Code::class)
            ->setConstructorArgs([$codeId, $objectType, $objectId, CodeManagerInterface::CUSTOMER_ACTIVATION, $code])
            ->getMock();
    }

    protected function getUuidGeneratorMock(): MockObject&UuidGeneratorInterface
    {
        return $this->getMockBuilder(UuidGeneratorInterface::class)
            ->disableOriginalConstructor()
            ->getMock();
    }

    protected function getCodeManager(
        UuidGeneratorInterface $uuidGenerator,
        CodeRepositoryInterface $codeRepository,
        CodeGenerator $codeGenerator,
        AlphaNumericCodeGenerator $alphaNumericCodeGenerator,
    ): CodeManager {
        return new CodeManager(
            $uuidGenerator,
            $codeRepository,
            $codeGenerator,
            $alphaNumericCodeGenerator,
            6,
            360
        );
    }

    protected function getCodeRepositoryMock(): MockObject&DoctrineCodeRepository
    {
        return $this->getMockBuilder(DoctrineCodeRepository::class)
            ->disableOriginalConstructor()
            ->getMock();
    }
}
