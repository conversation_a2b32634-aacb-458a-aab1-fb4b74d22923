<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Unit\Infrastructure\Security\Voter;

use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseVoterTest;
use OpenLoyalty\User\Infrastructure\Security\Voter\UserVoter;

final class UserVoterTest extends BaseVoterTest
{
    /**
     * @test
     */
    public function it_works(): void
    {
        $attributes = [
            UserVoter::PASSWORD_CHANGE => ['customer' => true, 'admin' => true],
            UserVoter::REVOKE_REFRESH_TOKEN => ['customer' => true, 'admin' => true],
        ];

        $voter = new UserVoter();

        $this->assertVoterAttributes($voter, $attributes);
    }

    /**
     * {@inheritdoc}
     */
    protected function getSubjectById($id): void
    {
        return;
    }
}
