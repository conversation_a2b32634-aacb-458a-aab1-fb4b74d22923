<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Unit\Infrastructure\Security\Voter;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseVoterTest;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionEvaluator;
use OpenLoyalty\User\Infrastructure\Security\Voter\CustomerVoter;
use PHPUnit\Framework\MockObject\MockObject;

final class CustomerVoterTest extends BaseVoterTest
{
    private const CUSTOMER_ID = '00000000-0000-474c-b092-b0dd880c0700';

    /**
     * @test
     */
    public function it_works(): void
    {
        $attributes = [
            CustomerVoter::CREATE_CUSTOMER => ['customer' => false, 'admin' => true, 'admin_reporter' => false],
            CustomerVoter::LIST_CUSTOMERS => ['customer' => false, 'admin' => true, 'admin_reporter' => true],
            CustomerVoter::ASSIGN_CHANNEL => ['customer' => false, 'admin' => true, 'admin_reporter' => false, 'id' => self::CUSTOMER_ID],
            CustomerVoter::ASSIGN_CUSTOMER_LEVEL => ['customer' => false, 'admin' => true, 'admin_reporter' => false, 'id' => self::CUSTOMER_ID],
            CustomerVoter::DEACTIVATE => ['customer' => false, 'admin' => true, 'admin_reporter' => false, 'id' => self::CUSTOMER_ID],
            CustomerVoter::ANONYMIZE => ['customer' => false, 'admin' => true, 'admin_reporter' => false, 'id' => self::CUSTOMER_ID],
            CustomerVoter::VIEW => ['customer' => false, 'admin' => true, 'admin_reporter' => true, 'id' => self::CUSTOMER_ID],
            CustomerVoter::VIEW_STATUS => ['customer' => false, 'admin' => true, 'admin_reporter' => true, 'id' => self::CUSTOMER_ID],
            CustomerVoter::EDIT => ['customer' => false, 'admin' => true, 'admin_reporter' => false, 'id' => self::CUSTOMER_ID],
            CustomerVoter::DELETE => ['customer' => false, 'admin' => true, 'admin_reporter' => false, 'id' => self::CUSTOMER_ID],
        ];

        /** @var UserPermissionCheckerInterface&MockObject $permissionChecker */
        $permissionChecker = $this->getMockBuilder(UserPermissionCheckerInterface::class)->getMock();
        $permissionChecker->method('hasPermissionInCurrentStore')->willReturnCallback(function (User $user, string $resource, array $accesses) {
            return (new UserPermissionEvaluator())->hasPermission(null, $user, $resource, $accesses);
        });

        $voter = new CustomerVoter($permissionChecker);

        $this->assertVoterAttributes($voter, $attributes);
    }

    /**
     * {@inheritdoc}
     */
    protected function getSubjectById($id)
    {
        $customer = $this->getMockBuilder(Customer::class)->disableOriginalConstructor()->getMock();
        $customer->method('getCustomerId')->willReturn(new CustomerId($id));

        return $customer;
    }
}
