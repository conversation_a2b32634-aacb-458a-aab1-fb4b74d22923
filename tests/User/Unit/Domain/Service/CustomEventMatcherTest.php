<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Unit\Domain\Service;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\CustomEvent\Domain\CustomEvent as DomainCustomEvent;
use OpenLoyalty\CustomEvent\Domain\CustomEventRepositoryInterface;
use OpenLoyalty\CustomEvent\Domain\ValueObject\CustomerData;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;
use OpenLoyalty\User\Domain\Model\CustomEvent;
use OpenLoyalty\User\Domain\Provider\CustomerIdProvider;
use OpenLoyalty\User\Domain\Service\CustomEventMatcher;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class CustomEventMatcherTest extends TestCase
{
    private MockObject&CustomerIdProvider $customerIdProvider;
    private MockObject&CustomerRepositoryInterface $customerRepository;
    private MockObject&EventBusInterface $eventBus;
    private MockObject&CustomEventRepositoryInterface $customEventRepository;

    protected function setUp(): void
    {
        $this->customerRepository = $this->getMockBuilder(CustomerRepositoryInterface::class)->getMock();
        $this->customerIdProvider = $this->getMockBuilder(CustomerIdProvider::class)->getMock();
        $this->eventBus = $this->getMockBuilder(EventBusInterface::class)->getMock();
        $this->customEventRepository = $this->createMock(CustomEventRepositoryInterface::class);
        $this->customEventRepository->method('byId')->willReturn($this->createMock(DomainCustomEvent::class));
    }

    /**
     * @test
     */
    public function it_assigns_custom_event_if_matches(): void
    {
        $matcher = new CustomEventMatcher($this->customerIdProvider, $this->customerRepository, $this->customEventRepository, $this->eventBus);

        $this->customerIdProvider->method('getId')->willReturn(new CustomerId('00000000-0000-474c-b092-b0dd880c0700'));
        $this->customerRepository->method('load')->willReturn(Customer::registerCustomer(
            new CustomerId('00000000-0000-474c-b092-b0dd880c0700'),
            new StoreId('00000000-0000-474c-b092-b0dd880c0700'),
            new \OpenLoyalty\User\Domain\ValueObject\CustomerData(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                new \DateTime()
            )
        ));
        $this->customerRepository->expects($this->once())->method('save');

        $matcher->match(
            new StoreId('00000000-2222-3333-4444-000000000004'),
            $this->createMock(CustomEvent::class),
            new CustomerData(),
            new DateTimeImmutable()
        );
    }

    /**
     * @test
     */
    public function it_does_not_assigns_custom_event_if_not_matches(): void
    {
        $matcher = new CustomEventMatcher($this->customerIdProvider, $this->customerRepository, $this->customEventRepository, $this->eventBus);
        $this->customerRepository->expects($this->never())->method('save');

        $matcher->match(
            new StoreId('00000000-2222-3333-4444-000000000004'),
            $this->createMock(CustomEvent::class),
            new CustomerData(),
            new DateTimeImmutable()
        );
    }
}
