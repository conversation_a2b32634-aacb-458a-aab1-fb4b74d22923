<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Unit\Domain\Service;

use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\LevelId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Level\Domain\Exception\LevelNotFoundException;
use OpenLoyalty\Level\Domain\Level;
use OpenLoyalty\Level\Domain\LevelRepository;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Domain\Service\MoveLevelService;
use OpenLoyalty\User\Domain\Service\MoveLevelServiceInterface;
use OpenLoyalty\User\Domain\SystemEvent\CustomerLevelChangedSystemEvent;
use OpenLoyalty\User\Domain\SystemEvent\CustomerUpdatedSystemEvent;
use OpenLoyalty\User\Infrastructure\CustomerRepository;
use OpenLoyalty\User\Infrastructure\LevelDowngradeModeProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class MoveLevelServiceTest extends TestCase
{
    private const CUSTOMER_ID_1 = '00000000-1111-0000-0000-000000001111';
    private const LEVEL_ID_1 = '00000000-4444-0000-0000-000000001111';
    private const LEVEL_ID_2 = '00000000-4444-0000-0000-000000002222';
    private MockObject&LevelDowngradeModeProvider $levelDowngradeModeProvider;
    private MockObject&CustomerRepository $customerRepository;
    private MockObject&LoggerInterface $logger;
    private MockObject&EventBusInterface $eventDispatcher;
    private MockObject&LevelRepository $levelRepository;
    private MockObject&StoreRepository $storeRepository;

    protected function setUp(): void
    {
        $this->levelDowngradeModeProvider = $this->getMockBuilder(LevelDowngradeModeProvider::class)->getMock();
        $this->customerRepository = $this->getMockBuilder(CustomerRepository::class)
            ->disableOriginalConstructor()->getMock();
        $this->logger = $this->getMockBuilder(LoggerInterface::class)->getMock();
        $this->eventDispatcher = $this->getMockBuilder(EventBusInterface::class)->getMock();
        $this->levelRepository = $this->getMockBuilder(LevelRepository::class)->getMock();
        $this->storeRepository = $this->getMockBuilder(StoreRepository::class)->getMock();
        $this->storeRepository->method('byId')->willReturn($this->getStore());
    }

    protected function getMoveLevelService(): MoveLevelServiceInterface
    {
        return new MoveLevelService(
            $this->levelDowngradeModeProvider,
            $this->customerRepository,
            $this->logger,
            $this->eventDispatcher,
            $this->levelRepository,
            $this->storeRepository
        );
    }

    /**
     * @return Customer&MockObject
     */
    protected function getCustomer(): Customer
    {
        $customer = $this->getMockBuilder(Customer::class)->getMock();
        $customer->method('getCustomerId')->willReturn(new CustomerId(self::CUSTOMER_ID_1));
        $customer->method('getEmail')->willReturn('<EMAIL>');
        $customer->method('getPhone')->willReturn('48000000000');
        $customer->method('getStoreId')->willReturn($this->getStore()->getStoreId());
        $customer->method('getLevelId')->willReturn(new LevelId(self::LEVEL_ID_2));

        return $customer;
    }

    /**
     * @test
     */
    public function it_moves_to_level(): void
    {
        $customer = $this->getCustomer();
        $this->customerRepository->method('load')->willReturn($customer);
        $this->levelRepository->method('byId')->willReturn(new Level(
            new LevelId(self::LEVEL_ID_1),
            $this->getStore(),
            0
        ));

        $customer->expects($this->once())->method('addToLevel');
        $this->customerRepository->expects($this->once())->method('save');

        $service = $this->getMoveLevelService();
        $service->moveToLevel(
            new CustomerId(self::CUSTOMER_ID_1),
            new LevelId(self::LEVEL_ID_1)
        );
    }

    /**
     * @test
     */
    public function it_moves_to_undefined_level(): void
    {
        $customer = $this->getCustomer();
        $this->customerRepository->method('load')->willReturn($customer);

        $customer->expects($this->once())->method('addToLevel');
        $this->customerRepository->expects($this->once())->method('save');

        $service = $this->getMoveLevelService();
        $service->moveToLevel(
            new CustomerId(self::CUSTOMER_ID_1),
            null
        );
    }

    /**
     * @test
     */
    public function it_throws_exception_when_level_doest_not_exist(): void
    {
        $this->expectException(LevelNotFoundException::class);
        $customer = $this->getCustomer();
        $this->customerRepository->method('load')->willReturn($customer);

        $this->levelRepository->method('byId')->willReturn(null);

        $service = $this->getMoveLevelService();
        $service->moveToLevel(
            new CustomerId(self::CUSTOMER_ID_1),
            new LevelId(self::LEVEL_ID_1)
        );
    }

    /**
     * @test
     */
    public function it_recalculates_level_if_x_days_and_base_earned_points_since_last_level_change(): void
    {
        $customer = $this->getCustomer();
        $this->customerRepository->method('load')->willReturn($customer);

        $this->levelDowngradeModeProvider->method('getMode')
            ->willReturn(LevelDowngradeModeProvider::MODE_X_DAYS);
        $this->levelDowngradeModeProvider->method('getBase')
            ->willReturn(LevelDowngradeModeProvider::BASE_EARNED_POINTS_SINCE_LAST_LEVEL_CHANGE);

        $this->levelRepository->method('byId')->willReturn(new Level(
            new LevelId(self::LEVEL_ID_1),
            $this->getStore(),
            0
        ));

        $customer->expects($this->once())->method('recalculateLevel');
        $this->customerRepository->expects($this->any())->method('save');

        $service = $this->getMoveLevelService();
        $service->moveToLevel(
            new CustomerId(self::CUSTOMER_ID_1),
            new LevelId(self::LEVEL_ID_1)
        );
    }

    /**
     * @test
     */
    public function it_emits_customer_level_changed(): void
    {
        $customer = $this->getCustomer();
        $this->customerRepository->method('load')->willReturn($customer);
        $newLevel = new Level(new LevelId(self::LEVEL_ID_1), $this->getStore(), 10);
        $oldLevel = new Level(new LevelId(self::LEVEL_ID_2), $this->getStore(), 0);
        $this->levelRepository->method('byId')
            ->withConsecutive([$newLevel->getLevelId()], [$oldLevel->getLevelId()])
            ->willReturnOnConsecutiveCalls(
                $newLevel,
                $oldLevel
            );

        $this->eventDispatcher->expects($this->exactly(2))->method('dispatch')->withConsecutive(
            [
                $this->equalToWithDelta(
                    new CustomerUpdatedSystemEvent($this->getStore()->getStoreId(), new CustomerId(self::CUSTOMER_ID_1)),
                    0.01
                ),
            ],
            [
                $this->equalToWithDelta(
                    new CustomerLevelChangedSystemEvent(
                        $this->getStore()->getStoreId(),
                        new CustomerId(self::CUSTOMER_ID_1),
                        true,
                        $newLevel->getLevelId()
                    ),
                    0.01),
            ]
        );

        $service = $this->getMoveLevelService();
        $service->moveToLevel(
            new CustomerId(self::CUSTOMER_ID_1),
            $newLevel->getLevelId()
        );
    }

    /**
     * @test
     */
    public function it_does_not_emits_customer_level_changed_if_level_not_changed(): void
    {
        $customer = $this->getCustomer();
        $this->customerRepository->method('load')->willReturn($customer);
        $newLevel = new Level(new LevelId(self::LEVEL_ID_2), $this->getStore(), 10);
        $this->levelRepository->method('byId')->willReturn($newLevel);

        $this->eventDispatcher->expects($this->never())->method('dispatch');

        $service = $this->getMoveLevelService();
        $service->moveToLevel(
            new CustomerId(self::CUSTOMER_ID_1),
            $newLevel->getLevelId()
        );
    }

    protected function getStore(): Store
    {
        return new Store(new StoreId('00000000-0000-1111-0000-000000000003'), 'ST_CODE', 'EUR', 'Test store');
    }
}
