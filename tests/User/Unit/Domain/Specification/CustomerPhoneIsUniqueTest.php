<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Unit\Domain\Specification;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\User\Domain\UserRepositoryInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class CustomerPhoneIsUniqueTest extends KernelTestCase
{
    private const EXISTING_CUSTOMER_ID = '00000000-0000-474c-b092-b0dd880c07e1';

    private \OpenLoyalty\User\Domain\Specification\CustomerPhoneIsUnique $customerPhoneUnique;

    /**
     * {@inheritdoc}
     */
    public function setUp(): void
    {
        parent::setUp();

        $customer = self::getContainer()->get(UserRepositoryInterface::class);
        $this->customerPhoneUnique = new \OpenLoyalty\User\Domain\Specification\CustomerPhoneIsUnique($customer);
    }

    /**
     * @test
     */
    public function it_return_false_when_customer_with_given_phone_number_exists(): void
    {
        $withPlusPrefix = $this->customerPhoneUnique->isSatisfiedBy(
            '+48234234000',
            new StoreId(LoadSettingsData::DEFAULT_STORE_ID)
        );
        $withoutPlusPrefix = $this->customerPhoneUnique->isSatisfiedBy(
            '48234234000',
            new StoreId(LoadSettingsData::DEFAULT_STORE_ID)
        );
        $this->assertFalse($withPlusPrefix);
        $this->assertFalse($withoutPlusPrefix);
    }

    /**
     * @test
     */
    public function it_return_true_when_customer_with_given_phone_number_not_exists(): void
    {
        $withPlusPrefix = $this->customerPhoneUnique->isSatisfiedBy(
            '+non_exists_phone_number',
            new StoreId(LoadSettingsData::DEFAULT_STORE_ID)
        );
        $withoutPlusPrefix = $this->customerPhoneUnique->isSatisfiedBy(
            'non_exists_phone_number',
            new StoreId(LoadSettingsData::DEFAULT_STORE_ID)
        );
        $this->assertTrue($withPlusPrefix);
        $this->assertTrue($withoutPlusPrefix);
    }

    /**
     * @test
     */
    public function it_return_true_when_given_phone_number_exists_to_customer(): void
    {
        $withPlusPrefix = $this->customerPhoneUnique->isSatisfiedBy(
            '+48234234000',
            new StoreId(LoadSettingsData::DEFAULT_STORE_ID),
            self::EXISTING_CUSTOMER_ID
        );
        $withoutPlusPrefix = $this->customerPhoneUnique->isSatisfiedBy(
            '48234234000',
            new StoreId(LoadSettingsData::DEFAULT_STORE_ID),
            self::EXISTING_CUSTOMER_ID
        );
        $this->assertTrue($withPlusPrefix);
        $this->assertTrue($withoutPlusPrefix);
    }

    /**
     * @test
     */
    public function it_return_false_when_given_phone_number_exists_and_is_not_belong_to_user(): void
    {
        $withPlusPrefix = $this->customerPhoneUnique->isSatisfiedBy(
            '+48456456000',
            new StoreId(LoadSettingsData::DEFAULT_STORE_ID),
            self::EXISTING_CUSTOMER_ID
        );
        $withoutPlusPrefix = $this->customerPhoneUnique->isSatisfiedBy(
            '48456456000',
            new StoreId(LoadSettingsData::DEFAULT_STORE_ID),
            self::EXISTING_CUSTOMER_ID
        );
        $this->assertFalse($withPlusPrefix);
        $this->assertFalse($withoutPlusPrefix);
    }
}
