<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Unit\Application\UseCase\Customer;

use Doctrine\ORM\EntityManagerInterface;
use OpenLoyalty\Core\Domain\Id\CodeId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\User\Application\Exception\CustomerNotFoundException;
use OpenLoyalty\User\Application\UseCase\Customer\SendActivationCodeUseCase;
use OpenLoyalty\User\Domain\Code;
use OpenLoyalty\User\Domain\Service\CodeFactoryInterface;
use OpenLoyalty\User\Infrastructure\Entity\Customer;
use PHPUnit\Framework\TestCase;

final class SendActivationCodeUseCaseTest extends TestCase
{
    private EntityManagerInterface $entityManager;
    private CodeFactoryInterface $codeFactory;
    private EventBusInterface $eventBus;
    private SendActivationCodeUseCase $useCase;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->codeFactory = $this->createMock(CodeFactoryInterface::class);
        $this->eventBus = $this->createMock(EventBusInterface::class);
        $this->useCase = new SendActivationCodeUseCase(
            $this->entityManager,
            $this->codeFactory,
            $this->eventBus
        );
    }

    /**
     * @test
     */
    public function it_throws_exception_while_not_found_customer(): void
    {
        $this->entityManager->method('find')->willReturn(null);
        $customerId = new CustomerId('00000000-0000-0000-0000-000000000000');

        $this->expectException(CustomerNotFoundException::class);

        $this->useCase->execute($customerId);
    }

    /**
     * @test
     */
    public function it_dispatch_event(): void
    {
        $customer = $this->createMock(Customer::class);
        $this->entityManager->method('find')->willReturn($customer);
        $storeId = new StoreId('00000000-0000-0000-0000-000000000000');
        $customer->method('getStoreId')->willReturn($storeId);
        $customerId = new CustomerId('00000000-0000-0000-0000-000000000000');

        $codeId = new CodeId('6d8e81b6-8b79-4eb0-ab90-37324168936f');
        $code = $this->createMock(Code::class);
        $code->method('getCodeId')->willReturn($codeId);

        $this->codeFactory->expects($this->once())->method('createCode')->willReturn($code);
        $this->eventBus->expects($this->once())->method('dispatch');

        $this->useCase->execute($customerId);
    }
}
