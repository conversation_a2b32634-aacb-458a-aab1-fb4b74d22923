<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Unit\Application\JobHandler;

use DateTimeImmutable;
use OpenLoyalty\Core\Application\TimezoneResolverInterface;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\CustomEvent\Domain\ValueObject\CustomerData;
use OpenLoyalty\User\Application\Job\MatchCustomEvent;
use OpenLoyalty\User\Application\JobHandler\MatchCustomEventHandler;
use OpenLoyalty\User\Domain\Model\CustomEvent;
use OpenLoyalty\User\Domain\Service\CustomEventMatcherInterface;
use PHPUnit\Framework\TestCase;

final class MatchCustomEventHandlerTest extends TestCase
{
    /**
     * @test
     */
    public function it_handles_job(): void
    {
        $matcher = $this->getMockBuilder(CustomEventMatcherInterface::class)->getMock();
        $matcher->expects($this->once())->method('match');
        $timezoneResolver = $this->getMockBuilder(TimezoneResolverInterface::class)->getMock();

        $handler = new MatchCustomEventHandler($matcher, $timezoneResolver);
        $handler->__invoke(new MatchCustomEvent(
            new StoreId('00000000-0000-0000-0000-000000000007'),
            $this->createMock(CustomEvent::class),
            new CustomerData(),
            new DateTimeImmutable()
        ));
    }
}
