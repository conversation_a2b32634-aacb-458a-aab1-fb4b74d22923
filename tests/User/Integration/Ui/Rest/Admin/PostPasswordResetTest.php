<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Integration\Ui\Rest\Admin;

use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\User\Domain\CodeRepositoryInterface;
use OpenLoyalty\User\Domain\Service\CodeManagerInterface;
use OpenLoyalty\User\Infrastructure\Entity\Admin;

final class PostPasswordResetTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_reset_password_for_admin_user(): void
    {
        $email = '<EMAIL>';
        $password = 'Abcdef123*';

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/admin/data',
            [
                'admin' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => $email,
                    'plainPassword' => $password,
                    'isActive' => true,
                    'roles' => ['3'],
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $client->request(
            'POST',
            '/api/admin/password/reset/request',
            [
                'username' => $email,
            ]
        );
        $this->assertNoContentResponseStatus($client->getResponse());

        $codeRepository = self::getContainer()->get(CodeRepositoryInterface::class);
        $code = $codeRepository->getLastByObjectAndAction(
            Admin::class,
            $data['adminId'],
            CodeManagerInterface::USER_PASSWORD_RESET
        );

        $client->request(
            'POST',
            '/api/admin/password/reset',
            [
                'reset' => [
                    'plainPassword' => 'XYZwsad098#',
                ],
                'token' => $code->getCode(),
            ]
        );
        $this->assertNoContentResponseStatus($client->getResponse());
    }

    /**
     * @test
     */
    public function it_return_validation_error_when_reset_password_for_admin_with_digits_only_in_password(): void
    {
        $email = '<EMAIL>';
        $password = 'Abcdef123*';

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/admin/data',
            [
                'admin' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => $email,
                    'plainPassword' => $password,
                    'isActive' => true,
                    'roles' => ['3'],
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/admin/password/reset',
            [
                'reset' => [
                    'plainPassword' => 'XYZwsad098#',
                ],
                'token' => 1234567890,
            ]
        );

        $response = $client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $errors = $data['errors'];
        $this->assertSame('reset', $errors[0]['path']);
        $this->assertSame('Bad confirmation token.', $errors[0]['message']);
    }
}
