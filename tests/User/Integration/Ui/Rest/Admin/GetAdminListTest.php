<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Integration\Ui\Rest\Admin;

use Carbon\Carbon;
use DateTimeImmutable;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\User\Integration\Traits\AdminApiTrait;
use OpenLoyalty\User\Domain\ApiKey;
use OpenLoyalty\User\Infrastructure\Entity\Role;
use OpenLoyalty\User\Infrastructure\Entity\User;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class GetAdminListTest extends AbstractApiTest
{
    use AdminApiTrait;

    private HttpKernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_gets_administrators_list(): void
    {
        $response = $this->getAdminList($this->client);
        $this->assertGetCollectionAdminStructureResponse($response);
    }

    /**
     * @test
     */
    public function it_gets_administrators_list_filtered_by_field_createdAt(): void
    {
        $entityManager = self::getContainer()->get('doctrine')->getManager();
        /** @var Role $role */
        $role = $entityManager->getRepository(Role::class)->findOneBy(['name' => 'Reporter admin']);

        Carbon::setTestNow('2002-05-01 00:00:00');

        $response = $this->postAdmin($this->client, email: '<EMAIL>', roles: [$role->getId()]);
        $this->assertOkResponseStatus($response);

        $response = $this->getAdminList($this->client, 'createdAt[gt]=2010-01-01T00:00:00Z');

        $this->assertResponseWithAllAdminsAfterDate($response, '2010-01-01T00:00:00Z');

        $this->deleteAdmin('<EMAIL>');
        Carbon::setTestNow();
    }

    /**
     * @test
     */
    public function it_gets_administrators_list_filtered_by_field_createAt(): void
    {
        $entityManager = self::getContainer()->get('doctrine')->getManager();
        /** @var Role $role */
        $role = $entityManager->getRepository(Role::class)->findOneBy(['name' => 'Reporter admin']);

        Carbon::setTestNow('2002-05-01 00:00:00');

        $response = $this->postAdmin($this->client, email: '<EMAIL>', roles: [$role->getId()]);

        $this->assertOkResponseStatus($response);

        $response = $this->getAdminList($this->client, 'createAt[gt]=2010-01-01T00:00:00Z');

        $this->assertResponseWithAllAdminsAfterDate($response, '2010-01-01T00:00:00Z');

        $this->deleteAdmin('<EMAIL>');
        Carbon::setTestNow();
    }

    /**
     * @test
     */
    public function it_gets_administrators_list_filtered_by_role(): void
    {
        $response = $this->getAdminList($this->client, 'role=Super admin');

        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertSame(2, $data['total']['filtered']);
        $this->assertNotEmpty($data['items']);
        foreach ($data['items'] as $user) {
            $this->assertSame('Super admin', $user['role']);
        }
    }

    /**
     * @test
     */
    public function it_gets_administrators_list_filtered_by_role_like_filter(): void
    {
        $response = $this->getAdminList($this->client, 'role[like]=Super');

        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertSame(2, $data['total']['filtered']);
        $this->assertNotEmpty($data['items']);
        foreach ($data['items'] as $user) {
            $this->assertSame('Super admin', $user['role']);
        }
    }

    private function assertGetCollectionAdminStructureResponse(Response $response): void
    {
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertArrayHasKey('items', $data);
        $this->assertNotEmpty($data['items']);
        foreach ($data['items'] as $user) {
            $this->assertArrayHasKey('createdAt', $user);
            $this->assertArrayHasKey('id', $user);
            $this->assertArrayHasKey('username', $user);
            $this->assertArrayHasKey('isActive', $user);
            $this->assertArrayHasKey('createAt', $user);
            $this->assertArrayHasKey('email', $user);
            $this->assertArrayHasKey('external', $user);
            $this->assertArrayHasKey('dtype', $user);
            $this->assertArrayHasKey('role', $user);
            $this->assertSame($user['dtype'], 'admin');
        }
    }

    private function assertResponseWithAllAdminsAfterDate(Response $response, string $date): void
    {
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertArrayHasKey('items', $data);
        $this->assertNotEmpty($data['items']);
        $this->assertCount(4, $data['items']);
        foreach ($data['items'] as $user) {
            $this->assertArrayHasKey('createAt', $user);
            $this->assertTrue(
                new DateTimeImmutable($user['createAt']) > new DateTimeImmutable($date),
                'All admins should be created after '.$date
            );
        }
    }

    private function deleteAdmin(string $email): void
    {
        $entityManager = self::$kernel->getContainer()->get('doctrine.orm.default_entity_manager');
        $entityManager->beginTransaction();
        try {
            $user = $entityManager->getRepository(User::class)->findOneBy(['email' => $email]);
            $apiKeyRepository = $entityManager->getRepository(ApiKey::class);
            $apiKey = $apiKeyRepository->findOneBy(['admin' => $user]);
            if ($apiKey) {
                $entityManager->remove($apiKey);
            }
            $entityManager->remove($user);

            $entityManager->flush();
            $entityManager->commit();
        } catch (ForeignKeyConstraintViolationException $e) {
            $entityManager->rollBack();

            throw $e;
        }
    }
}
