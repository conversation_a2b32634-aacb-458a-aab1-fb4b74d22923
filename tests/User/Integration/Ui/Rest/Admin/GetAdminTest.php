<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Integration\Ui\Rest\Admin;

use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use OpenLoyalty\Application;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\User\Infrastructure\Service\InternalAdminProvider;
use OpenLoyalty\User\Ui\Console\Command\GenerateAdminTokenCommand;
use Symfony\Component\Console\Tester\CommandTester;

final class GetAdminTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_get_administrator_data(): void
    {
        $kernel = self::bootKernel();

        $tokenManager = self::getContainer()->get(JWTTokenManagerInterface::class);
        $internalAdminProvider = self::getContainer()->get(InternalAdminProvider::class);

        $application = new Application($kernel);
        $application->add(new GenerateAdminTokenCommand(
            $tokenManager,
            $internalAdminProvider,
        ));

        $command = $application->find('oloy:user:generate-token');
        $commandTester = new CommandTester($command);
        $commandTester->execute(
            [
                'command' => $command->getName(),
                'mode' => 'read',
            ]
        );

        $output = $commandTester->getDisplay();
        preg_match('/----\s*(.*?)\s*----/s', $output, $matches);

        static::ensureKernelShutdown();

        $client = static::createClient();
        $client->setServerParameter('HTTP_Authorization', sprintf('Bearer %s', $matches[1]));

        $client->request('GET', '/api/admin/data');

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertSame('internal_admin_read', $data['username']);
    }
}
