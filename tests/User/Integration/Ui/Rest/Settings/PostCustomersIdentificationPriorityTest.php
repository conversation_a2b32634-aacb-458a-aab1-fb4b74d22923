<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

/*
 * Copyright Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Integration\Ui\Rest\Settings;

use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class PostCustomersIdentificationPriorityTest extends AbstractApiTest
{
    use TenantApiTrait;

    private string $tenantCode = 'member_settings_tenant';
    private HttpKernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
        $this->postTenant($this->client, $this->tenantCode);
    }

    /**
     * @test
     */
    public function it_validates_that_only_one_registration_method_can_be_configured(): void
    {
        $this->postSettingCustomersIdentificationPriorityWith([
            [
                'priority' => 3,
                'field' => 'phone',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => true,
            ],
            [
                'priority' => 2,
                'field' => 'loyaltyCardNumber',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => true,
            ],
            [
                'priority' => 1,
                'field' => 'email',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => false,
            ],
        ]);

        $patchResponse = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($patchResponse); // Validation error

        $responseData = json_decode($patchResponse->getContent(), true);
        $this->assertSame('Must be configured exactly one identification method', $responseData['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_validates_that_non_unique_identifier_can_not_be_event_matching(): void
    {
        $this->postSettingCustomersIdentificationPriorityWith([
            [
                'priority' => 3,
                'field' => 'email',
                'unique' => false,
                'eventMatching' => true,
                'identificationMethod' => false,
            ],
            [
                'priority' => 2,
                'field' => 'loyaltyCardNumber',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => false,
            ],
            [
                'priority' => 1,
                'field' => 'phone',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => true,
            ],
        ]);

        $patchResponse = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($patchResponse); // Validation error

        $responseData = json_decode($patchResponse->getContent(), true);
        $this->assertSame('Non unique email cannot be used for event matching', $responseData['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_validates_that_all_identifiers_can_not_be_non_unique(): void
    {
        $this->postSettingCustomersIdentificationPriorityWith([
            [
                'priority' => 3,
                'field' => 'email',
                'unique' => false,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
            [
                'priority' => 2,
                'field' => 'loyaltyCardNumber',
                'unique' => false,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
            [
                'priority' => 1,
                'field' => 'phone',
                'unique' => false,
                'eventMatching' => false,
                'identificationMethod' => true,
            ],
        ]);

        $patchResponse = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($patchResponse); // Validation error

        $responseData = json_decode($patchResponse->getContent(), true);
        $this->assertSame('At least one identifier must be unique', $responseData['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_validates_that_identification_method_must_be_unique(): void
    {
        $this->postSettingCustomersIdentificationPriorityWith([
            [
                'priority' => 3,
                'field' => 'email',
                'unique' => false,
                'eventMatching' => false,
                'identificationMethod' => true,
            ],
            [
                'priority' => 2,
                'field' => 'loyaltyCardNumber',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
            [
                'priority' => 1,
                'field' => 'phone',
                'unique' => false,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
        ]);

        $patchResponse = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($patchResponse); // Validation error

        $responseData = json_decode($patchResponse->getContent(), true);
        $this->assertSame('identificationMethod must be also unique', $responseData['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_validates_that_all_identifiers_must_be_configured(): void
    {
        $this->postSettingCustomersIdentificationPriorityWith([
            [
                'priority' => 3,
                'field' => 'email',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => true,
            ],
            [
                'priority' => 2,
                'field' => 'email',
                'unique' => false,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
            [
                'priority' => 1,
                'field' => 'phone',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => false,
            ],
        ]);

        $patchResponse = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($patchResponse); // Validation error

        $responseData = json_decode($patchResponse->getContent(), true);
        $this->assertSame('All identifiers (email, phone, loyaltyCardNumber) must be configured once', $responseData['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_validates_that_priority_values_must_be_between_1_and_3(): void
    {
        $this->postSettingCustomersIdentificationPriorityWith([
            [
                'priority' => 4, // Invalid priority value
                'field' => 'email',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => true,
            ],
            [
                'priority' => 2,
                'field' => 'loyaltyCardNumber',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => false,
            ],
            [
                'priority' => 1,
                'field' => 'phone',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => false,
            ],
        ]);

        $patchResponse = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($patchResponse); // Validation error

        $responseData = json_decode($patchResponse->getContent(), true);
        $this->assertSame('Priority value must be 1, 2, or 3. Got 4.', $responseData['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_validates_that_priority_values_must_be_unique(): void
    {
        $this->postSettingCustomersIdentificationPriorityWith([
            [
                'priority' => 1, // Duplicate priority value
                'field' => 'email',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => true,
            ],
            [
                'priority' => 2,
                'field' => 'loyaltyCardNumber',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => false,
            ],
            [
                'priority' => 1, // Duplicate priority value
                'field' => 'phone',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => false,
            ],
        ]);

        $patchResponse = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($patchResponse); // Validation error

        $responseData = json_decode($patchResponse->getContent(), true);
        $this->assertSame('Duplicate priority value: 1. Each priority must be unique.', $responseData['errors'][0]['message']);
    }

    private function postSettingCustomersIdentificationPriorityWith(array $setting): void
    {
        $this->client->request(
            'GET',
            '/api/'.$this->tenantCode.'/settings'
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $currentSettings = json_decode($response->getContent(), true)['settings'];

        $currentSettings['customersIdentificationConfigurations'] = $setting;

        $this->client->request(
            'POST',
            '/api/'.$this->tenantCode.'/settings',
            ['settings' => $currentSettings]
        );
    }
}
