<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Integration\Ui\Rest\Member\Activity;

use DateTimeImmutable;
use OpenLoyalty\Application;
use OpenLoyalty\Test\Common\Integration\AbstractMemberActivityTest;
use Symfony\Component\Console\Tester\CommandTester;
use Symfony\Component\HttpKernel\KernelInterface;

final class CustomEventAllTypesOrTransactionMemberActivityTest extends AbstractMemberActivityTest
{
    private const STORE_CODE = 'data_analytics_active_member';

    protected function setUp(): void
    {
        parent::setUp();
        $this->createTenant(self::STORE_CODE);
        $this->createTier(self::STORE_CODE);
        $this->createCustomEventSchemas(self::STORE_CODE);
    }

    /**
     * @test
     */
    public function it_activate_member_when_custom_event_is_given(): void
    {
        $this->setStoreActiveMemberSetting(
            self::STORE_CODE,
            [
                'transactionInXDays' => 10,
                'customEventsInXDays' => [
                    'days' => 10,
                    'allEvents' => true,
                    'eventTypes' => [],
                ],
                'operator' => 'or',
            ]
        );

        $now = new DateTimeImmutable();

        $customerEmail1 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail1);
        $this->createCustomEvent(
            self::STORE_CODE,
            $customerEmail1,
            'calories',
            ['calories' => 2000],
            $now->modify('-10 days')->setTime(0, 0),
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(1, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-11 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-10 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->format('Y-m-d')]);

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:customer:verify-members-activity');
        $commandTester = new CommandTester($command);
        $commandTester->execute(
            [
                'lastTransactionDate' => $now->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(1, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-11 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-10 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->format('Y-m-d')]);
    }

    /**
     * @test
     */
    public function it_activate_member_when_custom_event_is_given_with_other_type(): void
    {
        $this->setStoreActiveMemberSetting(
            self::STORE_CODE,
            [
                'transactionInXDays' => 10,
                'customEventsInXDays' => [
                    'days' => 10,
                    'allEvents' => true,
                    'eventTypes' => [],
                ],
                'operator' => 'or',
            ]
        );

        $now = new DateTimeImmutable();

        $customerEmail1 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail1);
        $this->createCustomEvent(
            self::STORE_CODE,
            $customerEmail1,
            'steps',
            ['steps' => 2000],
            $now->modify('-10 days')->setTime(0, 0),
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(1, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-11 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-10 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->format('Y-m-d')]);

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:customer:verify-members-activity');
        $commandTester = new CommandTester($command);
        $commandTester->execute(
            [
                'lastTransactionDate' => $now->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(1, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-11 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-10 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->format('Y-m-d')]);
    }

    /**
     * @test
     */
    public function it_does_not_activate_member_when_custom_event_is_from_the_future(): void
    {
        $this->setStoreActiveMemberSetting(
            self::STORE_CODE,
            [
                'transactionInXDays' => 10,
                'customEventsInXDays' => [
                    'days' => 10,
                    'allEvents' => true,
                    'eventTypes' => [],
                ],
                'operator' => 'or',
            ]
        );

        $now = new DateTimeImmutable();

        $customerEmail1 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail1);
        $this->createCustomEvent(
            self::STORE_CODE,
            $customerEmail1,
            'calories',
            ['calories' => 2000],
            $now->modify('+1 days')->setTime(0, 0),
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:customer:verify-members-activity');
        $commandTester = new CommandTester($command);
        $commandTester->execute(
            [
                'lastTransactionDate' => $now->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);
    }

    /**
     * @test
     */
    public function it_does_not_activate_member_when_custom_event_is_not_in_period(): void
    {
        $this->setStoreActiveMemberSetting(
            self::STORE_CODE,
            [
                'transactionInXDays' => 10,
                'customEventsInXDays' => [
                    'days' => 10,
                    'allEvents' => true,
                    'eventTypes' => [],
                ],
                'operator' => 'or',
            ]
        );

        $now = new DateTimeImmutable();

        $customerEmail1 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail1);
        $this->createCustomEvent(
            self::STORE_CODE,
            $customerEmail1,
            'calories',
            ['calories' => 2000],
            $now->modify('-11 days')->setTime(23, 59, 59),
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:customer:verify-members-activity');
        $commandTester = new CommandTester($command);
        $commandTester->execute(
            [
                'lastTransactionDate' => $now->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);
    }

    /**
     * @test
     */
    public function it_activate_member_when_transaction_is_given(): void
    {
        $this->setStoreActiveMemberSetting(
            self::STORE_CODE,
            [
                'transactionInXDays' => 10,
                'customEventsInXDays' => [
                    'days' => 10,
                    'allEvents' => true,
                    'eventTypes' => [],
                ],
                'operator' => 'or',
            ]
        );

        $now = new DateTimeImmutable();

        $customerEmail1 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail1);
        $this->createTransaction(
            self::STORE_CODE,
            $customerEmail1,
            $now->modify('-10 days')->setTime(0, 0),
            'document-1'
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(1, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-11 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-10 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->format('Y-m-d')]);

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:customer:verify-members-activity');
        $commandTester = new CommandTester($command);
        $commandTester->execute(
            [
                'lastTransactionDate' => $now->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(1, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-11 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-10 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->format('Y-m-d')]);
    }

    /**
     * @test
     */
    public function it_does_not_activate_member_when_transaction_is_from_the_future(): void
    {
        $this->setStoreActiveMemberSetting(
            self::STORE_CODE,
            [
                'transactionInXDays' => 10,
                'customEventsInXDays' => [
                    'days' => 10,
                    'allEvents' => true,
                    'eventTypes' => [],
                ],
                'operator' => 'or',
            ]
        );

        $now = new DateTimeImmutable();

        $customerEmail1 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail1);
        $this->createTransaction(
            self::STORE_CODE,
            $customerEmail1,
            $now->modify('+1 days')->setTime(0, 0),
            'document-1'
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:customer:verify-members-activity');
        $commandTester = new CommandTester($command);
        $commandTester->execute(
            [
                'lastTransactionDate' => $now->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);
    }

    /**
     * @test
     */
    public function it_does_not_activate_member_when_transaction_is_not_in_period(): void
    {
        $this->setStoreActiveMemberSetting(
            self::STORE_CODE,
            [
                'transactionInXDays' => 10,
                'customEventsInXDays' => [
                    'days' => 10,
                    'allEvents' => true,
                    'eventTypes' => [],
                ],
                'operator' => 'or',
            ]
        );

        $now = new DateTimeImmutable();

        $customerEmail1 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail1);
        $this->createTransaction(
            self::STORE_CODE,
            $customerEmail1,
            $now->modify('-11 days')->setTime(23, 59, 59),
            'document-1'
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:customer:verify-members-activity');
        $commandTester = new CommandTester($command);
        $commandTester->execute(
            [
                'lastTransactionDate' => $now->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);
    }

    /**
     * @test
     */
    public function it_activate_two_members_not_in_period_by_script(): void
    {
        $this->setStoreActiveMemberSetting(
            self::STORE_CODE,
            [
                'transactionInXDays' => 10,
                'customEventsInXDays' => [
                    'days' => 10,
                    'allEvents' => true,
                    'eventTypes' => [],
                ],
                'operator' => 'or',
            ]
        );

        $now = new DateTimeImmutable();

        $customerEmail1 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail1);
        $this->createCustomEvent(
            self::STORE_CODE,
            $customerEmail1,
            'calories',
            ['calories' => 2000],
            $now->modify('-11 days')->setTime(0, 0),
        );

        $customerEmail2 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail2);
        $this->createTransaction(
            self::STORE_CODE,
            $customerEmail2,
            $now->modify('-11 days')->setTime(0, 0),
            'document-1'
        );

        $customerEmail3 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail3);
        $this->createCustomEvent(
            self::STORE_CODE,
            $customerEmail3,
            'calories',
            ['calories' => 2000],
            $now->modify('-10 days')->setTime(0, 0),
        );

        $customerEmail4 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail4);
        $this->createTransaction(
            self::STORE_CODE,
            $customerEmail4,
            $now->modify('-10 days')->setTime(0, 0),
            'document-2'
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(2, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-11 days')->format('Y-m-d')]);
        $this->assertSame(2, $data['data'][$now->modify('-10 days')->format('Y-m-d')]);
        $this->assertSame(2, $data['data'][$now->format('Y-m-d')]);

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:customer:verify-members-activity');
        $commandTester = new CommandTester($command);

        $commandTester->execute(
            [
                'lastTransactionDate' => $now->modify('-1 days')->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(4, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-12 days')->format('Y-m-d')]);
        $this->assertSame(2, $data['data'][$now->modify('-11 days')->format('Y-m-d')]);
        $this->assertSame(4, $data['data'][$now->modify('-10 days')->format('Y-m-d')]);
        $this->assertSame(4, $data['data'][$now->format('Y-m-d')]);
    }

    /**
     * @test
     */
    public function it_deactivate_member_when_transaction_is_not_in_period(): void
    {
        $this->setStoreActiveMemberSetting(
            self::STORE_CODE,
            [
                'transactionInXDays' => 10,
                'customEventsInXDays' => [
                    'days' => 10,
                    'allEvents' => true,
                    'eventTypes' => [],
                ],
                'operator' => 'or',
            ]
        );

        $now = new DateTimeImmutable();

        $customerEmail1 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail1);
        $this->createTransaction(
            self::STORE_CODE,
            $customerEmail1,
            $now->modify('-11 days')->setTime(0, 0),
            'document-1'
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:customer:verify-members-activity');
        $commandTester = new CommandTester($command);

        $commandTester->execute(
            [
                'lastTransactionDate' => $now->modify('-1 days')->format('Y-m-d'),
                '--endPeriodDateTime' => $now->modify('-1 days')->setTime(23, 59, 59)->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(1, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-12 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-11 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->format('Y-m-d')]);

        $commandTester->execute(
            [
                'lastTransactionDate' => $now->format('Y-m-d'),
                '--endPeriodDateTime' => $now->setTime(23, 59, 59)->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-12 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-11 days')->format('Y-m-d')]);
        $this->assertSame(0, $data['data'][$now->format('Y-m-d')]);
    }

    /**
     * @test
     */
    public function it_deactivate_member_when_custom_event_is_not_in_period(): void
    {
        $this->setStoreActiveMemberSetting(
            self::STORE_CODE,
            [
                'transactionInXDays' => 10,
                'customEventsInXDays' => [
                    'days' => 10,
                    'allEvents' => true,
                    'eventTypes' => [],
                ],
                'operator' => 'or',
            ]
        );

        $now = new DateTimeImmutable();

        $customerEmail1 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail1);
        $this->createCustomEvent(
            self::STORE_CODE,
            $customerEmail1,
            'calories',
            ['calories' => 2000],
            $now->modify('-11 days')->setTime(0, 0),
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:customer:verify-members-activity');
        $commandTester = new CommandTester($command);

        $commandTester->execute(
            [
                'lastTransactionDate' => $now->modify('-1 days')->format('Y-m-d'),
                '--endPeriodDateTime' => $now->modify('-1 days')->setTime(23, 59, 59)->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(1, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-12 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-11 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->format('Y-m-d')]);

        $commandTester->execute(
            [
                'lastTransactionDate' => $now->format('Y-m-d'),
                '--endPeriodDateTime' => $now->setTime(23, 59, 59)->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-12 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-11 days')->format('Y-m-d')]);
        $this->assertSame(0, $data['data'][$now->format('Y-m-d')]);
    }

    /**
     * @test
     */
    public function it_not_deactivate_member_when_transaction_is_in_period(): void
    {
        $this->setStoreActiveMemberSetting(
            self::STORE_CODE,
            [
                'transactionInXDays' => 10,
                'customEventsInXDays' => [
                    'days' => 10,
                    'allEvents' => true,
                    'eventTypes' => [],
                ],
                'operator' => 'or',
            ]
        );

        $now = new DateTimeImmutable();

        $customerEmail1 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail1);
        $this->createCustomEvent(
            self::STORE_CODE,
            $customerEmail1,
            'calories',
            ['calories' => 2000],
            $now->modify('-12 days')->setTime(0, 0),
        );
        $this->createCustomEvent(
            self::STORE_CODE,
            $customerEmail1,
            'steps',
            ['steps' => 2000],
            $now->modify('-12 days')->setTime(0, 0),
        );
        $this->createTransaction(
            self::STORE_CODE,
            $customerEmail1,
            $now->modify('-11 days')->setTime(0, 0),
            'document-1'
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:customer:verify-members-activity');
        $commandTester = new CommandTester($command);

        $commandTester->execute(
            [
                'lastTransactionDate' => $now->modify('-2 days')->format('Y-m-d'),
                '--endPeriodDateTime' => $now->modify('-2 days')->setTime(23, 59, 59)->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(1, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-13 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-12 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->format('Y-m-d')]);

        $commandTester->execute(
            [
                'lastTransactionDate' => $now->modify('-1 days')->format('Y-m-d'),
                '--endPeriodDateTime' => $now->modify('-1 days')->setTime(23, 59, 59)->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(1, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-13 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-12 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->format('Y-m-d')]);

        $commandTester->execute(
            [
                'lastTransactionDate' => $now->format('Y-m-d'),
                '--endPeriodDateTime' => $now->setTime(23, 59, 59)->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-13 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-12 days')->format('Y-m-d')]);
        $this->assertSame(0, $data['data'][$now->format('Y-m-d')]);
    }

    /**
     * @test
     */
    public function it_not_deactivate_member_when_custom_event_is_in_period(): void
    {
        $this->setStoreActiveMemberSetting(
            self::STORE_CODE,
            [
                'transactionInXDays' => 10,
                'customEventsInXDays' => [
                    'days' => 10,
                    'allEvents' => true,
                    'eventTypes' => [],
                ],
                'operator' => 'or',
            ]
        );

        $now = new DateTimeImmutable();

        $customerEmail1 = '<EMAIL>';
        $this->createCustomer(self::STORE_CODE, $customerEmail1);
        $this->createCustomEvent(
            self::STORE_CODE,
            $customerEmail1,
            'calories',
            ['calories' => 2000],
            $now->modify('-12 days')->setTime(0, 0),
        );
        $this->createCustomEvent(
            self::STORE_CODE,
            $customerEmail1,
            'steps',
            ['steps' => 2000],
            $now->modify('-11 days')->setTime(0, 0),
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);

        $container = self::getContainer();
        $application = new Application($container->get(KernelInterface::class));
        $command = $application->find('oloy:customer:verify-members-activity');
        $commandTester = new CommandTester($command);

        $commandTester->execute(
            [
                'lastTransactionDate' => $now->modify('-2 days')->format('Y-m-d'),
                '--endPeriodDateTime' => $now->modify('-2 days')->setTime(23, 59, 59)->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(1, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-13 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-12 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->format('Y-m-d')]);

        $commandTester->execute(
            [
                'lastTransactionDate' => $now->modify('-1 days')->format('Y-m-d'),
                '--endPeriodDateTime' => $now->modify('-1 days')->setTime(23, 59, 59)->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(1, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-13 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-12 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->format('Y-m-d')]);

        $commandTester->execute(
            [
                'lastTransactionDate' => $now->format('Y-m-d'),
                '--endPeriodDateTime' => $now->setTime(23, 59, 59)->format('Y-m-d'),
            ]
        );

        $this->client->request(
            'GET',
            sprintf(
                '/api/'.self::STORE_CODE.'/analytics/dashboard/general-overview?dataType=%s&&intervalStartDate=%s&aggregationType=%s',
                'activeMembers',
                $now->modify('-30 days')->format('Y-m-d'),
                'day'
            )
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        $this->assertSame(0, $data['header']['activeMembers']);
        $this->assertSame(0, $data['data'][$now->modify('-13 days')->format('Y-m-d')]);
        $this->assertSame(1, $data['data'][$now->modify('-12 days')->format('Y-m-d')]);
        $this->assertSame(0, $data['data'][$now->format('Y-m-d')]);
    }
}
