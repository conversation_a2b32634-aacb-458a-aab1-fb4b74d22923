<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Integration\Ui\Rest\Member\HandlingDuplication;

use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\Settings\Integration\Traits\SettingsApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use OpenLoyalty\User\Domain\Provider\TierAssignTypeProvider;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class RegisterMemberDuplicationTest extends AbstractApiTest
{
    use MemberApiTrait;
    use TenantApiTrait;
    use SettingsApiTrait;

    private HttpKernelBrowser $client;
    private const TEST_EMAIL = '<EMAIL>';
    private const TEST_PHONE = '+48123456789';
    private const TEST_LOYALTY_CARD = 'DUP123456';
    private string $tenantCode = 'tenant_for_duplication_test';

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
        $this->postTenant($this->client, $this->tenantCode);
    }

    /**
     * @test
     */
    public function it_creating_members_with_duplicated_email_if_setting_allow_for_it(): void
    {
        $this->setSettingsIdentificationConfigs([
            [
                'priority' => 3,
                'field' => 'email',
                'unique' => false,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
            [
                'priority' => 2,
                'field' => 'loyaltyCardNumber',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => true,
            ],
            [
                'priority' => 1,
                'field' => 'phone',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
        ]);

        $this->postMember(
            $this->client,
            $this->tenantCode,
            self::TEST_EMAIL,
            loyaltyCardNumber: '111',
            phone: '111',
        );

        $this->postMember(
            $this->client,
            $this->tenantCode,
            self::TEST_EMAIL,
            loyaltyCardNumber: '222',
            phone: '222',
        );

        $this->assertCountOfMemberWith('email', self::TEST_EMAIL, 2);
    }

    /**
     * @test
     */
    public function it_creating_members_with_duplicated_phone_if_setting_allow_for_it(): void
    {
        $this->setSettingsIdentificationConfigs([
            [
                'priority' => 3,
                'field' => 'email',
                'unique' => true,
                'eventMatching' => true,
                'identificationMethod' => true,
            ],
            [
                'priority' => 2,
                'field' => 'loyaltyCardNumber',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
            [
                'priority' => 1,
                'field' => 'phone',
                'unique' => false,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
        ]);

        $this->postMember(
            $this->client,
            $this->tenantCode,
            '<EMAIL>',
            loyaltyCardNumber: '111',
            phone: self::TEST_PHONE,
        );

        $this->postMember(
            $this->client,
            $this->tenantCode,
            '<EMAIL>',
            loyaltyCardNumber: '222',
            phone: self::TEST_PHONE,
        );

        $this->assertCountOfMemberWith('phone', self::TEST_PHONE, 2);
    }

    /**
     * @test
     */
    public function it_creating_members_with_duplicated_loyalty_card_number_if_setting_allow_for_it(): void
    {
        $this->setSettingsIdentificationConfigs([
            [
                'priority' => 3,
                'field' => 'email',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => true,
            ],
            [
                'priority' => 2,
                'field' => 'loyaltyCardNumber',
                'unique' => false,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
            [
                'priority' => 1,
                'field' => 'phone',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
        ]);

        $this->postMember(
            $this->client,
            $this->tenantCode,
            '<EMAIL>',
            loyaltyCardNumber: self::TEST_LOYALTY_CARD,
            phone: '111',
        );

        $this->postMember(
            $this->client,
            $this->tenantCode,
            '<EMAIL>',
            loyaltyCardNumber: self::TEST_LOYALTY_CARD,
            phone: '222',
        );

        $this->assertCountOfMemberWith('loyaltyCardNumber', self::TEST_LOYALTY_CARD, 2);
    }

    /**
     * @test
     */
    public function it_prevents_from_adding_member_with_duplicated_email(): void
    {
        $this->setSettingsIdentificationConfigs([
            [
                'priority' => 3,
                'field' => 'email',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
            [
                'priority' => 2,
                'field' => 'loyaltyCardNumber',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => true,
            ],
            [
                'priority' => 1,
                'field' => 'phone',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
        ]);

        $this->postMember(
            $this->client,
            $this->tenantCode,
            self::TEST_EMAIL,
            loyaltyCardNumber: '111',
            phone: self::TEST_PHONE,
        );

        $response = $this->postMember(
            $this->client,
            $this->tenantCode,
            self::TEST_EMAIL,
            loyaltyCardNumber: '222',
            phone: self::TEST_PHONE,
        );
        $this->assertBadRequestResponseStatus($response);

        $this->assertSame('Customer with such email already exists.', json_decode($response->getContent(), true)['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_prevents_from_adding_member_with_duplicated_phone(): void
    {
        $this->setSettingsIdentificationConfigs([
            [
                'priority' => 3,
                'field' => 'email',
                'unique' => false,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
            [
                'priority' => 2,
                'field' => 'loyaltyCardNumber',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => true,
            ],
            [
                'priority' => 1,
                'field' => 'phone',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
        ]);

        $this->postMember(
            $this->client,
            $this->tenantCode,
            '<EMAIL>',
            loyaltyCardNumber: '111',
            phone: self::TEST_PHONE,
        );

        $response = $this->postMember(
            $this->client,
            $this->tenantCode,
            '<EMAIL>',
            loyaltyCardNumber: '222',
            phone: self::TEST_PHONE,
        );

        $this->assertBadRequestResponseStatus($response);

        $this->assertSame('Customer with such phone already exists.', json_decode($response->getContent(), true)['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_prevents_from_adding_member_with_duplicated_loyalty_card_number(): void
    {
        $this->setSettingsIdentificationConfigs([
            [
                'priority' => 3,
                'field' => 'email',
                'unique' => false,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
            [
                'priority' => 2,
                'field' => 'loyaltyCardNumber',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => true,
            ],
            [
                'priority' => 1,
                'field' => 'phone',
                'unique' => true,
                'eventMatching' => false,
                'identificationMethod' => false,
            ],
        ]);

        $this->postMember(
            $this->client,
            $this->tenantCode,
            '<EMAIL>',
            loyaltyCardNumber: self::TEST_LOYALTY_CARD,
            phone: '111',
        );

        $response = $this->postMember(
            $this->client,
            $this->tenantCode,
            '<EMAIL>',
            loyaltyCardNumber: self::TEST_LOYALTY_CARD,
            phone: '222',
        );

        $this->assertBadRequestResponseStatus($response);

        $this->assertSame('Customer with such loyalty card number already exists.', json_decode($response->getContent(), true)['errors'][0]['message']);
    }

    private function assertCountOfMemberWith(string $field, string $value, int $count): void
    {
        $this->client->request(
            'GET',
            sprintf('/api/%s/member?%s=%s', $this->tenantCode, $field, $value)
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertSame($count, $data['total']['all']);
    }

    private function setSettingsIdentificationConfigs(array $identificationConfigs): void
    {
        $requiredSettings = [
            'accountActivationRequired' => true,
            'activeMember' => [
                'transactionInXDays' => 10,
            ],
            'allowCustomersProfileEdits' => true,
            'customersIdentificationConfigurations' => $identificationConfigs,
            'excludeDeliveryCostsFromTierAssignment' => false,
            'excludedDeliverySKUs' => [],
            'excludedLevelCategories' => [],
            'excludedLevelSKUs' => [],
            'expireCouponsNotificationDays' => 1,
            'expireLevelsNotificationDays' => 1,
            'expirePointsNotificationDays' => 1,
            'identificationMethod' => 'email',
            'levelDowngradeBase' => 'active_points',
            'levelDowngradeMode' => 'none',
            'levelResetPointsOnDowngrade' => false,
            'logo' => [
                'path' => 'logo/default/logo.png',
                'mime' => 'image/png',
            ],
            'programName' => 'Open Loyalty',
            'programUrl' => '',
            'rewardWalletCode' => 'default',
            'tierAssignType' => TierAssignTypeProvider::TYPE_TRANSACTIONS,
            'tierWalletCode' => 'default',
            'tiersMode' => 'custom',
            'timezone' => 'Europe/Warsaw',
        ];

        $this->client->request(
            'PATCH',
            '/api/'.$this->tenantCode.'/settings',
            ['settings' => $requiredSettings]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
    }
}
