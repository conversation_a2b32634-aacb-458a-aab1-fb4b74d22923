<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Integration\Ui\Rest;

use OpenLoyalty\Core\Domain\Id\ChannelId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Import\Infrastructure\ImportResultItem;
use OpenLoyalty\Integration\Helpers\ResponseChecker;
use OpenLoyalty\Integration\Infrastructure\Utility\Traits\UploadedFileTrait;
use OpenLoyalty\Level\Infrastructure\DataFixtures\ORM\LoadLevelData;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Settings\Infrastructure\Model\Settings;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\Integration\Traits\TierSetApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;
use OpenLoyalty\User\Infrastructure\DataFixtures\ORM\LoadUserData;
use OpenLoyalty\User\Infrastructure\Entity\User;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * @covers \OpenLoyalty\User\Ui\Rest\Controller\Member\PostTier
 */
final class MemberControllerTest extends AbstractApiTest
{
    use TierSetApiTrait;
    use TenantApiTrait;
    use MemberApiTrait;
    use UploadedFileTrait;

    /**
     * @test
     */
    public function it_allows_to_set_customer_level_manually(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s/tier', LoadUserData::USER_USER_ID),
            [
                'levelId' => LoadLevelData::LEVEL2_ID,
            ]
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_allows_to_set_customer_level_manually_by_email(): void
    {
        $client = self::createAuthenticatedClient();

        //Create tenant
        $tenantCode = 'setMemberToTierTenantCode';
        $response = $this->postTenant($client, $tenantCode);
        $this->assertOkResponseStatus($response);

        //Create member
        $response = $this->postMember($client, $tenantCode, '<EMAIL>');
        $this->assertOkResponseStatus($response);

        //Create tierSet
        $response = $this->postTierSet($client, $tenantCode);
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $tierSetId = $data['tierSetId'];

        $client->request(
            'GET',
            sprintf('/api/%s/tierSet/%s/tiers?name=Default', $tenantCode, $tierSetId),
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $tierId = $data['items'][0]['levelId'];

        $client->jsonRequest(
            'POST',
            sprintf('/api/%s/member/%s/tier', $tenantCode, 'email=<EMAIL>'),
            [
                'levelId' => $tierId,
            ]
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_allows_to_remove_customer_level_manually(): void
    {
        $client = self::createAuthenticatedClient();

        $response = $this->postMemberToTier(
            $client,
            LoadSettingsData::DEFAULT_STORE_CODE,
            LoadUserData::USER_USER_ID,
            LoadLevelData::LEVEL2_ID
        );

        $this->assertNoContentResponseStatus($response);

        $client->jsonRequest(
            'POST',
            sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s/remove-manually-level', LoadUserData::USER_USER_ID)
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_does_not_allow_removing_level_manually_when_it_is_not_manually_assigned(): void
    {
        $client = self::createAuthenticatedClient();

        $response = $this->postMemberToTier(
            $client,
            LoadSettingsData::DEFAULT_STORE_CODE,
            LoadUserData::USER_USER_ID,
            LoadLevelData::LEVEL2_ID
        );

        $this->assertNoContentResponseStatus($response);

        $client->jsonRequest(
            'POST',
            sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s/remove-manually-level', LoadUserData::USER_USER_ID)
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $client->jsonRequest(
            'POST',
            sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s/remove-manually-level', LoadUserData::USER_USER_ID)
        );

        $response = $client->getResponse();

        $this->assertBadRequestResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_allows_to_register_new_customer(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'birthDate' => '1990-01-01',
                    'phone' => '+41998273729',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                    'loyaltyCardNumber' => '**********',
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('customerId', $data);
        $this->assertArrayHasKey('email', $data);
    }

    /**
     * @test
     */
    public function it_allows_to_register_new_customer_with_referrer_token(): void
    {
        $client = self::createAuthenticatedClient();

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER1_USER_ID
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('referralToken', $data);

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/register',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'plainPassword' => '123qwe!@#QWE',
                    'agreement1' => true,
                    'referrerToken' => $data['referralToken'],
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('customerId', $data);

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$data['customerId']
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('referrerCustomerId', $data);
        $this->assertSame(LoadUserData::USER1_USER_ID, $data['referrerCustomerId']);
    }

    /**
     * @test
     */
    public function it_register_customer_with_loyalty_card_number_birth_date(): void
    {
        $loyaltyCardNumber = '1234AAAB';
        $birthDate = '2000-10-10';
        $phone = '500230203';
        $email = '<EMAIL>';
        $client = self::createAuthenticatedClient();

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/register',
            [
                'customer' => [
                    'email' => $email,
                    'plainPassword' => '123qwe!@#QWE',
                    'agreement1' => true,
                    'loyaltyCardNumber' => $loyaltyCardNumber,
                    'birthDate' => $birthDate,
                    'phone' => $phone,
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('customerId', $data);

        $customerId = new CustomerId($data['customerId']);
        $userRepository = self::getContainer()->get(CustomerRepositoryInterface::class);
        $user = $userRepository->getById($customerId);

        $this->assertSame($loyaltyCardNumber, $user->getLoyaltyCardNumber());
        $this->assertSame(strtolower($email), $user->getEmail());
        $this->assertEquals(new \DateTimeImmutable($birthDate), $user->getBirthDate());
        $this->assertSame($phone, $user->getPhone());
        $this->assertNotNull($user->getReferralToken());

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId,
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertSame($loyaltyCardNumber, $data['loyaltyCardNumber']);
        $this->assertSame(strtolower($email), $data['email']);
        $this->assertEquals(new \DateTimeImmutable($birthDate), new \DateTimeImmutable($data['birthDate']));
        $this->assertSame($phone, $data['phone']);
    }

    /**
     * @test
     */
    public function it_update_customer_phone(): void
    {
        $newPhone = '+48123451000';
        $client = self::createAuthenticatedClient();
        $client->request(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER4_USER_ID,
            [
                'customer' => [
                    'phone' => $newPhone,
                    'email' => LoadUserData::USER4_USERNAME,
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);

        $customerId = new CustomerId($data['customerId']);
        $userRepository = self::getContainer()->get(CustomerRepositoryInterface::class);
        $user = $userRepository->getById($customerId);

        $this->assertSame($newPhone, $user->getPhone());

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER4_USER_ID,
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertSame($newPhone, $data['phone']);
    }

    /**
     * @test
     */
    public function it_update_customer_email_with_uppercase(): void
    {
        $newEmail = '<EMAIL>';
        $client = self::createAuthenticatedClient();
        $client->request(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER4_USER_ID,
            [
                'customer' => [
                    'email' => $newEmail,
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);

        $customerId = new CustomerId($data['customerId']);
        $userRepository = self::getContainer()->get(CustomerRepositoryInterface::class);
        $user = $userRepository->getById($customerId);

        $this->assertSame(strtolower($newEmail), $user->getEmail());

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER4_USER_ID,
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertSame(strtolower($newEmail), $data['email']);
    }

    /**
     * @test
     */
    public function it_create_user_with_all_active_wallets(): void
    {
        $client = self::createAuthenticatedClient();

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/walletType?_itemsOnPage=50',
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $allWallets = $data['total']['all'];

        $loyaltyCardNumber = '1234AAABACS';
        $birthDate = '2000-10-10';
        $phone = '500230212';
        $email = '<EMAIL>';

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/register',
            [
                'customer' => [
                    'email' => $email,
                    'plainPassword' => '123qwe!@#QWE',
                    'agreement1' => true,
                    'loyaltyCardNumber' => $loyaltyCardNumber,
                    'birthDate' => $birthDate,
                    'phone' => $phone,
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $customerId = $data['customerId'];

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId.'/wallet?_itemsOnPage=50',
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $memberWallets = $data['total']['all'];

        self::assertSame($allWallets, $memberWallets);
    }

    /**
     * @test
     */
    public function it_update_customer_loyalty_card_number_birth_date(): void
    {
        $loyaltyCardNumber = '123ABC';
        $birthDate = '2001-10-10';

        $client = self::createAuthenticatedClient();
        $client->request(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER4_USER_ID,
            [
                'customer' => [
                    'email' => LoadUserData::USER4_USERNAME,
                    'loyaltyCardNumber' => $loyaltyCardNumber,
                    'birthDate' => $birthDate,
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);

        $customerId = new CustomerId($data['customerId']);
        $userRepository = self::getContainer()->get(CustomerRepositoryInterface::class);
        $user = $userRepository->getById($customerId);

        $this->assertSame($loyaltyCardNumber, $user->getLoyaltyCardNumber());
        $this->assertEquals(new \DateTimeImmutable($birthDate), $user->getBirthDate());
    }

    /**
     * @test
     */
    public function it_does_not_allow_to_register_new_customer_with_existing_phone_number(): void
    {
        $client = self::createAuthenticatedClient();
        $defaultCustomerData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'gender' => 'male',
            'birthDate' => '1990-01-01',
            'phone' => '48234234000',
            'agreement1' => true,
            'agreement2' => true,
        ];

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => $defaultCustomerData,
            ]
        );

        $response = $client->getResponse();
        $this->assertBadRequestResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_does_not_allows_to_register_new_customer_with_invalid_birth_date(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'birthDate' => '1990-0-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                    'loyaltyCardNumber' => '**********',
                ],
            ],
            $this->addDisableOpenApiValidationHeader()
        );

        $response = $client->getResponse();

        $this->assertBadRequestResponseStatus($response);
    }

    /**
     * @test
     * @dataProvider emailAndGenderDataProvider
     */
    public function it_allows_to_register_new_customer_with_email_and_gender(string $email, string $gender): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => $email,
                    'gender' => $gender,
                    'agreement1' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('customerId', $data);
        $this->assertArrayHasKey('email', $data);

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$data['customerId'],
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('gender', $data);
        $this->assertSame($email, $data['email']);
        $this->assertSame($gender, $data['gender']);
    }

    public function emailAndGenderDataProvider(): array
    {
        return [
            ['<EMAIL>', 'male'],
            ['<EMAIL>', 'female'],
            ['<EMAIL>', 'not_disclosed'],
            ['说説äöüß@example.com', 'not_disclosed'],
        ];
    }

    /**
     * @test
     */
    public function it_allows_to_register_new_customer_with_labels(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'birthDate' => '1990-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'labels' => [
                        [
                            'key' => 'l1',
                            'value' => 'v1',
                        ],
                        [
                            'key' => 'l2',
                            'value' => 'v2',
                        ],
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                    'loyaltyCardNumber' => '1000000011',
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('customerId', $data);
        $this->assertArrayHasKey('email', $data);

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$data['customerId'],
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('labels', $data);

        $labels = $data['labels'];
        $this->assertCount(2, $labels);
        $this->assertSame('v1', $labels[0]['value']);
        $this->assertSame('l1', $labels[0]['key']);
        $this->assertSame('v2', $labels[1]['value']);
        $this->assertSame('l2', $labels[1]['key']);
    }

    /**
     * @test
     */
    public function it_register_and_assert_customer_data(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'phone' => '*********',
                    'gender' => 'male',
                    'birthDate' => '1990-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'company' => [
                        'name' => 'company name',
                        'nip' => '123412312331',
                    ],
                    'labels' => [
                        [
                            'key' => 'l1',
                            'value' => 'v1',
                        ],
                        [
                            'key' => 'l2',
                            'value' => 'v2',
                        ],
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                    'agreement3' => true,
                    'loyaltyCardNumber' => '10200000011',
                    'levelId' => LoadLevelData::LEVEL0_ID,
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('customerId', $data);
        $this->assertArrayHasKey('email', $data);

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$data['customerId'],
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertSame('John', $data['firstName']);
        $this->assertSame('Doe', $data['lastName']);
        $this->assertSame('<EMAIL>', $data['email']);
        $this->assertSame('*********', $data['phone']);
        $this->assertSame('male', $data['gender']);
        $this->assertSame('1990-01-01T00:00:00+01:00', $data['birthDate']);
        $address = $data['address'];
        $this->assertSame('Bagno', $address['street']);
        $this->assertSame('12', $address['address1']);
        $this->assertSame('00-800', $address['postal']);
        $this->assertSame('Warszawa', $address['city']);
        $this->assertSame('PL', $address['country']);
        $this->assertSame('mazowieckie', $address['province']);
        $company = $data['company'];
        $this->assertSame('company name', $company['name']);
        $this->assertSame('123412312331', $company['nip']);
        $labels = $data['labels'];
        $this->assertCount(2, $labels);
        $this->assertSame('v1', $labels[0]['value']);
        $this->assertSame('l1', $labels[0]['key']);
        $this->assertSame('v2', $labels[1]['value']);
        $this->assertSame('l2', $labels[1]['key']);
        $this->assertSame('10200000011', $data['loyaltyCardNumber']);
        $this->assertSame(true, $data['agreement1']);
        $this->assertSame(true, $data['agreement2']);
        $this->assertSame(true, $data['agreement3']);
        $this->assertSame(LoadLevelData::LEVEL0_ID, $data['currentLevel']['levelId']);
    }

    /**
     * @test
     */
    public function it_allows_to_register_new_customer_by_themselves(): void
    {
        $client = self::createClient();

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/register',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'birthDate' => '1990-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                    'plainPassword' => 'OpenLoyalty123!',
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('customerId', $data);
        $this->assertArrayHasKey('email', $data);
    }

    /**
     * @test
     */
    public function it_allows_to_register_new_customer_with_only_required_data_and_some_data_in_address(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'agreement1' => true,
                    'address' => [
                        'street' => 'Bagno',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                    ],
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('customerId', $data);
        $this->assertArrayHasKey('email', $data);
    }

    /**
     * @test
     */
    public function it_properly_validates_address(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'birthDate' => '1990-01-01',
                    'agreement1' => true,
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'country' => 'NOT_EXISTED_COUNTRY',
                        'province' => 'mazowieckie',
                    ],
                    'loyaltyCardNumber' => '**********',
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertBadRequestResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_allows_to_register_new_customer_without_certain_fields(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'gender' => 'male',
                    'email' => '<EMAIL>',
                    'agreement1' => true,
                ],
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('customerId', $data);
        $this->assertArrayHasKey('email', $data);
    }

    /**
     * @test
     */
    public function it_does_not_allow_to_register_new_customer_with_the_same_email(): void
    {
        $client = self::createAuthenticatedClient();

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'birthDate' => '1990-01-01',
                    'phone' => '+41998273729',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                    'loyaltyCardNumber' => '**********',
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'agreement1' => true,
                    'gender' => 'male',
                    'birthDate' => '1990-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'loyaltyCardNumber' => '000000000',
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertBadRequestResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_does_not_allow_to_register_new_customer_with_the_same_loyalty_card_number(): void
    {
        $client = self::createAuthenticatedClient();

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'birthDate' => '1990-01-01',
                    'phone' => '+41998273729',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                    'loyaltyCardNumber' => '**********',
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'agreement1' => true,
                    'gender' => 'male',
                    'birthDate' => '1990-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'loyaltyCardNumber' => '**********',
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertBadRequestResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_does_not_allow_to_register_new_customer_with_the_same_phone(): void
    {
        $client = self::createAuthenticatedClient();

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'birthDate' => '1990-01-01',
                    'phone' => '+41998273729',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                    'loyaltyCardNumber' => '0000000033',
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'John',
                    'lastName' => 'Doe',
                    'email' => '<EMAIL>',
                    'agreement1' => true,
                    'gender' => 'male',
                    'birthDate' => '1990-01-01',
                    'address' => [
                        'street' => 'Bagno',
                        'address1' => '12',
                        'postal' => '00-800',
                        'city' => 'Warszawa',
                        'country' => 'PL',
                        'province' => 'mazowieckie',
                    ],
                    'phone' => '+41998273729',
                    'loyaltyCardNumber' => '**********',
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertBadRequestResponseStatus($response);
    }

    public function getCustomersData(): array
    {
        return [
            [['labels' => 'test:testvalue;test2:test2value']],
            [['firstName' => 'Jane']],
            [['lastName' => 'de Novo']],
            [['email' => '<EMAIL>', 'phone' => '+443340000000']],
            [[
                'address' => [
                    'city' => 'London',
                    'street' => 'Baker St',
                ],
                'company' => [
                    'name' => 'X',
                    'nip' => '0000000000',
                ],
            ]],
        ];
    }

    /**
     * @test
     */
    public function it_assigns_level_to_customer_and_changes_customers_in_level_list(): void
    {
        $client = self::createAuthenticatedClient();

        // Check if user is on customers-belonging-to-a-tier list
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member?_itemsOnPage=50&levelId='.LoadLevelData::LEVEL0_ID,
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $cids = array_map(function ($customer) { return $customer['customerId']; }, $data['items']);
        $this->assertTrue(in_array(LoadUserData::USER_USER_ID, $cids));

        // Assign level manually
        $response = $this->postMemberToTier(
            $client,
            LoadSettingsData::DEFAULT_STORE_CODE,
            LoadUserData::USER1_USER_ID,
            LoadLevelData::LEVEL3_ID
        );

        $this->assertNoContentResponseStatus($response);

        // Check if user is not on the old level's list anymore
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member?_itemsOnPage=50&levelId='.LoadLevelData::LEVEL1_ID,
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $cids = array_map(function ($customer) { return $customer['customerId']; }, $data['items']);
        $this->assertFalse(in_array(LoadUserData::USER1_USER_ID, $cids));
    }

    /**
     * @test
     */
    public function it_allows_to_edit_customer_level(): void
    {
        $customerData = [];
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER1_USER_ID,
        );

        $this->allowCustomerToEditProfileSettings(true);

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $customerData['firstName'] = $data['firstName'];
        $customerData['lastName'] = $data['lastName'];
        $customerData['agreement1'] = true;
        $customerData['email'] = $data['email'];
        $customerData['phone'] = $data['phone'];
        $customerData['levelId'] = LoadLevelData::LEVEL3_ID;
        $client->request(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER1_USER_ID,
            [
                'customer' => $customerData,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER1_USER_ID,
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertSame($customerData['levelId'], $data['currentLevel']['levelId']);
        $this->assertSame($customerData['levelId'], $data['manuallyAssignedLevelId']);

        $this->allowCustomerToEditProfileSettings(false);
    }

    /**
     * @test
     */
    public function it_allows_to_save_edited_customer_details_with_empty_phone_number(): void
    {
        $userId = '22222222-0000-474c-b092-b0dd880c07e2';
        $client = self::createAuthenticatedClient();
        $this->allowCustomerToEditProfileSettings(true);

        $customerData = [
            'email' => '<EMAIL>',
            'birthDate' => '1998-02-02',
            'phone' => '',
            'firstName' => 'Jane',
            'lastName' => 'Done',
            'gender' => 'male',
            'address' => ['street' => 'Street'],
            'agreement1' => true,
        ];

        $apiPath = sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s', $userId);
        $client->request(
            'PUT',
            $apiPath,
            [
                'customer' => $customerData,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->request('GET', $apiPath);

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayNotHasKey('phone', $data);
        $this->allowCustomerToEditProfileSettings(false);
    }

    /**
     * @test
     */
    public function it_allows_to_update_phone_number_without_plus(): void
    {
        $userId = '22222222-0000-474c-b092-b0dd880c07e2';
        $client = self::createAuthenticatedClient();
        $this->allowCustomerToEditProfileSettings(true);

        $customerData = [
            'email' => '<EMAIL>',
            'birthDate' => '1998-02-02',
            'phone' => '123123123',
            'firstName' => 'Jane',
            'lastName' => 'Done',
            'gender' => 'male',
            'address' => ['street' => 'Street'],
            'agreement1' => true,
        ];

        $apiPath = sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s', $userId);
        $client->request(
            'PUT',
            $apiPath,
            [
                'customer' => $customerData,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->request('GET', $apiPath);

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertSame('123123123', $data['phone']);
        $this->allowCustomerToEditProfileSettings(false);
    }

    /**
     * @test
     */
    public function it_allows_to_update_first_name_and_last_name_with_empty_values(): void
    {
        $userId = '22222222-0000-474c-b092-b0dd880c07e2';
        $client = self::createAuthenticatedClient();
        $this->allowCustomerToEditProfileSettings(true);

        $customerData = [
            'email' => '<EMAIL>',
            'phone' => '123123123',
            'firstName' => '',
            'lastName' => '',
        ];

        $apiPath = sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s', $userId);
        $client->request(
            'PUT',
            $apiPath,
            [
                'customer' => $customerData,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->request('GET', $apiPath);

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertSame('', $data['firstName']);
        $this->assertSame('', $data['lastName']);
    }

    /**
     * @test
     */
    public function it_allows_to_update_customer_with_his_same_phone_number(): void
    {
        $userId = '22222222-0000-474c-b092-b0dd880c07e2';
        $client = self::createAuthenticatedClient();
        $customerData = [
            'email' => '<EMAIL>',
            'birthDate' => '1998-02-02',
            'phone' => '48123456789',
            'firstName' => 'Jane',
            'lastName' => 'Done',
            'gender' => 'male',
            'agreement1' => true,
        ];

        $client->request(
            'PUT',
            sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s', $userId),
            [
                'customer' => $customerData,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_does_not_allow_to_update_customer_with_phone_number_of_another_customer(): void
    {
        $userId = '22222222-0000-474c-b092-b0dd880c07e2';
        $client = self::createAuthenticatedClient();
        $this->allowCustomerToEditProfileSettings(true);

        $customerData = [
            'email' => '<EMAIL>',
            'birthDate' => '1998-02-02',
            'phone' => '+48456457000',
            'firstName' => 'Jane',
            'lastName' => 'Done',
            'gender' => 'male',
            'agreement1' => true,
        ];

        $client->request(
            'PUT',
            sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s', $userId),
            [
                'customer' => $customerData,
            ]
        );

        $response = $client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $this->allowCustomerToEditProfileSettings(false);
    }

    /**
     * @test
     */
    public function it_allows_to_get_customer_details(): void
    {
        $client = self::createAuthenticatedClient();

        $user = self::getContainer()->get('doctrine')->getManager()
            ->getRepository(User::class)->findOneBy([
                'email' => '<EMAIL>',
                'store' => new StoreId(LoadSettingsData::DEFAULT_STORE_ID),
            ]);
        $id = $user->getId();

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$id,
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertSame('John', $data['firstName']);
        $this->assertSame('male', $data['gender']);
    }

    /**
     * @test
     */
    public function it_allows_to_get_customer_details_with_locale(): void
    {
        $client = self::createAuthenticatedClient();

        $user = self::getContainer()->get('doctrine')->getManager()
            ->getRepository(User::class)->findOneBy([
                'email' => '<EMAIL>',
                'store' => new StoreId(LoadSettingsData::DEFAULT_STORE_ID),
            ]);
        $id = $user->getId();

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$id.'?_locale=pl',
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertSame('poziom0', $data['currentLevel']['name']);
    }

    /**
     * @test
     */
    public function it_allows_to_get_customers_list(): void
    {
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member'
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
    }

    /**
     * @test
     * @dataProvider getEmailOrPhoneCheckDataProvider
     */
    public function it_allows_to_check_if_email_or_phone_number_exists(string $emailOrPhone, int $expected): void
    {
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/check?emailOrPhone='.urlencode($emailOrPhone),
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('total', $data);
        $this->assertSame($expected, $data['total']);
    }

    /**
     * @test
     * @dataProvider getIdentifierCheckDataProvider
     */
    public function it_check_if_identifier_exists(string $identifier, int $expected): void
    {
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/check?identifier='.urlencode($identifier),
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('total', $data);
        $this->assertSame($expected, $data['total']);
    }

    /**
     * @test
     */
    public function it_return_empty_data_for_invalid_identifier(): void
    {
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/check?identifier[eq]='.urlencode('test_value'),
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('total', $data);
        $this->assertSame(0, $data['total']);
    }

    /**
     * @test
     */
    public function it_allows_to_check_if_email_or_phone_number_exists_with_invalid_request(): void
    {
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/check?emailOrPhone[eq]='.urlencode('test_value'),
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('total', $data);
        $this->assertSame(0, $data['total']);
    }

    /**
     * @test
     */
    public function email_or_phone_parameter_allows_search_by_customer_id(): void
    {
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member?emailOrPhone=00000000-0000-474c-b092-b0dd880c07e1',
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertSame(1, $data['total']['filtered']);
    }

    /**
     * @test
     */
    public function it_allows_to_get_customers_list_with_locale(): void
    {
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member?_locale=pl'
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertGreaterThan(0, count(array_filter(
            $data['items'],
            function ($item) { return 'poziom0' === $item['currentLevel']['name']; }
        )));
    }

    /**
     * @test
     */
    public function it_allows_to_assign_channel_to_customer(): void
    {
        $client = self::createAuthenticatedClient();
        $channelId = new ChannelId('00000000-0000-474c-1111-b0dd880c07e3');

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::TEST_USER_ID.'/channel',
            [
                'channelId' => (string) $channelId,
            ]
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::TEST_USER_ID,
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('channelId', $data);
        $this->assertSame((string) $channelId, $data['channelId'], json_encode($data));
    }

    /**
     * @test
     */
    public function it_allows_to_assign_channel_to_customer_by_email(): void
    {
        $client = self::createAuthenticatedClient();
        $channelId = new ChannelId('00000000-0000-474c-1111-b0dd880c07e3');

        //Create tenant
        $tenantCode = 'memberChannelTenantCode';
        $response = $this->postTenant($client, $tenantCode);
        $this->assertOkResponseStatus($response);

        //Create member
        $response = $this->postMember($client, $tenantCode, '<EMAIL>');
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $client->request(
            'POST',
            sprintf('/api/%s/member/email=%s/channel', $tenantCode, '<EMAIL>'),
            [
                'channelId' => (string) $channelId,
            ]
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $client->request(
            'GET',
            sprintf('/api/%s/member/%s', $tenantCode, $customerId),
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('channelId', $data);
        $this->assertSame((string) $channelId, $data['channelId'], json_encode($data));
    }

    /**
     * @test
     */
    public function it_allows_self_register_customer_with_minimal_set_of_data(): void
    {
        $client = self::createAuthenticatedClient();

        /* Create new customer with marketing agreement */
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/register',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'agreement1' => true,
                    'plainPassword' => 'OpenLoyalty123!',
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_allows_register_customer_with_minimal_set_of_data(): void
    {
        $client = self::createAuthenticatedClient();

        /* Create new customer with marketing agreement */
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_adds_points_to_customer_after_account_edit_and_agreement2_is_checked(): void
    {
        $client = self::createAuthenticatedClient();

        $customerId = LoadUserData::TEST_USER_ID;
        $points = $this->getCustomerActivePoints($customerId);

        //Update customer data with checked agreement2
        $client->request('GET', sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s', $customerId));

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $customerData = json_decode($response->getContent(), true);

        $this->assertSame(false, $customerData['agreement2'], 'Agreement2 should be false');

        $client->request(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId,
            [
                'customer' => [
                    'firstName' => $customerData['firstName'],
                    'lastName' => $customerData['lastName'],
                    'email' => $customerData['email'],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        //Check customer points
        $expectedPoints = $points;
        $this->assertSame($expectedPoints, $this->getCustomerActivePoints($customerId));
    }

    /**
     * @test
     */
    public function it_does_not_add_points_after_customer_sets_agreement2_to_false(): void
    {
        $client = self::createAuthenticatedClient();
        $customerId = LoadUserData::TEST_USER_ID;
        $points = $this->getCustomerActivePoints($customerId);

        //Update customer data with checked agreement2
        $client->request('GET', sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s', $customerId));

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $customerData = json_decode($response->getContent(), true);

        $client->request(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId,
            [
                'customer' => [
                    'firstName' => $customerData['firstName'],
                    'lastName' => $customerData['lastName'],
                    'email' => $customerData['email'],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        //Update customer data with checked agreement2
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId,
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $customerData = json_decode($response->getContent(), true);
        $this->assertSame(true, $customerData['agreement2'], 'Agreement2 should be true');

        $client->request(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId,
            [
                'customer' => [
                    'firstName' => $customerData['firstName'],
                    'lastName' => $customerData['lastName'],
                    'email' => $customerData['email'],
                    'agreement1' => true,
                    'agreement2' => 0,
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        //Check member points
        $this->assertSame($points, $this->getCustomerActivePoints($customerId));
    }

    /**
     * @test
     */
    public function it_does_not_add_points_after_2nd_attempt_to_subscribe_to_newsletter(): void
    {
        $client = self::createAuthenticatedClient();

        $customerId = '00000000-0000-474c-b092-b0dd880c07e2';
        $points = $this->getCustomerActivePoints($customerId);

        //Update member data with checked agreement2
        $client->request('GET', sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s', $customerId));
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $customerData = json_decode($response->getContent(), true);
        $this->assertSame(false, $customerData['agreement2'], 'Agreement2 should be false');

        $client->request(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId,
            [
                'customer' => [
                    'firstName' => $customerData['firstName'],
                    'lastName' => $customerData['lastName'],
                    'email' => $customerData['email'],
                    'agreement1' => true,
                    'agreement2' => true,
                ],
            ]
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        //Check member points
        $this->assertSame($points, $this->getCustomerActivePoints($customerId));
    }

    /**
     * @test
     */
    public function it_allows_to_order_by(): void
    {
        $request = [];
        $client = self::createAuthenticatedClient();

        $request['_orderBy']['email'] = 'asc';
        $client->jsonRequest('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member', $request);

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertNotCount(0, $data['items'], 'No items found.');

        $request['_orderBy']['firstName'] = 'asc';
        $client->jsonRequest('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member', $request);

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertNotCount(0, $data['items'], 'No items found.');
    }

    /**
     * @test
     *
     * @dataProvider getPartialPhrases
     */
    public function it_allows_to_get_customers_list_filtered_by_partial_phrase(
        string $field,
        string $phrase
    ): void {
        $request = [];
        $client = self::createAuthenticatedClient();

        $request[$field]['like'] = $phrase;
        $client->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member', $request);

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertNotCount(0, $data['items'], 'No items found.');

        foreach ($data['items'] as $customer) {
            $this->assertTrue(
                array_key_exists($field, $customer),
                sprintf('Field %s does not exists', $field)
            );
            $this->assertTrue(
                str_contains((string) $customer[$field], $phrase),
                sprintf('Searching phrase %s but found %s', $phrase, $customer[$field])
            );
        }
    }

    public function getPartialPhrases(): array
    {
        return [
            ['firstName', 'Jo'],
            ['firstName', '1'],
            ['firstName', 'John1'],
            ['lastName', 'Doe'],
            ['lastName', 'Doe1'],
            ['lastName', 'Smith'],
            ['phone', '48'],
            ['phone', '645'],
            ['email', '@'],
            ['email', 'user-1'],
            ['loyaltyCardNumber', '000000'],
        ];
    }

    /**
     * @test
     *
     * @dataProvider getStrictPhrases
     */
    public function it_allows_to_get_customers_list_filtered_by_strict_phrase(
        string $field,
        string $phrase,
        bool $shouldBeResults
    ): void {
        $client = self::createAuthenticatedClient();

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [$field => $phrase]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        if ($shouldBeResults) {
            $this->assertNotCount(0, $data['items'], 'No items found.');
        }

        foreach ($data['items'] as $customer) {
            $this->assertArrayHasKey($field, $customer, 'Field '.$field.' does not exists');
            $this->assertStringContainsString(
                $phrase,
                (string) $customer[$field],
                'Searching phrase '.$phrase.' but found '.$customer[$field]
            );
        }
    }

    public function getStrictPhrases(): array
    {
        return [
            ['firstName', 'John', true],
            ['firstName', '1', false],
            ['firstName', 'John1', true],
            ['lastName', 'Doe', true],
            ['lastName', 'Doe1', true],
            ['lastName', '1', false],
            ['phone', '48', false],
            ['phone', '+48456456000', true],
            ['email', '@', false],
            ['email', 'user-1', false],
            ['email', '<EMAIL>', true],
        ];
    }

    /**
     * @test
     *
     * @dataProvider getRangeFilters
     */
    public function it_allows_to_get_customers_list_filtered_by_range_filters(
        string $field,
        ?float $lower,
        ?float $upper
    ): void {
        $client = self::createAuthenticatedClient();

        $request = [];
        if (!is_null($lower)) {
            $request[$field]['gte'] = $lower;
        }
        if (!is_null($upper)) {
            $request[$field]['lte'] = $upper;
        }

        $client->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member', $request);

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertNotEmpty($data['items'], 'Response should have at least one matching member');

        foreach ($data['items'] as $customer) {
            $this->assertTrue(
                array_key_exists($field, $customer),
                sprintf('Field %s does not exists', $field)
            );
            $this->assertTrue(
                (is_null($lower) || $customer[$field] >= $lower) && (is_null($upper) || $customer[$field] <= $upper),
                sprintf('Looking for value in range [%s, %s] but found %d', $lower ?? '~', $upper ?? '~', $customer[$field])
            );
        }
    }

    public function getRangeFilters(): array
    {
        return [
            ['transactionsAmount', null, 2],
            ['transactionsAmount', 15, null],
            ['transactionsAmount', 0, 10],
            ['averageTransactionAmount', null, 3],
            ['averageTransactionAmount', 25, null],
            ['averageTransactionAmount', 180, 1700],
            ['transactionsCount', 3, 5],
            ['transactionsCount', 0, 0],
        ];
    }

    /**
     * @test
     *
     * @dataProvider getEmailOrPhone
     */
    public function it_allows_to_get_customers_list_filtered_by_email_or_phone(
        string $field,
        string $phrase,
        ?array $columns = null
    ): void {
        $client = self::createAuthenticatedClient();

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [$field => $phrase]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertNotCount(0, $data['items'], 'No items found.');

        foreach ($data['items'] as $customer) {
            $result = false;
            foreach ($columns as $column) {
                $this->assertTrue(
                    array_key_exists($column, $customer),
                    'Field '.$column.' does not exists'
                );

                $result = $result || str_contains($customer[$column], $phrase);
            }

            $this->assertTrue($result, sprintf('Searching phrase %s not found', $phrase));
        }
    }

    /**
     * @test
     */
    public function it_imports_customers(): void
    {
        $xmlContent = file_get_contents(__DIR__.'/../../../Resources/fixtures/import.xml');

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/import',
            [],
            [
                'file' => [
                    'file' => $this->createUploadedFile($xmlContent, 'import.xml', 'application/xml', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);

        $this->assertArrayHasKey('items', $data);
        $this->assertCount(1, $data['items']);
        $this->assertArrayHasKey('status', $data['items'][0]);
        $this->assertTrue(ImportResultItem::SUCCESS == $data['items'][0]['status']);
    }

    /**
     * @test
     */
    public function it_gets_customer_status_as_an_administrator(): void
    {
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER_USER_ID.'/status',
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);

        $this->assertArrayHasKey('firstName', $data);
        $this->assertArrayHasKey('lastName', $data);
        $this->assertArrayHasKey('customerId', $data);
        $this->assertArrayHasKey('activePoints', $data);
        $this->assertArrayHasKey('earnedPoints', $data);
        $this->assertArrayHasKey('spentPoints', $data);
        $this->assertArrayHasKey('expiredPoints', $data);
        $this->assertArrayHasKey('lockedPoints', $data);
        $this->assertArrayHasKey('levelName', $data);
        $this->assertArrayHasKey('levelConditionValue', $data);
        $this->assertArrayHasKey('nextLevelName', $data);
        $this->assertArrayHasKey('nextLevelConditionValue', $data);
        $this->assertArrayHasKey('transactionsAmountWithoutDeliveryCosts', $data);
        $this->assertArrayHasKey('averageTransactionsAmount', $data);
        $this->assertArrayHasKey('averageReturnTransactionsAmount', $data);
        $this->assertArrayHasKey('transactionsCount', $data);
        $this->assertArrayHasKey('returnTransactionsCount', $data);
        $this->assertArrayHasKey('transactionsAmount', $data);
        $this->assertArrayHasKey('returnTransactionsAmount', $data);
        $this->assertArrayHasKey('currency', $data);
        $this->assertArrayHasKey('pointsExpiringNextMonth', $data);
    }

    /**
     * @test
     */
    public function it_gets_the_same_account_values_in_the_customer_status_and_details(): void
    {
        $client = self::createAuthenticatedClient();
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER_USER_ID.'/status',
        );
        $response = $client->getResponse();
        $customerStatus = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER_USER_ID,
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $customerDetails = json_decode($response->getContent(), true);

        $this->assertSame($customerStatus['customerId'], $customerDetails['customerId']);
        $this->assertSame($customerStatus['activePoints'], $customerDetails['defaultAccount']['activePoints']);
        $this->assertSame($customerStatus['transferredPoints'], $customerDetails['defaultAccount']['transferredPoints']);
        $this->assertSame($customerStatus['lockedPoints'], $customerDetails['defaultAccount']['lockedPoints']);
        $this->assertSame($customerStatus['expiredPoints'], $customerDetails['defaultAccount']['expiredPoints']);
        $this->assertSame($customerStatus['spentPoints'], $customerDetails['defaultAccount']['spentPoints']);
        $this->assertSame($customerStatus['earnedPoints'], $customerDetails['defaultAccount']['earnedPoints']);
        $this->assertSame($customerStatus['blockedPoints'], $customerDetails['defaultAccount']['blockedPoints']);
    }

    /**
     * @test
     */
    public function it_does_not_allow_to_get_customer_status_as_a_different_customer(): void
    {
        $client = self::createAuthenticatedClient(
            LoadUserData::USER_USERNAME,
            LoadUserData::USER_PASSWORD,
            'member',
            LoadSettingsData::DEFAULT_STORE_CODE
        );
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER1_USER_ID.'/status'
        );
        $response = $client->getResponse();
        $this->assertNoAccessResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_gets_customer_status_as_a_client(): void
    {
        $client = self::createAuthenticatedClient(
            LoadUserData::USER_USERNAME,
            LoadUserData::USER_PASSWORD,
            'member',
            LoadSettingsData::DEFAULT_STORE_CODE
        );
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER_USER_ID.'/status'
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_receives_a_customer_status(): void
    {
        $client = self::createAuthenticatedClient(
            LoadUserData::USER_USERNAME,
            LoadUserData::USER_PASSWORD,
            'member',
            LoadSettingsData::DEFAULT_STORE_CODE
        );
        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/tier'
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);

        $this->assertArrayHasKey('items', $data);
        $first = reset($data['items']);

        $this->assertArrayHasKey('levelId', $first);
        $this->assertIsString($first['levelId']);
        $this->assertArrayHasKey('description', $first);
        $this->assertIsString($first['description']);
        $this->assertArrayHasKey('name', $first);
        $this->assertIsString($first['name']);
        $this->assertArrayHasKey('hasPhoto', $first);
        $this->assertIsBool($first['hasPhoto']);
        $this->assertArrayHasKey('conditionValue', $first);
        $this->assertIsFloat($first['conditionValue']);
    }

    /**
     * @test
     */
    public function it_sets_avatar(): void
    {
        $client = self::createAuthenticatedClient();

        $filesystem = self::getContainer()->get('filesystem');
        $filesystem->copy(__DIR__.'/../../../Resources/fixtures/avatar.png', __DIR__.'/../../../Resources/fixtures/avatar_sample.png');
        $uploadedFile = new UploadedFile(__DIR__.'/../../../Resources/fixtures/avatar_sample.png', 'avatar_sample.png');

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER_USER_ID.'/avatar',
            [],
            [
                'avatar' => ['file' => $uploadedFile],
            ]
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        self::ensureKernelShutdown();
        $getClient = self::createAuthenticatedClient();
        $getClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER_USER_ID,
        );
        $getResponse = $getClient->getResponse();
        $this->assertOkResponseStatus($getResponse);
        $data = json_decode($getResponse->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $this->assertNotEmpty($data['avatarPath']);
        $this->assertSame('avatar_sample.png', $data['avatarOriginalName']);
        $this->assertSame('image/png', $data['avatarMime']);
    }

    /**
     * @test
     */
    public function it_gets_avatar(): void
    {
        $client = self::createAuthenticatedClient();

        $filesystem = self::getContainer()->get('filesystem');
        $filesystem->copy(__DIR__.'/../../../Resources/fixtures/avatar.png', __DIR__.'/../../../Resources/fixtures/avatar_sample.png');
        $uploadedFile = new UploadedFile(__DIR__.'/../../../Resources/fixtures/avatar_sample.png', 'avatar_sample.png');

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER_USER_ID.'/avatar',
            [],
            [
                'avatar' => ['file' => $uploadedFile],
            ]
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER_USER_ID.'/avatar',
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $this->assertNotEmpty($response);
    }

    /**
     * @test
     * @covers \OpenLoyalty\User\Ui\Rest\Controller\Member\GetAvatar
     */
    public function it_gets_avatar_by_email(): void
    {
        $client = self::createAuthenticatedClient();

        $filesystem = self::getContainer()->get('filesystem');
        $filesystem->copy(__DIR__.'/../../../Resources/fixtures/avatar.png', __DIR__.'/../../../Resources/fixtures/avatar_sample.png');
        $uploadedFile = new UploadedFile(__DIR__.'/../../../Resources/fixtures/avatar_sample.png', 'avatar_sample.png');

        //Create tenant
        $tenantCode = 'avatarGetTenantCode';
        $response = $this->postTenant($client, $tenantCode);
        $this->assertOkResponseStatus($response);

        //Create member
        $response = $this->postMember($client, $tenantCode, '<EMAIL>');
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $client->request(
            'POST',
            sprintf('/api/%s/member/%s/avatar', $tenantCode, $customerId),
            [],
            [
                'avatar' => ['file' => $uploadedFile],
            ]
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $client->request(
            'GET',
            sprintf('/api/%s/member/email=%s/avatar', $tenantCode, '<EMAIL>'),
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $this->assertNotEmpty($response);
    }

    /**
     * @test
     * @covers \OpenLoyalty\User\Ui\Rest\Controller\Member\PostAvatar
     */
    public function it_set_avatar_by_email(): void
    {
        $client = self::createAuthenticatedClient();

        $filesystem = self::getContainer()->get('filesystem');
        $filesystem->copy(__DIR__.'/../../../Resources/fixtures/avatar.png', __DIR__.'/../../../Resources/fixtures/avatar_sample.png');
        $uploadedFile = new UploadedFile(__DIR__.'/../../../Resources/fixtures/avatar_sample.png', 'avatar_sample.png');

        //Create tenant
        $tenantCode = 'avatarPostTenantCode';
        $response = $this->postTenant($client, $tenantCode);
        $this->assertOkResponseStatus($response);

        //Create member
        $response = $this->postMember($client, $tenantCode, '<EMAIL>');
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $client->request(
            'POST',
            sprintf('/api/%s/member/email=%s/avatar', $tenantCode, '<EMAIL>'),
            [],
            [
                'avatar' => ['file' => $uploadedFile],
            ]
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $client->request(
            'GET',
            sprintf('/api/%s/member/%s/avatar', $tenantCode, $customerId),
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $this->assertNotEmpty($response);
    }

    /**
     * @test
     */
    public function it_removes_avatar(): void
    {
        $client = self::createAuthenticatedClient();

        $filesystem = self::getContainer()->get('filesystem');
        $filesystem->copy(__DIR__.'/../../../Resources/fixtures/avatar.png', __DIR__.'/../../../Resources/fixtures/avatar_sample.png');
        $uploadedFile = new UploadedFile(__DIR__.'/../../../Resources/fixtures/avatar_sample.png', 'avatar_sample.png');

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER_USER_ID.'/avatar',
            [],
            [
                'avatar' => ['file' => $uploadedFile],
            ]
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $client->request(
            'DELETE',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER_USER_ID.'/avatar',
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_removes_avatar_by_email(): void
    {
        $client = self::createAuthenticatedClient();

        $filesystem = self::getContainer()->get('filesystem');
        $filesystem->copy(__DIR__.'/../../../Resources/fixtures/avatar.png', __DIR__.'/../../../Resources/fixtures/avatar_sample.png');
        $uploadedFile = new UploadedFile(__DIR__.'/../../../Resources/fixtures/avatar_sample.png', 'avatar_sample.png');

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER_USER_ID.'/avatar',
            [],
            [
                'avatar' => ['file' => $uploadedFile],
            ]
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        $client->request(
            'DELETE',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/email='.LoadUserData::USER_USERNAME.'/avatar',
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_does_not_allow_to_update_company_without_tax_id(): void
    {
        $userId = '22222222-0000-474c-b092-b0dd880c07e2';
        $client = self::createAuthenticatedClient();

        $customerData = [
            'email' => '<EMAIL>',
            'company' => [
                'name' => 'Company A',
            ],
        ];

        $client->request(
            'PUT',
            sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s', $userId),
            [
                'customer' => $customerData,
            ]
        );

        $response = $client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        ResponseChecker::assertHasError($data, 'company.nip', 'This value should not be blank.');
    }

    /**
     * @test
     */
    public function it_can_not_set_xss_avatar(): void
    {
        $client = self::createAuthenticatedClient();

        $filesystem = self::getContainer()->get('filesystem');
        $kernelRootDir = self::getContainer()->getParameter('kernel.project_dir');
        $filesystem->copy(
            $kernelRootDir.'/tests/Integration/Resources/fixtures/user/xss_image_file.html',
            $kernelRootDir.'/tests/Integration/Ui/Resources/fixtures/xss_image_file.html'
        );
        $uploadedFile = new UploadedFile(
            $kernelRootDir.'/tests/Integration/Ui/Resources/fixtures/xss_image_file.html',
            'xss_image_file.html'
        );

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER3_USER_ID.'/avatar',
            [],
            [
                'avatar' => ['file' => $uploadedFile],
            ]
        );

        $response = $client->getResponse();
        $this->assertNoContentResponseStatus($response);

        self::ensureKernelShutdown();
        $getClient = self::createAuthenticatedClient();
        $getClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.LoadUserData::USER3_USER_ID,
        );
        $getResponse = $getClient->getResponse();
        $this->assertOkResponseStatus($getResponse);
        $data = json_decode($getResponse->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $this->assertNotEmpty($data['avatarPath']);
        $this->assertSame('xss_image_file.html', $data['avatarOriginalName']);
        $this->assertSame('image/jpeg', $data['avatarMime']);
    }

    /**
     * @test
     */
    public function it_does_not_allow_to_register_new_customer_with_too_long_data(): void
    {
        $client = self::createAuthenticatedClient();
        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'firstName' => 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                    'lastName' => 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                    'email' => '<EMAIL>',
                    'gender' => 'male',
                    'birthDate' => '1990-01-01',
                    'phone' => '+0000000000000',
                    'address' => [
                        'street' => 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                        'address1' => 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                        'address2' => 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                        'postal' => 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                        'city' => 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                        'country' => 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                        'province' => 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                    ],
                    'company' => [
                        'name' => 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                        'nip' => 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                    ],
                    'agreement1' => true,
                    'agreement2' => true,
                    'loyaltyCardNumber' => 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $responseData = json_decode($response->getContent(), true);
        ResponseChecker::assertHasError($responseData, 'firstName', 'This value is too long. It should have 255 characters or less.');
        ResponseChecker::assertHasError($responseData, 'lastName', 'This value is too long. It should have 255 characters or less.');
        ResponseChecker::assertHasError($responseData, 'loyaltyCardNumber', 'This value is too long. It should have 255 characters or less.');
        ResponseChecker::assertHasError($responseData, 'address.street', 'This value is too long. It should have 255 characters or less.');
        ResponseChecker::assertHasError($responseData, 'address.address1', 'This value is too long. It should have 255 characters or less.');
        ResponseChecker::assertHasError($responseData, 'address.address2', 'This value is too long. It should have 255 characters or less.');
        ResponseChecker::assertHasError($responseData, 'address.postal', 'This value is too long. It should have 32 characters or less.');
        ResponseChecker::assertHasError($responseData, 'address.city', 'This value is too long. It should have 255 characters or less.');
        ResponseChecker::assertHasError($responseData, 'address.province', 'This value is too long. It should have 255 characters or less.');
        ResponseChecker::assertHasError($responseData, 'company.name', 'This value is too long. It should have 255 characters or less.');
        ResponseChecker::assertHasError($responseData, 'company.nip', 'This value is too long. It should have 64 characters or less.');
    }

    public function getEmailOrPhone(): array
    {
        return [
            ['emailOrPhone', 'user-1', ['email', 'phone']],
            ['emailOrPhone', '<EMAIL>', ['email', 'phone']],
            ['emailOrPhone', '+48', ['email', 'phone']],
            ['emailOrPhone', '645', ['email', 'phone']],
        ];
    }

    public function getEmailOrPhoneCheckDataProvider(): array
    {
        return [
            ['user-1', 0],
            ['<EMAIL>', 1],
            ['+48234234000', 1],
            ['645', 0],
        ];
    }

    public function getIdentifierCheckDataProvider(): array
    {
        return [
            ['user-1', 0],
            ['<EMAIL>', 1],
            ['+48234234000', 1],
            ['645', 0],
            ['00000000-0000-474c-b092-b0dd880c07e1', 1],
            ['D1234566', 1],
        ];
    }

    protected function getStore(): Store
    {
        return new Store(
            new StoreId(LoadSettingsData::DEFAULT_STORE_ID),
            LoadSettingsData::DEFAULT_STORE_CODE,
            'EUR',
            'Store'
        );
    }

    private function allowCustomerToEditProfileSettings(bool $allow): void
    {
        $settingManager = self::getContainer()->get(SettingsManager::class);

        $setting = new Settings();
        $settingEntry = $settingManager->getSettingByKey('allowCustomersProfileEdits', $this->getStore());
        $settingEntry->setValue($allow);
        $setting->addEntry($settingEntry);

        $settingManager->save($setting);
    }
}
