<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Integration\Ui\Rest\ApiKey;

use DateTimeImmutable;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\User\Integration\Traits\ApiKeyApiTrait;
use OpenLoyalty\Test\User\Integration\Traits\MemberApiTrait;
use OpenLoyalty\User\Infrastructure\DataFixtures\ORM\LoadAdminData;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class GetListTest extends AbstractApiTest
{
    use MemberApiTrait;
    use ApiKeyApiTrait;

    protected HttpKernelBrowser $client;

    /**
     * @test
     */
    public function it_get_api_key_for_current_customer(): void
    {
        $response = $this->postApiKey(
            $this->client,
            name: 'Personal test token',
            expirationDate: new DateTimeImmutable('2222-12-12T00:00:00+00:00')
        );
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $apiKeyId = $data['apiKeyId'];

        $this->client->jsonRequest(
            'GET',
            '/api/admin/'.LoadAdminData::ADMIN_EXTERNAL_ID.'/api-key',
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('items', $data);
        foreach ($data['items'] as $item) {
            $this->assertArrayHasKey('token', $item);
            $this->assertEquals('************************', $item['token']);
            $this->assertArrayHasKey('name', $item);
            $this->assertArrayHasKey('legacy', $item);
        }

        $this->client->request(
            'GET',
            '/api/admin/'.LoadAdminData::ADMIN_EXTERNAL_ID.'/api-key?apiKeyId='.$apiKeyId,
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertCount(1, $data['items']);
        $this->assertArrayHasKey('token', $data['items'][0]);
        $this->assertArrayHasKey('name', $data['items'][0]);
        $this->assertArrayHasKey('legacy', $data['items'][0]);
        $this->assertEquals('Personal test token', $data['items'][0]['name']);
        $this->assertArrayHasKey('expirationDate', $data['items'][0]);
        $this->assertEquals('2222-12-12T00:00:00+00:00', $data['items'][0]['expirationDate']);
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }
}
