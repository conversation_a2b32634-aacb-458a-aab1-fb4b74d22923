<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Import\Integration\Traits;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

trait ImportApiTrait
{
    private function postImport(
        HttpKernelBrowser $httpClient,
        string $importType,
        UploadedFile $file,
        ?string $storeCode = 'DEFAULT',
        ?array $additionalData = [],
    ): Response {
        $httpClient->request(
            'POST',
            '/api/'.$storeCode.'/import/'.$importType,
            [
                'import' => [
                    'additionalData' => $additionalData,
                ],
            ],
            [
                'import' => [
                    'file' => $file,
                ],
            ]
        );

        return $httpClient->getResponse();
    }

    private function getImport(
        HttpKernelBrowser $httpClient,
        string $importId,
        ?string $storeCode = 'DEFAULT',
    ): Response {
            $httpClient->jsonRequest(
            'GET',
            '/api/'.$storeCode.'/import/'.$importId,
        );

        return $httpClient->getResponse();
    }
}
