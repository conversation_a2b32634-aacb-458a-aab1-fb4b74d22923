[{"customerId": "will be added in test", "points": "20", "walletTypeCode": "wallet-1", "comment": "Test comment", "registeredOn": "2024-05-16T10:00:00+02:00", "lockedUntilDays": 5}, {"customerId": "will be added in test", "points": "25", "walletTypeCode": "wallet-2", "comment": "Test comment", "registeredOn": "2024-05-16T10:00:00+02:00", "lockedUntilDays": 5}, {"customerId": "will be added in test", "points": "30", "walletTypeCode": "wallet-3", "comment": "Test comment", "registeredOn": "2024-05-16T10:00:00+02:00", "lockedUntilDays": 5}, {"customerId": "will be added in test", "points": "35", "walletTypeCode": "wallet-4", "comment": "Test comment", "registeredOn": "2024-05-16T10:00:00+02:00", "lockedUntilDays": 5}, {"customerId": "will be added in test", "points": "40", "walletTypeCode": "wallet-5", "comment": "Test comment", "registeredOn": "2024-05-16T10:00:00+02:00", "lockedUntilDays": 5}]