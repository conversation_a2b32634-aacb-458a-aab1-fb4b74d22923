<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Import\Integration\Ui\Rest;

use OpenLoyalty\Integration\Infrastructure\Utility\Traits\UploadedFileTrait;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\User\Infrastructure\DataFixtures\ORM\LoadUserData;

final class GetImportItemsListTest extends AbstractApiTest
{
    use UploadedFileTrait;

    /**
     * @test
     */
    public function it_return_list_of_imports(): void
    {
        $xmlContent = file_get_contents(__DIR__.'/../../Resources/fixtures/user/import_member.xml');

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/member',
            [],
            [
                'import' => [
                    'file' => $this->createUploadedFile($xmlContent, 'import.xml', 'application/xml', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/'.$data['importId'],
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertGreaterThan(0, count($data['items']));
        $importItem = reset($data['items']);

        $this->assertArrayHasKey('importId', $importItem);
        $this->assertArrayHasKey('status', $importItem);
        $this->assertArrayHasKey('createdAt', $importItem);
        $this->assertArrayHasKey('updatedAt', $importItem);
        $this->assertArrayHasKey('entity', $importItem);
    }

    /**
     * @test
     */
    public function it_is_filtered_by_entity_id(): void
    {
        $xmlContent = file_get_contents(__DIR__.'/../../Resources/fixtures/user/import_member.xml');

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/member',
            [],
            [
                'import' => [
                    'file' => $this->createUploadedFile($xmlContent, 'import.xml', 'application/xml', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/'.$data['importId'],
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $importItem = reset($data['items']);

        $client->jsonRequest(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/'.$importItem['importId'].'?entityId='.$importItem['entity']['id'],
        );
        $response = $client->getResponse();
        $filteredData = json_decode($response->getContent(), true);

        $this->assertCount(1, $filteredData['items']);
    }

    /**
     * @test
     */
    public function it_is_filtered_by_status(): void
    {
        $xmlContent = file_get_contents(__DIR__.'/../../Resources/fixtures/user/import_member.xml');

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/member',
            [],
            [
                'import' => [
                    'file' => $this->createUploadedFile($xmlContent, 'import.xml', 'application/xml', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/'.$data['importId'].'?status=succeed',
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertCount(1, $data['items']);
    }

    /**
     * @test
     */
    public function it_is_ordered(): void
    {
        $xmlContent = file_get_contents(__DIR__.'/../../Resources/fixtures/user/import_member.xml');

        $client = self::createAuthenticatedClient();
        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/member',
            [],
            [
                'import' => [
                    'file' => $this->createUploadedFile($xmlContent, 'import.xml', 'application/xml', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/'.$data['importId'].'?_orderBy[status]=desc',
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertCount(1, $data['items']);
    }

    /**
     * @test
     */
    public function it_does_not_get_import_list_items_when_user_is_not_admin(): void
    {
        $client = self::createAuthenticatedClient();

        $xmlContent = file_get_contents(__DIR__.'/../../Resources/fixtures/user/import_member.xml');

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/member',
            [],
            [
                'import' => [
                    'file' => $this->createUploadedFile($xmlContent, 'import.xml', 'application/xml', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $importId = $data['importId'];

        $client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/login_check',
            [
                'username' => LoadUserData::USER2_USERNAME,
                'password' => LoadUserData::USER2_PASSWORD,
            ]
        );

        $data = json_decode($client->getResponse()->getContent(), true);
        $token = $data['token'];
        $this->assertTrue(isset($token), 'Response should have field "token". '.$client->getResponse()->getContent());

        $client->setServerParameter('HTTP_Authorization', sprintf('Bearer %s', $token));

        $client->jsonRequest(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/'.$importId,
        );
        $response = $client->getResponse();
        $this->assertNoAccessResponseStatus($response);
    }
}
