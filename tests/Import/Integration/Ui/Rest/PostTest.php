<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Import\Integration\Ui\Rest;

use OpenLoyalty\Integration\Infrastructure\Utility\Traits\UploadedFileTrait;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\User\Infrastructure\DataFixtures\ORM\LoadUserData;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class PostTest extends AbstractApiTest
{
    use UploadedFileTrait;

    private HttpKernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_validate_import_incorrect_items(): void
    {
        $xmlContent = file_get_contents(__DIR__.'/../../Resources/fixtures/user/import_member_with_incorrect_email.xml');

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/member',
            [],
            [
                'import' => [
                    'file' => $this->createUploadedFile($xmlContent, 'import_member_with_incorrect_email.xml', 'application/xml', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->client->jsonRequest(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/'.$data['importId'],
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertCount(1, $data['items']);
        $this->assertSame('failed', $data['items'][0]['status']);
        $this->assertSame('{"email":["The value of \'Email address\' is not valid."]}', $data['items'][0]['message']);
        $this->assertSame('-', $data['items'][0]['entity']['id']);
        $this->assertSame('jon_unique123@example', $data['items'][0]['entity']['data']['email']);
        $this->assertSame('936592735', $data['items'][0]['entity']['data']['loyaltyCardNumber']);
        $this->assertSame('+48888888889', $data['items'][0]['entity']['data']['phone']);
    }

    /**
     * @test
     */
    public function it_import_and_register_member(): void
    {
        $xmlContent = file_get_contents(__DIR__.'/../../Resources/fixtures/user/import_member.xml');

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/member',
            [],
            [
                'import' => [
                    'file' => $this->createUploadedFile($xmlContent, 'import_member.xml', 'application/xml', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $importId = $data['importId'];
        $this->client->jsonRequest(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/'.$importId,
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertGreaterThan(0, count($data['items']));
        $importItem = reset($data['items']);

        $this->assertSame($importId, $importItem['importId']);
        $this->assertSame('succeed', $importItem['status']);

        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member?email=<EMAIL>',
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $this->assertCount(1, $data['items']);
        $member = reset($data['items']);

        self::assertSame('<EMAIL>', $member['email']);
    }

    /**
     * @test
     */
    public function it_does_not_create_import_when_user_is_not_admin(): void
    {
        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/login_check',
            [
                'username' => LoadUserData::USER2_USERNAME,
                'password' => LoadUserData::USER2_PASSWORD,
            ]
        );

        $data = json_decode($this->client->getResponse()->getContent(), true);
        $token = $data['token'];
        $this->assertTrue(isset($token), 'Response should have field "token". '.$this->client->getResponse()->getContent());

        $this->client->setServerParameter('HTTP_Authorization', sprintf('Bearer %s', $token));

        $xmlContent = file_get_contents(__DIR__.'/../../Resources/fixtures/user/import_member.xml');

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/member',
            [],
            [
                'import' => [
                    'file' => $this->createUploadedFile($xmlContent, 'import_member.xml', 'application/xml', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertNoAccessResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_import_and_register_member_with_empty_fields(): void
    {
        $xmlContent = file_get_contents(__DIR__.'/../../Resources/fixtures/user/import_member_with_empty_fields.xml');

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/member',
            [],
            [
                'import' => [
                    'file' => $this->createUploadedFile($xmlContent, 'import_member_with_empty_fields.xml', 'application/xml', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertOkResponseStatus($response);
        $importId = $data['importId'];
        $this->client->jsonRequest(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/'.$importId,
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $this->assertCount(1, $data['items']);
        $importItem = reset($data['items']);

        $this->assertSame($importId, $importItem['importId']);
        $this->assertSame('succeed', $importItem['status']);

        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member?email=<EMAIL>',
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $this->assertCount(1, $data['items']);
        $member = reset($data['items']);

        $this->assertSame('<EMAIL>', $member['email']);
    }

    /**
     * @test
     */
    public function it_import_group_values(): void
    {
        $csvContent = file_get_contents(__DIR__.'/../../Resources/fixtures/group_value/group_value.csv');

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/groupOfValues',
            [
                'groupOfValues' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Skus group',
                            'description' => 'Skus group',
                        ],
                    ],
                    'active' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $groupOfValuesId = $data['groupOfValuesId'];

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/groupValue',
            [
                'import' => [
                    'additionalData' => [
                        'groupOfValuesId' => $groupOfValuesId,
                    ],
                ],
            ],
            [
                'import' => [
                    'file' => $this->createUploadedFile($csvContent, 'group_value.csv', 'text/csv', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $importId = $data['importId'];
        $this->client->jsonRequest(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/'.$importId,
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $importItem = reset($data['items']);
        $this->assertSame($importId, $importItem['importId']);
        $this->assertSame('succeed', $importItem['status']);

        //get values endpoints
        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/groupOfValues/'.$groupOfValuesId.'/values',
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $groupValue = $data['items'][0];

        self::assertArrayHasKey('value', $groupValue);
        self::assertArrayHasKey('description', $groupValue);
    }

    /**
     * @test
     */
    public function it_validate_correctly_group_of_values_import_without_file(): void
    {
        $storeCode = 'tenantCode';
        $this->createTenant($storeCode);

        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/groupOfValues',
            [
                'groupOfValues' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Skus group',
                            'description' => 'Skus group',
                        ],
                    ],
                    'active' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $groupOfValuesId = $data['groupOfValuesId'];

        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/import/groupValue',
            [
                'import' => [
                    'additionalData' => [
                        'groupOfValuesId' => $groupOfValuesId,
                    ],
                ],
            ],
            [
                'import' => [
                    'file' => null,
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $errors = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $this->assertBadRequestResponseStatus($response);
        $this->assertArrayHasKey('errors', $errors);
        $this->assertCount(1, $errors['errors']);
        $this->assertArrayHasKey('message', $errors['errors'][0]);
        $this->assertSame('This value should not be blank.', $errors['errors'][0]['message']);
        $this->assertSame('file', $errors['errors'][0]['path']);
    }

    /**
     * @test
     */
    public function it_validate_correctly_member_import_without_file(): void
    {
        $storeCode = 'tenantCode';
        $this->createTenant($storeCode);

        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/import/member',
            [],
            [
                'import' => [
                    'file' => null,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $errors = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $this->assertBadRequestResponseStatus($response);
        $this->assertArrayHasKey('errors', $errors);
        $this->assertCount(1, $errors['errors']);
        $this->assertArrayHasKey('message', $errors['errors'][0]);
        $this->assertSame('This value should not be blank.', $errors['errors'][0]['message']);
        $this->assertSame('file', $errors['errors'][0]['path']);
    }

    /**
     * @test
     */
    public function it_validate_correctly_segment_import_without_file(): void
    {
        $storeCode = 'tenantCode';
        $this->createTenant($storeCode);

        $this->client->request(
            'POST',
            '/api/'.$storeCode.'/import/segmentMembers',
            [],
            [
                'import' => [
                    'file' => null,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $errors = json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $this->assertBadRequestResponseStatus($response);
        $this->assertArrayHasKey('errors', $errors);
        $this->assertCount(4, $errors['errors']);
        $this->assertArrayHasKey('message', $errors['errors'][3]);
        $this->assertSame('This value should not be blank.', $errors['errors'][0]['message']);
        $this->assertSame('file', $errors['errors'][3]['path']);
    }

    /**
     * @test
     */
    public function it_import_and_update_description_in_group_values(): void
    {
        $csvContent = file_get_contents(__DIR__.'/../../Resources/fixtures/group_value/group_value.csv');

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/groupOfValues',
            [
                'groupOfValues' => [
                    'translations' => [
                        'en' => [
                            'name' => 'new group',
                            'description' => 'new group',
                        ],
                    ],
                    'active' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $groupOfValuesId = $data['groupOfValuesId'];

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/groupValue',
            [
                'import' => [
                    'additionalData' => [
                        'groupOfValuesId' => $groupOfValuesId,
                    ],
                ],
            ],
            [
                'import' => [
                    'file' => $this->createUploadedFile($csvContent, 'group_value.csv', 'text/csv', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $importId = $data['importId'];
        $this->client->jsonRequest(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/'.$importId,
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $importItem = reset($data['items']);
        $this->assertSame($importId, $importItem['importId']);
        $this->assertSame('succeed', $importItem['status']);

        //get values endpoints
        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/groupOfValues/'.$groupOfValuesId.'/values',
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $groupValue = $data['items'][0];

        self::assertArrayHasKey('value', $groupValue);
        self::assertArrayHasKey('description', $groupValue);

        //----------
        $csvContent = file_get_contents(__DIR__.'/../../Resources/fixtures/group_value/group_values_update.csv');

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/groupValue',
            [
                'import' => [
                    'additionalData' => [
                        'groupOfValuesId' => $groupOfValuesId,
                    ],
                ],
            ],
            [
                'import' => [
                    'file' => $this->createUploadedFile($csvContent, 'group_values_update.csv', 'text/csv', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $importId = $data['importId'];
        $this->client->jsonRequest(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/'.$importId,
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $importItem = reset($data['items']);
        $this->assertSame($importId, $importItem['importId']);
        $this->assertSame('succeed', $importItem['status']);

        //get values endpoints
        $this->client->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/groupOfValues/'.$groupOfValuesId.'/values',
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        foreach ($data['items'] as $item) {
            if ('value1' === $item['value']) {
                self::assertSame('opis1', $item['description']);
            }
            if ('value2' === $item['value']) {
                self::assertSame('', $item['description']);
            }
        }
    }

    /**
     * @test
     */
    public function it_not_import_wrong_file_type(): void
    {
        $xmlContent = file_get_contents(__DIR__.'/../../Resources/fixtures/user/import_member.xml');

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/groupOfValues',
            [
                'groupOfValues' => [
                    'translations' => [
                        'en' => [
                            'name' => 'new group',
                            'description' => 'new group',
                        ],
                    ],
                    'active' => true,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $groupOfValuesId = $data['groupOfValuesId'];

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/groupValue',
            [
                'import' => [
                    'additionalData' => [
                        'groupOfValuesId' => $groupOfValuesId,
                    ],
                ],
            ],
            [
                'import' => [
                    'file' => $this->createUploadedFile($xmlContent, 'import_member.xml', 'text/xml', UPLOAD_ERR_OK),
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_does_not_import_group_value_when_group_is_not_found(): void
    {
        $csvContent = file_get_contents(__DIR__.'/../../Resources/fixtures/group_value/group_value.csv');

        $this->client->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/import/groupValue',
            [
                'import' => [
                    'additionalData' => [
                        'groupOfValuesId' => '52cca6f0-57d2-47e9-a6d8-f29f1f1cf012',
                    ],
                ],
            ],
            [
                'import' => [
                    'file' => $this->createUploadedFile($csvContent, 'group_value.csv', 'text/csv', UPLOAD_ERR_OK),
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
    }
}
