<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Import\Unit\Domain;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Import\Domain\Exception\NotSupportedImportTypeException;
use OpenLoyalty\Import\Domain\ItemDataProcessor;
use OpenLoyalty\Import\Domain\Processor\ProcessorInterface;
use OpenLoyalty\Import\Domain\ValueObject\Entity;
use OpenLoyalty\Import\Domain\ValueObject\ImportType;
use OpenLoyalty\Import\Domain\ValueObject\ItemData;
use PHPUnit\Framework\TestCase;

final class ItemDataProcessorTest extends TestCase
{
    /**
     * @test
     */
    public function it_throws_exception_when_does_not_supported(): void
    {
        $importType = $this->createMock(ImportType::class);
        $storeId = $this->createMock(StoreId::class);

        $this->expectException(NotSupportedImportTypeException::class);
        $processor = $this->createMock(ProcessorInterface::class);
        $processor->method('supports')->willReturn(false);

        $itemDataProcessor = new ItemDataProcessor([$processor]);
        $itemDataProcessor->processItemData(new ItemData([]), $importType, $storeId);
    }

    /**
     * @test
     */
    public function it_test_process_item_data(): void
    {
        $importType = $this->createMock(ImportType::class);
        $storeId = $this->createMock(StoreId::class);

        $processor = $this->createMock(ProcessorInterface::class);
        $processor->method('supports')->willReturn(true);
        $processor->method('process')->willReturn(new Entity(null, null));

        $itemDataProcessor = new ItemDataProcessor([$processor]);

        $result = $itemDataProcessor->processItemData(new ItemData([]), $importType, $storeId);

        self::assertEquals(new Entity(null, null), $result);
    }
}
