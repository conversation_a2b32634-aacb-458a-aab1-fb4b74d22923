<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Leaderboard\Integration\Ui\Rest;

use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use OpenLoyalty\Integration\Helpers\ResponseChecker;
use OpenLoyalty\Test\Campaign\Integration\Traits\CampaignApiTrait;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\Test\CustomEvent\Integration\Traits\CustomEventApiTrait;

/**
 * @covers \OpenLoyalty\Campaign\Ui\Rest\Controller\GetList
 */
final class GetListTest extends AbstractApiTest
{
    use CampaignApiTrait;
    use CustomEventApiTrait;
    use TenantApiTrait;

    /**
     * @test
     */
    public function it_filters_campaigns_with_trigger_equal_operator(): void
    {
        $client = self::createAuthenticatedClient();
        $tenantCode = 'triggerEqFilterTenant';
        $this->postTenant($client, $tenantCode);

        // Create campaigns with different trigger types
        $response = $this->postCampaign($client, $tenantCode, trigger: Trigger::TRANSACTION);
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Campaign name',
                        ],
                    ],
                    'active' => false,
                    'type' => 'direct',
                    'trigger' => Trigger::LEADERBOARD,
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'triggerStrategy' => [
                        'type' => 'daily',
                    ],
                ],
            ],
            server: $this->addDisableOpenApiValidationHeader()
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $response = $this->postCampaign($client, $tenantCode, trigger: Trigger::TIME);
        $this->assertOkResponseStatus($response);

        // Filter for leaderboard campaigns using eq operator
        $client->request(
            'GET',
            '/api/'.$tenantCode.'/campaign?trigger[eq]=leaderboard',
            [
                '_itemsOnPage' => 10,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertCount(1, $data['items']);
        $this->assertSame('leaderboard', $data['items'][0]['trigger']);
    }

    /**
     * @test
     */
    public function it_filters_campaigns_with_trigger_not_equal_operator(): void
    {
        $client = self::createAuthenticatedClient();
        $tenantCode = 'triggerNeqFilterTenant';
        $this->postTenant($client, $tenantCode);

        // Create campaigns with different trigger types
        $response = $this->postCampaign($client, $tenantCode, trigger: Trigger::TRANSACTION);
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Campaign name',
                        ],
                    ],
                    'active' => false,
                    'type' => 'direct',
                    'trigger' => Trigger::LEADERBOARD,
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'triggerStrategy' => [
                        'type' => 'daily',
                    ],
                ],
            ],
            server: $this->addDisableOpenApiValidationHeader()
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        // Filter for non-transaction campaigns using neq operator
        $client->request(
            'GET',
            '/api/'.$tenantCode.'/campaign?trigger[neq]=transaction',
            [
                '_itemsOnPage' => 10,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertCount(1, $data['items']);
        $this->assertSame('leaderboard', $data['items'][0]['trigger']);
    }

    /**
     * @test
     */
    public function it_filters_campaigns_with_trigger_like_operator(): void
    {
        $client = self::createAuthenticatedClient();
        $tenantCode = 'triggerLikeFilterTenant';
        $this->postTenant($client, $tenantCode);

        // Create campaigns with different trigger types
        $response = $this->postCampaign($client, $tenantCode, trigger: Trigger::TRANSACTION);
        $this->assertOkResponseStatus($response);

        $eventName = 'campaignCodeEvent';
        $eventCodeAttribute = 'attributeTest';
        $eventResponse = $this->postCustomEventSchema(
            $client,
            $tenantCode,
            customEventName: $eventName,
            eventCodeAttribute: $eventCodeAttribute
        );

        $this->assertNoContentResponseStatus($eventResponse);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            trigger: Trigger::CUSTOM_EVENT_UNIQUE_CODE,
            activity: [
                'startsAt' => '2024-01-01 00:00',
            ],
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '10',
                        ],
                    ],
                ],
            ],
            event: $eventName,
            eventCodeAttribute: $eventCodeAttribute,
            codeGenerator: [
                'length' => 12,
                'characterSet' => 'alphanum',
            ],
            generateCodes: 10
        );
        $this->assertOkResponseStatus($response);

        $response = $this->postCampaign(
            $client,
            $tenantCode,
            trigger: Trigger::CUSTOM_EVENT_UNIQUE_CODE,
            activity: [
                'startsAt' => '2024-01-01 00:00',
            ],
            rules: [
                [
                    'effects' => [
                        [
                            'effect' => 'give_points',
                            'pointsRule' => '10',
                        ],
                    ],
                ],
            ],
            event: $eventName,
            eventCodeAttribute: $eventCodeAttribute,
            codeGenerator: [
                'length' => 12,
                'characterSet' => 'alphanum',
            ],
            generateCodes: 10
        );
        $this->assertOkResponseStatus($response);
        $this->assertOkResponseStatus($response);

        // Filter for custom event campaigns using like operator
        $client->request(
            'GET',
            '/api/'.$tenantCode.'/campaign?trigger[like]=custom',
            [
                '_itemsOnPage' => 10,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertCount(2, $data['items']);
        $this->assertSame(3, $data['total']['all']);
        // Both custom_event and custom_event_unique_code should be returned
        $this->assertStringContainsString('custom_event', $data['items'][0]['trigger']);
        $this->assertStringContainsString('custom_event', $data['items'][1]['trigger']);
    }

    /**
     * @test
     */
    public function it_filters_campaigns_with_trigger_in_operator(): void
    {
        $client = self::createAuthenticatedClient();
        $tenantCode = 'triggerInFilterTenant';
        $this->postTenant($client, $tenantCode);

        // Create campaigns with different trigger types
        $response = $this->postCampaign($client, $tenantCode, trigger: Trigger::TRANSACTION);
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Campaign name',
                        ],
                    ],
                    'active' => false,
                    'type' => 'direct',
                    'trigger' => Trigger::LEADERBOARD,
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'triggerStrategy' => [
                        'type' => 'daily',
                    ],
                ],
            ],
            server: $this->addDisableOpenApiValidationHeader()
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $response = $this->postCampaign($client, $tenantCode, trigger: Trigger::TIME);
        $this->assertOkResponseStatus($response);

        // Filter for leaderboard and transaction campaigns using in operator
        $client->request(
            'GET',
            '/api/'.$tenantCode.'/campaign?trigger[in]=leaderboard,transaction',
            [
                '_itemsOnPage' => 10,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertCount(2, $data['items']);
        $triggers = array_column($data['items'], 'trigger');
        $this->assertTrue(in_array('leaderboard', $triggers));
        $this->assertTrue(in_array('transaction', $triggers));
    }

    /**
     * @test
     */
    public function it_filters_campaigns_with_trigger_not_in_operator(): void
    {
        $client = self::createAuthenticatedClient();
        $tenantCode = 'triggerNotInFilterTenant';
        $this->postTenant($client, $tenantCode);

        // Create campaigns with different trigger types
        $response = $this->postCampaign($client, $tenantCode, trigger: Trigger::TRANSACTION);
        $this->assertOkResponseStatus($response);

        $client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Campaign name',
                        ],
                    ],
                    'active' => false,
                    'type' => 'direct',
                    'trigger' => Trigger::LEADERBOARD,
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'triggerStrategy' => [
                        'type' => 'daily',
                    ],
                ],
            ],
            server: $this->addDisableOpenApiValidationHeader()
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $response = $this->postCampaign($client, $tenantCode, trigger: Trigger::TIME);
        $this->assertOkResponseStatus($response);

        // Filter for campaigns that are not leaderboard or time using notIn operator
        $client->request(
            'GET',
            '/api/'.$tenantCode.'/campaign?trigger[notIn]=leaderboard,time',
            [
                '_itemsOnPage' => 10,
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertCount(1, $data['items']);
        $this->assertSame('transaction', $data['items'][0]['trigger']);
    }

    /**
     * @test
     */
    public function it_returns_bad_request_when_using_array_with_eq_operator(): void
    {
        $client = self::createAuthenticatedClient();
        $tenantCode = 'triggerEqArrayTenant';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign($client, $tenantCode);
        $this->assertOkResponseStatus($response);

        // Try to filter with eq operator but providing multiple values
        $client->request(
            'GET',
            '/api/'.$tenantCode.'/campaign?trigger[eq]=leaderboard,transaction',
            [
                '_itemsOnPage' => 10,
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertBadRequestResponseStatus($response);
        ResponseChecker::assertHasError($data, 'trigger', 'Invalid operator eq');
    }

    /**
     * @test
     */
    public function it_returns_bad_request_when_using_invalid_operator(): void
    {
        $client = self::createAuthenticatedClient();
        $tenantCode = 'triggerInvalidOpTenant';
        $this->postTenant($client, $tenantCode);

        $response = $this->postCampaign($client, $tenantCode);
        $this->assertOkResponseStatus($response);

        $client->request(
            'GET',
            '/api/'.$tenantCode.'/campaign?trigger[invalid]=leaderboard',
            [
                '_itemsOnPage' => 10,
            ]
        );

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertBadRequestResponseStatus($response);
        $this->assertSame('Criteria trigger:code[invalid] is not supported', $data['message']);
    }
}
