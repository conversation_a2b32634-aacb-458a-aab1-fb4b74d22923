<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

/*
 * Copyright Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Leaderboard\Integration\Ui\Rest;

use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use OpenLoyalty\Test\Account\Integration\Traits\WalletTypeApiTrait;
use OpenLoyalty\Test\Campaign\Integration\Traits\CampaignApiTrait;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class LeaderboardCampaignTest extends AbstractApiTest
{
    use TenantApiTrait;
    use CampaignApiTrait;
    use WalletTypeApiTrait;

    protected HttpKernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_tests_leaderboard_campaign_creates(): void
    {
        $tenantCode = 'tenant-code';
        $this->postTenant($this->client, $tenantCode);

        //create wallet
        $walletCode = 'leaderboardWalletCode';
        $response = $this->postWalletType(
            $this->client,
            $walletCode,
            $tenantCode,
        );
        $this->assertOkResponseStatus($response);

        $this->client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Campaign name',
                        ],
                    ],
                    'active' => false,
                    'type' => 'direct',
                    'trigger' => Trigger::LEADERBOARD,
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'triggerStrategy' => [
                        'type' => 'daily',
                    ],
                    'leaderboard' => [
                        'metric' => [
                            'type' => 'earned_units_cumulative',
                            'walletTypeCode' => $walletCode
                        ]
                    ],
                ],
            ],
            server: $this->addDisableOpenApiValidationHeader()
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        $this->client->request('GET', '/api/'.$tenantCode.'/campaign/'.$campaignId);

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        self::assertSame('leaderboard', $data['trigger']);
        self::assertSame('earned_units_cumulative', $data['leaderboard']['type']);
        self::assertSame($walletCode, $data['leaderboard']['walletTypeCode']);
        self::assertNull($data['leaderboard']['completedAt']);
    }

    /**
     * @test
     */
    public function it_tests_get_leaderboard_endpoint_contract(): void
    {
        $tenantCode = 'tenant-code-leaderboard';
        $this->postTenant($this->client, $tenantCode);

        // Create a leaderboard campaign
        $this->client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Leaderboard Campaign',
                        ],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => Trigger::LEADERBOARD,
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'triggerStrategy' => [
                        'type' => 'daily',
                    ],
                ],
            ],
            server: $this->addDisableOpenApiValidationHeader()
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        // Create test customers
        $customerId = $this->createCustomer('<EMAIL>', $tenantCode);

        // Get leaderboard endpoint
        $this->client->request('GET', '/api/'.$tenantCode.'/campaign/'.$campaignId.'/leaderboard');
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        // Verify contract structure
        $this->assertArrayHasKey('items', $data);
        $this->assertArrayHasKey('total', $data);

        // Verify total structure
        $this->assertArrayHasKey('all', $data['total']);
        $this->assertArrayHasKey('filtered', $data['total']);
        $this->assertArrayHasKey('estimated', $data['total']);

        // If there are items, verify their structure
        if (count($data['items']) > 0) {
            $item = $data['items'][0];

            // Verify member structure
            $this->assertArrayHasKey('member', $item);
            $this->assertArrayHasKey('customerId', $item['member']);
            $this->assertArrayHasKey('firstName', $item['member']);
            $this->assertArrayHasKey('lastName', $item['member']);
            $this->assertArrayHasKey('email', $item['member']);
            $this->assertArrayHasKey('phone', $item['member']);
            $this->assertArrayHasKey('loyaltyCardNumber', $item['member']);

            // Verify ranking structure
            $this->assertArrayHasKey('ranking', $item);
            $this->assertArrayHasKey('score', $item['ranking']);
            $this->assertArrayHasKey('recalculatedAt', $item['ranking']);
            $this->assertArrayHasKey('position', $item['ranking']);
            $this->assertArrayHasKey('changedPositionAt', $item['ranking']);

            // Verify progress structure
            $this->assertArrayHasKey('progress', $item['ranking']);
            $this->assertArrayHasKey('general', $item['ranking']['progress']);
            $this->assertArrayHasKey('recalculated', $item['ranking']['progress']);
            $this->assertArrayHasKey('period', $item['ranking']['progress']);
        }
    }

    /**
     * @test
     */
    public function it_tests_get_member_campaign_rank_endpoint_contract(): void
    {
        $tenantCode = 'tenant-code-member-rank';
        $this->postTenant($this->client, $tenantCode);

        // Create a leaderboard campaign
        $this->client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Member Rank Campaign',
                        ],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => Trigger::LEADERBOARD,
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'triggerStrategy' => [
                        'type' => 'daily',
                    ],
                ],
            ],
            server: $this->addDisableOpenApiValidationHeader()
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        // Create test customer
        $customerId = $this->createCustomer('<EMAIL>', $tenantCode);

        // Get member campaign rank endpoint
        $this->client->request('GET', '/api/'.$tenantCode.'/campaign/'.$campaignId.'/leaderboard/member/'.$customerId);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        // Verify main structure
        $this->assertArrayHasKey('score', $data);
        $this->assertArrayHasKey('recalculatedAt', $data);
        $this->assertArrayHasKey('position', $data);
        $this->assertArrayHasKey('changedPositionAt', $data);

        // Verify progress structure
        $this->assertArrayHasKey('progress', $data);
        $this->assertArrayHasKey('general', $data['progress']);
        $this->assertArrayHasKey('recalculated', $data['progress']);
        $this->assertArrayHasKey('period', $data['progress']);

        // Verify surrounding structure
        $this->assertArrayHasKey('surrounding', $data);
        $this->assertArrayHasKey('higher', $data['surrounding']);
        $this->assertArrayHasKey('lower', $data['surrounding']);

        // If there are surrounding entries, verify their structure
        if (count($data['surrounding']['higher']) > 0) {
            $item = $data['surrounding']['higher'][0];

            // Verify member structure
            $this->assertArrayHasKey('member', $item);
            $this->assertArrayHasKey('customerId', $item['member']);
            $this->assertArrayHasKey('firstName', $item['member']);
            $this->assertArrayHasKey('lastName', $item['member']);
            $this->assertArrayHasKey('email', $item['member']);
            $this->assertArrayHasKey('phone', $item['member']);
            $this->assertArrayHasKey('loyaltyCardNumber', $item['member']);

            // Verify ranking structure
            $this->assertArrayHasKey('ranking', $item);
            $this->assertArrayHasKey('score', $item['ranking']);
            $this->assertArrayHasKey('recalculatedAt', $item['ranking']);
            $this->assertArrayHasKey('position', $item['ranking']);
            $this->assertArrayHasKey('changedPositionAt', $item['ranking']);

            // Verify progress structure
            $this->assertArrayHasKey('progress', $item['ranking']);
            $this->assertArrayHasKey('general', $item['ranking']['progress']);
            $this->assertArrayHasKey('recalculated', $item['ranking']['progress']);
            $this->assertArrayHasKey('period', $item['ranking']['progress']);
        }

        if (count($data['surrounding']['lower']) > 0) {
            $item = $data['surrounding']['lower'][0];

            // Verify member structure
            $this->assertArrayHasKey('member', $item);
            $this->assertArrayHasKey('customerId', $item['member']);
            $this->assertArrayHasKey('firstName', $item['member']);
            $this->assertArrayHasKey('lastName', $item['member']);
            $this->assertArrayHasKey('email', $item['member']);
            $this->assertArrayHasKey('phone', $item['member']);
            $this->assertArrayHasKey('loyaltyCardNumber', $item['member']);

            // Verify ranking structure
            $this->assertArrayHasKey('ranking', $item);
            $this->assertArrayHasKey('score', $item['ranking']);
            $this->assertArrayHasKey('recalculatedAt', $item['ranking']);
            $this->assertArrayHasKey('position', $item['ranking']);
            $this->assertArrayHasKey('changedPositionAt', $item['ranking']);

            // Verify progress structure
            $this->assertArrayHasKey('progress', $item['ranking']);
            $this->assertArrayHasKey('general', $item['ranking']['progress']);
            $this->assertArrayHasKey('recalculated', $item['ranking']['progress']);
            $this->assertArrayHasKey('period', $item['ranking']['progress']);
        }
    }

    /**
     * @test
     */
    public function it_tests_get_member_leaderboards_endpoint_contract(): void
    {
        $tenantCode = 'tenant-code-member-leaderboards';
        $this->postTenant($this->client, $tenantCode);

        // Create a leaderboard campaign
        $this->client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Member Leaderboards Campaign',
                        ],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => Trigger::LEADERBOARD,
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'triggerStrategy' => [
                        'type' => 'daily',
                    ],
                ],
            ],
            server: $this->addDisableOpenApiValidationHeader()
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        // Create test customer
        $customerId = $this->createCustomer('<EMAIL>', $tenantCode);

        // Get member leaderboards endpoint
        $this->client->request('GET', '/api/'.$tenantCode.'/campaign/leaderboard/member/'.$customerId);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        // Verify main structure
        $this->assertArrayHasKey('items', $data);
        $this->assertArrayHasKey('total', $data);

        // Verify total structure
        $this->assertArrayHasKey('all', $data['total']);
        $this->assertArrayHasKey('filtered', $data['total']);
        $this->assertArrayHasKey('estimated', $data['total']);

        // If there are items, verify their structure
        if (count($data['items']) > 0) {
            $item = $data['items'][0];

            // Verify campaign structure
            $this->assertArrayHasKey('campaign', $item);
            $this->assertArrayHasKey('campaignId', $item['campaign']);
            $this->assertArrayHasKey('name', $item['campaign']);

            // Verify ranking structure
            $this->assertArrayHasKey('ranking', $item);
            $this->assertArrayHasKey('score', $item['ranking']);
            $this->assertArrayHasKey('recalculatedAt', $item['ranking']);
            $this->assertArrayHasKey('position', $item['ranking']);
            $this->assertArrayHasKey('changedPositionAt', $item['ranking']);

            // Verify progress structure
            $this->assertArrayHasKey('progress', $item['ranking']);
            $this->assertArrayHasKey('general', $item['ranking']['progress']);
            $this->assertArrayHasKey('recalculated', $item['ranking']['progress']);
            $this->assertArrayHasKey('period', $item['ranking']['progress']);
        }
    }

    /**
     * @test
     */
    public function it_tests_member_campaign_rank_with_surrounding_parameter(): void
    {
        $tenantCode = 'tenant-code-surrounding';
        $this->postTenant($this->client, $tenantCode);

        // Create a leaderboard campaign
        $this->client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Surrounding Parameter Campaign',
                        ],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => Trigger::LEADERBOARD,
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'triggerStrategy' => [
                        'type' => 'daily',
                    ],
                ],
            ],
            server: $this->addDisableOpenApiValidationHeader()
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        // Create test customer
        $customerId = $this->createCustomer('<EMAIL>', $tenantCode);

        // Test with default surrounding parameter
        $this->client->request('GET', '/api/'.$tenantCode.'/campaign/'.$campaignId.'/leaderboard/member/'.$customerId);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        // Test with valid surrounding parameter
        $this->client->request('GET', '/api/'.$tenantCode.'/campaign/'.$campaignId.'/leaderboard/member/'.$customerId.'?surrounding=10');
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        // Test with maximum allowed surrounding parameter
        $this->client->request('GET', '/api/'.$tenantCode.'/campaign/'.$campaignId.'/leaderboard/member/'.$customerId.'?surrounding=12');
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        // Test with surrounding parameter exceeding maximum
        $this->client->request('GET', '/api/'.$tenantCode.'/campaign/'.$campaignId.'/leaderboard/member/'.$customerId.'?surrounding=13');
        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $data);
        $this->assertEquals('Maximum surrounding value is 12.', $data['message']);
    }

    /**
     * @test
     */
    public function it_tests_leaderboard_endpoint_returns_error_for_non_leaderboard_campaign(): void
    {
        $tenantCode = 'tenant-code-non-leaderboard';
        $this->postTenant($this->client, $tenantCode);

        // Create a non-leaderboard campaign (using 'transaction' trigger instead of 'leaderboard')
        $this->postCampaign($this->client, $tenantCode);

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        // Try to access the leaderboard endpoint for a non-leaderboard campaign
        $this->client->request('GET', '/api/'.$tenantCode.'/campaign/'.$campaignId.'/leaderboard');
        $response = $this->client->getResponse();
        $this->assertNotFoundResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $data);
        $this->assertEquals('Campaign is not a leaderboard.', $data['message']);
    }

    /**
     * @test
     */
    public function it_tests_leaderboard_endpoint_returns_error_for_non_exist_campaign(): void
    {
        $tenantCode = 'tenant-code-non-exist-leaderboard';
        $this->postTenant($this->client, $tenantCode);

        // Try to access the leaderboard endpoint for a non-leaderboard campaign
        $this->client->request('GET', '/api/'.$tenantCode.'/campaign/196fd395-d7af-4357-8d7a-85ed5d4ae6d5/leaderboard');
        $response = $this->client->getResponse();
        $this->assertNotFoundResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $data);
        $this->assertEquals('Campaign is not a leaderboard.', $data['message']);
    }

    /**
     * @test
     */
    public function it_tests_member_campaign_rank_endpoint_returns_error_for_non_leaderboard_campaign(): void
    {
        $tenantCode = 'tenant-code-non-leaderboard-member';
        $this->postTenant($this->client, $tenantCode);

        // Create a non-leaderboard campaign (using 'custom' trigger instead of 'leaderboard')
        $this->postCampaign($this->client, $tenantCode);

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $campaignId = $data['campaignId'];

        // Create test customer
        $customerId = $this->createCustomer('<EMAIL>', $tenantCode);

        // Try to access the member campaign rank endpoint for a non-leaderboard campaign
        $this->client->request('GET', '/api/'.$tenantCode.'/campaign/'.$campaignId.'/leaderboard/member/'.$customerId);
        $response = $this->client->getResponse();
        $this->assertNotFoundResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $data);
        $this->assertEquals('Campaign is not a leaderboard.', $data['message']);
    }

    /**
     * @test
     */
    public function it_tests_member_leaderboards_filtering_by_campaign_ids(): void
    {
        $tenantCode = 'tenant-code-campaign-filter';
        $this->postTenant($this->client, $tenantCode);

        // Create first leaderboard campaign
        $this->client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'First Leaderboard Campaign',
                        ],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => Trigger::LEADERBOARD,
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'triggerStrategy' => [
                        'type' => 'daily',
                    ],
                ],
            ],
            server: $this->addDisableOpenApiValidationHeader()
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $firstCampaignId = $data['campaignId'];

        // Create second leaderboard campaign
        $this->client->jsonRequest(
            'POST',
            '/api/'.$tenantCode.'/campaign',
            [
                'campaign' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Second Leaderboard Campaign',
                        ],
                    ],
                    'active' => true,
                    'type' => 'direct',
                    'trigger' => Trigger::LEADERBOARD,
                    'activity' => [
                        'startsAt' => '2023-01-01 00:00:00',
                        'endsAt' => null,
                    ],
                    'triggerStrategy' => [
                        'type' => 'daily',
                    ],
                ],
            ],
            server: $this->addDisableOpenApiValidationHeader()
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $secondCampaignId = $data['campaignId'];

        // Create test customer
        $customerId = $this->createCustomer('<EMAIL>', $tenantCode);

        // Test filtering by campaign IDs
        $this->client->request('GET', sprintf(
            '/api/%s/campaign/leaderboard/member/%s?campaignId[in]=%s,%s',
            $tenantCode,
            $customerId,
            $firstCampaignId,
            $secondCampaignId
        ));

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('items', $data);
        $this->assertArrayHasKey('total', $data);
    }

    /**
     * @test
     */
    public function it_tests_member_leaderboards_max_campaign_ids_limit(): void
    {
        $tenantCode = 'tenant-code-max-campaigns';
        $this->postTenant($this->client, $tenantCode);

        // Create test customer
        $customerId = $this->createCustomer('<EMAIL>', $tenantCode);

        // Test with more than MAX_CAMPAIGNS (3) campaign IDs
        $this->client->request('GET', sprintf(
            '/api/%s/campaign/leaderboard/member/%s?campaignId[in]=00000000-0000-0000-0000-000000000001,00000000-0000-0000-0000-000000000002,00000000-0000-0000-0000-000000000003,00000000-0000-0000-0000-000000000004',
            $tenantCode,
            $customerId
        ));

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);

        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $data);
        $this->assertEquals('Maximum 3 campaign IDs allowed.', $data['message']);
    }

    /**
     * Creates a customer for testing.
     */
    private function createCustomer(string $email, string $tenantCode): string
    {
        $customerData = [
            'customer' => [
                'firstName' => 'Test',
                'lastName' => 'User',
                'email' => $email,
                'gender' => 'male',
                'birthDate' => '1990-01-01',
                'phone' => '+48501911936',
                'agreement1' => true,
                'loyaltyCardNumber' => '123456789',
            ],
        ];

        $this->client->jsonRequest('POST', '/api/'.$tenantCode.'/member', $customerData);
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        return $data['customerId'];
    }
}
