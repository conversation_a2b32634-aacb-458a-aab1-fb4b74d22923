nelmio_api_doc:
    documentation:
        paths:
            /api/{storeCode}/transaction:
                post:
                    operationId: _transactionPost
                    tags:
                        - Transactions
                    summary: Add a transaction
                    description: |
                        <label style="background-color: #BFE1F6;padding:5px;">Integration</label><br><br>
                        This method allows creating a new Transaction.
                    parameters:
                        -   ref: "#/components/parameters/storeCode"
                    requestBody:
                        required: true
                        content:
                            application/json:
                                schema:
                                    ref: '#/components/schemas/PostTransaction'
                    responses:
                        200:
                            description: Return registered transaction id.
                            content:
                                application/json:
                                    schema:
                                        type: object
                                        properties:
                                            transactionId:
                                                type: string
                                                format: uuid
                                                description: Registered transaction identity
                                                example: 00000000-0000-0000-0000-000000000000
                        400:
                            $ref: '#/components/responses/BadRequest'
                        401:
                            description: Unauthorized
                            content:
                                application/json:
                                    schema:
                                        oneOf:
                                            -   $ref: '#/components/schemas/ExpiredToken'
                                            -   $ref: '#/components/schemas/InvalidToken'
                                            -   $ref: '#/components/schemas/Unauthorized'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                get:
                    tags:
                        - Transactions
                    summary: 'Get transactions list'
                    description: "<label style=\"background-color: #BFE1F6;padding:5px;\">Integration</label><br><br>\nThis method returns a list of all Transaction in the system.  \nIf sorting is not chosen, the rows will be returned in an unspecified order.  \nTo sort a result, use an `_orderBy` parameter in query.\n"
                    operationId: transactionGetList
                    parameters:
                        -   ref: '#/components/parameters/storeCode'
                        -   name: transactionId
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'customerData:customerId'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'customerData:loyaltyCardNumber'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'customerData:name'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'customerData:email'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'customerData:phone'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'customerData:nip'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: customerId
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'header:documentType'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'header:documentNumber'
                            description: 'Specifies the document number used in the transaction.'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'header:linkedDocumentNumber'
                            description: 'Used only for return transactions and refers to the original ‘sell’ document.'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'header:purchasePlace'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: channelId
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'header:purchasedAt'
                            in: query
                            required: false
                            schema:
                                type: string
                                format: date-time
                        -   name: grossValue
                            in: query
                            required: false
                            schema:
                                type: number
                        -   name: matched
                            in: query
                            required: false
                            schema:
                                type: boolean
                        -   name: 'header:labels'
                            in: query
                            description: 'Labels using pattern e.g. (key1;value1),(key2;value2),...'
                            required: false
                            schema:
                                type: string
                        -   name: 'items:labels'
                            in: query
                            description: 'Labels using pattern e.g. (key1;value1),(key2;value2),...'
                            required: false
                            schema:
                                type: string
                        -   name: 'items:category'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'items:sku'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'items:maker'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: channelId
                            in: query
                            required: false
                            schema:
                                type: string
                        -   ref: '#/components/parameters/page'
                        -   ref: '#/components/parameters/itemsOnPage'
                        -   ref: '#/components/parameters/orderBy'
                    responses:
                        '200':
                            description: 'List of transactions'
                            content:
                                application/json:
                                    schema:
                                        properties:
                                            items: { type: array, items: { ref: '#/components/schemas/Transaction' } }
                                            total: { $ref: '#/components/schemas/SearchableTotalResponse' }
                                        type: object
                        '400':
                            ref: '#/components/responses/BadRequest'
                        '403':
                            ref: '#/components/responses/AccessDenied'
            /api/{storeCode}/transaction/{transaction}:
                get:
                    operationId: transactionGet
                    tags:
                        - Transactions
                    summary: Get transaction’s details
                    description: |
                        <label style="background-color: #BFE1F6;padding:5px;">Integration</label><br><br>
                        This method returns all information about a specific Transaction.
                    parameters:
                        -   ref: "#/components/parameters/storeCode"
                        -   ref: "#/components/parameters/transaction"
                    responses:
                        200:
                            description: Transaction details.
                            content:
                                application/json:
                                    schema:
                                        $ref: '#/components/schemas/Transaction'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                        404:
                            $ref: '#/components/responses/NotFound'
        components:
            schemas:
                PostTransaction:
                    type: object
                    additionalProperties: false
                    properties:
                        transaction:
                            additionalProperties: false
                            required:
                                - header
                                - items
                            type: object
                            properties:
                                customerData:
                                    additionalProperties: false
                                    type: object
                                    properties:
                                        customerId:
                                            type: string
                                        address:
                                            $ref: '#/components/schemas/CustomerAddressFormType'
                                        email:
                                            type: string
                                        loyaltyCardNumber:
                                            type: string
                                        name:
                                            type: string
                                        nip:
                                            type: string
                                        phone:
                                            type: string
                                items:
                                    type: array
                                    items:
                                        type: object
                                        additionalProperties: false
                                        required:
                                            - sku
                                            - name
                                            - grossValue
                                            - category
                                        properties:
                                            category:
                                                type: string
                                            grossValue:
                                                type: number
                                                format: float
                                            maker:
                                                type: string
                                            name:
                                                type: string
                                            quantity:
                                                deprecated: true
                                                type: integer
                                                description: "The quantity of the product. This field is required if 'highPrecisionQuantity' is not provided. It is recommended to use 'highPrecisionQuantity', as 'quantity' is deprecated."
                                            highPrecisionQuantity:
                                                type: number
                                                format: float
                                                description: "The precise quantity of the product, with a maximum of 3 decimal places. This field is required if 'quantity' is not provided."
                                            sku:
                                                type: string
                                            labels:
                                                type: array
                                                items:
                                                    $ref: '#/components/schemas/TransactionLabelFormType'
                                channelId:
                                    type: string
                                    format: uuid
                                header:
                                    type: object
                                    additionalProperties: false
                                    required:
                                        - documentNumber
                                        - purchasedAt
                                    properties:
                                        documentNumber:
                                            type: string
                                            description: 'Specifies the document number used in the transaction.'
                                        linkedDocumentNumber:
                                            type: string
                                            description: 'Used only for return transactions and refers to the original ‘sell’ document.'
                                        documentType:
                                            type: string
                                            enum:
                                                - sell
                                                - return
                                        labels:
                                            type: array
                                            items:
                                                $ref: '#/components/schemas/TransactionLabelFormType'
                                        purchasedAt:
                                            type: string
                                        purchasePlace:
                                            type: string
                Transaction:
                    type: object
                    properties:
                        grossValue:
                            type: float
                            example: 3.0
                        metrics:
                            type: array
                            items:
                                type: object
                                properties:
                                    name:
                                        type: string
                                    value:
                                        type: float
                                        example: 1.0
                                    calculatedAt:
                                        format: date-time
                        transactionId:
                            type: string
                            format: uuid
                        customerId:
                            type: string
                            format: uuid
                        customerData:
                            type: object
                            properties:
                                customerId:
                                    type: string
                                    format: uuid
                                email:
                                    type: string
                                    format: email
                                    example: '<EMAIL>'
                                name:
                                    type: string
                                nip:
                                    type: string
                                phone:
                                    type: string
                                loyaltyCardNumber:
                                    type: string
                                address:
                                    $ref: '#/components/schemas/CustomerAddressFormType'
                        items:
                            type: array
                            items:
                                type: object
                                properties:
                                    name:
                                        type: string
                                    quantity:
                                        deprecated: true
                                        type: integer
                                    highPrecisionQuantity:
                                        type: float
                                        example: 1.125
                                    grossValue:
                                        type: float
                                        example: 1.0
                                    category:
                                        type: string
                                    labels:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                key:
                                                    type: string
                                                value:
                                                    type: string
                                    maker:
                                        type: string
                                    sku:
                                        type: string
                        header:
                            type: object
                            properties:
                                documentNumber:
                                    type: string
                                    description: 'Specifies the document number used in the transaction.'
                                documentType:
                                    type: string
                                    enum:
                                        - sell
                                        - return
                                linkedDocumentNumber:
                                    type: string
                                    description: 'Used only for return transactions and refers to the original ‘sell’ document.'
                                purchasedAt:
                                    type: string
                                    format: date-time
                                purchasePlace:
                                    type: string
                                excludedDeliverySKUs:
                                    type: array
                                    items: { }
                                excludedSKUs:
                                    type: array
                                    items: { }
                                excludedCategories:
                                    type: array
                                    items: { }
                                labels:
                                    type: array
                                    items: { }
                        matched:
                            type: boolean
                        currency:
                            type: string
                        pointsEarned:
                            type: float
                        unitsDeducted:
                            type: float
                        channelId:
                            type: string
                            format: uuid
                        channelName:
                            type: string
                        assignedToCustomerAt:
                            nullable: true
                            type: string
                            format: date-time