nelmio_api_doc:
    documentation:
        paths:
            /api/{storeCode}/tier:
                get:
                    operationId: _tierGetList
                    tags:
                        - Tier
                    summary: Get tiers list
                    description: |
                        This method returns a list of all created Tiers in the system.  
                        If sorting is not chosen, the rows will be returned in an unspecified order.  
                        To sort a result, use an `_orderBy` parameter in query.
                    deprecated: true
                    parameters:
                        -   ref: "#/components/parameters/storeCode"
                        -   name: levelId
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: conditionValue
                            in: query
                            required: false
                            schema:
                                type: number
                        -   name: name
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: description
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: active
                            description: Filter by active filed
                            in: query
                            required: false
                            schema:
                                type: boolean
                        -   name: 'reward:name'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'reward:code'
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: 'reward:value'
                            in: query
                            required: false
                            schema:
                                type: number
                        -   ref: "#/components/parameters/page"
                        -   ref: "#/components/parameters/itemsOnPage"
                        -   ref: "#/components/parameters/orderBy"
                    responses:
                        200:
                            description: List of tiers
                            content:
                                application/json:
                                    schema:
                                        required:
                                            - items
                                            - total
                                        properties:
                                            items:
                                                type: array
                                                items:
                                                    $ref: '#/components/schemas/TierResponse'
                                            total:
                                                ref: '#/components/schemas/SearchableTotalResponse'
                        400:
                            $ref: '#/components/responses/BadRequest'
                        401:
                            description: Unauthorized
                            content:
                                application/json:
                                    schema:
                                        oneOf:
                                            -   $ref: '#/components/schemas/ExpiredToken'
                                            -   $ref: '#/components/schemas/InvalidToken'
                                            -   $ref: '#/components/schemas/Unauthorized'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                        404:
                            $ref: '#/components/responses/NotFound'
            /api/{storeCode}/tier/{tier}:
                get:
                    operationId: _tierGet
                    tags:
                        - Tier
                    summary: Get tier’s details
                    description: |
                        <label style="background-color: #D4EDBC;padding:5px;">Management</label><br><br>
                        This method returns a specific Tier from the system.
                    parameters:
                        -   ref: "#/components/parameters/storeCode"
                        -   ref: "#/components/parameters/tier"
                    responses:
                        200:
                            description: Tier details.
                            content:
                                application/json:
                                    schema:
                                        $ref: '#/components/schemas/TierResponse'
                        401:
                            description: Unauthorized
                            content:
                                application/json:
                                    schema:
                                        oneOf:
                                            -   $ref: '#/components/schemas/ExpiredToken'
                                            -   $ref: '#/components/schemas/InvalidToken'
                                            -   $ref: '#/components/schemas/Unauthorized'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                        404:
                            $ref: '#/components/responses/NotFound'
            /api/{storeCode}/tierSet:
                post:
                    operationId: _tierSetPost
                    tags:
                        - Tier
                    summary: Add a tier set
                    parameters:
                        -   ref: "#/components/parameters/storeCode"
                    requestBody:
                        required: true
                        content:
                            application/json:
                                schema:
                                    ref: '#/components/schemas/PostTierSet'
                    responses:
                        200:
                            description: Successfully created tier set .
                            content:
                                application/json:
                                    schema:
                                        type: object
                                        properties:
                                            tierSetId:
                                                type: string
                                                format: uuid
                                                description: Created tierSet identity
                                                example: 00000000-0000-0000-0000-000000000000
                        400:
                            $ref: '#/components/responses/BadRequest'
                        401:
                            description: Unauthorized
                            content:
                                application/json:
                                    schema:
                                        oneOf:
                                            -   $ref: '#/components/schemas/ExpiredToken'
                                            -   $ref: '#/components/schemas/InvalidToken'
                                            -   $ref: '#/components/schemas/Unauthorized'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                get:
                    operationId: _getTierSetList
                    summary: Get tier sets list
                    tags:
                        - Tier
                    parameters:
                        -   ref: "#/components/parameters/storeCode"
                        -   name: name
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: active
                            description: Filter by active filed
                            in: query
                            required: false
                            schema:
                                type: boolean
                        -   name: createdAt
                            in: query
                            required: false
                            schema:
                                type: string
                                format: date-time
                        -   ref: "#/components/parameters/page"
                        -   ref: "#/components/parameters/itemsOnPage"
                        -   ref: "#/components/parameters/orderBy"
                    responses:
                        200:
                            description: Tier set list.
                            content:
                                application/json:
                                    schema:
                                        required:
                                            - items
                                            - total
                                        properties:
                                            items:
                                                type: array
                                                items:
                                                    $ref: '#/components/schemas/TierSetResponseList'
                                            total:
                                                ref: '#/components/schemas/SearchableTotalResponse'
                        401:
                            description: Unauthorized
                            content:
                                application/json:
                                    schema:
                                        oneOf:
                                            -   $ref: '#/components/schemas/ExpiredToken'
                                            -   $ref: '#/components/schemas/InvalidToken'
                                            -   $ref: '#/components/schemas/Unauthorized'
                        403:
                            $ref: '#/components/responses/AccessDenied'
            /api/{storeCode}/member/{member}/tierSet/{tierSet}:
                get:
                    operationId: _tierSetMemberProgress
                    tags:
                        - Tier
                    summary: Return tier set member progress.
                    description: 'This method returns detailed member progress information for a specific tier set'
                    parameters:
                        -   ref: "#/components/parameters/storeCode"
                        -   name: member
                            in: path
                            required: true
                            schema:
                                type: string
                                pattern: "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}|email=[a-zA-Z0-9!#$%&'\\.*+\\-@=?^_`{|}~;]+|phone=[0-9+]+|loyaltyCardNumber=[0-9a-zA-Z\\._-]+"
                        -   ref: "#/components/parameters/tierSet"
                    responses:
                        200:
                            description: Tier set member progress.
                            content:
                                application/json:
                                    schema:
                                        $ref: "#/components/schemas/TierSetMemberProgress"

                        400:
                            $ref: '#/components/responses/BadRequest'
                        401:
                            description: Unauthorized
                            content:
                                application/json:
                                    schema:
                                        oneOf:
                                            -   $ref: '#/components/schemas/ExpiredToken'
                                            -   $ref: '#/components/schemas/InvalidToken'
                                            -   $ref: '#/components/schemas/Unauthorized'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                        404:
                            $ref: '#/components/responses/NotFound'
            /api/{storeCode}/member/{member}/tierSet:
                get:
                    operationId: _memberTierSetList
                    tags:
                        - Tier
                    summary: Get list of tier sets which member is assigned
                    parameters:
                        -   ref: "#/components/parameters/storeCode"
                        -   name: member
                            in: path
                            required: true
                            schema:
                                type: string
                                pattern: "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}|email=[a-zA-Z0-9!#$%&'\\.*+\\-@=?^_{|}~;]+|phone=[0-9+]+|loyaltyCardNumber=[0-9a-zA-Z\\._-]+"
                        -   name: currentTierId
                            description: Filter by current tier id field
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: tierSetId
                            description: Filter by tier set Id field
                            in: query
                            required: false
                            schema:
                                type: string
                    responses:
                        200:
                            description: List of member tier sets.
                            content:
                                application/json:
                                    schema:
                                        required:
                                            - items
                                            - total
                                        properties:
                                            items:
                                                type: array
                                                items:
                                                    $ref: '#/components/schemas/MemberTierSet'
                                            total:
                                                ref: '#/components/schemas/SearchableTotalResponse'
                        400:
                            $ref: '#/components/responses/BadRequest'
                        401:
                            description: Unauthorized
                            content:
                                application/json:
                                    schema:
                                        oneOf:
                                            -   $ref: '#/components/schemas/ExpiredToken'
                                            -   $ref: '#/components/schemas/InvalidToken'
                                            -   $ref: '#/components/schemas/Unauthorized'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                        404:
                            $ref: '#/components/responses/NotFound'
            /api/{storeCode}/tierSet/{tierSet}:
                put:
                    operationId: _tierSetPut
                    tags:
                        - Tier
                    summary: Update a tier set
                    description: |
                        <label style="background-color: #D4EDBC;padding:5px;">Management</label><br><br>
                    parameters:
                        -   ref: "#/components/parameters/storeCode"
                        -   ref: "#/components/parameters/tierSet"
                    requestBody:
                        required: true
                        content:
                            application/json:
                                schema:
                                    ref: '#/components/schemas/PutTierSet'
                    responses:
                        204:
                            $ref: '#/components/responses/NoContent'
                        400:
                            $ref: '#/components/responses/BadRequest'
                        401:
                            description: Unauthorized
                            content:
                                application/json:
                                    schema:
                                        oneOf:
                                            -   $ref: '#/components/schemas/ExpiredToken'
                                            -   $ref: '#/components/schemas/InvalidToken'
                                            -   $ref: '#/components/schemas/Unauthorized'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                        404:
                            $ref: '#/components/responses/NotFound'
                get:
                    operationId: _tierSetGet
                    tags:
                        - Tier
                    summary: Get tier set’s details
                    description: |
                        <label style="background-color: #D4EDBC;padding:5px;">Management</label><br><br>
                    parameters:
                        -   ref: "#/components/parameters/storeCode"
                        -   ref: "#/components/parameters/tierSet"
                    responses:
                        200:
                            description: Tier set details
                            content:
                                application/json:
                                    schema:
                                        $ref: '#/components/schemas/TierSetResponse'
                        401:
                            description: Unauthorized
                            content:
                                application/json:
                                    schema:
                                        oneOf:
                                            -   $ref: '#/components/schemas/ExpiredToken'
                                            -   $ref: '#/components/schemas/InvalidToken'
                                            -   $ref: '#/components/schemas/Unauthorized'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                        404:
                            $ref: '#/components/responses/NotFound'
            /api/{storeCode}/tierSet/{tierSet}/tiers:
                get:
                    operationId: _getTierSetTiers
                    tags:
                        - Tier
                    summary: Get tiers list from a tier set
                    description: |
                        <label style="background-color: #D4EDBC;padding:5px;">Management</label><br><br>
                    parameters:
                        -   ref: "#/components/parameters/storeCode"
                        -   ref: "#/components/parameters/tierSet"
                        -   name: name
                            in: query
                            required: false
                            schema:
                                type: string
                        -   name: active
                            in: query
                            required: false
                            schema:
                                type: boolean
                        -   name: createdAt
                            in: query
                            required: false
                            schema:
                                type: string
                                format: date-time
                        -   ref: "#/components/parameters/page"
                        -   ref: "#/components/parameters/itemsOnPage"
                        -   ref: "#/components/parameters/orderBy"
                    responses:
                        200:
                            description: Tiers list
                            content:
                                application/json:
                                    schema:
                                        type: object
                                        required:
                                            - items
                                            - total
                                        properties:
                                            items:
                                                type: array
                                                items:
                                                    $ref: '#/components/schemas/TierSetTiersResponse'
                                            total:
                                                ref: '#/components/schemas/SearchableTotalResponse'
                        401:
                            description: Unauthorized
                            content:
                                application/json:
                                    schema:
                                        oneOf:
                                            -   $ref: '#/components/schemas/ExpiredToken'
                                            -   $ref: '#/components/schemas/InvalidToken'
                                            -   $ref: '#/components/schemas/Unauthorized'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                put:
                    operationId: _tierPutList
                    tags:
                        - Tier
                    summary: Update existing tier list with conditions
                    description: |
                        <label style="background-color: #D4EDBC;padding:5px;">Management</label><br><br>
                        Method allows to edit existing tier list with conditions.
                    parameters:
                        -   ref: "#/components/parameters/storeCode"
                        -   ref: "#/components/parameters/tierSet"
                    requestBody:
                        required: true
                        content:
                            application/json:
                                schema:
                                    type: object
                                    required: [ 'tiers' ]
                                    properties:
                                        tiers:
                                            $ref: '#/components/schemas/TiersListPut'
                    responses:
                        204:
                            $ref: '#/components/responses/NoContent'
                        400:
                            $ref: '#/components/responses/BadRequest'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                        404:
                            $ref: '#/components/responses/NotFound'

        components:
            schemas:
                TiersListPut:
                    type: array
                    items:
                        type: object
                        additionalProperties: false
                        required: [ 'conditions', 'translations' ]
                        properties:
                            levelId:
                                type: string
                                format: uuid
                                description: "Leave empty to add a new tier. Use existing id to update specific tier."
                            translations:
                                $ref: '#/components/schemas/Translations'
                            active:
                                type: boolean
                                description: "Tier availability"
                            conditions:
                                description: "Conditions are determined by the tier set configuration"
                                type: array
                                items:
                                    type: object
                                    required: [ 'conditionId', 'value' ]
                                    properties:
                                        conditionId:
                                            type: string
                                            format: uuid
                                            description: "Relevant conditionId from the tier set configuration"
                                        value:
                                            type: float
                                            description: "Minimal value for the tier. Remember it must not be lower than the lower tier’s value."
                            rewards:
                                type: array
                                description: "Lifetime benefits associated with tier."
                                items:
                                    type: object
                                    required: [ 'name', 'code' ]
                                    properties:
                                        rewardId:
                                            type: string
                                            format: uuid
                                            description: "Reward ID. Leave empty to to add a new benefit."
                                        name:
                                            type: string
                                            description: "Benefit name."
                                        value:
                                            type: number
                                            format: float
                                            description: "Benefit value."
                                        code:
                                            type: string
                                            description: "Benefit code."
                                        labels:
                                            type: array
                                            description: "Custom attributes"
                                            items:
                                                type: object
                                                properties:
                                                    key:
                                                        type: string
                                                        description: "Custom attribute key"
                                                    value:
                                                        type: string
                                                        description: "Custom attribute value"
                                        active:
                                            type: boolean
                                            description: "Benefit availability"
                                        startAt:
                                            type: string
                                            format: date-time
                                            description: "Benefit availability start date"
                                        endAt:
                                            type: string
                                            format: date-time
                                            description: "Benefit availability end date"
                TierSetResponseList:
                    additionalProperties: false
                    required:
                        - tierSetId
                        - name
                        - translations
                        - active
                        - conditions
                        - downgrade
                        - createdAt
                        - updatedAt
                        - isMigrated
                        - tiers
                        - labels
                        - isDefault
                    type: object
                    properties:
                        tierSetId:
                            type: string
                            format: uuid
                            description: Tier set id
                        name:
                            type: string
                            description: Tier set name
                        description:
                            type: string
                            description: Tier set description
                        active:
                            type: boolean
                            description: The field shows whether the tier set is active
                        createdAt:
                            type: string
                            format: date-time
                            description: Tier set created at date
                        updatedAt:
                            type: string
                            format: date-time
                            description: Tier set updated at date
                        labels:
                            $ref: '#/components/schemas/Labels'
                        conditions:
                            description: Conditions are determined by the tier set configuration
                            type: array
                            items:
                                type: object
                                additionalProperties: false
                                required:
                                    - id
                                    - attribute
                                properties:
                                    id:
                                        type: string
                                        format: uuid
                                    attribute:
                                        type: string
                                        description: >
                                            Specifies the type of attribute. The possible values are:
                                            
                                            - `activeUnits`: The number of units currently active.
                                            - `totalEarnedUnits`: The total number of units earned.
                                            - `monthsSinceJoiningProgram`: The number of months since the program was joined.
                                            - `totalSpending`: The total amount of spending.
                                            - `cumulatedEarnedUnits`: The total number of units earned that will be reset when recalculation occur
                                        enum:
                                            - activeUnits
                                            - totalEarnedUnits
                                            - monthsSinceJoiningProgram
                                            - totalSpending
                                            - cumulatedEarnedUnits
                                    walletType:
                                        type: string
                                        example: default
                                        description: This field occurs for attributes related to units
                        tiers:
                            type: array
                            description: This field return tiers belong to tier set
                            items:
                                type: object
                                additionalProperties: false
                                required:
                                    - levelId
                                    - name
                                    - active
                                properties:
                                    levelId:
                                        type: string
                                        format: uuid
                                    name:
                                        type: string
                                    description:
                                        type: string
                                    active:
                                        description: The field shows whether the tier is active
                        translations:
                            $ref: '#/components/schemas/Translations'
                        isMigrated:
                            type: boolean
                            description: This field shows whether the tier set has been migrated from the old tier system
                        isDefault:
                            type: boolean
                            description: This field shows which tier set was added first, which means that the tier set is default
                        downgrade:
                            $ref: '#/components/schemas/Downgrade'
                TierResponse:
                    type: object
                    properties:
                        levelId:
                            type: string
                            format: uuid
                            description: This field show tier id
                        name:
                            type: string
                            description: This field show tier name
                        description:
                            type: string
                            description: This field show tier description
                        hasPhoto:
                            type: boolean
                            description: This field show if tier have photo
                        storeCode:
                            type: string
                            description: The field shows what store tier it is in
                        tierSet:
                            type: object
                            description: This field shows related tier set
                            additionalProperties: false
                            required:
                                - tierSetId
                                - name
                            properties:
                                tierSetId:
                                    type: string
                                    format: uuid
                                    description: This field shows tier set id
                                name:
                                    type: string
                                    description: This field shows tier set name
                        conditions:
                            type: array
                            description: This field shows conditions that member must meet to be at this tier
                            items:
                                type: object
                                additionalProperties: false
                                required:
                                    - conditionId
                                    - attribute
                                    - value
                                properties:
                                    conditionId:
                                        type: string
                                        format: uuid
                                    attribute:
                                        description: >
                                            Specifies the type of attribute. The possible values are:

                                            - `activeUnits`: The number of units currently active.
                                            - `totalEarnedUnits`: The total number of units earned.
                                            - `monthsSinceJoiningProgram`: The number of months since the program was joined.
                                            - `totalSpending`: The total amount of spending.
                                            - `cumulatedEarnedUnits`: The total number of units earned that will be reset when recalculation occur
                                        enum:
                                            - activeUnits
                                            - totalEarnedUnits
                                            - monthsSinceJoiningProgram
                                            - totalSpending
                                            - cumulatedEarnedUnits
                                    value:
                                        type: string
                                        description: This field shows the value that was specified for the attribute
                        active:
                            type: boolean
                            description: The field shows whether the tier is active
                        rewards:
                            type: array
                            description: "Lifetime benefits associated with tier."
                            items:
                                type: object
                                additionalProperties: false
                                required:
                                    - rewardId
                                    - name
                                    - code
                                    - active
                                    - createdAt
                                    - updatedAt
                                properties:
                                    rewardId:
                                        type: object
                                        description: "Reward ID."
                                        required:
                                            - rewardId
                                        properties:
                                            rewardId:
                                                type: string
                                                format: uuid
                                    name:
                                        type: string
                                        description: "Benefit name."
                                    value:
                                        type: number
                                        format: float
                                        description: "Benefit value."
                                    code:
                                        type: string
                                        description: "Benefit code."
                                    labels:
                                        $ref: '#/components/schemas/Labels'
                                    active:
                                        type: boolean
                                        description: "Benefit availability"
                                    startAt:
                                        type: string
                                        format: date-time
                                        description: "Benefit availability start date"
                                    endAt:
                                        type: string
                                        format: date-time
                                        description: "Benefit availability end date"
                                    createdAt:
                                        type: string
                                        format: date-time
                                        description: "Benefit created at date"
                                    updatedAt:
                                        type: string
                                        format: date-time
                                        description: "Benefit updated at date"
                                    createdBy:
                                        type: string
                                        format: uuid
                                        description: "Benefit created by"
                                    updatedBy:
                                        type: string
                                        format: uuid
                                        description: "Benefit updated by"
                        sortOrder:
                            type: number
                            description: This field shows order of tiers, the higher the number, the higher the tier.
                        isDefault:
                            type: boolean
                            description: This field shows whether the tier is the default tier that was created automatically along with the tier set. Tier serves as the starting tier in tie set
                        translations:
                            type: array
                            items:
                                type: object
                                required:
                                    - id
                                    - locale
                                properties:
                                    id:
                                        type: integer
                                    name:
                                        type: string
                                    description:
                                        type: string
                                    locale:
                                        type: string
                        createdAt:
                            type: string
                            format: date-time
                            description: tier created at date
                        updatedAt:
                            type: string
                            format: date-time
                            description: tier updated at date
                        createdBy:
                            type: string
                            format: uuid
                            description: tier created by
                        updatedBy:
                            type: string
                            format: uuid
                            description: tier updated by
                        conditionValue:
                            type: number
                            format: float
                            deprecated: true
                    additionalProperties: false
                    required:
                        - levelId
                        - name
                        - hasPhoto
                        - storeCode
                        - active
                        - rewards
                        - translations
                        - createdAt
                        - updatedAt
                        - sortOrder
                        - isDefault
                        - conditions
                MemberTierSet:
                    type: object
                    properties:
                        currentTierId:
                            type: string
                            format: uuid
                            description: Current member tier for tier set
                        tierSetId:
                            type: string
                            format: uuid
                            description: Tier set Id
                        tierSetName:
                            type: string
                            description: Tier set name
                        currentTierName:
                            type: string
                            description: Tier name
                        manually:
                            type: boolean
                            description: The field will indicate whether a given tier in tier set has been assigned manually
                    additionalProperties: false
                    required:
                        - currentTierId
                        - tierSetId
                        - tierSetName
                        - currentTierName
                        - manually
                TierSetTiersResponse:
                    type: object
                    properties:
                        levelId:
                            type: string
                            format: uuid
                            description: This field show tier id
                        tierSet:
                            type: object
                            description: This field shows related tier set
                            additionalProperties: false
                            required:
                                - tierSetId
                                - name
                            properties:
                                tierSetId:
                                    type: object
                                    properties:
                                        tierSetId:
                                            type: string
                                            format: uuid
                                name:
                                    type: string
                        name:
                            type: string
                            description: This field show tier name
                        description:
                            type: string
                            description: This field show tier description
                        storeCode:
                            type: string
                            description: The field shows what store tier it is in
                        active:
                            type: boolean
                            description: The field shows whether the tier is active
                        rewards:
                            type: array
                            items:
                                type: object
                                additionalProperties: false
                                required:
                                    - rewardId
                                    - name
                                    - code
                                    - active
                                    - createdAt
                                    - updatedAt
                                properties:
                                    rewardId:
                                        type: string
                                        format: uuid
                                        description: "Reward ID."
                                    name:
                                        type: string
                                        description: "Benefit name."
                                    value:
                                        type: number
                                        format: float
                                        description: "Benefit value."
                                    code:
                                        type: string
                                        description: "Benefit code."
                                    labels:
                                        $ref: '#/components/schemas/Labels'
                                    active:
                                        type: boolean
                                    startAt:
                                        type: string
                                        format: date-time
                                    endAt:
                                        type: string
                                        format: date-time
                                    createdAt:
                                        type: string
                                        format: date-time
                                    updatedAt:
                                        type: string
                                        format: date-time
                                    createdBy:
                                        type: string
                                        format: uuid
                                    updatedBy:
                                        type: string
                                        format: uuid
                        conditions:
                            type: array
                            description: This field shows conditions that member must meet to be at this tier
                            items:
                                type: object
                                additionalProperties: false
                                required:
                                    - conditionId
                                    - attribute
                                    - value
                                properties:
                                    conditionId:
                                        type: string
                                        format: uuid
                                    attribute:
                                        description: >
                                            Specifies the type of attribute. The possible values are:
                                            
                                            - `activeUnits`: The number of units currently active.
                                            - `totalEarnedUnits`: The total number of units earned.
                                            - `monthsSinceJoiningProgram`: The number of months since the program was joined.
                                            - `totalSpending`: The total amount of spending.
                                            - `cumulatedEarnedUnits`: The total number of units earned that will be reset when recalculation occur
                                        enum:
                                            - activeUnits
                                            - totalEarnedUnits
                                            - monthsSinceJoiningProgram
                                            - totalSpending
                                            - cumulatedEarnedUnits
                                    value:
                                        type: number
                                        format: float
                                        description: This field shows the value that was specified for the attribute
                        translations:
                            $ref: '#/components/schemas/Translations'
                        createdAt:
                            type: string
                            format: date-time
                            description: This field shows date then tier was created at
                        updatedAt:
                            type: string
                            format: date-time
                            description: This field shows date then tier was updated at
                        hasPhoto:
                            type: boolean
                            description: This field show if tier have photo
                        conditionValue:
                            deprecated: true
                            type: number
                            format: float
                        isDefault:
                            type: boolean
                            description: This field shows whether the tier is the default tier that was created automatically along with the tier set. Tier serves as the starting tier in tie set
                    additionalProperties: false
                    required:
                        - levelId
                        - tierSet
                        - name
                        - storeCode
                        - active
                        - rewards
                        - createdAt
                        - updatedAt
                        - translations
                        - isDefault
                        - hasPhoto
                        - conditions
                TierSetMemberProgress:
                    type: object
                    properties:
                        currentTierId:
                            type: string
                            format: uuid
                            description: The field displays the member's current tier id
                        currentTierName:
                            type: string
                            description: The field displays the member's current tier name
                        nextTierId:
                            type: string
                            format: uuid
                            description: The field displays the member's next tier id
                        nextTierName:
                            type: string
                            description: The field displays the member's next tier name
                        tierSetId:
                            type: string
                            format: uuid
                            description: The field will show the tierSet id in which the tier is located
                        tierSetName:
                            type: string
                            description: The field will show the tierSet id in which the tier is located
                        currentProgress:
                            type: float
                            description: Shows you your current progress percentage and what progress you are missing to reach the next tier
                        lastPromotionAt:
                            type: string
                            format: date-time
                            description: Shows when the last promotion to a higher tier occurred
                        lastDowngradeAt:
                            type: string
                            format: date-time
                            description: Shows when the last downgrade to a lower tier occurred
                        nextRecalculationAt:
                            type: string
                            format: date-time
                            description: Shows the date of the next recalculation
                        manually:
                            type: boolean
                            description: Shows whether the current tier has been assigned manually
                        downgrade:
                            type: string
                            description: Shows downgrade mode
                        nextTierCurrentProgress:
                            type: array
                            items:
                                type: object
                                properties:
                                    conditionId:
                                        type: string
                                        format: uuid
                                        description: Shows conditions id
                                    attribute:
                                        type: string
                                        description: The field shows condition attribute
                                        enum:
                                            - activeUnits
                                            - totalEarnedUnits
                                            - monthsSinceJoiningProgram
                                            - totalSpending
                                            - cumulatedEarnedUnits
                                    currentValue:
                                        type: float
                                        description: The field shows your current value for condition attribute
                                    valueGoal:
                                        type: float
                                        description: The field shows what value to aim for to reach the next level
                                    walletType:
                                        type: string
                                        description: This field shows chosen wallet only for attributes related to units
                                additionalProperties: false
                                required:
                                    - conditionId
                                    - attribute
                    additionalProperties: false
                    required:
                        - tierSetId
                        - tierSetName
                        - downgrade
                        - manually
                        - nextTierCurrentProgress
                PostTierSet:
                    type: object
                    required:
                        - tierSet
                    additionalProperties: false
                    properties:
                        tierSet:
                            type: object
                            additionalProperties: false
                            required:
                                - translations
                                - conditions
                            properties:
                                translations:
                                    $ref: '#/components/schemas/Translations'
                                active:
                                    type: boolean
                                    description: The field determines whether the tier set will be active
                                labels:
                                    $ref: '#/components/schemas/Labels'
                                conditions:
                                    description: Conditions settings
                                    type: array
                                    items:
                                        type: object
                                        additionalProperties: false
                                        required:
                                            - attribute
                                        properties:
                                            attribute:
                                                type: string
                                                description: >
                                                    Specifies the type of attribute. The possible values are:
                                                    
                                                    - `activeUnits`: The number of units currently active.
                                                    - `totalEarnedUnits`: The total number of units earned.
                                                    - `monthsSinceJoiningProgram`: The number of months since the program was joined.
                                                    - `totalSpending`: The total amount of spending.
                                                    - `cumulatedEarnedUnits`: The total number of units earned that will be reset when recalculation occur
                                                enum:
                                                    - activeUnits
                                                    - totalEarnedUnits
                                                    - monthsSinceJoiningProgram
                                                    - totalSpending
                                                    - cumulatedEarnedUnits
                                            walletType:
                                                type: string
                                                example: default
                                                description: This field is only required for attributes related to units
                                    example:
                                        -   attribute: activeUnits
                                            walletType: default
                                        -   attribute: totalEarnedUnits
                                            walletType: default
                                        -   attribute: monthsSinceJoiningProgram
                                downgrade:
                                    $ref: '#/components/schemas/Downgrade'
                PutTierSet:
                    type: object
                    required:
                        - tierSet
                    additionalProperties: false
                    properties:
                        tierSet:
                            type: object
                            additionalProperties: false
                            required:
                                - translations
                                - conditions
                            properties:
                                translations:
                                    $ref: '#/components/schemas/Translations'
                                active:
                                    type: boolean
                                    description: The field determines whether the tier set will be active
                                labels:
                                    $ref: '#/components/schemas/Labels'
                                conditions:
                                    type: array
                                    items:
                                        type: object
                                        additionalProperties: false
                                        required:
                                            - attribute
                                        properties:
                                            id:
                                                type: string
                                                format: uuid
                                                description: "Leave empty to add a new condition. Use existing id to update specific condition."
                                            attribute:
                                                type: string
                                                description: >
                                                    Specifies the type of attribute. The possible values are:

                                                    - `activeUnits`: The number of units currently active.
                                                    - `totalEarnedUnits`: The total number of units earned.
                                                    - `monthsSinceJoiningProgram`: The number of months since the program was joined.
                                                    - `totalSpending`: The total amount of spending.
                                                    - `cumulatedEarnedUnits`: The total number of units earned that will be reset when recalculation occur
                                                enum:
                                                    - activeUnits
                                                    - totalEarnedUnits
                                                    - monthsSinceJoiningProgram
                                                    - totalSpending
                                                    - cumulatedEarnedUnits
                                            walletType:
                                                type: string
                                                example: default
                                                description: This field is only required for attributes related to units
                                downgrade:
                                    $ref: '#/components/schemas/Downgrade'
                Labels:
                    type: array
                    description: >
                        A list of custom attributes used for tagging and describing resources.
                        Each item in the list contains a key-value pair, where `key` is the name
                        of the attribute, and `value` is the value assigned to that attribute.
                    items:
                        type: object
                        additionalProperties: false
                        required:
                            - key
                            - value
                        properties:
                            key:
                                type: string
                                description: "The name of the custom attribute."
                            value:
                                type: string
                                description: "The value assigned to the custom attribute."
                Downgrade:
                    type: object
                    description: Downgrade settings
                    additionalProperties: false
                    properties:
                        mode:
                            type: string
                            description: >
                                Specifies the mode of downgrade. The possible values are:

                                - `none`: Downgrade will not occur.
                                
                                - `automatic`: Downgrade will occur automatically when the conditions for being at a given tier are not met.
                                
                                - `periodic`: Downgrade will occur periodically. If this mode is selected, you must also choose a `period`.
                            enum:
                                - none
                                - automatic
                                - periodic
                        period:
                            type: string
                            description: >
                                Specifies the period for periodic downgrades. This is only available when the mode is set to `periodic`.
                                
                                - `registration_anniversary`: Downgrade occurs annually on the anniversary of your registration date. For example, 
                                if you registered on 2020-10-10, the next downgrade will take place on 2025-10-10, followed by 2026-10-10, and so on.
                                
                                - `annual_recalculation_on_chosen_dates`: Allows you to specify a list of dates on which the downgrade will occur every year. For instance,
                                if you add 03-10 and 07-15, downgrades will happen annually on March 10 and July 15.
                                
                                - `recalculation_every_month_after_tier_promotion`: Enables you to set a specific interval (in months) after the last tier promotion for the downgrade to occur. For example, 
                                if your promotion date is 2025-10-10 and the interval is set to 2 months, your downgrade date will be 2025-12-10.
                            enum:
                                - registration_anniversary
                                - annual_recalculation_on_chosen_dates
                                - recalculation_every_month_after_tier_promotion
                                - yearly
                                - monthly
                                - weekly
                        recalculationDates:
                            description: >
                                Specifies dates when recalculation will be annually started. This is only available when the period is set to `annual_recalculation_on_chosen_dates`.
                            type: array
                            items:
                                type: string
                                example: "03-10"
                        interval:
                            description: >
                                Specifies the number of months that must pass after the last tier promotion to start downgrade. This is only available when the period is set to `recalculation_every_month_after_tier_promotion`.
                            type: integer
                            example: 2

