nelmio_api_doc:
    documentation:
        paths:
            /api/store/{store}:
                get:
                    operationId: _storeGet
                    tags:
                        - Store
                    summary: Get tenant’s details
                    description: |
                        <label style="background-color: #D4EDBC;padding:5px;">Management</label><br><br>
                        This method returns a specific Store (tenant).
                    parameters:
                        - ref: "#/components/parameters/store"
                    responses:
                        200:
                            description: Store details.
                            content:
                                application/json:
                                    schema:
                                        $ref: '#/components/schemas/StoreResponse'
                        401:
                            $ref: '#/components/responses/Unauthorized'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                        404:
                            $ref: '#/components/responses/NotFound'
                put:
                    tags:
                        - Store
                    description: |
                        <label style="background-color: #D4EDBC;padding:5px;">Management</label><br><br>
                        This method allows updating a specific Store (tenant) in the system.
            /api/store:
                get:
                    operationId: storeGetList
                    tags:
                        - Store
                    summary: Get tenants list
                    description: |
                        <label style="background-color: #D4EDBC;padding:5px;">Management</label><br><br>
                        This method returns a specific Store (tenant).
                    parameters:
                        -   name: storeId
                            in: query
                            required: false
                            explode: true
                            style: deepObject
                            description: 'Store ID'
                            schema:
                                type: string
                                format: uuid
                        -   name: name
                            in: query
                            required: false
                            description: 'Store name'
                            explode: true
                            style: deepObject
                            schema:
                                type: string
                            examples:
                                equal:
                                    summary: Equal value
                                    value:
                                        eq: value
                        -   name: code
                            in: query
                            required: false
                            explode: true
                            style: deepObject
                            description: 'Code'
                            schema:
                                type: string
                        -   name: currency
                            in: query
                            required: false
                            explode: true
                            style: deepObject
                            description: 'Currency'
                            schema:
                                type: string
                        -   name: active
                            in: query
                            examples:
                                equal:
                                    summary: Equal value
                                    value:
                                        eq: true
                            required: false
                            explode: true
                            style: deepObject
                            description: 'Active'
                            schema:
                                type: boolean
                        - ref: "#/components/parameters/page"
                        - ref: "#/components/parameters/itemsOnPage"
                        - ref: "#/components/parameters/orderBy"
                    responses:
                        200:
                            description: List of stores.
                            content:
                                application/json:
                                    schema:
                                        allOf:
                                            - ref: '#/components/schemas/ListOfItems'
                                            - properties:
                                                  items:
                                                      type: array
                                                      items:
                                                          type: object
                                                          ref: '#/components/schemas/StoreResponse'

                        401:
                            $ref: '#/components/responses/Unauthorized'
                        403:
                            $ref: '#/components/responses/AccessDenied'
                        404:
                            $ref: '#/components/responses/NotFound'
                post:
                    description: |
                        <label style="background-color: #D4EDBC;padding:5px;">Management</label><br><br>
                        This method allows adding a new Store to the system.
        components:
            schemas:
                StoreResponse:
                    type: object
                    additionalProperties: false
                    description: Store
                    required:
                        - storeId
                        - code
                        - currency
                        - name
                        - active
                    properties:
                        storeId:
                            type: string
                            format: uuid
                        code:
                            type: string
                            example: DEFAULT
                        currency:
                            type: string
                            example: USD
                        name:
                            type: string
                        active:
                            type: boolean
                        createdBy:
                            type: string
                        createdAt:
                            type: string
                            format: date-time
                        updatedBy:
                            type: string
                        updatedAt:
                            type: string
                            format: date-time