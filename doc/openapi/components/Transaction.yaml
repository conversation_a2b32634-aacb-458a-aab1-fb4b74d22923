nelmio_api_doc:
  documentation:
    components:
      schemas:
        Transaction:
          type: object
          properties:
            grossValue:
              type: float
              example: 3.0
            metrics:
              type: array
              items:
                type: object
                properties:
                  name:
                    type: string
                  value:
                    type: float
                    example: 1.0
                  calculatedAt:
                    format: date-time
            transactionId:
              type: string
              format: uuid
            customerId:
              type: string
              format: uuid
            customerData:
              type: object
              properties:
                customerId:
                  type: string
                  format: uuid
                email:
                  type: string
                  format: email
                  example: '<EMAIL>'
                name:
                  type: string
                nip:
                  type: string
                phone:
                  type: string
                loyaltyCardNumber:
                  type: string
                address:
                  $ref: '#/components/schemas/CustomerAddressFormType'
            items:
              type: array
              items:
                type: object
                properties:
                  name:
                    type: string
                  quantity:
                    deprecated: true
                    type: integer
                    description: "The quantity of the product. This field is required if 'highPrecisionQuantity' is not provided. It is recommended to use 'highPrecisionQuantity', as 'quantity' is deprecated."
                  highPrecisionQuantity:
                    type: number
                    format: float
                    description: "The precise quantity of the product, with a maximum of 3 decimal places. This field is required if 'quantity' is not provided."
                  grossValue:
                    type: float
                    example: 1.0
                  category:
                    type: string
                  labels:
                    type: array
                    items:
                      type: object
                      properties:
                        key:
                          type: string
                        value:
                          type: string
                  maker:
                    type: string
                  sku:
                    type: string
            header:
              type: object
              properties:
                documentNumber:
                  type: string
                documentType:
                  type: string
                  enum:
                    - sell
                    - return
                linkedDocumentNumber:
                  type: string
                  description: 'Field is used with “documentType”: “return”'
                purchasedAt:
                  type: string
                  format: date-time
                purchasePlace:
                  type: string
                excludedDeliverySKUs:
                  type: array
                  items: { }
                excludedSKUs:
                  type: array
                  items: { }
                excludedCategories:
                  type: array
                  items: { }
                labels:
                  type: array
                  items: { }
            matched:
              type: boolean
            currency:
              type: string
            pointsEarned:
              type: integer
            unitsDeducted:
              type: integer
            channelId:
              type: string
              format: uuid
            channelName:
              type: string
            assignedToCustomerAt:
              nullable: true
              type: string
              format: date-time