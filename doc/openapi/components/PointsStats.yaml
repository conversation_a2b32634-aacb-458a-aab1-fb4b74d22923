nelmio_api_doc:
  documentation:
    components:
      schemas:
        PointsStats:
          type: object
          properties:
            totalPointsActive:
              type: float
              example: 72544.8
            totalPointsSpent:
              type: float
              example: 3425.25
            totalPointsAdded:
              type: float
              example: 47563.0
            totalPointsExpired:
              type: float
              example: 432.9
            totalPointsPending:
              type: float
              example: 65764.76