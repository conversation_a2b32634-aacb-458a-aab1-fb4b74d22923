nelmio_api_doc:
  documentation:
    components:
      schemas:
        TransactionsStats:
          type: object
          properties:
            countIntervals:
              type: object
              properties:
                in_1_days:
                  type: integer
                  example: 2
                in_7_days:
                  type: integer
                  example: 23
                in_30_days:
                  type: integer
                  example: 153
                in_365_days:
                  type: integer
                  example: 345
            total:
              type: integer
              example: 531
            amount:
              type: float
              example: 453.7
              description: transactions total amount
            amountWithoutDeliveryCosts:
              type: float
              example: 389.5
              description: transactions total amount without delivery costs
            currency:
              type: string
              example: USD