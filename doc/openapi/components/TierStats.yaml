nelmio_api_doc:
  documentation:
    components:
      schemas:
        TierStats:
          type: object
          properties:
            levelId:
              type: string
              format: uuid
            name:
              type: string
              example: 'Tier1'
            conditionValue:
              type: float
              example: 3.8
            store:
              type: string
              example: DEFAULT
            customers:
              type: bool
              example: true