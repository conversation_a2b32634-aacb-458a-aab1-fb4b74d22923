nelmio_api_doc:
  documentation:
    components:
      schemas:
        ListOfItems:
          type: object
          properties:
            items:
              type: array
              items:
                type: object
            total:
              type: object
              description: 'Total results'
              deprecated: true
              properties:
                all:
                  type: integer
                  deprecated: true
                filtered:
                  type: integer
                  deprecated: true
                estimated:
                  type: boolean
                  description: 'Field estimated is used to show that the results are estimated. It might happen when there are more than 5000 results due to performance optimization.'
                  deprecated: true