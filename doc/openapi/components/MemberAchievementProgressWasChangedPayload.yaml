nelmio_api_doc:
  documentation:
    components:
      schemas:
        MemberAchievementProgressWasChangedPayload:
          type: object
          properties:
            achievementId:
              type: string
              format: uuid
            completedCount:
              type: integer
            progressStatuses:
              type: object
            trigger:
              type: object
              properties:
                triggerId:
                  type: string
                type:
                  type: string
                  enum:
                    - transaction
                    - custom_event
