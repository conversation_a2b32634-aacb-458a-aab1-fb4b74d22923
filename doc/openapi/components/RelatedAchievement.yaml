nelmio_api_doc:
  documentation:
    components:
      schemas:
        RelatedAchievement:
          type: object
          deprecated: true
          properties:
            achievementId:
              type: string
              format: uuid
            achievementName:
              type: string
            limitReached:
              type: boolean
              description: "This property indicates whether the limit for achievement is reached by member"
            memberProgress:
              $ref: '#/components/schemas/MemberAchievementProgress'
            lastTrigger:
              type: object
              description: "Last trigger which triggered related achievement"
              properties:
                triggerId:
                  type: string
                  format: uuid
                triggerType:
                  type: string
                  enum:
                    - transaction
                    - custom_event