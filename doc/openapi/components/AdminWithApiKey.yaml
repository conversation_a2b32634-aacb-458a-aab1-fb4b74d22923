nelmio_api_doc:
  documentation:
    components:
      schemas:
        AdminWithApiKey:
          type: object
          properties:
            admin:
              type: object
              properties:
                plainPassword:
                  type: string
                external:
                  type: boolean
                  example: false
                apiKey:
                  type: string
                  deprecated: true
                  description: Use "apiKeyPost" endpoint instead.
                isActive:
                  type: boolean
                notificationsEnabled:
                  type: boolean
                firstName:
                  type: string
                lastName:
                  type: string
                phone:
                  type: string
                roles:
                  type: array
                  items:
                    type: integer
                email:
                  type: string
              required:
                - email
                - roles
                - apiKey