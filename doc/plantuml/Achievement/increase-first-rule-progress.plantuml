@startuml
skinparam noteTextAlignment center

title
Increase first rule progress
end title

actor Client as CL
box Open Loyalty
participant Transaction as T
participant Achievement as A
participant Campaign as C
participant Messaging as M
participant Queue as SQS
end box
participant "External system" as ES

note over A
**Initial achievement state:**

Completion count = 0

**Rule I**
trigger = transaction
goal = 3
currentProgress = 0
aggregation = number of event occurrences

**Rule II**
trigger = custom event
goal = 3
currentProgress = 0
aggregation = number of event occurrences
end note

CL -> T: Send transaction
activate CL
activate T

T --> CL: Response
deactivate CL

T ->> SQS: Match transaction
deactivate T

SQS o->> T: Handle match transaction
activate T

T -> A: Internal event: **TransactionAssignedToCustomer**
deactivate T
activate A

A ->> SQS: Run achievement
deactivate A

SQS o->> A: Handle run achievement
activate A

note over A
**Achievement state after client request:**

**Rule I**
currentProgress = 1
end note

par

A -> C: Internal event:\n**MemberAchievementProgressWasChanged**
activate C
C ->> SQS: Run campaign
deactivate C

SQS o->> C: Handle run campaign
activate C
deactivate C

A -> M: Internal event:\n**MemberAchievementProgressWasChanged**
deactivate A
activate M

M ->> SQS: Send webhook event
deactivate M

SQS o->> M: Handle send webhook event
activate M

M -> ES: Send webhook:\n**MemberAchievementProgressWasChanged**
activate ES
ES --> M: Response
deactivate ES
deactivate M

end par

note over ES
**Webhook payload:**

**Achievement:**
Completion count = 0

**Rules:**

**Rule I:**
trigger = transaction
goal = 3
currentProgress = 1
aggregation = number of event occurrences

**Rule II:**
trigger = custom event
goal = 3
currentProgress = 0
aggregation = number of event occurrences
end note

@enduml
