@startuml
skinparam noteTextAlignment center

title
Manually increase progress for two rules and decrease achievement completion
end title

actor Client as CL
box Open Loyalty
participant Achievement as A
participant Campaign as C
participant Messaging as M
participant Queue as SQS
end box
participant "External system" as ES

note over A
**Initial achievement state:**

Completion count = 2

**Rule I**
trigger = transaction
goal = 3
currentProgress = 1
aggregation = number of event occurrences

**Rule II**
trigger = transaction
goal = 3
currentProgress = 50
aggregation = number of specific event attributes
end note

CL -> A: Manually change achievement progress
activate CL
activate A
note right
**Manually change request payload:**

Completion count = 1

**Rule I**
currentProgress = 2

**Rule II**
currentProgress = 70
end note

A -> A: Update achievement progress

par

A -> C: Internal event:\n**MemberAchievementProgressWasChanged**
activate C
C ->> SQS: Run campaign
deactivate C

SQS o->> C: Handle run campaign
activate C
deactivate C

A -> M: Internal event:\n**MemberAchievementProgressWasChanged**
activate M

M ->> SQS: Send webhook event
deactivate M

SQS o->> M: Handle send webhook event
activate M

M -> ES: Send webhook:\n**MemberAchievementProgressWasChanged**
activate ES
ES --> M: Response
deactivate ES
deactivate M

end par

note over A
**Achievement state after client request:**

Completion count = 1

**Rule I**
currentProgress = 2

**Rule II**
currentProgress = 70
end note

A --> CL: Response
deactivate A
deactivate CL

note over ES
**Webhook payload:**

**Achievement:**
Completion count = 1

**Rules:**

**Rule I:**
trigger = transaction
goal = 3
currentProgress = 2
aggregation = number of event occurrences

**Rule II:**
trigger = custom event
goal = 100
currentProgress = 70
aggregation = number of specific event attributes
end note

@enduml
