services:
    Aws\S3\S3Client:
        lazy: true
    Aws\S3\S3ClientLocal:
        lazy: true
        class: Aws\S3\S3Client
        arguments:
            -
                version: 'latest'
                region: '%env(AMAZON_S3_REGION)%'
                endpoint: '%env(AWS_LOCAL_S3_ENDPOINT)%'
                use_path_style_endpoint: true
                credentials:
                    key: '%env(AMAZON_S3_KEY)%'
                    secret: '%env(AMAZON_S3_SECRET)%'
    Aws\Sdk:
        lazy: true

flysystem:
    storages:
        # EXPORT ASYNC
        export_async.storage.aws:
            adapter: 'aws'
            options:
                client: 'Aws\S3\S3Client'
                bucket: '%env(AMAZON_S3_BUCKET_NAME_PRIVATE)%'
                prefix: 'export_async'

        export_async.storage.aws_local:
            adapter: 'aws'
            options:
                client: 'Aws\S3\S3ClientLocal'
                bucket: '%env(AMAZON_S3_BUCKET_NAME_PRIVATE)%'
                prefix: 'export_async'

        export_async.storage.local:
            adapter: 'local'
            options:
                directory: '%kernel.project_dir%/var/storage/export_async'
                permissions:
                    file:
                        private: 0o644
                    dir:
                        private: 0o755

        export_async.storage:
            adapter: 'lazy'
            options:
                source: 'export_async.storage.%env(DEFAULT_STORAGE_ADAPTER)%'

        # IMPORT ASYNC
        import_async.storage.aws:
            adapter: 'aws'
            options:
                client: 'Aws\S3\S3Client'
                bucket: '%env(AMAZON_S3_BUCKET_NAME_PRIVATE)%'
                prefix: 'import_async'

        import_async.storage.aws_local:
            adapter: 'aws'
            options:
                client: 'Aws\S3\S3ClientLocal'
                bucket: '%env(AMAZON_S3_BUCKET_NAME_PRIVATE)%'
                prefix: 'import_async'

        import_async.storage.local:
            adapter: 'local'
            options:
                directory: '%env(DEFAULT_STORAGE_DIRECTORY)%%kernel.project_dir%/var/storage/import_async'
                permissions:
                    file:
                        private: 0o644
                    dir:
                        private: 0o755

        import_async.storage:
            adapter: 'lazy'
            options:
                source: 'import_async.storage.%env(DEFAULT_STORAGE_ADAPTER)%'

        # ANALYTICS
        analytics.storage.aws:
            adapter: 'aws'
            options:
                client: 'Aws\S3\S3Client'
                bucket: '%env(AMAZON_S3_BUCKET_NAME_ANALYTICS)%'
                prefix: 'analytics'

        analytics.storage.aws_local:
            adapter: 'aws'
            options:
                client: 'Aws\S3\S3ClientLocal'
                bucket: '%env(AMAZON_S3_BUCKET_NAME_ANALYTICS)%'
                prefix: 'analytics'

        analytics.storage.local:
            adapter: 'local'
            options:
                directory: '%kernel.project_dir%/var/storage/analytics'
                permissions:
                    file:
                        private: 0o644
                    dir:
                        private: 0o755

        analytics.storage:
            adapter: 'lazy'
            options:
                source: 'analytics.storage.%env(DEFAULT_STORAGE_ADAPTER)%'

        # ARCHIVES
        archive.storage.aws:
            adapter: 'aws'
            options:
                client: 'Aws\S3\S3Client'
                bucket: '%env(AMAZON_S3_BUCKET_NAME_ARCHIVES)%'
                prefix: 'archives'

        archive.storage.aws_local:
            adapter: 'aws'
            options:
                client: 'Aws\S3\S3ClientLocal'
                bucket: '%env(AMAZON_S3_BUCKET_NAME_ARCHIVES)%'
                prefix: 'archives'

        archive.storage.local:
            adapter: 'local'
            options:
                directory: '%kernel.project_dir%/var/storage/archives'
                permissions:
                    file:
                        private: 0o644
                    dir:
                        private: 0o755

        archive.storage:
            adapter: 'lazy'
            options:
                source: 'archive.storage.%env(DEFAULT_STORAGE_ADAPTER)%'

        # UPLOAD
        upload.storage.aws:
            adapter: 'aws'
            options:
                client: 'Aws\S3\S3Client'
                bucket: '%env(AMAZON_S3_BUCKET_NAME_PRIVATE)%'
                prefix: 'uploads'

        upload.storage.aws_local:
            adapter: 'aws'
            options:
                client: 'Aws\S3\S3ClientLocal'
                bucket: '%env(AMAZON_S3_BUCKET_NAME_PRIVATE)%'
                prefix: 'uploads'

        upload.storage.local:
            adapter: 'local'
            options:
                directory: '%kernel.project_dir%/var/uploads'
                permissions:
                    file:
                        private: 0o644
                    dir:
                        private: 0o755

        upload.storage:
            adapter: 'lazy'
            options:
                source: 'upload.storage.%env(DEFAULT_STORAGE_ADAPTER)%'
