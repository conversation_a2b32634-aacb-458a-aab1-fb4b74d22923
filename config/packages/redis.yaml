snc_redis:
    clients:
        default:
            type: phpredis
            alias: default
            dsn: '%redis_dsn%'
            options:
                connection_persistent: '%env(bool:REDIS_CONNECTION_PERSISTENT)%'
                connection_timeout: '%env(int:REDIS_CONNECTION_TIMEOUT)%'
                read_write_timeout: '%env(int:REDIS_READ_WRITE_TIMEOUT)%'
                prefix: '__GeneratedCachePrefix__'