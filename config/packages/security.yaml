security:
    enable_authenticator_manager: true
    access_decision_manager:
        strategy: unanimous
        allow_if_all_abstain: true

    role_hierarchy:
        ROLE_PARTICIPANT: ROLE_USER
        ROLE_ADMIN:       ROLE_USER

    password_hashers:
        OpenLoyalty\User\Infrastructure\Entity\User:
            algorithm: bcrypt
            cost: 4

    providers:
        admin_provider:
            id: OpenLoyalty\User\Infrastructure\Service\AdminProvider
        customer_provider:
            id: OpenLoyalty\User\Infrastructure\Service\CustomerProvider
        chain_provider:
            id: OpenLoyalty\User\Infrastructure\Service\ChainUserProvider
        oidc_user_provider:
            id: OpenLoyalty\User\Infrastructure\Service\OidcUserProvider
    firewalls:
        api_token_refresh:
            pattern: ^/api/token/refresh
            stateless: true
            provider: chain_provider
            refresh_jwt: ~
        api_member_token_refresh:
            pattern: ^/api/([a-zA-Z_\-0-9]+)/token/refresh
            stateless: true
            provider: customer_provider
            refresh_jwt: ~
        admin_login:
            pattern:  ^/api/admin/login_check
            stateless: true
            form_login:
                check_path:               /api/admin/login_check
                success_handler:          lexik_jwt_authentication.handler.authentication_success
                failure_handler:          lexik_jwt_authentication.handler.authentication_failure
                require_previous_session: false
                username_parameter: 'username'
                password_parameter: 'password'
                provider: admin_provider
        member_login:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/member/login
            stateless: true
            provider: customer_provider
            form_login:
                check_path:               api_member_login_check
                success_handler:          lexik_jwt_authentication.handler.authentication_success
                failure_handler:          lexik_jwt_authentication.handler.authentication_failure
                require_previous_session: false
                username_parameter: 'username'
                password_parameter: 'password'
        admin_oidc_login:
            pattern: ^/api/admin/oidc/login_check
            provider: oidc_user_provider
            access_token:
                token_handler:
                    oidc_user_info:
                        claim: email
                        base_uri: '%oidc_user_info_url%'
                success_handler: lexik_jwt_authentication.handler.authentication_success
                failure_handler: lexik_jwt_authentication.handler.authentication_failure
        refresh:
            pattern:  ^/api/token/refresh
            stateless: true
        refresh_member:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/token/refresh
            stateless: true
        settings_choices:
            pattern:  ^/api/([a-zA-Z_\-0-9]+/)?settings/choices
            stateless: true
        self_registration:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/member/register
            stateless: true
        activate_account:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/member/activate
            stateless: true
        password_reset:
            pattern:  ^/api/admin/password/reset
            stateless: true
        password_reset_request:
            pattern:  ^/api/admin/password/reset/request
            stateless: true
        customer_password_reset:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/member/password/reset.*
            stateless: true
        customer_resend_code:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/member/resend-code
            stateless: true
        reward_brand_icon:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/reward/([a-zA-Z\-0-9]+)/brand_icon
            methods: ["GET"]
            stateless: true
        reward_photo:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/reward/([a-zA-Z\-0-9]+)/photo
            methods: ["GET"]
            stateless: true
        tier_photo:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/tier/([a-zA-Z\-0-9]+)/photo
            methods: ["GET"]
            stateless: true
        settings_logo:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/settings/logo
            methods: ["GET"]
            stateless: true
        settings_activation:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/settings/activation
            methods: ["GET"]
            stateless: true
        doc:
            pattern:  ^/doc
            stateless: true
        translations:
            pattern:  ^/api/translation(\?.*)?$
            methods: ["GET"]
            stateless: true
        customer_avatar:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/member/([a-zA-Z\-0-9]+)/avatar
            methods: ["GET"]
            stateless: true
        admin_reward_featured:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/reward/public
            methods: ["GET"]
            stateless: true
        manifest:
            pattern:  ^/api/([a-zA-Z_\-0-9]+)/settings/manifest
            methods: ["GET"]
            stateless: true
        healthcheck:
            pattern:  ^/api/healthcheck
            methods: ["GET"]
            stateless: true
        version:
            pattern:  ^/api/?$
            methods: ["GET"]
            stateless: true
        admin_api:
            pattern:   ^/api
            stateless: true
            provider: chain_provider
            entry_point: OpenLoyalty\User\Infrastructure\Security\Authenticator\TokenAuthenticator
            custom_authenticators:
              - OpenLoyalty\User\Infrastructure\Security\Authenticator\TokenAuthenticator
              - app.jwt_token_authenticator
              - OpenLoyalty\User\Infrastructure\Authentication\Provider\Auth0AuthenticationProvider
        main:
            pattern: ^/
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
    access_control:
        - { path: ^/api/admin/(([a-zA-Z_\-0-9]+/)?)login_check, roles: PUBLIC_ACCESS }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/reward/public, roles: PUBLIC_ACCESS, methods: ["GET"] }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/member/register, roles: PUBLIC_ACCESS }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/member/activate/*, roles: PUBLIC_ACCESS }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/reward/([a-zA-Z\-0-9]+)/photo, roles: PUBLIC_ACCESS, methods: ["GET"] }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/reward/([a-zA-Z\-0-9]+)/brand_icon, roles: PUBLIC_ACCESS, methods: ["GET"] }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/rewardCategory(/[a-zA-Z\-0-9]+)?, roles: PUBLIC_ACCESS, methods: ["GET"] }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/earningRule/([a-zA-Z\-0-9]+)/photo, roles: PUBLIC_ACCESS, methods: ["GET"] }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/tier/([a-zA-Z\-0-9]+)/photo, roles: PUBLIC_ACCESS, methods: ["GET"] }
        - { path: ^/api/admin/password/reset, roles: PUBLIC_ACCESS }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/member/password/reset.*, roles: PUBLIC_ACCESS }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/member/resend-code, roles: PUBLIC_ACCESS }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/member/login, roles: PUBLIC_ACCESS }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/member/login_check, roles: PUBLIC_ACCESS }
        - { path: ^/api/token/refresh, roles: PUBLIC_ACCESS }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/token/refresh, roles: PUBLIC_ACCESS }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/settings/manifest, roles: PUBLIC_ACCESS }
        - { path: ^/api/([a-zA-Z_\-0-9]+/)?settings/choices, roles: PUBLIC_ACCESS }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/settings/logo, roles: PUBLIC_ACCESS, methods: ["GET"] }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/settings/activation, roles: PUBLIC_ACCESS, methods: ["GET"] }
        - { path: ^/api/translation(\?.*)?$, roles: PUBLIC_ACCESS, methods: ["GET"] }
        - { path: ^/api/healthcheck, roles: PUBLIC_ACCESS, methods: ["GET"] }
        - { path: ^/api/?$, roles: PUBLIC_ACCESS, methods: ["GET"] }
        - { path: ^/api/([a-zA-Z_\-0-9]+)/member/([a-zA-Z\-0-9]+)/avatar, roles: PUBLIC_ACCESS, methods: ["GET"] }
        - { path: ^/api, roles: IS_AUTHENTICATED_FULLY }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            OpenLoyalty\User\Infrastructure\Entity\User:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
