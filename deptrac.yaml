imports:
    - src/Account/deptrac.yaml
    - src/Achievement/deptrac.yaml
    - src/Analytics/deptrac.yaml
    - src/Audit/deptrac.yaml
    - src/Campaign/deptrac.yaml
    - src/Channel/deptrac.yaml
    - src/Core/deptrac.yaml
    - src/CustomEvent/deptrac.yaml
    - src/Tools/BulkActions/deptrac.yaml
    - src/DataAnalytics/deptrac.yaml
    - src/Event/deptrac.yaml
    - src/Export/deptrac.yaml
    - src/Import/deptrac.yaml
    - src/InternalEvent/deptrac.yaml
    - src/Level/deptrac.yaml
    - src/Member/deptrac.yaml
    - src/Messaging/deptrac.yaml
    - src/Points/deptrac.yaml
    - src/Reward/deptrac.yaml
    - src/Segment/deptrac.yaml
    - src/Settings/deptrac.yaml
    - src/Transaction/deptrac.yaml
    - src/Translation/deptrac.yaml
    - src/User/deptrac.yaml
    - src/Utility/deptrac.yaml
    - src/Leaderboard/deptrac.yaml

deptrac:
    formatters:
        graphviz:
            groups:
                Account:
                    - AccountDomain
                    - AccountApplication
                    - AccountInfrastructure
                    - AccountUi
                Achievement:
                    - AchievementDomain
                    - AchievementApplication
                    - AchievementInfrastructure
                    - AchievementUi
                Analytics:
                    - AnalyticsDomain
                    - AnalyticsApplication
                    - AnalyticsInfrastructure
                    - AnalyticsUi
                Audit:
                    - AuditDomain
                    - AuditApplication
                    - AuditInfrastructure
                    - AuditUi
                Campaign:
                    - CampaignDomain
                    - CampaignApplication
                    - CampaignInfrastructure
                    - CampaignUi
                Channel:
                    - ChannelDomain
                    - ChannelApplication
                    - ChannelInfrastructure
                    - ChannelUi
                Core:
                    - CoreDomain
                    - CoreApplication
                    - CoreInfrastructure
                    - CoreUi
                CustomEvent:
                    - CustomEventDomain
                    - CustomEventApplication
                    - CustomEventInfrastructure
                    - CustomEventUi
                DataAnalytics:
                    - DataAnalyticsDomain
                    - DataAnalyticsApplication
                    - DataAnalyticsInfrastructure
                    - DataAnalyticsUi
                Export:
                    - ExportDomain
                    - ExportApplication
                    - ExportInfrastructure
                    - ExportUi
                GroupOfValues:
                    - GroupOfValuesDomain
                    - GroupOfValuesApplication
                    - GroupOfValuesInfrastructure
                    - GroupOfValuesUi
                Import:
                    - ImportDomain
                    - ImportApplication
                    - ImportInfrastructure
                    - ImportUi
                InternalEvent:
                    - InternalEventDomain
                    - InternalEventApplication
                    - InternalEventInfrastructure
                    - InternalEventUi
                Level:
                    - LevelDomain
                    - LevelApplication
                    - LevelInfrastructure
                    - LevelUi
                Member:
                    - MemberDomain
                    - MemberApplication
                    - MemberInfrastructure
                    - MemberUi
                Messaging:
                    - MessagingDomain
                    - MessagingApplication
                    - MessagingInfrastructure
                    - MessagingUi
                Points:
                    - PointsDomain
                    - PointsApplication
                    - PointsInfrastructure
                    - PointsUi
                Reward:
                    - RewardDomain
                    - RewardApplication
                    - RewardInfrastructure
                    - RewardUi
                Segment:
                    - SegmentDomain
                    - SegmentApplication
                    - SegmentInfrastructure
                    - SegmentUi
                Settings:
                    - SettingsDomain
                    - SettingsApplication
                    - SettingsInfrastructure
                    - SettingsUi
                Transaction:
                    - TransactionDomain
                    - TransactionApplication
                    - TransactionInfrastructure
                    - TransactionUi
                Translation:
                    - TranslationDomain
                    - TranslationApplication
                    - TranslationInfrastructure
                    - TranslationUi
                User:
                    - UserDomain
                    - UserApplication
                    - UserInfrastructure
                    - UserUi
                Utility:
                    - UtilityDomain
                    - UtilityApplication
                    - UtilityInfrastructure
                    - UtilityUi
